import { useState, useRef } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, Paper, Popper, Fade, Divider } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import BusinessIcon from '@mui/icons-material/Business';

const ClientInfoTooltip = ({ client }) => {
  const [open, setOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const iconRef = useRef(null);

  // Función para mostrar el tooltip inmediatamente
  const handleMouseEnter = (event) => {
    // Guardar el elemento ancla para el Popper
    setAnchorEl(event.currentTarget);
    setOpen(true);
  };

  // Función para ocultar el tooltip
  const handleMouseLeave = () => {
    setOpen(false);
  };

  // Si no hay cliente, mostrar un mensaje
  if (!client) {
    return <Typography variant="body2">Cliente no especificado</Typography>;
  }

  // Si el cliente no tiene datos de empresa, mostrar un mensaje básico
  if (!client.company) {
    return <Typography variant="body2">{client.name || 'Cliente sin nombre'}</Typography>;
  }

  // Extraer información del cliente
  const companyName = client.company?.name || 'Empresa no especificada';
  const contactName = client.contact ?
    `${client.contact.first_name || ''} ${client.contact.last_name || ''}`.trim() :
    'Contacto no especificado';

  return (
    <>
      <Box
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        ref={iconRef}
        sx={{
          cursor: 'help',
          display: 'inline-block',
          maxWidth: '100%',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          '&:hover': {
            textDecoration: 'underline',
            color: 'primary.main'
          }
        }}
      >
        <Typography
          component="span"
          sx={{
            fontSize: '0.85rem'
          }}
        >
          {companyName}
        </Typography>
      </Box>

      <Popper
        open={open}
        anchorEl={anchorEl}
        placement="bottom-start"
        transition
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 10],
            },
          },
        ]}
        sx={{ zIndex: 1500 }}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={300}>
            <Paper
              elevation={3}
              sx={{
                p: 2,
                maxWidth: 550,
                maxHeight: 600,
                overflow: 'auto',
                bgcolor: '#f5f5f5',
                border: '1px solid #e0e0e0',
                borderRadius: 1
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <BusinessIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" color="primary" fontWeight="bold">
                  Información del Cliente
                </Typography>
              </Box>
              <Divider sx={{ mb: 2 }} />

              {/* Información de la empresa */}
              <Typography variant="subtitle2" color="primary" gutterBottom sx={{ mt: 1, borderBottom: '1px solid #e0e0e0', pb: 0.5, fontWeight: 'bold' }}>
                Empresa
              </Typography>
              <Box sx={{ mb: 2, pl: 2 }}>
                <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                  <strong>Nombre:</strong> {client.company?.name || 'No especificado'}
                </Typography>
                {client.billing_code && (
                  <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                    <strong>CIF/NIF:</strong> {client.billing_code}
                  </Typography>
                )}
                {client.company?.address && (
                  <>
                    <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                      <strong>Dirección:</strong> {client.company.address.street || 'No especificada'}
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                      <strong>Ciudad:</strong> {client.company.address.city || 'No especificada'}
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                      <strong>Código Postal:</strong> {client.company.address.postal_code || 'No especificado'}
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                      <strong>Región:</strong> {client.company.address.region || 'No especificada'}
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                      <strong>País:</strong> {client.company.address.country || 'No especificado'}
                    </Typography>
                  </>
                )}
              </Box>

              {/* Información de contacto */}
              <Typography variant="subtitle2" color="primary" gutterBottom sx={{ mt: 1, borderBottom: '1px solid #e0e0e0', pb: 0.5, fontWeight: 'bold' }}>
                Contacto
              </Typography>
              <Box sx={{ mb: 2, pl: 2 }}>
                {client.contact ? (
                  <>
                    <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                      <strong>Nombre:</strong> {contactName}
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                      <strong>Email:</strong> {client.contact.email || 'No especificado'}
                    </Typography>
                    <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                      <strong>Teléfono:</strong> {client.contact.phone || 'No especificado'}
                    </Typography>
                    {client.contact.position && (
                      <Typography variant="body2" gutterBottom sx={{ fontSize: '0.9rem' }}>
                        <strong>Cargo:</strong> {client.contact.position}
                      </Typography>
                    )}
                  </>
                ) : (
                  <Typography variant="body2" sx={{ fontSize: '0.9rem' }}>No hay información de contacto</Typography>
                )}
              </Box>


            </Paper>
          </Fade>
        )}
      </Popper>
    </>
  );
};

ClientInfoTooltip.propTypes = {
  client: PropTypes.object
};

export default ClientInfoTooltip;
