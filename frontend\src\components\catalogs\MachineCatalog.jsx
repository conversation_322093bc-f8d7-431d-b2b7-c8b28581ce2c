import {
  useState, useEffect, useCallback, useRef
} from 'react';
import { API_URL, buildApiUrl } from '../../config';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Alert,
  Snackbar,
  Grid,
  Tabs,
  Tab,
  Chip,
  Switch,
  FormControlLabel
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';

// URL de la API importada desde config.js

const MACHINE_TYPES = ['Todos', 'Offset', 'Digital', 'Plotter', 'CTP', 'Encuadernadora', 'Guillotina', 'Plegadora'];
const MACHINE_STATUS = ['Activa', 'Inactiva', 'Mantenimiento'];

const TYPE_COLORS = {
  'Offset': 'primary',
  'Digital': 'secondary',
  'Plotter': 'info',
  'CTP': 'warning',
  'Encuadernadora': 'success',
  'Guillotina': 'error',
  'Plegadora': 'default'
};

const STATUS_COLORS = {
  'Activa': 'success',
  'Inactiva': 'error',
  'Mantenimiento': 'warning'
};

const MachineCatalog = () => {
  const [machines, setMachines] = useState([]);
  const [filteredMachines, setFilteredMachines] = useState([]);
  const [selectedType, setSelectedType] = useState('Todos');
  const [showOnlyActive, setShowOnlyActive] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingMachine, setEditingMachine] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [formData, setFormData] = useState({
    machine_id: '',
    name: '',
    type: 'Offset',
    manufacturer: '',
    model: '',
    max_width: 0,
    max_height: 0,
    min_width: 0,
    min_height: 0,
    max_thickness: 0,
    status: 'Activa',
    purchase_date: '',
    last_maintenance: '',
    hourly_cost: 0,
    cfa_percentage: 0,
    setup_time: 30,
    sheets_per_hour: 0,
    maculatura: 150,
    ink_consumption: 0.15,
    print_units: 4,
    click_color_cost: 0,
    click_bw_cost: 0,
    speed: 0,
    notes: '',
    planificar: true
  });
  const fetchedRef = useRef(false);

  const fetchMachines = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/machines/'));
      if (!response.ok) throw new Error('Error al obtener el catálogo de máquinas');
      const data = await response.json();
      setMachines(data);

      // Aplicar filtros directamente
      let filtered = data;
      if (selectedType !== 'Todos') {
        filtered = filtered.filter(machine => machine.type === selectedType);
      }
      if (showOnlyActive) {
        filtered = filtered.filter(machine => machine.status === 'Activa');
      }
      setFilteredMachines(filtered);
    } catch (err) {
      setSnackbar({ open: true, message: err.message, severity: 'error' });
    }
  }, [selectedType, showOnlyActive]);

  useEffect(() => {
    if (!fetchedRef.current) {
      fetchMachines();
      fetchedRef.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleTypeChange = (event, newType) => {
    setSelectedType(newType);

    // Aplicar filtros cuando cambia el tipo
    let filtered = machines;
    if (newType !== 'Todos') {
      filtered = filtered.filter(machine => machine.type === newType);
    }
    if (showOnlyActive) {
      filtered = filtered.filter(machine => machine.status === 'Activa');
    }
    setFilteredMachines(filtered);
  };

  const handleToggleActive = () => {
    const newShowOnlyActive = !showOnlyActive;
    setShowOnlyActive(newShowOnlyActive);

    // Actualizar la lista filtrada cuando cambia el switch
    let filtered = machines;
    if (selectedType !== 'Todos') {
      filtered = filtered.filter(machine => machine.type === selectedType);
    }
    if (newShowOnlyActive) {
      filtered = filtered.filter(machine => machine.status === 'Activa');
    }
    setFilteredMachines(filtered);
  };

  const handleOpenDialog = (machine = null) => {
    if (machine) {
      setEditingMachine(machine);
      // Asegurarse de que todos los campos tengan valores por defecto
      setFormData({
        ...machine,
        hourly_cost: machine.hourly_cost || 0,
        cfa_percentage: machine.cfa_percentage || 0,
        setup_time: machine.setup_time || 30,
        sheets_per_hour: machine.sheets_per_hour || 0,
        maculatura: machine.maculatura || 150,
        ink_consumption: machine.ink_consumption || 0.15,
        print_units: machine.print_units || 4,
        click_color_cost: machine.click_color_cost || 0,
        click_bw_cost: machine.click_bw_cost || 0,
        speed: machine.speed || 0,
        notes: machine.notes || ''
      });
    } else {
      setEditingMachine(null);
      setFormData({
        machine_id: '',
        name: '',
        type: 'Offset',
        manufacturer: '',
        model: '',
        max_width: 0,
        max_height: 0,
        min_width: 0,
        min_height: 0,
        max_thickness: 0,
        status: 'Activa',
        purchase_date: '',
        last_maintenance: '',
        hourly_cost: 0,
        cfa_percentage: 0,
        setup_time: 30,
        sheets_per_hour: 0,
        click_color_cost: 0,
        click_bw_cost: 0,
        speed: 0,
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingMachine(null);
    setFormData({
      machine_id: '',
      name: '',
      type: 'Offset',
      manufacturer: '',
      model: '',
      max_width: 0,
      max_height: 0,
      min_width: 0,
      min_height: 0,
      max_thickness: 0,
      status: 'Activa',
      purchase_date: '',
      last_maintenance: '',
      hourly_cost: 0,
      cfa_percentage: 0,
      setup_time: 30,
      sheets_per_hour: 0,
      maculatura: 150,
      ink_consumption: 0.15,
      print_units: 4,
      click_color_cost: 0,
      click_bw_cost: 0,
      speed: 0,
      notes: ''
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const url = editingMachine
        ? `${API_URL}/machines/${editingMachine.machine_id}`
        : `${API_URL}/machines`;

      const response = await fetch(url, {
        method: editingMachine ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Error al guardar la máquina');

      await fetchMachines();
      handleCloseDialog();
      showSnackbar(
        editingMachine
          ? 'Máquina actualizada correctamente'
          : 'Máquina añadida correctamente'
      );
    } catch (err) {
      setSnackbar({ open: true, message: err.message, severity: 'error' });
    }
  };

  const handleDelete = async (machineId) => {
    if (!window.confirm('¿Estás seguro de que deseas eliminar esta máquina?')) {
      return;
    }

    try {
      const response = await fetch(`${API_URL}/machines/${machineId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Error al eliminar la máquina');

      await fetchMachines();
      showSnackbar('Máquina eliminada correctamente');
    } catch (err) {
      setSnackbar({ open: true, message: err.message, severity: 'error' });
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">Catálogo de Máquinas</Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={showOnlyActive}
                onChange={handleToggleActive}
                color="primary"
              />
            }
            label="Mostrar solo activas"
          />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Añadir Máquina
          </Button>
        </Box>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedType}
          onChange={handleTypeChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {MACHINE_TYPES.map((type) => (
            <Tab
              key={type}
              label={type === 'Todos' ? type : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {type}
                  {type !== 'Todos' && (
                    <Chip
                      label={machines.filter(m => m.type === type).length}
                      size="small"
                      color={TYPE_COLORS[type]}
                      sx={{ minWidth: 28, height: 20 }}
                    />
                  )}
                </Box>
              )}
              value={type}
              sx={{
                '&.Mui-selected': {
                  color: type !== 'Todos' ? `${TYPE_COLORS[type]}.main` : 'primary.main',
                  fontWeight: 'bold',
                }
              }}
            />
          ))}
        </Tabs>
      </Paper>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'primary.main' }}>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>ID</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Nombre</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Tipo</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Fabricante</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Modelo</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Estado</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Coste/Hora</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>CFA %</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Planificar</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Dimensiones Máx.</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredMachines.map((machine) => (
              <TableRow key={machine.machine_id} sx={{
                '&:nth-of-type(odd)': { backgroundColor: 'action.hover' },
                whiteSpace: 'nowrap'
              }}>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{machine.machine_id}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{machine.name}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  <Chip
                    label={machine.type}
                    color={TYPE_COLORS[machine.type]}
                    size="small"
                    variant="filled"
                    sx={{ fontWeight: 'medium' }}
                  />
                </TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{machine.manufacturer}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{machine.model}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  <Chip
                    label={machine.status}
                    color={STATUS_COLORS[machine.status]}
                    size="small"
                    variant="filled"
                    sx={{ fontWeight: 'medium' }}
                  />
                </TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  {machine.hourly_cost ? `${machine.hourly_cost.toFixed(2)} €/h` : '0.00 €/h'}
                </TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  {machine.cfa_percentage ? `${machine.cfa_percentage.toFixed(1)}%` : '0.0%'}
                </TableCell>

                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  <Chip
                    label={machine.planificar ? "Sí" : "No"}
                    color={machine.planificar ? "success" : "default"}
                    size="small"
                    variant="filled"
                  />
                </TableCell>

                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{`${machine.max_width} × ${machine.max_height} mm`}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  <IconButton
                    color="primary"
                    onClick={() => handleOpenDialog(machine)}
                    size="small"
                    sx={{ mr: 1 }}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    color="error"
                    onClick={() => handleDelete(machine.machine_id)}
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ backgroundColor: 'primary.main', color: 'white' }}>
          {editingMachine ? 'Editar Máquina' : 'Añadir Nueva Máquina'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="ID de Máquina"
                  name="machine_id"
                  value={formData.machine_id}
                  onChange={handleInputChange}
                  required
                  disabled={!!editingMachine}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Nombre"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth required>
                  <InputLabel>Tipo</InputLabel>
                  <Select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    label="Tipo"
                  >
                    {MACHINE_TYPES.slice(1).map((type) => (
                      <MenuItem key={type} value={type}>{type}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth required>
                  <InputLabel>Estado</InputLabel>
                  <Select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    label="Estado"
                  >
                    {MACHINE_STATUS.map((status) => (
                      <MenuItem key={status} value={status}>{status}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Fabricante"
                  name="manufacturer"
                  value={formData.manufacturer}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Modelo"
                  name="model"
                  value={formData.model}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Ancho Máximo (mm)"
                  name="max_width"
                  type="number"
                  value={formData.max_width}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Alto Máximo (mm)"
                  name="max_height"
                  type="number"
                  value={formData.max_height}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Ancho Mínimo (mm)"
                  name="min_width"
                  type="number"
                  value={formData.min_width}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Alto Mínimo (mm)"
                  name="min_height"
                  type="number"
                  value={formData.min_height}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Grosor Máximo (mm)"
                  name="max_thickness"
                  type="number"
                  value={formData.max_thickness}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              {formData.type === 'Digital' && (
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Velocidad (A4/minuto)"
                    name="speed"
                    type="number"
                    value={formData.speed}
                    onChange={handleInputChange}
                    helperText="Velocidad de impresión en hojas A4 por minuto"
                    InputProps={{
                      inputProps: { min: 0, step: 1 }
                    }}
                  />
                </Grid>
              )}
              {formData.type === 'Offset' && (
                <>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Tiempo de arranque (minutos)"
                      name="setup_time"
                      type="number"
                      value={formData.setup_time}
                      onChange={handleInputChange}
                      helperText="Tiempo necesario para preparar la máquina antes de imprimir"
                      InputProps={{
                        inputProps: { min: 0, step: 1 }
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Velocidad (pliegos/hora)"
                      name="sheets_per_hour"
                      type="number"
                      value={formData.sheets_per_hour}
                      onChange={handleInputChange}
                      helperText="Velocidad media de impresión en pliegos por hora"
                      InputProps={{
                        inputProps: { min: 0, step: 100 }
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Maculatura (pliegos)"
                      name="maculatura"
                      type="number"
                      value={formData.maculatura}
                      onChange={handleInputChange}
                      helperText="Pliegos adicionales para arranque y ajuste"
                      InputProps={{
                        inputProps: { min: 0, step: 10 }
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Consumo de tinta (gramos/m²)"
                      name="ink_consumption"
                      type="number"
                      value={formData.ink_consumption}
                      onChange={handleInputChange}
                      helperText="Consumo medio de tinta por metro cuadrado"
                      InputProps={{
                        inputProps: { min: 0, step: 0.1, max: 10 }
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Cuerpos de impresión"
                      name="print_units"
                      type="number"
                      value={formData.print_units}
                      onChange={handleInputChange}
                      helperText="Número de cuerpos de impresión (1, 4, 8, etc.)"
                      InputProps={{
                        inputProps: { min: 1, step: 1, max: 12 }
                      }}
                    />
                  </Grid>
                </>
              )}
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Coste por Hora (€)"
                  name="hourly_cost"
                  type="number"
                  value={formData.hourly_cost}
                  onChange={handleInputChange}
                  required
                  InputProps={{
                    inputProps: { min: 0, step: 0.01 }
                  }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="CFA (% del coste por hora)"
                  name="cfa_percentage"
                  type="number"
                  value={formData.cfa_percentage}
                  onChange={handleInputChange}
                  required
                  helperText="Coste Fijo de Arranque como porcentaje del coste por hora"
                  InputProps={{
                    inputProps: { min: 0, max: 100, step: 0.1 }
                  }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Fecha de Compra"
                  name="purchase_date"
                  type="date"
                  value={formData.purchase_date}
                  onChange={handleInputChange}
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Último Mantenimiento"
                  name="last_maintenance"
                  type="date"
                  value={formData.last_maintenance}
                  onChange={handleInputChange}
                  required
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              {formData.type === 'Digital' && (
                <>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Coste por Click Color (€)"
                      name="click_color_cost"
                      type="number"
                      value={formData.click_color_cost}
                      onChange={handleInputChange}
                      helperText="Coste por impresión a color (CMYK) en formato SRA3"
                      InputProps={{
                        inputProps: { min: 0, step: 0.0001 }
                      }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Coste por Click B/N (€)"
                      name="click_bw_cost"
                      type="number"
                      value={formData.click_bw_cost}
                      onChange={handleInputChange}
                      helperText="Coste por impresión en blanco y negro en formato SRA3"
                      InputProps={{
                        inputProps: { min: 0, step: 0.0001 }
                      }}
                    />
                  </Grid>

                </>
              )}
              <Grid item xs={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.planificar}
                      onChange={(e) => setFormData({...formData, planificar: e.target.checked})}
                      color="primary"
                    />
                  }
                  label="Mostrar en planificador"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notas"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  multiline
                  rows={3}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCloseDialog} variant="outlined">Cancelar</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {editingMachine ? 'Guardar Cambios' : 'Añadir Máquina'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} variant="filled">
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default MachineCatalog;
