from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from typing import Optional, Dict, Any

from services.auth_service import AuthService
from models.auth import UserRole

# Configurar OAuth2
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

# Crear instancia del servicio de autenticación
auth_service = AuthService()

def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    Dependencia para obtener el usuario actual a partir del token JWT.
    
    Args:
        token: Token JWT obtenido de la cabecera de autorización
        
    Returns:
        Dict[str, Any]: Datos del usuario autenticado
        
    Raises:
        HTTPException: Si el token es inválido o el usuario no existe
    """
    valid, message, payload = auth_service.verify_token(token)
    if not valid:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=message,
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = auth_service.get_user_from_token(token)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No se pudo obtener información del usuario",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

def get_current_active_user(current_user: Dict[str, Any] = Depends(get_current_user)) -> Dict[str, Any]:
    """
    Dependencia para obtener el usuario actual y verificar que esté activo.
    
    Args:
        current_user: Usuario obtenido de get_current_user
        
    Returns:
        Dict[str, Any]: Datos del usuario activo
        
    Raises:
        HTTPException: Si el usuario no está activo
    """
    if not current_user.get("active", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Usuario inactivo",
        )
    
    return current_user

def get_admin_user(current_user: Dict[str, Any] = Depends(get_current_active_user)) -> Dict[str, Any]:
    """
    Dependencia para obtener el usuario actual y verificar que sea administrador.
    
    Args:
        current_user: Usuario obtenido de get_current_active_user
        
    Returns:
        Dict[str, Any]: Datos del usuario administrador
        
    Raises:
        HTTPException: Si el usuario no es administrador
    """
    if current_user.get("role") != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Se requieren privilegios de administrador",
        )
    
    return current_user
