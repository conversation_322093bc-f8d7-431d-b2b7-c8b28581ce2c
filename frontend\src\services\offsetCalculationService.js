/**
 * Servicio para el cálculo de pliegos y costes en máquinas offset
 * Este archivo contiene funciones para calcular pliegos, costes de papel, máquina y planchas
 * utilizando el endpoint /v2/calculate-offset
 */
import { buildApiUrl } from '../config';
import { generatePartJobSummary } from './jobSummaryService';
import { recalculateShippingCost } from './shippingCalculationService';
import { calculatePartPaperWeight } from './silentSheetCalculationService';

/**
 * Calcula los pliegos para una parte específica del presupuesto usando una máquina offset
 * @param {Object} params - Parámetros para el cálculo
 * @param {Object} params.part - Parte del presupuesto a calcular
 * @param {number} params.copies - Número de copias a imprimir
 * @param {Function} params.buildApiUrl - Función para construir URLs de API
 * @param {Function} params.showSnackbar - Función para mostrar notificaciones
 * @param {Function} params.setCalculatingSheets - Función para actualizar el estado de cálculo
 * @param {Function} params.setBudgetParts - Función para actualizar las partes del presupuesto
 * @param {Function} params.setBudget - Función para actualizar el presupuesto
 * @param {Function} params.setCurrentCalculatedPart - Función para establecer la parte calculada actual
 * @param {Function} params.setSheetCalculationModal - Función para mostrar/ocultar el modal de cálculo
 * @param {Function} params.setSheetCalculation - Función para establecer los datos del cálculo de pliegos
 * @param {Array} params.budgetParts - Array con todas las partes del presupuesto
 * @param {Object} params.budget - Objeto con los datos del presupuesto
 * @param {number} params.partIndex - Índice de la parte en el array de partes
 * @returns {Promise<Object>} - Resultado del cálculo
 */
export const calculateOffsetSheets = async ({
  part,
  copies,
  buildApiUrl,
  showSnackbar,
  setCalculatingSheets,
  setBudgetParts,
  setBudget,
  setCurrentCalculatedPart,
  setSheetCalculationModal,
  setSheetCalculation,
  budgetParts,
  budget,
  partIndex
}) => {
  // Validar que tenemos todos los datos necesarios
  if (!part || !part.paper || !part.machine || !part.pageCount) {
    showSnackbar('Selecciona papel, máquina y número de páginas para esta parte', 'warning');
    return null;
  }

  // Validar que la máquina es de tipo offset
  if (part.machine.type !== 'Offset') {
    showSnackbar('Esta función solo puede usarse con máquinas offset', 'warning');
    return null;
  }

  setCalculatingSheets(true);

  try {
    // Determinar el tamaño de página
    let pageSize;

    if (part.pageSize === 'Personalizado') {
      // Si es personalizado, usar el valor del campo customPageSize
      const dimensions = part.customPageSize.split('x').map(dim => parseFloat(dim.trim()));
      if (dimensions.length !== 2 || isNaN(dimensions[0]) || isNaN(dimensions[1])) {
        showSnackbar('Formato de tamaño personalizado incorrecto. Usa el formato "ancho x alto"', 'error');
        setCalculatingSheets(false);
        return null;
      }
      pageSize = {
        width: dimensions[0],
        height: dimensions[1]
      };
    } else {
      // Si es un tamaño estándar, usar las dimensiones predefinidas
      switch (part.pageSize) {
        case 'A4':
          pageSize = { width: 210, height: 297 };
          break;
        case 'A5':
          pageSize = { width: 148, height: 210 };
          break;
        case 'A3':
          pageSize = { width: 297, height: 420 };
          break;
        case 'Carta':
          pageSize = { width: 216, height: 279 };
          break;
        case 'Legal':
          pageSize = { width: 216, height: 356 };
          break;
        default:
          showSnackbar('Tamaño de página no reconocido', 'error');
          setCalculatingSheets(false);
          return null;
      }
    }

    // Obtener dimensiones del papel seleccionado (en mm)
    let paperWidth = part.paper.dimension_width;
    let paperHeight = part.paper.dimension_height;

    // Si las dimensiones son muy pequeñas, podrían estar en cm, convertir a mm
    if (paperWidth < 100 || paperHeight < 100) {
      paperWidth = paperWidth * 10;
      paperHeight = paperHeight * 10;
    }

    // Validar que todos los valores necesarios estén presentes
    if (!paperWidth || !paperHeight) {
      showSnackbar('El papel seleccionado no tiene dimensiones válidas', 'error');
      setCalculatingSheets(false);
      return null;
    }

    // Asegurarse de que todos los valores sean números
    const numPaginas = parseInt(part.pageCount);
    const anchoPagina = parseFloat(pageSize.width);
    const altoPagina = parseFloat(pageSize.height);
    const anchoPliego = parseFloat(paperWidth);
    const altoPliego = parseFloat(paperHeight);

    // Validar que todos los valores sean números válidos
    if (isNaN(numPaginas) || isNaN(anchoPagina) || isNaN(altoPagina) || isNaN(anchoPliego) || isNaN(altoPliego)) {
      showSnackbar('Algunos valores no son números válidos', 'error');
      setCalculatingSheets(false);
      return null;
    }

    // Validar que el número de páginas sea par (requerido por el backend)
    if (numPaginas % 2 !== 0) {
      showSnackbar('El número de páginas debe ser par', 'warning');
      setCalculatingSheets(false);
      return null;
    }

    // Preparar los datos para el endpoint v2/calculate-offset
    const requestBody = {
      machine_id: part.machine?.machine_id || part.machine?.product_id,
      copies: parseInt(copies) || 1,
      colors_front: part.colorConfig?.frontColors || 4,
      colors_back: part.colorConfig?.backColors || 0,
      paper_id: part.paper?.paper_id || part.paper?.product_id,
      custom_setup_time: part.customSetupTime,
      custom_sheets_per_hour: part.customSheetsPerHour,
      custom_maculatura: part.customMaculatura,

      // Parámetros para modo automático
      num_paginas: numPaginas,
      ancho_pagina: anchoPagina,
      alto_pagina: altoPagina,
      ancho_pliego: anchoPliego,
      alto_pliego: altoPliego
    };



    // Mostrar en consola los parámetros que se envían al endpoint
    console.log('Parámetros enviados al endpoint /v2/calculate-offset:', JSON.stringify(requestBody, null, 2));

    // Llamar a la API para calcular pliegos
    const response = await fetch(buildApiUrl('/v2/calculate-offset'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Error al calcular pliegos: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Mostrar en consola la respuesta completa del endpoint
    console.log('Respuesta completa del endpoint /v2/calculate-offset:', JSON.stringify(data, null, 2));

    // La respuesta del endpoint se mostrará en el useEffect del componente

    // Verificar si tenemos esquemas_utilizados en la respuesta
    // (Esta información se usa en el frontend para mostrar detalles adicionales)

    // Extraer costes directamente de la respuesta
    const paperCost = data.paper_cost || 0;
    const machineCost = data.printing_cost + (data.cfa_cost || 0);
    const plateCost = data.plates_cost || 0;

    // Pasar directamente los datos del endpoint al modal, añadiendo solo lo que falte
    const normalizedData = {
      // Asegurarnos de que las propiedades esenciales estén presentes
      copies: copies,
      // Mantener la estructura de mejor_combinacion para compatibilidad
      mejor_combinacion: {
        total_pliegos: data.total_sheets || 0,
        total_planchas: data.total_plates || 0,
        esquemas_utilizados: data.esquemas_utilizados || [],
        copies: copies,
        total_passes: data.total_passes || 0
      },
      // Añadir todos los datos del endpoint directamente
      ...data,

      // Añadir los parámetros calculados a partir de los esquemas utilizados
      // is_duplex se calcula a partir de colors_back
      is_duplex: data.colors_back > 0,
      // Estos campos ahora se calculan a partir de esquemas_utilizados en lugar de venir del backend
      is_workandturn: data.using_detailed_schemes && data.esquemas_utilizados && data.esquemas_utilizados.some(esquema => esquema.es_tira_retira),
      is_using_workandturn: data.using_detailed_schemes && data.esquemas_utilizados && data.esquemas_utilizados.some(esquema => esquema.es_tira_retira),
      can_use_workandturn: data.print_units < 8, // Calculado a partir del número de cuerpos de impresión
      sheet_type: data.using_detailed_schemes && data.esquemas_utilizados && data.esquemas_utilizados.length > 0 ?
                  data.esquemas_utilizados[0].sheet_type || "WorkAndBack" : "WorkAndBack",
      folding_scheme: data.using_detailed_schemes && data.esquemas_utilizados && data.esquemas_utilizados.length > 0 ?
                     data.esquemas_utilizados[0].nombre : null,
      // Calculamos needs_two_passes a partir de los esquemas
      needs_two_passes: data.using_detailed_schemes && data.esquemas_utilizados &&
                       data.esquemas_utilizados.some(esquema => esquema.needs_two_passes),
      // Añadir datos de la máquina en la estructura esperada por el modal
      machine_data: {
        setup_time: data.setup_time_minutes || 0,
        setup_time_minutes: data.setup_time_minutes || 0,
        setup_time_total_minutes: data.setup_time_total_minutes || 0,
        printing_time_minutes: data.printing_time_minutes || 0,
        plate_change_time_minutes: data.plate_change_time_minutes || 0,
        plate_changes: data.plate_changes || 0,
        total_time_minutes: data.total_time_minutes || 0,
        total_time_hours: data.total_time_hours || 0,
        hourly_cost: data.hourly_cost || 0,
        cfa_percentage: data.cfa_percentage || 0,
        ink_cost: data.ink_cost || 0,
        ink_consumption: data.ink_consumption || 0,
        ink_price_per_kg: data.ink_price_per_kg || 0,
        ink_weight_kg: data.ink_weight_kg || 0,
        sheets_per_hour: data.sheets_per_hour || 0,
        name: data.machine_name || '',
        maculatura: data.maculatura || 0
      },
      // Añadir los nuevos campos de maculatura
      total_physical_sheets: data.total_physical_sheets || 0,
      maculatura: data.maculatura || 0,
      total_maculatura: data.total_maculatura || 0,
      total_paper: data.total_paper || 0,
      // Añadir datos del papel en la estructura esperada por el modal
      paper_data: {
        paper_name: data.paper_name || '',
        name: data.paper_name || '',
        descriptive_name: data.paper_name || '',
        price_per_1000: data.paper_cost_per_1000 || 0,
        paper_cost: data.paper_cost || 0
      },
      // Costos
      paper_cost: data.paper_cost || 0,
      machine_cost: data.printing_cost + (data.cfa_cost || 0),
      plate_cost: data.plates_cost || 0,
      total_cost: data.total_cost || 0
    };



    // Generar el resumen del trabajo y añadirlo a la descripción
    const jobSummary = generatePartJobSummary(part, data, budget, copies);
    const currentDesc = budget.description || '';

    // Actualizar la descripción con el resumen del trabajo
    setBudget({
      ...budget,
      description: currentDesc + (currentDesc ? '\n\n' : '') + jobSummary
    });

    // Actualizar la parte con los resultados del cálculo
    const updatedParts = [...budgetParts];
    updatedParts[partIndex] = {
      ...part,
      sheetCalculation: normalizedData,
      paperCost,
      machineCost,
      plateCost,
      totalCost: paperCost + machineCost + plateCost
    };

    setBudgetParts(updatedParts);
    
    // Calcular el peso total del papel y recalcular el costo de envío
    try {
      console.log('Recalculando el costo de envío después del cálculo offset...');
      
      // Calcular el peso de cada parte y el peso total
      let totalPaperWeight = 0;
      const partWeights = [];
      
      updatedParts.forEach((p, idx) => {
        const partWeight = calculatePartPaperWeight(p, budget.copies);
        partWeights.push({
          partIndex: idx,
          weight: partWeight
        });
        totalPaperWeight += partWeight;
      });
      
      // Redondear a 2 decimales
      const weightData = {
        totalWeight: Math.round(totalPaperWeight * 100) / 100,
        partWeights: partWeights
      };
      
      console.log(`Peso total calculado para envío: ${weightData.totalWeight} kg`);
      console.log('Pesos por parte:', weightData.partWeights);
      
      // Llamar a la función de recálculo de envío
      recalculateShippingCost({
        budgetParts: updatedParts,
        budget,
        calculateTotalPaperWeight: () => weightData.totalWeight,
        buildApiUrl,
        setBudget,
        showSnackbar
      }).then(shippingResult => {
        console.log('Resultado del cálculo de envío:', shippingResult);
        
        // Distribuir el costo de envío entre las partes
        if (shippingResult && shippingResult.cost > 0 && weightData.totalWeight > 0) {
          const updatedPartsWithShipping = [...updatedParts];
          
          weightData.partWeights.forEach(partWeight => {
            const partShippingCost = (partWeight.weight / weightData.totalWeight) * shippingResult.cost;
            const roundedShippingCost = Math.round(partShippingCost * 100) / 100;
            
            // Actualizar el costo total de la parte incluyendo el envío
            const partToUpdate = updatedPartsWithShipping[partWeight.partIndex];
            const partCosts = partToUpdate.paperCost + partToUpdate.machineCost + partToUpdate.plateCost;
            
            updatedPartsWithShipping[partWeight.partIndex] = {
              ...partToUpdate,
              shippingCost: roundedShippingCost,
              totalCost: partCosts + roundedShippingCost
            };
          });
          
          // Actualizar las partes con los costos de envío incluidos
          setBudgetParts(updatedPartsWithShipping);
        }
      }).catch(error => {
        console.error('Error al recalcular el costo de envío:', error);
      });
    } catch (error) {
      console.error('Error al calcular el peso del papel para envío:', error);
    }

    // Mostrar en consola los datos normalizados que se pasan al componente
    console.log('Datos normalizados para el componente:', JSON.stringify(normalizedData, null, 2));

    // Verificar si setSheetCalculation está disponible antes de llamarlo
    if (typeof setSheetCalculation === 'function') {
      setSheetCalculation(normalizedData);
    }

    // Actualizar la parte actual y mostrar el modal
    if (typeof setCurrentCalculatedPart === 'function') {
      setCurrentCalculatedPart(part);
    }

    // Mostrar el modal si la función está disponible
    if (typeof setSheetCalculationModal === 'function') {
      setSheetCalculationModal(true);
    }

    // Finalizar el estado de cálculo
    if (typeof setCalculatingSheets === 'function') {
      setCalculatingSheets(false);
    }

    return normalizedData;
  } catch (error) {
    showSnackbar(`Error al calcular pliegos: ${error.message}`, 'error');
    setCalculatingSheets(false);
    return null;
  }
};

/**
 * Calcula el total de pliegos físicos a partir de los esquemas utilizados
 * @param {Array} esquemas - Esquemas utilizados en el cálculo
 * @param {number} copies - Número de copias a imprimir
 * @returns {number} - Total de pliegos físicos
 */
export const calculateTotalPhysicalSheets = (esquemas, copies) => {
  if (!esquemas || esquemas.length === 0 || !copies) return 0;

  let totalPliegosFisicos = 0;

  // Para cada esquema utilizado
  esquemas.forEach(esquema => {
    // Si es Tira y Retira, cada pliego físico produce 2 unidades
    const factor = esquema.es_tira_retira ? 0.5 : 1;
    totalPliegosFisicos += esquema.numero_pliegos * factor * parseInt(copies);
  });

  return Math.ceil(totalPliegosFisicos);
};

export default {
  calculateOffsetSheets,
  calculateTotalPhysicalSheets
};
