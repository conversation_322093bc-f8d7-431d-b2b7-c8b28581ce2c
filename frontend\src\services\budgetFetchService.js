/**
 * Servicio para obtener y procesar presupuestos
 */
import { buildApiUrl } from '../config';

/**
 * Obtiene un presupuesto por ID
 * @param {string} budgetId - ID del presupuesto
 * @param {Object} options - Opciones para la obtención del presupuesto
 * @returns {Promise<Object>} - Presupuesto obtenido
 */
export const fetchBudgetById = async (budgetId, options) => {
  const {
    // Funciones de actualización de estado
    setBudget,
    setBudgetParts,
    setSelectedProcesses,
    setCalculatedProcessCost,
    setPdfInfo,
    setSelectedPdf,
    setColorConfig,
    setProductConfig,
    setIsEditMode,

    // Datos de catálogos
    clients,
    papers,
    machines,
    processes,

    // Funciones para obtener datos
    fetchClients,
    fetchPapers,
    fetchMachines,

    // Referencias
    fetchedClientsRef,
    fetchedPapersRef,
    fetchedMachinesRef,

    // Funciones auxiliares
    createEmptyPart,
    getPageSizeFromDimensions,
    showSnackbar
  } = options;

  try {
    console.log(`Obteniendo presupuesto con ID: ${budgetId}`);

    // Obtener el token de autenticación del localStorage
    const token = localStorage.getItem('auth_token');

    if (!token) {
      throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
    }

    const response = await fetch(buildApiUrl(`/budgets/${budgetId}`), {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) throw new Error('Error al obtener el presupuesto');
    const data = await response.json();
    console.log('Datos del presupuesto recibidos:', data);

    // Esperar a que se carguen los catálogos antes de establecer el presupuesto
    await Promise.all([
      fetchedClientsRef.current ? Promise.resolve() : fetchClients(),
      fetchedPapersRef.current ? Promise.resolve() : fetchPapers(),
      fetchedMachinesRef.current ? Promise.resolve() : fetchMachines()
    ]);

    // Establecer las referencias como cargadas
    fetchedClientsRef.current = true;
    fetchedPapersRef.current = true;
    fetchedMachinesRef.current = true;

    // Encontrar el objeto completo del cliente
    const clientObj = clients.find(c => c.client_id === data.client_id);

    // Actualizar el estado del formulario con los datos básicos del presupuesto
    setBudget({
      otNumber: data.ot_number || '',
      description: data.description || '',
      client: clientObj || null,
      jobType: data.job_type || '',
      copies: data.quantity?.toString() || '',
      parts: data.parts || [],
      shipping_cost: data.shipping_cost || 0,
      total_paper_weight_kg: data.total_paper_weight_kg || 0,
      pdf_filename: data.pdf_filename || null
    });

    // Cargar las partes del presupuesto
    // Primero intentamos obtener las partes del campo parts
    // Si no existe, intentamos obtenerlas del campo product_config.parts
    const budgetParts = data.parts || (data.product_config && data.product_config.parts) || [];
    console.log('Partes encontradas en el presupuesto:', {
      'data.parts': data.parts,
      'data.product_config?.parts': data.product_config?.parts,
      'budgetParts': budgetParts
    });

    if (budgetParts.length > 0) {
      console.log('Cargando partes del presupuesto:', budgetParts);

      // Mapear las partes con los objetos completos de papel y máquina
      const loadedParts = budgetParts.map(part => {
        // Encontrar los objetos completos de papel y máquina para esta parte
        const paperObj = papers.find(p => p.product_id === part.paper_id);
        const machineObj = machines.find(m => m.machine_id === part.machine_id);

        // Determinar el tamaño de página para esta parte
        let pageSize = '';
        let customPageSize = '';

        if (part.page_size) {
          const width = part.page_size.width;
          const height = part.page_size.height;

          // Verificar si es un tamaño estándar
          if (width === 210 && height === 297) {
            pageSize = 'A4';
          } else if (width === 148 && height === 210) {
            pageSize = 'A5';
          } else if (width === 297 && height === 420) {
            pageSize = 'A3';
          } else if (width === 216 && height === 279) {
            pageSize = 'Carta';
          } else {
            // Si no es un tamaño estándar, es personalizado
            pageSize = 'Personalizado';
            customPageSize = `${width} x ${height}`;
          }
        }

        // Determinar si es una máquina digital
        const isDigital = machineObj && machineObj.type === 'Digital';

        return {
          part_id: part.part_id || `part-${Date.now()}-${Math.random()}`,
          name: part.name || 'General',
          description: part.description || '',
          assembly_order: part.assembly_order || 'None', // Tipo de encuadernado
          pageSize: pageSize || 'A4',
          customPageSize: customPageSize || '',
          pageCount: part.page_count?.toString() || '',
          paper: paperObj || null,
          machine: machineObj || null,
          sheetCalculation: part.sheet_calculation || null,
          paperCost: part.paper_cost || 0,
          machineCost: part.machine_cost || 0,
          plateCost: isDigital ? 0 : (part.plate_cost || 0),
          inkCost: isDigital ? 0 : (part.ink_cost || 0), // Recuperar ink_cost para máquinas offset
          maculaturaCost: isDigital ? 0 : (part.maculatura_cost || 0), // Recuperar maculatura_cost para máquinas offset
          clickCost: isDigital ? (part.click_cost || part.plate_cost || 0) : 0, // Recuperar click_cost para máquinas digitales
          processCosts: part.process_costs || [],
          totalCost: part.total_cost || 0,
          customPrintTime: part.custom_print_time || null,
          colorConfig: part.color_config || {
            frontColors: 4,
            backColors: 4,
            pantones: 0
          }
        };
      });

      console.log('Partes cargadas:', loadedParts);
      setBudgetParts(loadedParts);
    } else {
      // Si no hay partes, crear una parte por defecto usando los datos principales del presupuesto
      const defaultPart = createEmptyPart(0, 'General');

      // Intentar usar los datos principales del presupuesto para la parte por defecto
      if (data.page_size) {
        const pageSize = getPageSizeFromDimensions(data.page_size);
        defaultPart.pageSize = pageSize;

        if (pageSize === 'Personalizado') {
          defaultPart.customPageSize = `${data.page_size.width} x ${data.page_size.height}`;
        }
      }

      if (data.page_count) {
        defaultPart.pageCount = data.page_count.toString();
      }

      if (data.paper_id) {
        defaultPart.paper = papers.find(p => p.product_id === data.paper_id) || null;
      }

      if (data.machine_id) {
        defaultPart.machine = machines.find(m => m.machine_id === data.machine_id) || null;
      }

      // Usar el tipo de encuadernado del presupuesto principal si existe
      if (data.assembly_order) {
        defaultPart.assembly_order = data.assembly_order;
      }

      // Determinar si es una máquina digital
      const isDigital = defaultPart.machine && defaultPart.machine.type === 'Digital';

      // Usar los costos del presupuesto para la parte por defecto
      defaultPart.paperCost = data.paper_cost || 0;
      defaultPart.machineCost = data.machine_cost || 0;
      defaultPart.plateCost = isDigital ? 0 : (data.plate_cost || 0);
      defaultPart.inkCost = isDigital ? 0 : (data.ink_cost || 0);
      defaultPart.maculaturaCost = isDigital ? 0 : (data.maculatura_cost || 0);
      defaultPart.clickCost = isDigital ? (data.click_cost || data.plate_cost || 0) : 0;
      defaultPart.processCosts = data.process_costs || [];
      defaultPart.totalCost = data.total_cost || 0;
      defaultPart.customPrintTime = data.custom_print_time || null;

      // Usar la configuración de colores del presupuesto principal si existe
      if (data.color_config) {
        defaultPart.colorConfig = data.color_config;
      }

      console.log('Parte por defecto creada:', defaultPart);
      setBudgetParts([defaultPart]);
    }

    // Si hay un costo total de acabados en el presupuesto principal, usarlo
    if (data.process_total_cost !== undefined) {
      console.log('Estableciendo costo total de acabados desde el presupuesto principal:', data.process_total_cost);
      setCalculatedProcessCost(data.process_total_cost);
    }

    // Cargar información del PDF si existe
    if (data.pdf_filename) {
      console.log('Cargando información del PDF:', data.pdf_filename);

      // Si tenemos la información del PDF guardada en el presupuesto, usarla directamente
      if (data.pdf_info) {
        console.log('Usando información del PDF guardada en el presupuesto:', data.pdf_info);
        setPdfInfo(data.pdf_info);
        // Crear un objeto File simulado para mantener la referencia
        const mockFile = {
          name: data.pdf_filename,
          type: 'application/pdf',
          size: data.pdf_info.file_size_bytes || 0,
          lastModified: new Date().getTime()
        };
        setSelectedPdf(mockFile);
      } else {
        // Si no tenemos la información guardada, intentar obtenerla del servidor
        fetch(buildApiUrl(`/uploads/pdf-info/${data.pdf_filename}`), {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
          .then(response => {
            if (response.ok) {
              return response.json();
            }
            throw new Error('No se pudo obtener información del PDF');
          })
          .then(pdfInfoData => {
            console.log('Información del PDF cargada desde el servidor:', pdfInfoData);
            setPdfInfo(pdfInfoData);
            // Crear un objeto File simulado para mantener la referencia
            const mockFile = {
              name: data.pdf_filename,
              type: 'application/pdf',
              size: pdfInfoData.file_size_bytes || 0,
              lastModified: new Date().getTime()
            };
            setSelectedPdf(mockFile);
          })
          .catch(error => {
            console.error('Error al cargar información del PDF:', error);
            showSnackbar('No se pudo cargar la información del PDF asociado', 'warning');
          });
      }
    }

    // Cargar configuración de colores si existe
    if (data.color_config) {
      console.log('Cargando configuración de colores:', data.color_config);
      setColorConfig(data.color_config);
    }

    // Cargar configuración del producto si existe
    if (data.product_config) {
      console.log('Cargando configuración del producto:', data.product_config);
      setProductConfig(data.product_config);
    } else if (data.color_config) {
      // Si no hay configuración de producto pero sí de colores, crear una configuración de producto
      // basada en la configuración de colores para mantener compatibilidad con presupuestos antiguos
      setProductConfig({
        frontColors: data.color_config.frontColors,
        backColors: data.color_config.backColors,
        pantones: data.color_config.pantones,
        selectedParts: [],
        workStyle: 'Flat', // Valor por defecto
        customOptions: {}
      });
    }

    // Cargar los procesos seleccionados si existen
    if (data.process_costs && Array.isArray(data.process_costs) && data.process_costs.length > 0) {
      console.log('Cargando procesos seleccionados:', data.process_costs);

      // Asegurarse de que los procesos estén cargados antes de continuar
      let processesToUse = processes;
      if (processes.length === 0) {
        // Cargar los procesos directamente
        try {
          const apiUrl = buildApiUrl('/processes/');
          console.log('Obteniendo procesos desde:', apiUrl);
          const processesResponse = await fetch(apiUrl, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          if (!processesResponse.ok) throw new Error('Error al obtener los procesos');
          const processesData = await processesResponse.json();
          console.log('Datos de procesos recibidos:', processesData);
          // Filtrar solo procesos de acabado, corte, plegado, encuadernación, etc.
          const filteredProcesses = processesData.filter(process => {
            return process.type && ['Corte', 'Plegado', 'Encuadernación', 'Acabado', 'Barnizado', 'Laminado', 'Troquelado'].includes(process.type);
          });
          console.log('Procesos filtrados:', filteredProcesses);
          processesToUse = filteredProcesses;
        } catch (error) {
          console.error('Error al cargar procesos:', error);
          processesToUse = [];
        }
      }

      // Mapear los procesos del presupuesto a objetos completos de proceso
      const loadedProcesses = data.process_costs.map(processData => {
        // Buscar el proceso completo en el catálogo
        const fullProcess = processesToUse.find(p => p.process_id === processData.process_id);

        if (fullProcess) {
          // Combinar los datos del proceso del catálogo con los datos específicos del presupuesto
          return {
            ...fullProcess,
            quantity: processData.quantity || parseInt(data.quantity) || 1
          };
        } else {
          // Si no se encuentra en el catálogo, usar los datos del presupuesto
          return {
            process_id: processData.process_id,
            name: processData.name,
            type: processData.type,
            unit_cost: processData.unit_cost || 0,
            quantity: processData.quantity || parseInt(data.quantity) || 1
          };
        }
      });

      console.log('Procesos cargados:', loadedProcesses);

      // Calcular el costo total de los procesos
      const totalProcessCost = loadedProcesses.reduce((total, process) => {
        return total + (process.total_cost || 0);
      }, 0);

      console.log('Costo total de procesos calculado:', totalProcessCost);
      setSelectedProcesses(loadedProcesses);
      setCalculatedProcessCost(totalProcessCost);
    } else {
      // Si no hay procesos, resetear la lista
      setSelectedProcesses([]);
      setCalculatedProcessCost(0);
    }

    setIsEditMode(true);
    return data;
  } catch (err) {
    console.error('Error al obtener el presupuesto:', err);
    showSnackbar(err.message, 'error');
    return null;
  }
};

export default {
  fetchBudgetById
};
