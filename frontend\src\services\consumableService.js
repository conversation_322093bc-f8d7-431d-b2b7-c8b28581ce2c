import { buildApiUrl } from '../config';

/**
 * Servicio para gestionar los consumibles a través de la API
 */
const consumableService = {
  /**
   * Obtiene todos los consumibles
   * @returns {Promise<Array>} Lista de consumibles
   */
  getAllConsumables: async () => {
    try {
      const response = await fetch(buildApiUrl('/consumables/'));
      if (!response.ok) {
        throw new Error(`Error al obtener consumibles: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error en getAllConsumables:', error);
      throw error;
    }
  },

  /**
   * Obtiene un consumible por su ID
   * @param {string} id ID del consumible
   * @returns {Promise<Object>} Datos del consumible
   */
  getConsumableById: async (id) => {
    try {
      const response = await fetch(buildApiUrl(`/consumables/${id}`));
      if (!response.ok) {
        throw new Error(`Error al obtener consumible: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error en getConsumableById(${id}):`, error);
      throw error;
    }
  },

  /**
   * Crea un nuevo consumible
   * @param {Object} consumableData Datos del consumible a crear
   * @returns {Promise<Object>} Datos del consumible creado
   */
  createConsumable: async (consumableData) => {
    try {
      const response = await fetch(buildApiUrl('/consumables/'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(consumableData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Error al crear consumible: ${errorData.detail || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error en createConsumable:', error);
      throw error;
    }
  },

  /**
   * Actualiza un consumible existente
   * @param {string} id ID del consumible a actualizar
   * @param {Object} consumableData Datos actualizados del consumible
   * @returns {Promise<Object>} Datos del consumible actualizado
   */
  updateConsumable: async (id, consumableData) => {
    try {
      const response = await fetch(buildApiUrl(`/consumables/${id}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(consumableData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Error al actualizar consumible: ${errorData.detail || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Error en updateConsumable(${id}):`, error);
      throw error;
    }
  },

  /**
   * Elimina un consumible
   * @param {string} id ID del consumible a eliminar
   * @returns {Promise<Object>} Mensaje de confirmación
   */
  deleteConsumable: async (id) => {
    try {
      const response = await fetch(buildApiUrl(`/consumables/${id}`), {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Error al eliminar consumible: ${errorData.detail || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Error en deleteConsumable(${id}):`, error);
      throw error;
    }
  },

  /**
   * Obtiene consumibles por tipo
   * @param {string} type Tipo de consumible
   * @returns {Promise<Array>} Lista de consumibles del tipo especificado
   */
  getConsumablesByType: async (type) => {
    try {
      const response = await fetch(buildApiUrl(`/consumables/type/${type}`));
      if (!response.ok) {
        throw new Error(`Error al obtener consumibles por tipo: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error en getConsumablesByType(${type}):`, error);
      throw error;
    }
  },

  /**
   * Obtiene consumibles por estado
   * @param {string} status Estado del consumible
   * @returns {Promise<Array>} Lista de consumibles con el estado especificado
   */
  getConsumablesByStatus: async (status) => {
    try {
      const response = await fetch(buildApiUrl(`/consumables/status/${status}`));
      if (!response.ok) {
        throw new Error(`Error al obtener consumibles por estado: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`Error en getConsumablesByStatus(${status}):`, error);
      throw error;
    }
  },
};

export default consumableService;
