from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
from folding_schemes import get_all_schemes, get_scheme, FoldingScheme
from models.models import EsquemaResponse
from utils.logger import log_info, log_error

router = APIRouter(
    prefix="/folding-schemes",
    tags=["folding_schemes"],
    responses={404: {"description": "Esquema de plegado no encontrado"}}
)

def folding_scheme_to_dict(scheme: FoldingScheme) -> Dict[str, Any]:
    """
    Convierte un objeto FoldingScheme a un diccionario serializable
    """
    return {
        "name": scheme.name,
        "pages_per_sheet": scheme.pages_per_sheet,
        "rows": scheme.rows,
        "cols": scheme.cols,
        "page_layout": scheme.page_layout,
        "folding_steps": [
            {
                "orientation": step.fold_type.value,
                "description": step.description
            }
            for step in scheme.folding_steps
        ],
        "min_margin": scheme.min_margin,
        "space_between_pages": scheme.space_between_pages
    }

@router.get("/", response_model=List[Dict[str, Any]])
def get_all_folding_schemes():
    """
    Obtiene todos los esquemas de plegado disponibles
    """
    try:
        log_info("Obteniendo todos los esquemas de plegado")
        schemes = get_all_schemes()
        
        # Convertir cada esquema a diccionario
        schemes_list = []
        for name, scheme in schemes.items():
            scheme_dict = folding_scheme_to_dict(scheme)
            schemes_list.append(scheme_dict)
        
        log_info(f"Se encontraron {len(schemes_list)} esquemas de plegado")
        return schemes_list
        
    except Exception as e:
        log_error(f"Error al obtener esquemas de plegado: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error al obtener los esquemas de plegado: {str(e)}"
        )

@router.get("/{scheme_name}", response_model=Dict[str, Any])
def get_folding_scheme(scheme_name: str):
    """
    Obtiene un esquema de plegado específico por su nombre
    """
    try:
        log_info(f"Obteniendo esquema de plegado: {scheme_name}")
        scheme = get_scheme(scheme_name)
        
        if not scheme:
            raise HTTPException(
                status_code=404,
                detail=f"Esquema de plegado '{scheme_name}' no encontrado"
            )
        
        scheme_dict = folding_scheme_to_dict(scheme)
        log_info(f"Esquema '{scheme_name}' encontrado")
        return scheme_dict
        
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error al obtener esquema '{scheme_name}': {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error al obtener el esquema de plegado: {str(e)}"
        )
