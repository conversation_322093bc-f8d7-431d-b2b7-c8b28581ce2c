from fastapi import APIRouter, HTTPException
from models.models import OffsetCalculationRequest, OffsetCalculationResponse, SheetType
from utils.offset_calculator import calculate_offset_cost_and_time
from utils.logger import log_info, log_error

router = APIRouter(
    prefix="/v2",
    tags=["offset_calculator"],
    responses={404: {"description": "Máquina no encontrada"}}
)

@router.post("/calculate-offset", response_model=OffsetCalculationResponse, summary="Calcula costes y tiempos para máquinas offset (v2)")
def calculate_offset_endpoint(request: OffsetCalculationRequest):
    """
    Calcula el coste y tiempo de impresión para una máquina offset utilizando parámetros avanzados.

    Esta versión utiliza los campos setup_time y sheets_per_hour para realizar cálculos más precisos
    y calcula automáticamente los pliegos necesarios a partir del número de páginas y dimensiones.

    Parámetros obligatorios:
    - **machine_id**: ID de la máquina offset
    - **copies**: Número de ejemplares
    - **num_paginas**: Número de páginas a imprimir
    - **ancho_pagina**: Ancho de la página en mm
    - **alto_pagina**: Alto de la página en mm

    Parámetros opcionales:
    - **colors_front**: Número de colores en el anverso (por defecto 4)
    - **colors_back**: Número de colores en el reverso (por defecto 4)
    - **paper_id**: ID del papel (opcional)
    - **ancho_pliego**: Ancho del pliego en mm (opcional, se puede obtener del papel)
    - **alto_pliego**: Alto del pliego en mm (opcional, se puede obtener del papel)

    Parámetros personalizados para ajustes finos:
    - **custom_setup_time**: Tiempo de arranque personalizado en minutos (opcional)
    - **custom_sheets_per_hour**: Velocidad personalizada en pliegos/hora (opcional)
    - **custom_maculatura**: Maculatura personalizada en pliegos adicionales (opcional)

    Retorna un objeto con información detallada sobre costes y tiempos.
    """
    try:
        log_info(f"Solicitud de cálculo offset v2 recibida: {request}")

        from utils.paper_calculator import calcular_pliegos
        from utils.catalog_manager import load_paper_catalog, load_machines

        # Si no se proporcionan las dimensiones del pliego, obtenerlas del papel
        ancho_pliego = request.ancho_pliego
        alto_pliego = request.alto_pliego

        if (ancho_pliego is None or alto_pliego is None) and request.paper_id:
            paper_catalog = load_paper_catalog()
            paper = next((p for p in paper_catalog if p.get("product_id") == request.paper_id), None)

            if paper:
                ancho_pliego = paper.get("dimension_width", 0)
                alto_pliego = paper.get("dimension_height", 0)
                log_info(f"Dimensiones del pliego obtenidas del papel: {ancho_pliego}x{alto_pliego}mm")

        if ancho_pliego is None or alto_pliego is None:
            raise HTTPException(
                status_code=400,
                detail="Se requieren las dimensiones del pliego (ancho_pliego, alto_pliego) o un paper_id válido"
            )

        # Cargar datos de la máquina para obtener print_units
        machines = load_machines()
        machine = next((m for m in machines if m.get("machine_id") == request.machine_id), None)

        if not machine:
            raise HTTPException(status_code=404, detail=f"Máquina con ID {request.machine_id} no encontrada")

        # Obtener print_units de la máquina
        print_units = machine.get("print_units", 4)  # Valor por defecto: 4 cuerpos
        log_info(f"Usando máquina {request.machine_id} con {print_units} cuerpos de impresión")

        # Calcular los pliegos
        calculo_pliegos = calcular_pliegos(
            num_paginas=request.num_paginas,
            ancho_pagina=request.ancho_pagina,
            alto_pagina=request.alto_pagina,
            ancho_pliego=ancho_pliego,
            alto_pliego=alto_pliego,
            front_colors=request.colors_front,
            back_colors=request.colors_back,
            is_digital=False,  # Siempre es offset
            copies=request.copies,
            print_units=print_units  # Añadir print_units como parámetro
        )

        log_info(f"Cálculo de pliegos completado: {calculo_pliegos}")

        # Extraer la información necesaria para el cálculo de offset
        mejor_combinacion = calculo_pliegos.mejor_combinacion
        total_sheets = mejor_combinacion.total_pliegos
        total_plates = mejor_combinacion.total_planchas

        # Determinar el tipo de pliego y esquema de plegado
        # Si hay varios esquemas, usamos el primero como referencia
        if mejor_combinacion.esquemas_utilizados:
            primer_esquema = mejor_combinacion.esquemas_utilizados[0]
            sheet_type = primer_esquema.sheet_type
            folding_scheme = primer_esquema.nombre
            is_workandturn = primer_esquema.es_tira_retira
        else:
            sheet_type = SheetType.WORK_AND_BACK
            folding_scheme = None
            is_workandturn = False

        # Calcular el coste y tiempo
        result = calculate_offset_cost_and_time(
            machine_id=request.machine_id,
            total_sheets=total_sheets,
            copies=request.copies,
            total_plates=total_plates,
            # Los siguientes parámetros son redundantes cuando tenemos esquemas_utilizados
            # pero los mantenemos para compatibilidad
            colors_front=request.colors_front,
            colors_back=request.colors_back,
            is_workandturn=is_workandturn,
            sheet_type=sheet_type,
            folding_scheme=folding_scheme,
            paper_id=request.paper_id,
            custom_setup_time=request.custom_setup_time,
            custom_sheets_per_hour=request.custom_sheets_per_hour,
            custom_maculatura=request.custom_maculatura,
            esquemas_utilizados=mejor_combinacion.esquemas_utilizados if mejor_combinacion.esquemas_utilizados else None
        )

        # Añadir información del cálculo de pliegos a la respuesta
        result["num_paginas"] = request.num_paginas
        result["esquemas_utilizados"] = mejor_combinacion.esquemas_utilizados
        result["calculo_pliegos_info"] = {
            "total_pliegos": mejor_combinacion.total_pliegos,
            "total_planchas": mejor_combinacion.total_planchas,
            "total_passes": mejor_combinacion.total_passes
        }
        
        # Añadir el tipo de máquina a la respuesta
        result["machine_type"] = "Offset"

        # Añadir logs adicionales para depuración
        log_info(f"Cálculo offset v2 completado. total_sheets={result['total_sheets']}, copies={result['copies']}, total_physical_sheets={result['total_physical_sheets']}")
        
        # Mostrar la solicitud y el resultado en formato JSON para mejor visualización
        import json
        print("\nSolicitud de cálculo de pliegos (JSON):")
        request_dict = request.dict()
        print(json.dumps(request_dict, indent=2, ensure_ascii=False, default=str))
        
        print("\nResultado del cálculo de pliegos (JSON):")
        print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
        print()
        return result

    except HTTPException as e:
        log_error(f"Error HTTP en cálculo offset v2: {e.detail}")
        raise e
    except Exception as e:
        log_error(f"Error en cálculo offset v2: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error al calcular: {str(e)}")
