# Imprenta - Calculadora de Pliegos

Sistema de cálculo de pliegos para imprentas que permite determinar la disposición óptima de páginas en pliegos de impresión, considerando diferentes esquemas de plegado.

## Características

- **Cálculo de Pliegos**: Determina el número óptimo de pliegos necesarios para un trabajo de impresión
- **Múltiples Esquemas de Plegado**: Soporta varios esquemas (F4-1, F6-2, F8-7, F12-5, F16-7, F32-7)
- **Rotación Automática**: Calcula automáticamente la mejor orientación para aprovechar el papel
- **Catálogo de Papeles**: Gestión de diferentes tipos y tamaños de papel
- **Optimización**: Encuentra la mejor combinación de esquemas para minimizar el desperdicio

## Estructura del Proyecto

```
Imprenta/
├── backend/
│   ├── main.py              # API FastAPI
│   ├── folding_schemes.py   # Lógica de esquemas de plegado
│   └── paper_catalog.py     # Gestión del catálogo de papeles
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── PaperCalculator.jsx
│   │   │   └── PaperCatalog.jsx
│   │   └── ...
│   └── ...
└── docs/
    └── FoldingSchemes.md    # Documentación de esquemas
```

## Esquemas de Plegado Soportados

- **F4-1**: 4 páginas (2×1)
- **F6-2**: 6 páginas (3×1, plegado tipo puerta)
- **F8-7**: 8 páginas (2×2)
- **F12-5**: 12 páginas (3×2)
- **F16-7**: 16 páginas (4×2)
- **F32-7**: 32 páginas (4×4)

## Instalación

### Backend (Python)

```bash
# Crear entorno virtual
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Instalar dependencias
pip install -r requirements.txt

# Iniciar servidor
python main.py
```

### Frontend (React)

```bash
cd frontend
npm install
npm run dev
```

## Uso

1. El backend se ejecuta en `http://localhost:3005`
2. El frontend se ejecuta en `http://localhost:5173`
3. Accede a la interfaz web y:
   - Selecciona el tamaño de página deseado
   - Ingresa el número de páginas del documento
   - Selecciona el tamaño del pliego
   - La aplicación calculará automáticamente la mejor disposición

## API Endpoints

- `POST /calcular-pliegos`: Calcula el número de pliegos necesarios
- `GET /papeles`: Obtiene el catálogo de papeles disponibles
- `POST /papeles`: Añade un nuevo papel al catálogo

## Tecnologías Utilizadas

- **Backend**: Python, FastAPI
- **Frontend**: React, Vite
- **Estilos**: CSS Modules
- **Documentación**: Markdown

## Contribuir

1. Haz un Fork del proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.
