import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  InputAdornment
} from '@mui/material';
import { LocalShipping as LocalShippingIcon } from '@mui/icons-material';
import { buildApiUrl } from '../../config';

const ShippingForm = ({ open, onClose, otNumber, budgetId, clientId, clientName, onSuccess }) => {
  const [formData, setFormData] = useState({
    ot_number: otNumber || '',
    budget_id: budgetId || '',
    client_id: clientId || '',
    client_name: clientName || '',
    carrier: '',
    tracking_number: '',
    packages: 1,
    weight: '',
    notes: '',
    delivery_address: {
      street: '',
      city: '',
      postal_code: '',
      country: 'España'
    },
    contact_person: '',
    contact_phone: ''
  });

  // Actualizar el formulario cuando cambian las props
  useEffect(() => {
    console.log('Props actualizadas:', { otNumber, budgetId, clientId, clientName });
    setFormData(prev => ({
      ...prev,
      ot_number: otNumber || '',
      budget_id: budgetId || '',
      client_id: clientId || '',
      client_name: clientName || '',
      // Resetear el peso para que no se mantenga de un envío anterior
      weight: ''
    }));
  }, [otNumber, budgetId, clientId, clientName]);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [deliveryNotePath, setDeliveryNotePath] = useState(null);
  const [resultData, setResultData] = useState(null);
  const [weightWarning, setWeightWarning] = useState(false);

  // Cargar datos del cliente y del presupuesto cuando se abre el formulario
  useEffect(() => {
    if (open) {
      if (clientId) {
        fetchClientData();
      }
      if (budgetId) {
        fetchBudgetData();
      }
    }
  }, [open, clientId, budgetId]);

  const fetchClientData = async () => {
    try {
      const response = await fetch(buildApiUrl(`/clients/${clientId}`));
      if (response.ok) {
        const clientData = await response.json();

        // Actualizar la dirección y contacto con los datos del cliente
        if (clientData.company && clientData.company.address) {
          setFormData(prev => ({
            ...prev,
            delivery_address: {
              street: clientData.company.address.street || '',
              city: clientData.company.address.city || '',
              postal_code: clientData.company.address.postal_code || '',
              country: clientData.company.address.country || 'España'
            }
          }));
        }

        if (clientData.contact) {
          const contactName = `${clientData.contact.first_name || ''} ${clientData.contact.last_name || ''}`.trim();
          setFormData(prev => ({
            ...prev,
            contact_person: contactName || '',
            contact_phone: clientData.contact.phone || ''
          }));
        }
      }
    } catch (err) {
      console.error('Error al cargar datos del cliente:', err);
    }
  };

  // Función para obtener los datos del presupuesto
  const fetchBudgetData = async () => {
    try {
      console.log(`Obteniendo datos del presupuesto con ID: ${budgetId}`);

      // Obtener el token de autenticación del localStorage
      const token = localStorage.getItem('auth_token');

      if (!token) {
        console.warn('No se encontró el token de autenticación para obtener datos del presupuesto');
      }

      const response = await fetch(buildApiUrl(`/budgets/${budgetId}`), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const budgetData = await response.json();
        console.log('Datos del presupuesto obtenidos:', budgetData);

        // Verificar si el presupuesto tiene información de peso
        if (budgetData.total_paper_weight_kg) {
          // Actualizar el peso en el formulario
          const weightValue = budgetData.total_paper_weight_kg.toString();
          console.log(`Peso del presupuesto cargado: ${weightValue} kg`);

          setFormData(prev => ({
            ...prev,
            weight: weightValue
          }));

          // Verificar que el estado se actualizó correctamente
          setTimeout(() => {
            console.log('Estado del formulario después de actualizar el peso:', formData);
          }, 100);
        } else {
          console.log('El presupuesto no tiene información de peso');
          setWeightWarning(true);
        }
      } else {
        console.error(`Error al obtener presupuesto: ${response.status} ${response.statusText}`);
        setWeightWarning(true);
      }
    } catch (err) {
      console.error('Error al cargar datos del presupuesto:', err);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Manejar campos anidados (dirección)
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validar que todos los campos requeridos estén presentes
      const requiredFields = [
        { field: 'ot_number', name: 'Número de OT' },
        { field: 'budget_id', name: 'ID de Presupuesto' },
        { field: 'client_id', name: 'ID de Cliente' },
        { field: 'client_name', name: 'Nombre de Cliente' },
        { field: 'carrier', name: 'Transportista' },
        { field: 'contact_person', name: 'Persona de Contacto' },
        { field: 'contact_phone', name: 'Teléfono de Contacto' }
      ];

      const addressFields = [
        { field: 'street', name: 'Calle' },
        { field: 'city', name: 'Ciudad' },
        { field: 'postal_code', name: 'Código Postal' },
        { field: 'country', name: 'País' }
      ];

      // Verificar campos principales
      for (const { field, name } of requiredFields) {
        if (!formData[field]) {
          throw new Error(`El campo ${name} es obligatorio`);
        }
      }

      // Verificar campos de dirección
      for (const { field, name } of addressFields) {
        if (!formData.delivery_address[field]) {
          throw new Error(`El campo ${name} en la dirección de entrega es obligatorio`);
        }
      }

      // Verificar que el número de bultos sea al menos 1
      if (formData.packages < 1) {
        throw new Error('El número de bultos debe ser al menos 1');
      }

      console.log('Enviando datos:', formData);

      const response = await fetch(buildApiUrl('/shipping'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error en la respuesta:', response.status, errorText);

        try {
          // Intentar parsear como JSON
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.detail || 'Error al crear el registro de envío');
        } catch (parseError) {
          // Si no es JSON, mostrar el texto completo
          throw new Error(`Error ${response.status}: ${errorText}`);
        }
      }

      const result = await response.json();

      setSuccess(true);
      setDeliveryNotePath(result.delivery_note_path);
      setResultData(result);

      // Notificar al componente padre
      if (onSuccess) {
        onSuccess(result);
      }

      // Cerrar el formulario después de 3 segundos
      setTimeout(() => {
        onClose();
        setSuccess(false);
        setDeliveryNotePath(null);
      }, 3000);

    } catch (err) {
      console.error('Error al enviar el formulario:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadDeliveryNote = () => {
    if (deliveryNotePath && resultData) {
      console.log('Ruta del albarán:', deliveryNotePath);
      console.log('Datos del resultado:', resultData);

      // Verificar si tenemos el nombre del archivo
      if (resultData.delivery_note_filename) {
        // Usar el nombre de archivo proporcionado por el backend
        const downloadUrl = buildApiUrl(`/uploads/download/${resultData.delivery_note_filename}`);
        console.log('URL de descarga (usando nombre de archivo):', downloadUrl);

        // Abrir la URL en una nueva pestaña
        window.open(downloadUrl, '_blank');
      } else {
        // Usar el ID de envío como alternativa
        const downloadUrl = buildApiUrl(`/uploads/download-delivery-note/${resultData.shipping_id}`);
        console.log('URL de descarga (usando ID de envío):', downloadUrl);

        // Abrir la URL en una nueva pestaña
        window.open(downloadUrl, '_blank');
      }
    } else {
      console.error('No hay ruta de albarán disponible o datos de resultado');
      setError('No se pudo obtener la información necesaria para descargar el albarán');
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <LocalShippingIcon sx={{ mr: 1 }} />
          Registrar Envío
        </div>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Envío registrado correctamente
            {deliveryNotePath && (
              <Button
                color="inherit"
                size="small"
                onClick={handleDownloadDeliveryNote}
                sx={{ ml: 2 }}
              >
                Descargar Albarán
              </Button>
            )}
          </Alert>
        )}

        {weightWarning && !formData.weight && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            No se pudo cargar automáticamente el peso del presupuesto. Por favor, ingrese el peso manualmente.
          </Alert>
        )}

        <Grid container spacing={2} sx={{ mt: 1 }}>
          {/* Información del pedido */}
          <Grid item xs={12}>
            <Typography variant="h6">Información del Pedido</Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Número de OT"
              value={formData.ot_number}
              fullWidth
              disabled
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Cliente"
              value={formData.client_name}
              fullWidth
              disabled
            />
          </Grid>

          {/* Información del transportista */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mt: 2 }}>Información del Transportista</Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth required>
              <InputLabel>Transportista</InputLabel>
              <Select
                name="carrier"
                value={formData.carrier}
                onChange={handleChange}
                label="Transportista"
              >
                <MenuItem value="SEUR">SEUR</MenuItem>
                <MenuItem value="MRW">MRW</MenuItem>
                <MenuItem value="DHL">DHL</MenuItem>
                <MenuItem value="Correos Express">Correos Express</MenuItem>
                <MenuItem value="GLS">GLS</MenuItem>
                <MenuItem value="Transporte Propio">Transporte Propio</MenuItem>
                <MenuItem value="Recogida Cliente">Recogida Cliente</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Número de Seguimiento"
              name="tracking_number"
              value={formData.tracking_number}
              onChange={handleChange}
              fullWidth
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Número de Bultos"
              name="packages"
              type="number"
              value={formData.packages}
              onChange={handleChange}
              fullWidth
              required
              InputProps={{
                inputProps: { min: 1 }
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Peso"
              name="weight"
              type="number"
              value={formData.weight}
              onChange={handleChange}
              fullWidth
              helperText={formData.weight ? "Peso cargado automáticamente del presupuesto" : ""}
              InputProps={{
                endAdornment: <InputAdornment position="end">kg</InputAdornment>,
                inputProps: { min: 0, step: 0.1 }
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  ...(formData.weight && {
                    backgroundColor: 'rgba(76, 175, 80, 0.1)', // Fondo verde claro si hay peso
                  })
                }
              }}
            />
          </Grid>

          {/* Dirección de entrega */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mt: 2 }}>Dirección de Entrega</Typography>
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Calle y Número"
              name="delivery_address.street"
              value={formData.delivery_address.street}
              onChange={handleChange}
              fullWidth
              required
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              label="Ciudad"
              name="delivery_address.city"
              value={formData.delivery_address.city}
              onChange={handleChange}
              fullWidth
              required
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              label="Código Postal"
              name="delivery_address.postal_code"
              value={formData.delivery_address.postal_code}
              onChange={handleChange}
              fullWidth
              required
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              label="País"
              name="delivery_address.country"
              value={formData.delivery_address.country}
              onChange={handleChange}
              fullWidth
              required
            />
          </Grid>

          {/* Información de contacto */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mt: 2 }}>Información de Contacto</Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Persona de Contacto"
              name="contact_person"
              value={formData.contact_person}
              onChange={handleChange}
              fullWidth
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Teléfono de Contacto"
              name="contact_phone"
              value={formData.contact_phone}
              onChange={handleChange}
              fullWidth
              required
            />
          </Grid>

          {/* Observaciones */}
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mt: 2 }}>Observaciones</Typography>
          </Grid>

          <Grid item xs={12}>
            <TextField
              label="Notas"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              fullWidth
              multiline
              rows={4}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={loading || !formData.carrier || !formData.delivery_address.street || !formData.contact_person}
          startIcon={loading ? <CircularProgress size={20} /> : <LocalShippingIcon />}
        >
          {loading ? 'Enviando...' : 'Registrar Envío'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ShippingForm;
