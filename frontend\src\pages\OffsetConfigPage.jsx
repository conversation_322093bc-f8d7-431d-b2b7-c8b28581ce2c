import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Grid,
  TextField,
  Button,
  Divider,
  Snackbar,
  Alert,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import RestoreIcon from '@mui/icons-material/Restore';
import SaveIcon from '@mui/icons-material/Save';
import { OFFSET_STANDARD_TIMES, OFFSET_SPEEDS } from '../utils/offsetUtils';
import ConfigService from '../services/ConfigService';

const OffsetConfigPage = () => {
  // Estados para los valores de configuración
  const [standardTimes, setStandardTimes] = useState({ ...OFFSET_STANDARD_TIMES });
  const [speeds, setSpeeds] = useState({ ...OFFSET_SPEEDS });
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Cargar la configuración al montar el componente
  useEffect(() => {
    loadConfig();
  }, []);

  // Función para cargar la configuración
  const loadConfig = async () => {
    try {
      setLoading(true);
      const config = await ConfigService.getConfig();

      // Si hay configuración de offset, usarla
      if (config.offset_standard_times) {
        setStandardTimes(config.offset_standard_times);
      }

      if (config.offset_speeds) {
        setSpeeds(config.offset_speeds);
      }

    } catch (error) {
      console.error('Error al cargar la configuración:', error);
      showSnackbar('Error al cargar la configuración', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Función para guardar la configuración
  const saveConfig = async () => {
    try {
      setLoading(true);

      // Obtener la configuración actual
      const currentConfig = await ConfigService.getConfig();

      // Actualizar con los nuevos valores
      const updatedConfig = {
        ...currentConfig,
        offset_standard_times: standardTimes,
        offset_speeds: speeds
      };

      // Guardar la configuración actualizada
      await ConfigService.updateConfig(updatedConfig);

      showSnackbar('Configuración guardada correctamente', 'success');
    } catch (error) {
      console.error('Error al guardar la configuración:', error);
      showSnackbar('Error al guardar la configuración', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Función para restaurar valores por defecto
  const restoreDefaults = () => {
    setStandardTimes({ ...OFFSET_STANDARD_TIMES });
    setSpeeds({ ...OFFSET_SPEEDS });
    showSnackbar('Valores restaurados a los predeterminados', 'info');
  };

  // Función para mostrar mensajes
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // Función para cerrar el snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Función para manejar cambios en los tiempos estándar
  const handleStandardTimeChange = (key, value) => {
    // Convertir a número y validar
    const numValue = parseFloat(value);
    if (isNaN(numValue) || numValue < 0) return;

    setStandardTimes(prev => ({
      ...prev,
      [key]: numValue
    }));
  };

  // Función para manejar cambios en las velocidades
  const handleSpeedChange = (key, value) => {
    // Convertir a número y validar
    const numValue = parseInt(value, 10);
    if (isNaN(numValue) || numValue < 0) return;

    setSpeeds(prev => ({
      ...prev,
      [key]: numValue
    }));
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 3, mt: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Configuración de Impresión Offset
          </Typography>
          <Box>
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<RestoreIcon />}
              onClick={restoreDefaults}
              sx={{ mr: 2 }}
            >
              Restaurar Valores
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={saveConfig}
              disabled={loading}
            >
              Guardar Configuración
            </Button>
          </Box>
        </Box>

        <Typography variant="body1" sx={{ mb: 3 }}>
          Ajuste los parámetros utilizados para calcular tiempos y optimizar trabajos de impresión offset.
          Estos valores afectan a la planificación de producción y a los cálculos de costos.
        </Typography>

        <Grid container spacing={4}>
          {/* Tiempos estándar */}
          <Grid item xs={12} md={6}>
            <Card elevation={2}>
              <CardHeader
                title="Tiempos Estándar (horas)"
                action={
                  <Tooltip title="Estos tiempos se utilizan para calcular la duración total de los trabajos de impresión">
                    <IconButton>
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Tiempo de preparación"
                      fullWidth
                      type="number"
                      inputProps={{ step: 0.05, min: 0 }}
                      value={standardTimes.SETUP}
                      onChange={(e) => handleStandardTimeChange('SETUP', e.target.value)}
                      margin="normal"
                      helperText="Tiempo para preparar la máquina"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Tiempo de limpieza"
                      fullWidth
                      type="number"
                      inputProps={{ step: 0.05, min: 0 }}
                      value={standardTimes.CLEANUP}
                      onChange={(e) => handleStandardTimeChange('CLEANUP', e.target.value)}
                      margin="normal"
                      helperText="Tiempo para limpiar la máquina"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Cambio de plancha"
                      fullWidth
                      type="number"
                      inputProps={{ step: 0.05, min: 0 }}
                      value={standardTimes.PLATE_CHANGE}
                      onChange={(e) => handleStandardTimeChange('PLATE_CHANGE', e.target.value)}
                      margin="normal"
                      helperText="Tiempo para cambiar una plancha"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Cambio de tinta"
                      fullWidth
                      type="number"
                      inputProps={{ step: 0.05, min: 0 }}
                      value={standardTimes.INK_CHANGE}
                      onChange={(e) => handleStandardTimeChange('INK_CHANGE', e.target.value)}
                      margin="normal"
                      helperText="Tiempo para cambiar tintas"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Cambio de papel"
                      fullWidth
                      type="number"
                      inputProps={{ step: 0.05, min: 0 }}
                      value={standardTimes.PAPER_CHANGE}
                      onChange={(e) => handleStandardTimeChange('PAPER_CHANGE', e.target.value)}
                      margin="normal"
                      helperText="Tiempo para cambiar el papel"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Mantenimiento"
                      fullWidth
                      type="number"
                      inputProps={{ step: 0.05, min: 0 }}
                      value={standardTimes.MAINTENANCE}
                      onChange={(e) => handleStandardTimeChange('MAINTENANCE', e.target.value)}
                      margin="normal"
                      helperText="Tiempo para mantenimiento básico"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Control de calidad"
                      fullWidth
                      type="number"
                      inputProps={{ step: 0.05, min: 0 }}
                      value={standardTimes.QUALITY_CHECK}
                      onChange={(e) => handleStandardTimeChange('QUALITY_CHECK', e.target.value)}
                      margin="normal"
                      helperText="Tiempo para control de calidad"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Velocidades de impresión */}
          <Grid item xs={12} md={6}>
            <Card elevation={2}>
              <CardHeader
                title="Velocidades de Impresión (pliegos/hora)"
                action={
                  <Tooltip title="Estas velocidades se utilizan para calcular el tiempo de impresión según la complejidad del trabajo">
                    <IconButton>
                      <InfoIcon />
                    </IconButton>
                  </Tooltip>
                }
              />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Trabajos simples"
                      fullWidth
                      type="number"
                      inputProps={{ step: 100, min: 0 }}
                      value={speeds.SIMPLE}
                      onChange={(e) => handleSpeedChange('SIMPLE', e.target.value)}
                      margin="normal"
                      helperText="1-2 colores"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Trabajos estándar"
                      fullWidth
                      type="number"
                      inputProps={{ step: 100, min: 0 }}
                      value={speeds.STANDARD}
                      onChange={(e) => handleSpeedChange('STANDARD', e.target.value)}
                      margin="normal"
                      helperText="4 colores"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Trabajos complejos"
                      fullWidth
                      type="number"
                      inputProps={{ step: 100, min: 0 }}
                      value={speeds.COMPLEX}
                      onChange={(e) => handleSpeedChange('COMPLEX', e.target.value)}
                      margin="normal"
                      helperText=">4 colores, barnices"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Papeles especiales"
                      fullWidth
                      type="number"
                      inputProps={{ step: 100, min: 0 }}
                      value={speeds.SPECIAL}
                      onChange={(e) => handleSpeedChange('SPECIAL', e.target.value)}
                      margin="normal"
                      helperText="Papeles especiales o gramajes altos"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>



        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            Impacto en los cálculos
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Typography variant="body2" paragraph>
            Los valores configurados aquí afectan directamente a:
          </Typography>
          <ul>
            <li>
              <Typography variant="body2">
                Cálculo de tiempos de impresión en presupuestos
              </Typography>
            </li>
            <li>
              <Typography variant="body2">
                Planificación automática de procesos en el calendario de producción
              </Typography>
            </li>
            <li>
              <Typography variant="body2">
                Optimización de secuencias de trabajos para minimizar tiempos de preparación
              </Typography>
            </li>
            <li>
              <Typography variant="body2">
                Detección y resolución de conflictos en la programación
              </Typography>
            </li>
            <li>
              <Typography variant="body2">
                Cálculo de costes de tinta y planchas en la calculadora de offset
              </Typography>
            </li>
          </ul>
        </Box>
      </Paper>

      {/* Snackbar para mensajes */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default OffsetConfigPage;
