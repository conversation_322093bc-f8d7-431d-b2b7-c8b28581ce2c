/**
 * Servicio especializado para manejar cambios en el formulario de presupuesto
 * Extrae la lógica compleja de handleChange del BudgetForm
 */
import ProductService from './ProductService';

/**
 * Actualiza el estado básico del presupuesto
 * @param {string} name - Nombre del campo
 * @param {any} value - Valor del campo
 * @param {Function} setBudget - Función para actualizar el presupuesto
 */
export const updateBudgetField = (name, value, setBudget) => {
  setBudget(prev => ({
    ...prev,
    [name]: value
  }));
};

/**
 * Marca las partes con cálculos como desactualizadas cuando cambian las copias
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {Function} setBudgetParts - Función para actualizar las partes
 * @param {Function} showSnackbar - Función para mostrar mensajes
 * @returns {boolean} - true si se marcaron partes como desactualizadas
 */
export const markPartsAsOutdated = (budgetParts, setBudgetParts, showSnackbar) => {
  // Verificar si hay partes con cálculos previos
  const hasPartsWithCalculations = budgetParts.some(part => part.sheetCalculation);

  if (hasPartsWithCalculations) {
    // Crear una copia de las partes y marcar como desactualizadas aquellas que tienen cálculos previos
    const updatedParts = budgetParts.map(part => {
      if (part.sheetCalculation) {
        return {
          ...part,
          sheetCalculation: {
            ...part.sheetCalculation,
            outdated: true // Marcar como desactualizada
          }
        };
      }
      return part;
    });

    // Actualizar las partes del presupuesto
    setBudgetParts(updatedParts);

    // Mostrar mensaje al usuario
    showSnackbar('Se ha modificado el número de ejemplares. Debe recalcular los pliegos de cada parte antes de actualizar el presupuesto.', 'warning');
    
    return true;
  }
  
  return false;
};

/**
 * Parsea la configuración de colores desde una cadena (ej: "4/4")
 * @param {string} colorString - Cadena de colores
 * @returns {Object|null} - Configuración de colores parseada
 */
export const parseColorConfig = (colorString) => {
  if (!colorString) return null;

  // Formato esperado: frontColors/backColors (ej: 4/4, 4/0, 1/1)
  const parts = colorString.split('/');
  if (parts.length !== 2) return null;

  const frontColors = parseInt(parts[0]) || 0;
  const backColors = parseInt(parts[1]) || 0;

  return {
    frontColors,
    backColors,
    pantones: 0 // Por defecto sin pantones
  };
};

/**
 * Calcula el costo total de procesos
 * @param {Array} processes - Array de procesos
 * @param {number} copies - Número de copias
 * @returns {number} - Costo total calculado
 */
export const calculateProcessesTotalCost = (processes, copies) => {
  return processes.reduce((total, process) => {
    const qty = process.quantity || copies || 1;
    const unitCost = process.unit_cost || 0;
    return total + (unitCost * qty);
  }, 0);
};

/**
 * Prepara procesos con cantidades basadas en el número de copias
 * @param {Array} processes - Array de procesos
 * @param {number} copies - Número de copias
 * @returns {Array} - Procesos con cantidades actualizadas
 */
export const prepareProcessesWithQuantities = (processes, copies) => {
  return processes.map(process => ({
    ...process,
    quantity: parseInt(copies) || 1,
    quantityModified: false
  }));
};

/**
 * Carga los acabados predeterminados de un producto
 * @param {Object} product - Producto del catálogo
 * @param {Array} processes - Procesos disponibles
 * @param {number} copies - Número de copias
 * @param {Function} setSelectedProcesses - Función para actualizar procesos seleccionados
 * @param {Function} setCalculatedProcessCost - Función para actualizar costo calculado
 */
export const loadDefaultFinishing = (product, processes, copies, setSelectedProcesses, setCalculatedProcessCost) => {
  if (!product.default_finishing || !Array.isArray(product.default_finishing) || product.default_finishing.length === 0) {
    return;
  }

  // Si el producto tiene procesos de acabado detallados, usarlos
  if (product.finishing_processes && Array.isArray(product.finishing_processes) && product.finishing_processes.length > 0) {
    // Preparar los procesos con las cantidades correctas
    const processesWithQuantities = prepareProcessesWithQuantities(product.finishing_processes, copies);

    // Calcular el costo manualmente
    const manualTotal = calculateProcessesTotalCost(processesWithQuantities, copies);

    // Actualizar los procesos seleccionados y el costo calculado
    setSelectedProcesses(processesWithQuantities);
    setCalculatedProcessCost(manualTotal);
  } else {
    // Si no, buscar los procesos por nombre
    const defaultProcesses = product.default_finishing
      .map(name => processes.find(p => p.name === name))
      .filter(Boolean)
      .map(process => ({
        ...process,
        quantity: parseInt(copies) || 1,
        quantityModified: false
      }));

    if (defaultProcesses.length > 0) {
      // Calcular el costo manualmente
      const manualTotal = calculateProcessesTotalCost(defaultProcesses, copies);

      // Actualizar los procesos seleccionados y el costo calculado
      setSelectedProcesses(defaultProcesses);
      setCalculatedProcessCost(manualTotal);
    }
  }
};

/**
 * Crea partes del presupuesto basadas en las partes del producto
 * @param {Object} product - Producto del catálogo
 * @param {Function} createEmptyPart - Función para crear parte vacía
 * @param {Function} parseColorConfig - Función para parsear colores
 * @returns {Array} - Array de partes creadas
 */
export const createPartsFromProduct = (product, createEmptyPart, parseColorConfig) => {
  if (!product.parts || !Array.isArray(product.parts) || product.parts.length === 0) {
    return null;
  }

  return product.parts.map((part, index) => {
    // Crear una parte con los valores predeterminados del producto
    const newPart = createEmptyPart(index, part.name || `Parte ${index + 1}`);

    // Añadir la descripción de la parte si existe
    if (part.description) {
      newPart.description = part.description;
    }

    // Añadir el tipo de encuadernado del producto si existe
    if (product.assembly_order) {
      newPart.assembly_order = product.assembly_order;
    }

    // Añadir la configuración de colores si existe
    if (part.default_colors) {
      const colorConfig = parseColorConfig(part.default_colors);
      if (colorConfig) {
        newPart.colorConfig = colorConfig;
      }
    }

    return newPart;
  });
};

/**
 * Crea una parte por defecto cuando el producto no tiene partes específicas
 * @param {Object} product - Producto del catálogo
 * @param {Function} createEmptyPart - Función para crear parte vacía
 * @param {Function} parseColorConfig - Función para parsear colores
 * @returns {Object} - Parte por defecto creada
 */
export const createDefaultPart = (product, createEmptyPart, parseColorConfig) => {
  const defaultPart = createEmptyPart(0, 'General');

  // Añadir la configuración de colores predeterminada del producto si existe
  if (product.default_colors) {
    const colorConfig = parseColorConfig(product.default_colors);
    if (colorConfig) {
      defaultPart.colorConfig = colorConfig;
    }
  }

  // Añadir la descripción del producto como descripción de la parte
  if (product.description) {
    defaultPart.description = product.description;
  }

  // Añadir el tipo de encuadernado del producto si existe
  if (product.assembly_order) {
    defaultPart.assembly_order = product.assembly_order;
  }

  return defaultPart;
};

/**
 * Maneja el cambio del tipo de trabajo, cargando la información del producto
 * @param {Object} params - Parámetros para el manejo del cambio
 * @returns {Promise<void>}
 */
export const handleJobTypeChange = async (params) => {
  const {
    jobType,
    budget,
    processes,
    setProductConfig,
    setBudgetParts,
    setSelectedProcesses,
    setCalculatedProcessCost,
    createEmptyPart,
    showSnackbar
  } = params;

  try {
    // Obtener el producto del catálogo por tipo usando el servicio
    const product = await ProductService.getProductByType(jobType);

    if (!product) {
      return;
    }

    // Actualizar el estilo de trabajo en la configuración del producto
    if (product.work_style) {
      setProductConfig(prev => ({
        ...prev,
        workStyle: product.work_style
      }));
    }

    // Crear partes basadas en el producto
    const newParts = createPartsFromProduct(product, createEmptyPart, parseColorConfig);

    if (newParts) {
      // Actualizar las partes del presupuesto
      setBudgetParts(newParts);

      // Actualizar la configuración del producto con las partes seleccionadas
      setProductConfig(prev => ({
        ...prev,
        selectedParts: [...product.parts]
      }));

      // Cargar los acabados predeterminados del producto
      loadDefaultFinishing(product, processes, budget.copies, setSelectedProcesses, setCalculatedProcessCost);

      showSnackbar(`Se cargaron ${product.parts.length} partes del producto ${jobType}`, 'success');
    } else {
      // Si no hay partes, crear una parte por defecto
      const defaultPart = createDefaultPart(product, createEmptyPart, parseColorConfig);

      setBudgetParts([defaultPart]);
      setProductConfig(prev => ({
        ...prev,
        selectedParts: []
      }));

      // Cargar los acabados predeterminados del producto
      loadDefaultFinishing(product, processes, budget.copies, setSelectedProcesses, setCalculatedProcessCost);
    }
  } catch (error) {
    console.error('Error al cargar la información del producto:', error);
    showSnackbar(`Error al cargar la información del producto: ${error.message}`, 'error');
  }
};

/**
 * Función principal para manejar cambios en el formulario
 * @param {Object} params - Parámetros para el manejo del cambio
 * @returns {Promise<void>}
 */
export const handleFormChange = async (params) => {
  const {
    event,
    budget,
    budgetParts,
    processes,
    isEditMode,
    setBudget,
    setBudgetParts,
    setProductConfig,
    setSelectedProcesses,
    setCalculatedProcessCost,
    createEmptyPart,
    showSnackbar
  } = params;

  const { name, value } = event.target;

  // Actualizar el campo básico del presupuesto
  updateBudgetField(name, value, setBudget);

  // Manejar cambios específicos según el campo
  if (name === 'copies' && isEditMode) {
    // Si se cambia el número de ejemplares en modo edición, marcar partes como desactualizadas
    markPartsAsOutdated(budgetParts, setBudgetParts, showSnackbar);
  } else if (name === 'jobType' && value) {
    // Si cambia el tipo de trabajo, cargar la información del producto
    await handleJobTypeChange({
      jobType: value,
      budget,
      processes,
      setProductConfig,
      setBudgetParts,
      setSelectedProcesses,
      setCalculatedProcessCost,
      createEmptyPart,
      showSnackbar
    });
  }
};

export default {
  handleFormChange,
  handleJobTypeChange,
  updateBudgetField,
  markPartsAsOutdated,
  parseColorConfig,
  calculateProcessesTotalCost,
  prepareProcessesWithQuantities,
  loadDefaultFinishing,
  createPartsFromProduct,
  createDefaultPart
};
