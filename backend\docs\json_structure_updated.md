# Estructura del JSON para el Generador JDF

El JSON de entrada debe seguir la siguiente estructura para generar correctamente el archivo JDF. Todos los campos marcados con * son obligatorios.

```json
{
  // Información básica del trabajo
  "job_id": "string*",                // Identificador único del trabajo
  "descriptive_name": "string*",      // Nombre descriptivo del trabajo
  "author": "string",                 // Autor del JDF (default: "JDFast")
  "agent_name": "string",             // Nombre del agente (default: "JDFast")
  "agent_version": "string",          // Versión del agente (default: "3.1")
  "comment": "string",                // Comentarios adicionales
  "product_type": "string",           // Tipo de producto
  "binding_side": "string",           // Lado de encuadernación (ej: "Left")
  
  // Signaturas - Define las partes del trabajo y sus características
  "signatures": [                     // Array de signaturas*
    {
      "signature_ID": "string*",      // ID único de la signatura (ej: "Str-1")
      "job_part_id_name": "string*",  // Nombre de la parte del trabajo (ej: "interior", "cubierta")
      "press_name": "string*",        // Nombre de la prensa (ej: "Prensa B1", "HPINDIGO")
      "assembly_order": "string",     // Orden de ensamblaje (ej: "Gathering")
      "color_config": "string*",      // Configuración de colores (ej: "4/4", "1/1", "4/0", "5/5")
      "color_details": {              // Detalles opcionales de los colores
        "front": ["string"],          // Colores cara frontal (ej: ["C", "M", "Y", "K", "Pantone 123"])
        "back": ["string"]            // Colores cara posterior (ej: ["C", "M", "Y", "K"])
      },
      "stripping_params": {
        "signature_name": "string*",  // Nombre de la signatura (ej: "Sig-1")
        "sheets": [                   // Array de hojas en la signatura
          {
            "sheet_name": "string*",              // Nombre de la hoja (ej: "Sheet-1-1")
            "bindery_signature_name": "string*",  // Nombre del plegado (ej: "BSN-1-1")
            "paper_ref": "string*",               // Referencia al papel (ej: "Pap-001")
            "fold_catalog": "string*",            // Catálogo de plegado (ej: "F16-7")
            "work_style": "string*",              // Estilo de trabajo: "WorkAndBack" | "Perfecting" | "WorkAndTurn" | "Flat"
            "strip_cell_params": {                // Parámetros de imposición
              // Parámetros de recorte (mm)
              "spine": "number",                  // Calle interior
              "trim_face": "number",              // Calle frontal
              "trim_foot": "number",              // Calle inferior
              "trim_head": "number",              // Calle superior
              "trim_size_width": "number",        // Ancho final
              "trim_size_height": "number",       // Alto final
              
              // Parámetros de sangrado (mm)
              "bleed_face": "number",             // Sangrado frontal
              "bleed_foot": "number",             // Sangrado pie
              "bleed_head": "number",             // Sangrado cabeza
              "bleed_spine": "number",            // Sangrado lomo
              
              // Parámetros de plegado
              "back_overfold": "number",          // Sobredoblez trasero
              "milling_depth": "number",          // Profundidad de fresado
              "front_overfold": "number",         // Sobredoblez frontal
              
              // Ajustes de posición
              "creep_x": "number",                // Desplazamiento X
              "creep_y": "number",                // Desplazamiento Y
              "margin_top": "number",             // Margen superior (mm)
              "margin_left": "number",            // Margen izquierdo (mm)
              "margin_right": "number",           // Margen derecho (mm)
              "margin_bottom": "number",          // Margen inferior (mm)
              
              // Orientación y posición relativa
              "orientation": "string",            // "Rotate0" | "Rotate90" | "Rotate180" | "Rotate270"
              "relative_box_x1": "number",        // Caja relativa X1 (0-1)
              "relative_box_y1": "number",        // Caja relativa Y1 (0-1)
              "relative_box_x2": "number",        // Caja relativa X2 (0-1)
              "relative_box_y2": "number"         // Caja relativa Y2 (0-1)
            }
          }
        ]
      }
    }
  ],
  
  // RunLists - Define los archivos PDF y su distribución
  "runlists": [                       // Array de listas de ejecución (opcional)
    {
      "runlist_id": "string*",        // ID único de la RunList (ej: "RL-1d")
      "pages": "number*",             // Número de páginas
      "page_range": "string*",        // Rango de páginas (ej: "0 ~ 15")
      "signature_ref": "string*",     // Referencia a la signatura (ej: "Str-1")
      "pdf_url": "string*"            // URL del archivo PDF
    }
  ],
  
  // Configuraciones de papel
  "paper_configs": [                  // Array de configuraciones de papel (opcional)
    {
      "weight": "number*",            // Peso del papel (g/m²)
      "dimension_width": "number*",   // Ancho del papel (mm)
      "dimension_height": "number*",  // Alto del papel (mm)
      "media_type": "string*",        // Tipo de medio (ej: "Paper")
      "product_id": "string*",        // ID del producto (ej: "Pap-001")
      "thickness": "number*",         // Grosor del papel (µm)
      "descriptive_name": "string*"   // Nombre descriptivo del papel
    }
  ],
  
  // Información del cliente
  "customer_info": {                  // Información del cliente (opcional)
    "customer_id": "string",          // ID del cliente
    "billing_code": "string",         // Código de facturación
    "order_id": "string",             // ID del pedido
    "company_name": "string",         // Nombre de la empresa
    "country": "string",              // País
    "region": "string",              // Región/Provincia
    "city": "string",                // Ciudad
    "street": "string",              // Dirección
    "postal_code": "string",         // Código postal
    "job_title": "string",           // Puesto de trabajo
    "first_name": "string",          // Nombre
    "family_name": "string",         // Apellidos
    "phone": "string",               // Teléfono
    "fax": "string",                 // Fax
    "email": "string"                // Correo electrónico
  }
}
```

## Notas Importantes

1. **Campos Obligatorios**
   - Los campos marcados con * son obligatorios cuando su sección padre está presente
   - Al menos debe haber una signatura definida
   - Cada signatura debe tener al menos una hoja
   - Los parámetros de imposición (strip_cell_params) son opcionales pero recomendados
   - Las secciones `runlists`, `paper_configs` y `customer_info` son completamente opcionales

2. **Tipos de Datos**
   - Todos los valores numéricos relacionados con dimensiones están en milímetros (mm)
   - Las orientaciones deben ser una de: "Rotate0", "Rotate90", "Rotate180", "Rotate270"
   - Los estilos de trabajo deben ser uno de: "WorkAndBack", "Perfecting", "Flat", "WorkAndTurn"
   - Las cajas relativas deben tener valores entre 0 y 1
   - Las configuraciones de colores siguen el formato "X/Y", donde X es el número de colores en la cara frontal y Y en la posterior

3. **Relación Prensa-Plancha**
   - Cada prensa tiene asociada su propia plancha con dimensiones específicas:
     - Prensa B1: Plancha 2917.7 x 2182.7 mm
     - Prensa B2: Plancha 2267.72 x 1842.52 mm
   - Las planchas se generan automáticamente según el tipo de prensa especificado en `press_name`
   - Las prensas HPINDIGO no requieren planchas

4. **Identificadores**
   - Los IDs de prensa siguen el formato: `Press-{index}` (ej: "Press-1", "Press-2")
   - Los IDs de plancha siguen el formato: `Plate-{index}` (ej: "Plate-001", "Plate-002")
   - Cada plancha tiene un ProductID único: `Pla-{index}` (ej: "Pla-1", "Pla-2")

5. **Restricciones**
   - Una plancha solo puede ser utilizada por una prensa
   - La referencia a la plancha en `MediaRef` debe coincidir con la prensa en `DeviceRef`
   - No se pueden mezclar planchas de diferentes formatos en la misma prensa

6. **Configuraciones de Color**
   - El campo `color_config` sigue el formato "X/Y" donde:
     - X: Número de colores en la cara frontal (ej: 4 para CMYK, 1 para K, 5 para CMYK+Pantone)
     - Y: Número de colores en la cara posterior (ej: 4 para CMYK, 0 para sin impresión)
   - Ejemplos comunes: "4/4" (CMYK/CMYK), "1/1" (K/K), "4/0" (CMYK/-), "5/5" (CMYK+Pantone/CMYK+Pantone)
   - El campo `color_details` permite especificar exactamente qué colores se utilizan en cada cara

## Estructura Minima

```json
{
  "job_id": "string*",                // Identificador único del trabajo
  "descriptive_name": "string*",      // Nombre descriptivo del trabajo
  
  "signatures": [                     // Array de signaturas*
    {
      "signature_ID": "string*",      // ID único de la signatura (ej: "Str-1")
      "job_part_id_name": "string*",  // Nombre de la parte del trabajo (ej: "interior", "cubierta")
      "press_name": "string*",        // Nombre de la prensa (ej: "Prensa B1", "HPINDIGO")
      "assembly_order": "string",     // Orden de ensamblaje (ej: "Gathering")
      "color_config": "string*",      // Configuración de colores (ej: "4/4", "1/1", "4/0")
      "stripping_params": {
        "signature_name": "string*",  // Nombre de la signatura (ej: "Sig-1")
        "sheets": [                   // Array de hojas en la signatura
          {
            "sheet_name": "string*",              // Nombre de la hoja (ej: "Sheet-1-1")
            "bindery_signature_name": "string*",  // Nombre del plegado (ej: "BSN-1-1")
            "paper_ref": "string*",               // Referencia al papel (ej: "Pap-001")
            "fold_catalog": "string*",            // Catálogo de plegado (ej: "F16-7")
            "work_style": "string*"              // Estilo de trabajo: "WorkAndBack" | "Perfecting" | "WorkAndTurn" | "Flat"
            
          }
        ]
      }
    }
  ],
  
  "paper_configs": [                  // Array de configuraciones de papel (opcional)
    {
      "weight": "number*",            // Peso del papel (g/m²)
      "dimension_width": "number*",   // Ancho del papel (mm)
      "dimension_height": "number*",  // Alto del papel (mm)
      "media_type": "string*",        // Tipo de medio (ej: "Paper")
      "product_id": "string*",        // ID del producto
      "thickness": "number*",         // Grosor del papel (mm)
      "descriptive_name": "string*"   // Nombre descriptivo del papel
    }
  ]
}
```
