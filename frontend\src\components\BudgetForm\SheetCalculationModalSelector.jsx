// No necesitamos importar React explícitamente en React 17+
import PropTypes from 'prop-types';
import OffsetCalculationModal from './OffsetCalculationModal';
import DigitalCalculationModal from './DigitalCalculationModal';
import SheetCalculationModal from './SheetCalculationModal'; // Mantenemos el modal original para compatibilidad

/**
 * Componente selector que elige el modal adecuado según el tipo de máquina
 * Si useSpecializedModals es true, usa los modales específicos para offset y digital
 * Si es false, usa el modal general (SheetCalculationModal)
 */
const SheetCalculationModalSelector = ({
  open,
  onClose,
  sheetCalculation,
  budget,
  part, // Añadimos la prop part para acceder directamente a la máquina
  copies, // Añadimos la prop copies para pasarla a los modales
  customMachinePrintTime,
  handleCustomMachineTimeChange,
  selectedProcesses,
  calculatedProcessCost,
  calculationResults, // Añadimos los resultados del cálculo
  useSpecializedModals = true // Por defecto, usar los modales especializados
}) => {
  // Si no se deben usar los modales especializados, mostrar el modal general
  if (!useSpecializedModals) {
    return (
      <SheetCalculationModal
        open={open}
        onClose={onClose}
        sheetCalculation={sheetCalculation}
        budget={budget}
        customMachinePrintTime={customMachinePrintTime}
        handleCustomMachineTimeChange={handleCustomMachineTimeChange}
        selectedProcesses={selectedProcesses}
        calculatedProcessCost={calculatedProcessCost}
      />
    );
  }

  // Determinar el tipo de máquina con más opciones de búsqueda
  let machineType = null;

  // Intentar obtener el tipo de máquina de diferentes fuentes
  if (part && part.machine && part.machine.type) {
    machineType = part.machine.type;

  } else if (budget && budget.machine && budget.machine.type) {
    machineType = budget.machine.type;
  } else if (budget && budget.part && budget.part.machine && budget.part.machine.type) {
    machineType = budget.part.machine.type;
  } else if (sheetCalculation && sheetCalculation.machine_data && sheetCalculation.machine_data.type) {
    machineType = sheetCalculation.machine_data.type;
  } else if (calculationResults && calculationResults.machine_id) {
    // Si tenemos el ID de la máquina, podemos intentar determinar el tipo por el nombre
    const machineId = calculationResults.machine_id;
    if (machineId.includes('OFFSET') || machineId.includes('MAQ')) {
      machineType = 'Offset';
    } else if (machineId.includes('DIGITAL') || machineId.includes('DIG')) {
      machineType = 'Digital';
    }
  }

  // Si todavía no tenemos el tipo, intentar inferirlo de los resultados
  if (!machineType && calculationResults) {
    if (calculationResults.total_plates || calculationResults.plates_cost) {
      machineType = 'Offset';
    } else if (calculationResults.total_clicks || calculationResults.click_cost) {
      machineType = 'Digital';
    }
  }

  // Usar los resultados del cálculo o los datos del budget/part según corresponda
  const dataToUse = calculationResults || sheetCalculation || budget;

  // Intentar obtener el número de copias de diferentes fuentes
  const copiesRawValue = copies ||
                      (calculationResults?.copies) ||
                      (budget?.copies) ||
                      (part?.copies) ||
                      (sheetCalculation?.copies) ||
                      (sheetCalculation?.mejor_combinacion?.copies);

  // Convertir a número si es string, o usar 0 como valor por defecto
  const copiesToUse = copiesRawValue ? parseInt(copiesRawValue, 10) : 0;



  // Elegir el modal adecuado según el tipo de máquina
  if (machineType === 'Digital') {
    return (
      <DigitalCalculationModal
        open={open}
        onClose={onClose}
        sheetCalculation={dataToUse}
        budget={part || budget}
        copies={copiesToUse}
        customMachinePrintTime={customMachinePrintTime}
        handleCustomMachineTimeChange={handleCustomMachineTimeChange}
        selectedProcesses={selectedProcesses}
        calculatedProcessCost={calculatedProcessCost}
      />
    );
  } else if (machineType === 'Offset') {
    return (
      <OffsetCalculationModal
        open={open}
        onClose={onClose}
        sheetCalculation={dataToUse}
        budget={part || budget}
        copies={copiesToUse}
        customMachinePrintTime={customMachinePrintTime}
        handleCustomMachineTimeChange={handleCustomMachineTimeChange}
        selectedProcesses={selectedProcesses}
        calculatedProcessCost={calculatedProcessCost}
      />
    );
  } else {
    // Si el tipo de máquina no está definido o no es reconocido, usar el modal general
    return (
      <SheetCalculationModal
        open={open}
        onClose={onClose}
        sheetCalculation={sheetCalculation}
        budget={budget}
        customMachinePrintTime={customMachinePrintTime}
        handleCustomMachineTimeChange={handleCustomMachineTimeChange}
        selectedProcesses={selectedProcesses}
        calculatedProcessCost={calculatedProcessCost}
      />
    );
  }
};

SheetCalculationModalSelector.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  sheetCalculation: PropTypes.object,
  budget: PropTypes.object,
  part: PropTypes.object, // Añadimos la validación para part
  copies: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // Añadimos la validación para copies
  customMachinePrintTime: PropTypes.number,
  handleCustomMachineTimeChange: PropTypes.func,
  selectedProcesses: PropTypes.array,
  calculatedProcessCost: PropTypes.number,
  calculationResults: PropTypes.object, // Añadimos la validación para calculationResults
  useSpecializedModals: PropTypes.bool
};

export default SheetCalculationModalSelector;
