import { buildApiUrl } from '../config';

/**
 * Servicio para enviar JSON_OT a un servidor externo
 */
const JDFExternalService = {
  /**
   * Envía un JSON_OT al servidor externo usando el backend como intermediario
   * @param {string} budgetId - ID del presupuesto
   * @returns {Promise<Object>} - Respuesta del servidor
   */
  async sendJDFToExternalServer(budgetId) {
    try {
      console.log(`Enviando solicitud al backend para procesar y enviar JSON_OT del presupuesto ${budgetId}`);
      
      // Usamos el nuevo endpoint del backend que maneja todo el proceso
      const response = await fetch(buildApiUrl(`/external/send-budget-jdf/${budgetId}`), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        let errorMessage = 'Error al enviar el JSON_OT al servidor externo';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.detail || errorMessage;
        } catch {
          // Si no podemos parsear la respuesta como JSON, usamos el mensaje por defecto
        }
        throw new Error(errorMessage);
      }
      
      const result = await response.json();
      console.log('Respuesta del servidor:', result);
      return result;
    } catch (error) {
      console.error('Error en JDFExternalService:', error);
      throw error;
    }
  }
};

export default JDFExternalService;
