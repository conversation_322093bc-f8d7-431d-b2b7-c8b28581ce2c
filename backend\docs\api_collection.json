{"info": {"name": "Imprenta API Collection", "description": "Colección de endpoints para la API del sistema de gestión de imprenta", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Clientes", "item": [{"name": "Obtener todos los clientes", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/clients/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", ""]}}}, {"name": "Obtener cliente por ID", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/clients/CL-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", "CL-001"]}}}, {"name": "Crear nuevo cliente", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/clients/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": \"CL-NEW\",\n  \"billing_code\": \"BC-001\",\n  \"order_id\": \"OT-001\",\n  \"company\": {\n    \"name\": \"Nueva Empresa S.L.\",\n    \"address\": {\n      \"country\": \"España\",\n      \"region\": \"Madrid\",\n      \"city\": \"Madrid\",\n      \"street\": \"Calle Principal 123\",\n      \"postal_code\": \"28001\"\n    }\n  },\n  \"contact\": {\n    \"position\": \"<PERSON><PERSON><PERSON>\",\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON>\",\n    \"phone\": \"*********\",\n    \"fax\": \"*********\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"active\": true,\n  \"created_at\": \"2025-03-11\",\n  \"updated_at\": \"2025-03-11\",\n  \"notes\": \"Cliente nuevo\"\n}"}}}, {"name": "Actualizar cliente", "request": {"method": "PUT", "url": {"raw": "http://localhost:3005/clients/CL-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", "CL-001"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"active\": true,\n  \"notes\": \"Cliente actualizado\"\n}"}}}, {"name": "Eliminar cliente", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/clients/CL-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", "CL-001"]}}}]}, {"name": "Presupuestos", "item": [{"name": "Obtener todos los presupuestos", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/budgets/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", ""]}}}, {"name": "Obtener presupuesto por ID", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/budgets/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "PRES-ABC123"]}}}, {"name": "Crear nuevo presupuesto", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/budgets/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ot_number\": \"OT-12345\",\n  \"description\": \"Folletos promocionales\",\n  \"client_id\": \"Customer-1\",\n  \"job_type\": \"Folleto\",\n  \"quantity\": 1000,\n  \"page_size\": {\n    \"width\": 210,\n    \"height\": 297\n  },\n  \"page_count\": 8,\n  \"paper_id\": \"PAP-001\",\n  \"machine_id\": \"MAQ-001\",\n  \"sheet_calculation\": null\n}"}}}, {"name": "Actualizar presupuesto", "request": {"method": "PUT", "url": {"raw": "http://localhost:3005/budgets/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "PRES-ABC123"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ot_number\": \"OT-12345\",\n  \"description\": \"Folletos promocionales actualizados\",\n  \"client_id\": \"Customer-1\",\n  \"job_type\": \"Folleto\",\n  \"quantity\": 2000,\n  \"page_size\": {\n    \"width\": 210,\n    \"height\": 297\n  },\n  \"page_count\": 12,\n  \"paper_id\": \"PAP-001\",\n  \"machine_id\": \"MAQ-001\",\n  \"sheet_calculation\": null\n}"}}}, {"name": "Eliminar presupuesto", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/budgets/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "PRES-ABC123"]}}}, {"name": "Buscar presupuestos", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/budgets/search?client_id=Customer-1&status=Pendiente&job_type=Folleto", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "search"], "query": [{"key": "client_id", "value": "Customer-1"}, {"key": "status", "value": "Pendiente"}, {"key": "job_type", "value": "<PERSON><PERSON><PERSON>"}]}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Obtener todos los papeles", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/papers/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", ""]}}}, {"name": "Obtener papel por ID", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/papers/Pap-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", "Pap-001"]}}}, {"name": "<PERSON><PERSON><PERSON> nuevo papel", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/papers/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"Pap-NEW\",\n  \"descriptive_name\": \"Offset 100g A3\",\n  \"media_type\": \"Paper\",\n  \"category\": \"Offset\",\n  \"finish\": \"Natural\",\n  \"dimension_width\": 420,\n  \"dimension_height\": 297,\n  \"thickness\": 100,\n  \"weight\": 100,\n  \"grainDirection\": \"Long\",\n  \"manufacturer\": \"PaperCo\",\n  \"inStock\": true,\n  \"color\": \"Blanco\",\n  \"notes\": \"Papel de alta calidad\",\n  \"price_per_1000\": 25.5\n}"}}}, {"name": "<PERSON>ual<PERSON><PERSON> papel", "request": {"method": "PUT", "url": {"raw": "http://localhost:3005/papers/Pap-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", "Pap-001"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"inStock\": true,\n  \"price_per_1000\": 28.5\n}"}}}, {"name": "Eliminar papel", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/papers/Pap-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", "Pap-001"]}}}]}, {"name": "Máquinas", "item": [{"name": "Obtener todas las máquinas", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/machines/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", ""]}}}, {"name": "Obtener máquina por ID", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/machines/M-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", "M-001"]}}}, {"name": "Crear nueva máquina", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/machines/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"machine_id\": \"M-NEW\",\n  \"name\": \"Nueva Prensa Digital\",\n  \"type\": \"Digital\",\n  \"manufacturer\": \"PrintCorp\",\n  \"model\": \"DP-2000\",\n  \"max_width\": 330,\n  \"max_height\": 488,\n  \"min_width\": 100,\n  \"min_height\": 148,\n  \"max_thickness\": 300,\n  \"status\": \"Activa\",\n  \"purchase_date\": \"2025-01-15\",\n  \"last_maintenance\": \"2025-03-01\",\n  \"notes\": \"Máquina nueva de alta velocidad\"\n}"}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "url": {"raw": "http://localhost:3005/machines/M-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", "M-001"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"Activa\",\n  \"last_maintenance\": \"2025-03-10\",\n  \"notes\": \"Mantenimiento realizado\"\n}"}}}, {"name": "Eliminar m<PERSON><PERSON>", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/machines/M-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", "M-001"]}}}]}]}