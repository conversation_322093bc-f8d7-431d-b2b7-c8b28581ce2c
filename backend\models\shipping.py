from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
import uuid

class ShippingRecord(BaseModel):
    """Modelo para registros de envío"""
    shipping_id: str = Field(default_factory=lambda: f"SHIP-{uuid.uuid4().hex[:6].upper()}")
    ot_number: str
    budget_id: str
    client_id: str
    client_name: str
    shipping_date: str = Field(default_factory=lambda: datetime.now().isoformat())
    carrier: str
    tracking_number: Optional[str] = None
    packages: int = 1
    weight: Optional[float] = None
    notes: Optional[str] = None
    delivery_address: Dict[str, Any]
    contact_person: str
    contact_phone: str
    delivery_status: str = "En Proceso"  # En Proceso, Entregado, Devuelto, Cancelado
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    
class ShippingRecordCreate(BaseModel):
    """Modelo para crear registros de envío"""
    ot_number: str
    budget_id: str
    client_id: str
    client_name: str
    carrier: str
    tracking_number: Optional[str] = None
    packages: int = 1
    weight: Optional[float] = None
    notes: Optional[str] = None
    delivery_address: Dict[str, Any]
    contact_person: str
    contact_phone: str
    
class ShippingRecordUpdate(BaseModel):
    """Modelo para actualizar registros de envío"""
    carrier: Optional[str] = None
    tracking_number: Optional[str] = None
    packages: Optional[int] = None
    weight: Optional[float] = None
    notes: Optional[str] = None
    delivery_status: Optional[str] = None
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())
