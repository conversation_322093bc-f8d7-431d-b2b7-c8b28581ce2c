import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Button,
  Alert,
  TextField,
  IconButton,
  Tooltip
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import DownloadIcon from '@mui/icons-material/Download';
import SearchIcon from '@mui/icons-material/Search';
import { buildApiUrl } from '../config';
import { ApiInterceptor, LogService } from '../services/logViewerServices';

const LogViewer = () => {
  const [logFiles, setLogFiles] = useState([]);
  const [selectedFile, setSelectedFile] = useState('');
  const [logContent, setLogContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredContent, setFilteredContent] = useState('');

  // Cargar la lista de archivos de log al montar el componente
  useEffect(() => {
    fetchLogFiles();
    // Registrar la navegación al componente LogViewer
    LogService.logNavigation('LogViewer');
  }, []);

  // Cargar el contenido del log actual cuando se monta el componente
  useEffect(() => {
    fetchCurrentLog();
  }, []);

  // Filtrar el contenido del log cuando cambia el término de búsqueda
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredContent(logContent);
    } else {
      const lines = logContent.split('\n');
      const filtered = lines.filter(line =>
        line.toLowerCase().includes(searchTerm.toLowerCase())
      ).join('\n');
      setFilteredContent(filtered);
    }
  }, [searchTerm, logContent]);

  // Función para obtener la lista de archivos de log
  const fetchLogFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await ApiInterceptor.fetch(buildApiUrl('/logs/files'));

      if (!response.ok) {
        throw new Error(`Error al obtener los archivos de log: ${response.statusText}`);
      }

      const data = await response.json();
      setLogFiles(data);

      // Si hay archivos y no hay ninguno seleccionado, seleccionar el primero
      if (data.length > 0 && !selectedFile) {
        setSelectedFile(data[0]);
        fetchLogContent(data[0]);
      }

      // Registrar la acción del usuario
      LogService.logUserAction('fetch_log_files', { count: data.length });
    } catch (err) {
      LogService.logError('Error al obtener archivos de log', { error: err.message });
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Función para obtener el contenido del log actual
  const fetchCurrentLog = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await ApiInterceptor.fetch(buildApiUrl('/logs/current'));

      if (!response.ok) {
        throw new Error(`Error al obtener el log actual: ${response.statusText}`);
      }

      const data = await response.json();
      setLogContent(data.content);
      setFilteredContent(data.content);

      // Registrar la acción del usuario
      LogService.logUserAction('fetch_current_log', { size: data.content.length });
    } catch (err) {
      LogService.logError('Error al obtener log actual', { error: err.message });
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Función para obtener el contenido de un archivo de log específico
  const fetchLogContent = async (filename) => {
    try {
      setLoading(true);
      setError(null);

      const response = await ApiInterceptor.fetch(buildApiUrl(`/logs/content/${filename}`));

      if (!response.ok) {
        throw new Error(`Error al obtener el contenido del log: ${response.statusText}`);
      }

      const data = await response.json();
      setLogContent(data.content);
      setFilteredContent(data.content);

      // Registrar la acción del usuario
      LogService.logUserAction('fetch_log_content', { filename, size: data.content.length });
    } catch (err) {
      LogService.logError('Error al obtener contenido del log', { filename, error: err.message });
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Función para manejar el cambio de archivo seleccionado
  const handleFileChange = (event) => {
    const filename = event.target.value;
    setSelectedFile(filename);
    fetchLogContent(filename);

    // Registrar la acción del usuario
    LogService.logUserAction('select_log_file', { filename });
  };

  // Función para descargar el contenido del log
  const handleDownload = () => {
    const element = document.createElement('a');
    const file = new Blob([logContent], {type: 'text/plain'});
    element.href = URL.createObjectURL(file);
    element.download = selectedFile || 'current_log.log';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);

    // Registrar la acción del usuario
    LogService.logUserAction('download_log', {
      filename: selectedFile || 'current_log.log',
      size: logContent.length
    });
  };

  // Función para manejar el cambio en el término de búsqueda
  const handleSearchChange = (event) => {
    const term = event.target.value;
    setSearchTerm(term);

    // Registrar la acción del usuario si el término tiene al menos 3 caracteres
    if (term.length >= 3) {
      LogService.logUserAction('search_logs', { term });
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Visor de Logs
      </Typography>

      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="log-file-label">Archivo de Log</InputLabel>
          <Select
            labelId="log-file-label"
            value={selectedFile}
            onChange={handleFileChange}
            label="Archivo de Log"
          >
            <MenuItem value="">
              <em>Log Actual</em>
            </MenuItem>
            {logFiles.map((file) => (
              <MenuItem key={file} value={file}>
                {file}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Tooltip title="Refrescar">
          <IconButton
            color="primary"
            onClick={selectedFile ? () => fetchLogContent(selectedFile) : fetchCurrentLog}
            disabled={loading}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Descargar">
          <IconButton
            color="primary"
            onClick={handleDownload}
            disabled={loading || !logContent}
          >
            <DownloadIcon />
          </IconButton>
        </Tooltip>

        <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto' }}>
          <TextField
            label="Buscar"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ width: 200 }}
          />
          <IconButton color="primary">
            <SearchIcon />
          </IconButton>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Paper
          elevation={3}
          sx={{
            p: 2,
            maxHeight: 'calc(100vh - 250px)',
            overflow: 'auto',
            fontFamily: 'monospace',
            whiteSpace: 'pre-wrap',
            fontSize: '0.85rem',
            bgcolor: '#f5f5f5'
          }}
        >
          {filteredContent || 'No hay contenido para mostrar'}
        </Paper>
      )}
    </Box>
  );
};

export default LogViewer;
