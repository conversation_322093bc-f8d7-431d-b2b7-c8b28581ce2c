import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Paper,
  IconButton,
  CircularProgress,
  TextField,
  InputAdornment,
  Tooltip
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import CloseIcon from '@mui/icons-material/Close';
import { buildApiUrl } from '../../config';

/**
 * Componente que muestra información detallada de una máquina y su coste para un trabajo específico
 */
const MachineInfoModal = ({ open, onClose, machine, totalSheets, budget, onUpdatePrintTime, sheetCalculation, selectedProcesses }) => {
  const [loading, setLoading] = useState(false);
  const [machineDetails, setMachineDetails] = useState(null);
  const [customPrintTime, setCustomPrintTime] = useState('');
  const [isEditingTime, setIsEditingTime] = useState(false);

  // Estados para valores editables
  const [customHourlyCost, setCustomHourlyCost] = useState('');
  const [isEditingHourlyCost, setIsEditingHourlyCost] = useState(false);

  const [customSpeed, setCustomSpeed] = useState('');
  const [isEditingSpeed, setIsEditingSpeed] = useState(false);

  const [customCFAPercentage, setCustomCFAPercentage] = useState('');
  const [isEditingCFAPercentage, setIsEditingCFAPercentage] = useState(false);

  const [customSetupTime, setCustomSetupTime] = useState('');
  const [isEditingSetupTime, setIsEditingSetupTime] = useState(false);

  const [customMaculatura, setCustomMaculatura] = useState('');
  const [isEditingMaculatura, setIsEditingMaculatura] = useState(false);

  // Cargar los detalles de la máquina cuando se abre el modal
  useEffect(() => {
    if (open && machine) {
      setLoading(true);

      // Obtener los detalles completos de la máquina
      fetch(buildApiUrl(`/machines/${machine.machine_id}`))
        .then(response => {
          if (!response.ok) throw new Error('Error al obtener los detalles de la máquina');
          return response.json();
        })
        .then(data => {
          setMachineDetails(data);
          setLoading(false);
        })
        .catch(error => {
          console.error('Error:', error);
          setLoading(false);
        });
    }
  }, [open, machine]);

  // Inicializar los valores personalizados cuando se cargan los detalles de la máquina
  useEffect(() => {
    if (open && machine && budget) {
      // Si hay un tiempo personalizado en la parte seleccionada, usarlo
      if (budget.customPrintTime !== undefined && budget.customPrintTime !== null) {
        console.log('Usando tiempo personalizado de la parte:', budget.customPrintTime);
        setCustomPrintTime(budget.customPrintTime.toString());
      } else if (machineDetails && totalSheets) {
        // Estimación inicial: 1 hora por cada 1000 pliegos
        const estimatedTime = Math.max(1, (totalSheets / 1000));
        setCustomPrintTime(estimatedTime.toFixed(2));
      }

      // Inicializar otros valores personalizados con los valores de la máquina
      if (machineDetails) {
        setCustomHourlyCost(machineDetails.hourly_cost?.toString() || '');
        setCustomSpeed(machineDetails.speed?.toString() || '');
        setCustomCFAPercentage(machineDetails.cfa_percentage?.toString() || '');
        setCustomSetupTime(machineDetails.setup_time?.toString() || '0.5');
        setCustomMaculatura('100'); // Valor por defecto: 100 pliegos por arranque
      }
    }
  }, [open, machine, budget, machineDetails, totalSheets]);

  // Manejar cambios en el tiempo personalizado
  const handlePrintTimeChange = (event) => {
    const value = event.target.value;
    // Permitir solo números y punto decimal
    if (/^\d*\.?\d*$/.test(value)) {
      setCustomPrintTime(value);
    }
  };

  // Finalizar la edición del tiempo
  const handlePrintTimeBlur = () => {
    // Asegurarse de que el valor sea al menos 0.1
    const numericValue = parseFloat(customPrintTime) || 0;
    const validValue = Math.max(0.1, numericValue).toFixed(2);
    setCustomPrintTime(validValue);
    setIsEditingTime(false);

    // Notificar al componente padre sobre el cambio en el tiempo estimado
    if (onUpdatePrintTime) {
      console.log('Actualizando tiempo de impresión a:', parseFloat(validValue));
      onUpdatePrintTime(parseFloat(validValue));
    }
  };

  // Manejar el clic en el botón de guardar tiempo
  const handleSaveTimeClick = () => {
    // Asegurarse de que el valor sea al menos 0.1
    const numericValue = parseFloat(customPrintTime) || 0;
    const validValue = Math.max(0.1, numericValue).toFixed(2);
    setCustomPrintTime(validValue);
    setIsEditingTime(false);

    // Notificar al componente padre sobre el cambio en el tiempo estimado
    if (onUpdatePrintTime) {
      console.log('Guardando tiempo de impresión:', parseFloat(validValue));
      console.log('Budget:', budget);
      console.log('Machine:', machine);
      onUpdatePrintTime(parseFloat(validValue));
    }
  };

  // Funciones para manejar cambios en los valores editables

  // Coste por hora
  const handleHourlyCostChange = (event) => {
    const value = event.target.value;
    // Permitir solo números y punto decimal
    if (/^\d*\.?\d*$/.test(value)) {
      setCustomHourlyCost(value);
    }
  };

  const handleSaveHourlyCostClick = () => {
    const numericValue = parseFloat(customHourlyCost) || 0;
    const validValue = Math.max(0.1, numericValue).toFixed(2);
    setCustomHourlyCost(validValue);
    setIsEditingHourlyCost(false);

    // Actualizar el valor en machineDetails para que se refleje en los cálculos
    setMachineDetails({
      ...machineDetails,
      hourly_cost: parseFloat(validValue)
    });
  };

  // Velocidad
  const handleSpeedChange = (event) => {
    const value = event.target.value;
    // Permitir solo números y punto decimal
    if (/^\d*\.?\d*$/.test(value)) {
      setCustomSpeed(value);
    }
  };

  const handleSaveSpeedClick = () => {
    const numericValue = parseFloat(customSpeed) || 0;
    const validValue = Math.max(1, numericValue).toFixed(0);
    setCustomSpeed(validValue);
    setIsEditingSpeed(false);

    // Actualizar el valor en machineDetails para que se refleje en los cálculos
    setMachineDetails({
      ...machineDetails,
      speed: parseFloat(validValue)
    });
  };

  // CFA Percentage
  const handleCFAPercentageChange = (event) => {
    const value = event.target.value;
    // Permitir solo números y punto decimal
    if (/^\d*\.?\d*$/.test(value)) {
      setCustomCFAPercentage(value);
    }
  };

  const handleSaveCFAPercentageClick = () => {
    const numericValue = parseFloat(customCFAPercentage) || 0;
    const validValue = Math.max(0, Math.min(100, numericValue)).toFixed(1);
    setCustomCFAPercentage(validValue);
    setIsEditingCFAPercentage(false);

    // Actualizar el valor en machineDetails para que se refleje en los cálculos
    setMachineDetails({
      ...machineDetails,
      cfa_percentage: parseFloat(validValue)
    });
  };

  // Tiempo de arranque
  const handleSetupTimeChange = (event) => {
    const value = event.target.value;
    // Permitir solo números y punto decimal
    if (/^\d*\.?\d*$/.test(value)) {
      setCustomSetupTime(value);
    }
  };

  const handleSaveSetupTimeClick = () => {
    const numericValue = parseFloat(customSetupTime) || 0;
    const validValue = Math.max(0.1, numericValue).toFixed(2);
    setCustomSetupTime(validValue);
    setIsEditingSetupTime(false);

    // Actualizar el valor en machineDetails para que se refleje en los cálculos
    setMachineDetails({
      ...machineDetails,
      setup_time: parseFloat(validValue)
    });
  };

  // Maculatura
  const handleMaculaturaChange = (event) => {
    const value = event.target.value;
    // Permitir solo números enteros
    if (/^\d*$/.test(value)) {
      setCustomMaculatura(value);
    }
  };

  const handleSaveMaculaturaClick = () => {
    const numericValue = parseInt(customMaculatura) || 0;
    const validValue = Math.max(0, numericValue).toString();
    setCustomMaculatura(validValue);
    setIsEditingMaculatura(false);

    // No necesitamos actualizar machineDetails ya que la maculatura se calcula en el momento
  };

  // Calcular el tiempo de impresión en horas (mínimo 0.1 hora)
  const calculatePrintTime = () => {
    // Si hay un tiempo personalizado, usarlo
    if (customPrintTime) {
      return parseFloat(customPrintTime);
    }

    if (!totalSheets) return 1;

    // Si es una máquina digital y tiene velocidad definida, calcular basado en la velocidad
    if (machineDetails && machineDetails.type === 'Digital' && machineDetails.speed) {
      // Obtener la velocidad en A4/minuto
      const speedA4PerMinute = parseFloat(machineDetails.speed);

      if (!isNaN(speedA4PerMinute) && speedA4PerMinute > 0) {
        // Calcular cuántas páginas A4 se imprimen en total
        let totalA4Pages = 0;

        // Si tenemos datos de clicks, usar esos datos
        if (budget && budget.clicksData) {
          // Sumar páginas color y B/N
          totalA4Pages = (budget.clicksData.color_sides || 0) + (budget.clicksData.bw_sides || 0);
        }
        // Si no, estimar basado en pliegos (cada pliego SRA3 equivale a 2 A4 por cara)
        else if (totalSheets) {
          // Cada pliego SRA3 equivale a 2 A4 por cara
          // Si es tira y retira, son 4 A4 por pliego (2 por cada cara)
          const esTiraRetira = sheetCalculation?.mejor_combinacion?.esquemas_utilizados?.some(e => e.es_tira_retira) || false;
          const a4PorPliego = esTiraRetira ? 4 : 2;
          totalA4Pages = totalSheets * a4PorPliego;
        }

        // Calcular tiempo en minutos y convertir a horas
        const timeInMinutes = totalA4Pages / speedA4PerMinute;
        const timeInHours = timeInMinutes / 60;

        // Asegurar un mínimo de 0.1 horas (6 minutos)
        return Math.max(0.1, timeInHours);
      }
    }

    // Para máquinas offset, calcular basado en la velocidad y el tiempo de arranque
    if (machineDetails && machineDetails.type === 'Offset') {
      // Obtener la velocidad en pliegos/hora (usar valor personalizado si existe)
      const printSpeed = parseFloat(customSpeed || machineDetails.speed || '10000');
      // Obtener el tiempo de arranque por arranque (usar valor personalizado si existe)
      const setupTimePerRun = parseFloat(customSetupTime || machineDetails.setup_time || '0.5');

      // Calcular el número total de arranques
      let totalArranques = 0;
      if (sheetCalculation && sheetCalculation.mejor_combinacion) {
        sheetCalculation.mejor_combinacion.esquemas_utilizados.forEach(esquema => {
          const arranquesPorEsquema = esquema.es_tira_retira ? 1 : 2;
          totalArranques += arranquesPorEsquema;
        });
      }

      // Calcular el tiempo total de arranque
      const totalSetupTime = totalArranques * setupTimePerRun;

      // Calcular el tiempo de impresión
      const printTimeHours = totalSheets / printSpeed;

      // Tiempo total = tiempo de arranque + tiempo de impresión
      const totalTime = totalSetupTime + printTimeHours;

      // Asegurar un mínimo de 0.5 horas
      return Math.max(0.5, totalTime);
    }

    // Si no es offset o no hay datos suficientes, usar la estimación tradicional
    // Estimación: 1 hora por cada 1000 pliegos
    const estimatedTime = Math.max(1, (totalSheets / 1000));
    return estimatedTime;
  };

  // Calcular el coste fijo de arranque (CFA)
  const calculateCFACost = () => {
    if (!machineDetails) return 0;

    // Usar valores personalizados si existen
    const cfaPercentage = parseFloat(customCFAPercentage) || machineDetails.cfa_percentage || 0;
    const hourlyCost = parseFloat(customHourlyCost) || machineDetails.hourly_cost || 0;

    const cfaCost = (hourlyCost * cfaPercentage / 100);
    return cfaCost;
  };

  // Calcular el coste por tiempo de uso
  const calculateUsageCost = () => {
    if (!machineDetails) return 0;

    // Usar valores personalizados si existen
    const hourlyCost = parseFloat(customHourlyCost) || machineDetails.hourly_cost || 0;
    const printTime = calculatePrintTime();

    const usageCost = hourlyCost * printTime;
    return usageCost;
  };

  // Calcular el coste total de máquina (CFA + coste por tiempo de uso)
  const calculateMachineCost = () => {
    if (!machineDetails) return 0;

    const cfaCost = calculateCFACost();
    const usageCost = calculateUsageCost();
    return (cfaCost + usageCost).toFixed(2);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        Información de Máquina
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : machineDetails ? (
          <Grid container spacing={3}>
            {/* Información general de la máquina */}
            <Grid item xs={12}>
              <Paper elevation={1} sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {machineDetails.name}
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2">
                      <strong>Fabricante:</strong> {machineDetails.manufacturer}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Modelo:</strong> {machineDetails.model}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Tipo:</strong> {machineDetails.type}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Estado:</strong> {machineDetails.status}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ mr: 1 }}>
                        <strong>Coste por hora:</strong>
                      </Typography>
                      {isEditingHourlyCost ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <TextField
                            value={customHourlyCost}
                            onChange={handleHourlyCostChange}
                            variant="outlined"
                            size="small"
                            autoFocus
                            sx={{ width: '100px' }}
                            InputProps={{
                              endAdornment: <InputAdornment position="end">€/h</InputAdornment>,
                            }}
                          />
                          <Button
                            variant="contained"
                            color="primary"
                            size="small"
                            onClick={handleSaveHourlyCostClick}
                            sx={{ ml: 1, minWidth: '80px' }}
                          >
                            Guardar
                          </Button>
                        </Box>
                      ) : (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body2">
                            {customHourlyCost || machineDetails.hourly_cost?.toFixed(2) || '0.00'} €/h
                          </Typography>
                          <Tooltip title="Editar coste por hora">
                            <IconButton
                              size="small"
                              onClick={() => setIsEditingHourlyCost(true)}
                              sx={{ ml: 1 }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      )}
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ mr: 1 }}>
                        <strong>Velocidad:</strong>
                      </Typography>
                      {isEditingSpeed ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <TextField
                            value={customSpeed}
                            onChange={handleSpeedChange}
                            variant="outlined"
                            size="small"
                            autoFocus
                            sx={{ width: '100px' }}
                            InputProps={{
                              endAdornment: <InputAdornment position="end">
                                {machineDetails.type === 'Digital' ? 'A4/min' : 'p/h'}
                              </InputAdornment>,
                            }}
                          />
                          <Button
                            variant="contained"
                            color="primary"
                            size="small"
                            onClick={handleSaveSpeedClick}
                            sx={{ ml: 1, minWidth: '80px' }}
                          >
                            Guardar
                          </Button>
                        </Box>
                      ) : (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body2">
                            {customSpeed || machineDetails.speed || (machineDetails.type === 'Digital' ? 'N/A' : '10000')}
                            {machineDetails.type === 'Digital' ? ' A4/min' : ' pliegos/hora'}
                          </Typography>
                          <Tooltip title="Editar velocidad">
                            <IconButton
                              size="small"
                              onClick={() => setIsEditingSpeed(true)}
                              sx={{ ml: 1 }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      )}
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2" sx={{ mr: 1 }}>
                        <strong>CFA (%):</strong>
                      </Typography>
                      {isEditingCFAPercentage ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <TextField
                            value={customCFAPercentage}
                            onChange={handleCFAPercentageChange}
                            variant="outlined"
                            size="small"
                            autoFocus
                            sx={{ width: '100px' }}
                            InputProps={{
                              endAdornment: <InputAdornment position="end">%</InputAdornment>,
                            }}
                          />
                          <Button
                            variant="contained"
                            color="primary"
                            size="small"
                            onClick={handleSaveCFAPercentageClick}
                            sx={{ ml: 1, minWidth: '80px' }}
                          >
                            Guardar
                          </Button>
                        </Box>
                      ) : (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body2">
                            {customCFAPercentage || machineDetails.cfa_percentage?.toFixed(1) || '0.0'}%
                          </Typography>
                          <Tooltip title="Editar CFA">
                            <IconButton
                              size="small"
                              onClick={() => setIsEditingCFAPercentage(true)}
                              sx={{ ml: 1 }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>



            {machineDetails.notes && (
              <Grid item xs={12}>
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Typography variant="body2">
                    <strong>Notas:</strong> {machineDetails.notes}
                  </Typography>
                </Paper>
              </Grid>
            )}

            {/* Cálculo para el trabajo actual */}
            {budget && totalSheets > 0 && (
              <Grid item xs={12}>
                <Paper elevation={1} sx={{ p: 2, bgcolor: '#f0f8ff' }}>
                  <Typography variant="h6" gutterBottom>
                    Cálculo para este trabajo
                  </Typography>
                  <Grid container spacing={2}>
                    {/* Columna izquierda */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2">
                        <strong>Total de pliegos:</strong> {totalSheets.toLocaleString()}
                      </Typography>

                      {/* Información específica para máquinas offset */}
                      {machineDetails.type === 'Offset' && (
                        <>
                          <Typography variant="body2">
                            <strong>Velocidad de máquina:</strong> {customSpeed || machineDetails.speed || '10000'} pliegos/hora
                          </Typography>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, mt: 1 }}>
                            <Typography variant="body2" sx={{ mr: 1 }}>
                              <strong>Tiempo de arranque:</strong>
                            </Typography>
                            {isEditingSetupTime ? (
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <TextField
                                  value={customSetupTime}
                                  onChange={handleSetupTimeChange}
                                  variant="outlined"
                                  size="small"
                                  autoFocus
                                  sx={{ width: '100px' }}
                                  InputProps={{
                                    endAdornment: <InputAdornment position="end">h</InputAdornment>,
                                  }}
                                />
                                <Button
                                  variant="contained"
                                  color="primary"
                                  size="small"
                                  onClick={handleSaveSetupTimeClick}
                                  sx={{ ml: 1, minWidth: '80px' }}
                                >
                                  Guardar
                                </Button>
                              </Box>
                            ) : (
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography variant="body2">
                                  {customSetupTime || machineDetails.setup_time || '0.5'} horas/arranque
                                </Typography>
                                <Tooltip title="Editar tiempo de arranque">
                                  <IconButton
                                    size="small"
                                    onClick={() => setIsEditingSetupTime(true)}
                                    sx={{ ml: 1 }}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            )}
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Typography variant="body2" sx={{ mr: 1 }}>
                              <strong>Maculatura:</strong>
                            </Typography>
                            {isEditingMaculatura ? (
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <TextField
                                  value={customMaculatura}
                                  onChange={handleMaculaturaChange}
                                  variant="outlined"
                                  size="small"
                                  autoFocus
                                  sx={{ width: '100px' }}
                                  InputProps={{
                                    endAdornment: <InputAdornment position="end">p/a</InputAdornment>,
                                  }}
                                />
                                <Button
                                  variant="contained"
                                  color="primary"
                                  size="small"
                                  onClick={handleSaveMaculaturaClick}
                                  sx={{ ml: 1, minWidth: '80px' }}
                                >
                                  Guardar
                                </Button>
                              </Box>
                            ) : (
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography variant="body2">
                                  {(() => {
                                    // Calcular maculatura (pliegos adicionales para arranque y procesos)
                                    // Usar el valor personalizado o por defecto (100 pliegos por arranque)
                                    const pliegosPorArranque = parseInt(customMaculatura) || 100;
                                    let totalArranques = 0;

                                    if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                      sheetCalculation.mejor_combinacion.esquemas_utilizados.forEach(esquema => {
                                        const arranquesPorEsquema = esquema.es_tira_retira ? 1 : 2;
                                        totalArranques += arranquesPorEsquema;
                                      });
                                    }

                                    const totalMaculatura = totalArranques * pliegosPorArranque;
                                    return `${totalMaculatura} pliegos (${pliegosPorArranque} por arranque)`;
                                  })()}
                                </Typography>
                                <Tooltip title="Editar maculatura por arranque">
                                  <IconButton
                                    size="small"
                                    onClick={() => setIsEditingMaculatura(true)}
                                    sx={{ ml: 1 }}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            )}
                          </Box>
                        </>
                      )}

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, mt: 1 }}>
                        <Typography variant="body2" sx={{ mr: 1 }}>
                          <strong>Tiempo estimado:</strong>
                        </Typography>
                        {isEditingTime ? (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <TextField
                              value={customPrintTime}
                              onChange={handlePrintTimeChange}
                              variant="outlined"
                              size="small"
                              autoFocus
                              sx={{ width: '100px' }}
                              InputProps={{
                                endAdornment: <InputAdornment position="end">h</InputAdornment>,
                              }}
                            />
                            <Button
                              variant="contained"
                              color="primary"
                              size="small"
                              onClick={handleSaveTimeClick}
                              sx={{ ml: 1, minWidth: '80px' }}
                            >
                              Guardar
                            </Button>
                          </Box>
                        ) : (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body2">
                              {customPrintTime || calculatePrintTime().toFixed(2)} horas
                            </Typography>
                            <Tooltip title="Editar tiempo estimado">
                              <IconButton
                                size="small"
                                onClick={() => setIsEditingTime(true)}
                                sx={{ ml: 1 }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        )}
                      </Box>

                      {/* Desglose del tiempo para máquinas offset */}
                      {machineDetails.type === 'Offset' && (
                        <Box sx={{ ml: 2, mb: 1, borderLeft: '2px solid #e3f2fd', pl: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            <strong>Desglose:</strong>
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            - Tiempo de arranque: {(() => {
                              const setupTimePerRun = parseFloat(customSetupTime || machineDetails.setup_time || '0.5');
                              let totalArranques = 0;

                              if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                sheetCalculation.mejor_combinacion.esquemas_utilizados.forEach(esquema => {
                                  const arranquesPorEsquema = esquema.es_tira_retira ? 1 : 2;
                                  totalArranques += arranquesPorEsquema;
                                });
                              }

                              const totalSetupTime = totalArranques * setupTimePerRun;
                              return `${totalSetupTime.toFixed(2)} horas`;
                            })()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            - Tiempo de impresión: {(() => {
                              const printSpeed = parseFloat(customSpeed || machineDetails.speed || '10000');
                              const printTimeHours = totalSheets / printSpeed;
                              return `${printTimeHours.toFixed(2)} horas`;
                            })()}
                          </Typography>
                        </Box>
                      )}

                      <Typography variant="body2">
                        <strong>Coste por hora:</strong> {machineDetails.hourly_cost?.toFixed(2) || '0.00'} €/h
                      </Typography>
                      <Typography variant="body2">
                        <strong>CFA (%):</strong> {machineDetails.cfa_percentage?.toFixed(1) || '0.0'}%
                      </Typography>
                    </Grid>

                    {/* Columna derecha */}
                    <Grid item xs={12} md={6}>
                      {/* Información de pliegos */}
                      <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'primary.main', p: 1, mb: 1, bgcolor: '#e3f2fd', borderRadius: 1 }}>
                        <strong>Pliegos impresos:</strong> {totalSheets ? totalSheets.toLocaleString() : '0'}
                      </Typography>

                      {/* Mostrar número de arranques solo para máquinas offset */}
                      {machineDetails.type === 'Offset' && sheetCalculation && sheetCalculation.mejor_combinacion && (
                        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'primary.main', p: 1, mb: 1, bgcolor: '#e3f2fd', borderRadius: 1 }}>
                          <strong>Número de arranques (caras):</strong> {(() => {
                            // Contar el número total de caras (arranques)
                            let totalCaras = 0;
                            sheetCalculation.mejor_combinacion.esquemas_utilizados.forEach(esquema => {
                              // Si es tira y retira, solo cuenta como 1 arranque (se usa la misma plancha)
                              // Si es solo tira, cuenta como 2 arranques (una para cada cara)
                              const carasPorEsquema = esquema.es_tira_retira ? 1 : 2;
                              totalCaras += carasPorEsquema;
                            });
                            return totalCaras;
                          })()}
                        </Typography>
                      )}

                      {/* Costes de máquina */}
                      <Typography variant="body2">
                        <strong>Coste CFA:</strong> {calculateCFACost().toFixed(2)} €
                      </Typography>
                      <Typography variant="body2">
                        <strong>Coste por tiempo de uso:</strong> {calculateUsageCost().toFixed(2)} €
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'primary.main', p: 1, bgcolor: '#e3f2fd', borderRadius: 1 }}>
                        <strong>Coste total de máquina:</strong> {calculateMachineCost()} €
                      </Typography>
                    </Grid>
                  </Grid>

                  {/* Coste total */}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main', p: 2, bgcolor: '#e3f2fd', borderRadius: 1, display: 'flex', justifyContent: 'space-between' }}>
                      <span>COSTE TOTAL DE MÁQUINA:</span>
                      <span>
                        {(() => {
                          // Coste de máquina (solo incluye el coste de la máquina, no el papel)
                          const machineCost = parseFloat(calculateMachineCost());

                          // Devolver solo el coste de la máquina
                          return machineCost.toFixed(2) + ' €';
                        })()}
                      </span>
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            )}
          </Grid>
        ) : (
          <Typography variant="body1" color="text.secondary" align="center">
            No se ha podido cargar la información de la máquina
          </Typography>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

MachineInfoModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  machine: PropTypes.object,
  totalSheets: PropTypes.number,
  budget: PropTypes.object,
  onUpdatePrintTime: PropTypes.func,
  sheetCalculation: PropTypes.object,
  selectedProcesses: PropTypes.array
};

export default MachineInfoModal;
