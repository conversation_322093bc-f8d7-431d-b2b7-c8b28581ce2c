# Documentación del Frontend - Aplicación de Gestión de Imprenta

## Índice
1. [Introducción](#introducción)
2. [Estructura del Proyecto](#estructura-del-proyecto)
3. [Componentes Principales](#componentes-principales)
   - [App](#app)
   - [BudgetForm](#budgetform)
   - [PaperCalculator](#papercalculator)
   - [PaperCatalog](#papercatalog)
   - [MachineCatalog](#machinecatalog)
   - [ClientCatalog](#clientcatalog)
   - [Planning](#planning)
4. [Flujos de Trabajo](#flujos-de-trabajo)
5. [Tecnologías Utilizadas](#tecnologías-utilizadas)

## Introducción

La aplicación de Gestión de Imprenta es una solución integral para la administración de presupuestos, cálculos de materiales y gestión de catálogos en una imprenta. Permite a los usuarios crear presupuestos detallados, calcular la cantidad de pliegos necesarios para trabajos de impresión, y administrar catálogos de papel, máquinas y clientes.

## Estructura del Proyecto

El frontend está organizado en una estructura de componentes React, utilizando Material-UI como biblioteca de componentes de interfaz de usuario. La estructura principal es:

```
frontend/
├── src/
│   ├── components/
│   │   ├── BudgetForm.jsx
│   │   ├── PaperCalculator.jsx
│   │   ├── PaperCatalog.jsx
│   │   ├── MachineCatalog.jsx
│   │   ├── ClientCatalog.jsx
│   │   └── Planning.jsx
│   ├── pages/
│   │   ├── BudgetList.jsx
│   ├── services/
│   │   └── planningService.js
│   ├── App.jsx
│   └── main.jsx
```

## Componentes Principales

### App

**Descripción**: Componente principal que gestiona la navegación y estructura general de la aplicación.

**Funcionalidades**:
- Implementa un sistema de navegación por pestañas mediante un estado interno.
- Proporciona una barra de navegación superior con accesos directos a las principales secciones.
- Incluye un menú desplegable para acceder a los catálogos de configuración.
- Gestiona la visualización condicional de los diferentes componentes según la pestaña seleccionada.

**Estructura de Navegación**:
- **Presupuestos**: Acceso directo al formulario de presupuestos (BudgetForm).
- **Lista de Presupuestos**: Acceso directo a la lista de presupuestos (BudgetList).
- **Calculadora**: Acceso a la calculadora de pliegos (PaperCalculator).
- **Planificación**: Acceso directo al calendario de planificación de trabajos (Planning).
- **Configuración**: Menú desplegable que incluye:
  - Catálogo de Papel
  - Catálogo de Máquinas
  - Catálogo de Clientes
  - Catálogo de Procesos
  - Calculadora de Pliegos
  - Ver Logs
  - Planificación

### BudgetForm

**Descripción**: Componente para la creación y gestión de presupuestos de trabajos de impresión.

**Funcionalidades**:
- Generación automática de números de orden de trabajo (OT) con formato OT-XXXXX.
- Selección de cliente desde el catálogo de clientes activos.
- Selección de tipo de trabajo desde una lista predefinida.
- Configuración de detalles del producto: número de ejemplares, tamaño de página, número de páginas.
- Selección de papel desde el catálogo de papeles en stock.
- Selección de máquina de impresión desde el catálogo de máquinas activas.
- Cálculo automático de pliegos necesarios para el trabajo.
- Visualización de resultados detallados del cálculo en un modal.
- Visualización de información detallada del cliente seleccionado en un modal.

**Modales Implementados**:
1. **Modal de Cálculo de Pliegos**:
   - Muestra un resumen del producto (páginas y tamaño).
   - Presenta los detalles del papel seleccionado.
   - Indica el total de pliegos y planchas necesarios.
   - Muestra los esquemas utilizados y todas las posibles configuraciones.

2. **Modal de Información del Cliente**:
   - Muestra datos generales de la empresa (nombre, ID, códigos, estado).
   - Presenta la dirección completa del cliente.
   - Incluye información de contacto detallada.
   - Muestra notas adicionales sobre el cliente.

**Validaciones**:
- Comprueba que todos los campos requeridos estén completos.
- Valida que el número de páginas sea al menos 1.
- Verifica que se haya seleccionado un cliente antes de mostrar su información.

### PaperCalculator

**Descripción**: Herramienta especializada para calcular la disposición óptima de páginas en pliegos de papel.

**Funcionalidades**:
- Selección de formato de página predefinido (A3, A4, A5, SRA3) o personalizado.
- Entrada del número de páginas del producto.
- Selección de papel desde el catálogo o entrada manual de dimensiones.
- Cálculo automático de la disposición óptima de páginas en pliegos.
- Visualización de resultados detallados incluyendo:
  - Número total de pliegos necesarios.
  - Número de planchas requeridas.
  - Esquemas de imposición (disposición de páginas).
  - Información sobre orientación y tira/retira.
  - Listado de todos los esquemas posibles.
- Visualización gráfica de la disposición de páginas en el pliego.

**Formatos de Página Predefinidos**:
- A5 (148 × 210 mm)
- A4 (210 × 297 mm)
- A3 (297 × 420 mm)
- SRA3 (320 × 450 mm)

### PaperCatalog

**Descripción**: Componente para la gestión del catálogo de papeles disponibles en la imprenta.

**Funcionalidades**:
- Visualización de todos los papeles en formato de tabla.
- Filtrado para mostrar solo papeles en stock.
- Adición de nuevos papeles al catálogo.
- Edición de papeles existentes.
- Eliminación de papeles del catálogo.
- Actualización del estado de stock de los papeles.

**Atributos de Papel Gestionados**:
- Nombre
- Fabricante
- Categoría (Offset, Estucado, Reciclado, Autocopiativo, Especial)
- Gramaje (g/m²)
- Acabado (Mate, Brillo, Satin, Natural)
- Dimensiones (ancho × alto en mm)
- Dirección de fibra (Long, Short)
- Estado de stock
- Precio

### MachineCatalog

**Descripción**: Componente para la gestión del catálogo de máquinas de impresión y acabado.

**Funcionalidades**:
- Visualización de todas las máquinas en formato de tabla.
- Filtrado por tipo de máquina.
- Adición de nuevas máquinas al catálogo.
- Edición de máquinas existentes.
- Eliminación de máquinas del catálogo.
- Actualización del estado de las máquinas.

**Tipos de Máquinas Gestionados**:
- Offset
- Digital
- Plotter
- CTP
- Encuadernadora
- Guillotina
- Plegadora

**Estados de Máquina**:
- Activa
- Inactiva
- Mantenimiento

**Atributos de Máquina Gestionados**:
- Nombre
- Modelo
- Fabricante
- Tipo
- Tamaño máximo de impresión
- Velocidad
- Estado
- Notas adicionales

### ClientCatalog

**Descripción**: Componente para la gestión del catálogo de clientes de la imprenta.

**Funcionalidades**:
- Visualización de todos los clientes en formato de tabla.
- Filtrado para mostrar solo clientes activos.
- Adición de nuevos clientes al catálogo.
- Edición de clientes existentes.
- Eliminación de clientes del catálogo.
- Actualización del estado de los clientes.

**Estructura de Datos de Cliente**:
- **Información General**:
  - ID de cliente
  - Código de facturación
  - ID de pedido
  - Estado (activo/inactivo)

- **Información de Empresa**:
  - Nombre de la empresa
  - Dirección completa (país, región, ciudad, calle, código postal)

- **Información de Contacto**:
  - Nombre y apellidos
  - Cargo
  - Teléfono
  - Fax
  - Email

- **Notas Adicionales**

### Planning

**Descripción**: Componente para la planificación y programación de trabajos en las máquinas de impresión.

**Funcionalidades**:
- Visualización de un calendario con los trabajos programados.
- Creación de nuevos eventos de trabajo mediante selección de slot.
- Edición de eventos existentes mediante clic en el evento.
- Eliminación de eventos programados.
- Arrastrar y soltar eventos para cambiar su programación (drag & drop).
- Visualización de información detallada de cada trabajo al pasar el cursor.
- Asignación de trabajos a máquinas específicas.
- Visualización del estado de los trabajos mediante colores:
  - Pendiente: Naranja
  - En proceso: Azul
  - Completado: Verde
- Programación con espacio de 30 minutos entre trabajos para limpieza y organización.

**Vistas del Calendario**:
- Mes: Vista mensual completa
- Semana: Vista detallada de la semana
- Día: Vista detallada de un día
- Agenda: Lista cronológica de eventos

**Formulario de Evento**:
- Título del evento
- Selección de presupuesto asociado
- Selección de máquina
- Cantidad de pliegos
- Estado del trabajo
- Fecha y hora de inicio
- Fecha y hora de fin

## Flujos de Trabajo

### Creación de Presupuesto
1. El usuario navega a la sección "Presupuestos".
2. Completa el formulario con los detalles del trabajo.
3. Puede seleccionar un cliente existente y ver sus detalles completos mediante el botón de información.
4. Puede calcular la cantidad de pliegos necesarios utilizando el botón "Calcular Pliegos".
5. Revisa los resultados del cálculo en el modal correspondiente.
6. Guarda el presupuesto mediante el botón "Guardar".

### Cálculo de Pliegos
1. El usuario puede acceder a la calculadora desde la sección "Calculadora" o desde el formulario de presupuestos.
2. Introduce el número de páginas y selecciona el tamaño de página.
3. Selecciona el papel a utilizar o introduce manualmente las dimensiones.
4. Inicia el cálculo mediante el botón correspondiente.
5. Revisa los resultados detallados, incluyendo la visualización gráfica de la disposición.

### Gestión de Catálogos
1. El usuario accede al catálogo deseado desde el menú de configuración.
2. Puede visualizar todos los elementos existentes en formato de tabla.
3. Puede filtrar los elementos según criterios específicos.
4. Puede añadir nuevos elementos mediante el botón correspondiente.
5. Puede editar o eliminar elementos existentes mediante los iconos de acción.

### Planificación de Trabajos
1. El usuario accede a la sección "Planificación" desde la barra de navegación principal.
2. Visualiza el calendario con los trabajos programados en diferentes máquinas.
3. Puede crear un nuevo evento haciendo clic en un slot de tiempo vacío.
4. Puede editar un evento existente haciendo clic sobre él.
5. Puede arrastrar y soltar eventos para cambiar su programación.
6. Puede cambiar la vista del calendario (mes, semana, día, agenda) según sus necesidades.
7. Puede asignar trabajos a máquinas específicas y establecer su estado.

## Tecnologías Utilizadas

- **React**: Biblioteca JavaScript para la construcción de interfaces de usuario.
- **Material-UI**: Biblioteca de componentes React que implementa Material Design.
- **Fetch API**: Para realizar peticiones HTTP al backend.
- **useState/useEffect/useCallback**: Hooks de React para gestión de estado y efectos secundarios.
- **Grid System**: Sistema de rejilla de Material-UI para diseños responsivos.
- **Dialog/Modal**: Componentes para mostrar información adicional o formularios.
- **Snackbar**: Para mostrar notificaciones y mensajes de retroalimentación.
- **Tables**: Para visualización de datos en formato tabular.
- **Forms**: Para entrada y validación de datos.
- **react-big-calendar**: Biblioteca para implementar calendarios interactivos con funcionalidad de arrastrar y soltar.
- **date-fns**: Biblioteca para manipulación y formateo de fechas.
