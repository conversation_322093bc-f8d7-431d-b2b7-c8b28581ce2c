import { useState, useEffect, useMemo } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  CircularProgress,
  Alert,
  AlertTitle,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Tooltip
} from '@mui/material';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/es';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import withDragAndDrop from 'react-big-calendar/lib/addons/dragAndDrop';
import 'react-big-calendar/lib/addons/dragAndDrop/styles.css';
import { reorganizeProcesses } from '../../utils/offsetUtils';
import { buildApiUrl } from '../../config';
import { ApiInterceptor } from '../../services/simplifiedServices';
import { format } from 'date-fns';
import PropTypes from 'prop-types';

// Configurar moment para localización
moment.locale('es');
const localizer = momentLocalizer(moment);

// Crear el componente de calendario con drag & drop
const DnDCalendar = withDragAndDrop(Calendar);

// ID de la máquina Prensa B1 Offset
const OFFSET_MACHINE_ID = 'MAQ-003';

// Colores para los diferentes estados
const STATUS_COLORS = {
  'Pendiente': '#ff9800',  // Naranja
  'En Proceso': '#2196f3', // Azul
  'Completado': '#4caf50', // Verde
  'Cancelado': '#f44336'   // Rojo
};

const OffsetPressPlanner = () => {
  // Estados básicos
  const [processes, setProcesses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedProcess, setSelectedProcess] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [successMessage, setSuccessMessage] = useState(null);

  // Estados para varios filtros y vistas
  const [view, setView] = useState('week'); // 'day', 'week', 'work_week'
  const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'Pendiente', 'En Proceso', etc.

  // Cargar procesos de producción
  useEffect(() => {
    const fetchProcesses = async () => {
      try {
        setLoading(true);
        setError(null);

        const url = buildApiUrl('/production/');

        console.log('Obteniendo procesos desde:', url);

        const response = await ApiInterceptor.fetch(url);
        if (!response.ok) {
          throw new Error(`Error al obtener procesos: ${response.statusText}`);
        }

        const data = await response.json();

        // Filtrar solo procesos de impresión para la máquina offset
        const offsetProcesses = data.filter(process =>
          process.machine_id === OFFSET_MACHINE_ID &&
          process.process_type === 'Impresión'
        );

        console.log(`Encontrados ${offsetProcesses.length} procesos para la máquina offset:`, offsetProcesses);

        setProcesses(offsetProcesses);
        setSuccessMessage(`Se han cargado ${offsetProcesses.length} trabajos de la Prensa B1 Offset`);

        // Limpiar mensaje después de 3 segundos
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        console.error('Error al cargar procesos:', err);
        setError(`Error al cargar los procesos: ${err.message}`);

        // Limpiar mensaje de error después de 10 segundos
        setTimeout(() => {
          setError(null);
        }, 10000);
      } finally {
        setLoading(false);
      }
    };

    fetchProcesses();
  }, []);

  // Convertir procesos a eventos para el calendario
  const calendarEvents = useMemo(() => {
    if (!processes.length) return [];

    console.log('Convirtiendo procesos a eventos de calendario');

    return processes.map(process => {
      // Determinar color según estado
      const backgroundColor = STATUS_COLORS[process.status] || '#757575';

      return {
        id: process.process_id,
        title: `${process.ot_number} - ${process.name}`,
        start: new Date(process.start_date),
        end: new Date(process.end_date),
        allDay: false,
        resource: process,
        backgroundColor
      };
    });
  }, [processes]);

  // Filtrar eventos por estado
  const filteredEvents = useMemo(() => {
    if (filterStatus === 'all') return calendarEvents;

    return calendarEvents.filter(event =>
      event.resource.status === filterStatus
    );
  }, [calendarEvents, filterStatus]);

  // Función para verificar si un evento se solapa con otros
  const checkForOverlap = (eventId, start, end) => {
    // Filtrar eventos de la misma máquina, excluyendo el evento actual
    const machineEvents = filteredEvents.filter(event =>
      event.id !== eventId &&
      event.resource.machine_id === OFFSET_MACHINE_ID
    );

    // Verificar solapamientos
    for (const event of machineEvents) {
      // Si el inicio del nuevo evento está entre el inicio y fin de un evento existente
      if ((start >= event.start && start < event.end) ||
          // O si el fin del nuevo evento está entre el inicio y fin de un evento existente
          (end > event.start && end <= event.end) ||
          // O si el nuevo evento engloba completamente a un evento existente
          (start <= event.start && end >= event.end)) {
        return true; // Hay solapamiento
      }
    }

    return false; // No hay solapamiento
  };

  // Función para manejar el cambio de fecha/vista en el calendario
  const handleNavigate = (date) => {
    console.log('Navegando a fecha:', date);
    setSelectedDate(date);
  };

  // Función para manejar el cambio de vista en el calendario
  const handleViewChange = (newView) => {
    console.log('Cambiando a vista:', newView);
    setView(newView);
  };

  // Función para manejar el clic en un evento (abrir detalles)
  const handleSelectEvent = (event) => {
    console.log('Evento seleccionado:', event);
    setSelectedProcess(event.resource);
    setIsEditModalOpen(true);
  };

  // Función para guardar cambios en un proceso
  const handleSaveProcess = async () => {
    try {
      setLoading(true);

      // Preparar datos para actualizar
      const updateData = {
        status: selectedProcess.status,
        start_date: selectedProcess.start_date,
        end_date: selectedProcess.end_date,
        estimated_hours: selectedProcess.estimated_hours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${selectedProcess.process_id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === selectedProcess.process_id ? { ...p, ...updateData } : p
        )
      );

      setSuccessMessage(`Proceso ${selectedProcess.name} actualizado correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);

      // Cerrar modal
      setIsEditModalOpen(false);
      setSelectedProcess(null);
    } catch (err) {
      console.error('Error al guardar proceso:', err);
      setError(`Error al guardar los cambios: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para cambiar el estado de un proceso
  const handleStatusChange = (e) => {
    setSelectedProcess({
      ...selectedProcess,
      status: e.target.value
    });
  };

  // Función para cambiar duración estimada
  const handleDurationChange = (e) => {
    const hours = parseFloat(e.target.value);
    if (isNaN(hours) || hours <= 0) return;

    // Actualizar horas estimadas
    setSelectedProcess({
      ...selectedProcess,
      estimated_hours: hours,

      // También actualizar fecha de fin basada en nueva duración
      end_date: (() => {
        const start = new Date(selectedProcess.start_date);
        const end = new Date(start);
        end.setTime(start.getTime() + (hours * 60 * 60 * 1000));
        return end.toISOString();
      })()
    });
  };

  // Función para manejar el movimiento de un evento
  const handleEventDrop = async ({ event, start, end }) => {
    try {
      console.log('Evento movido:', event);
      console.log('Nueva fecha de inicio:', start);
      console.log('Nueva fecha de fin:', end);

      // Verificar si el proceso está completado
      if (event.resource.status === 'Completado') {
        setError('No se pueden mover procesos completados');
        return;
      }

      // Verificar que los procesos "En Proceso" no se coloquen antes de la fecha actual
      if (event.resource.status === 'En Proceso') {
        const now = new Date();
        if (start < now) {
          setError('No se pueden programar procesos "En Proceso" antes de la fecha y hora actual');
          return;
        }
      }

      // Verificar solapamientos
      if (checkForOverlap(event.id, start, end)) {
        setError('No se puede mover el evento a esta posición porque se solapa con otro proceso');
        return;
      }

      setLoading(true);

      // Calcular la duración en horas
      const durationMs = end - start;
      const durationHours = durationMs / (1000 * 60 * 60);

      // Preparar datos para actualizar
      const updateData = {
        start_date: start.toISOString(),
        end_date: end.toISOString(),
        estimated_hours: durationHours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${event.id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === event.id ? {
            ...p,
            start_date: start.toISOString(),
            end_date: end.toISOString(),
            estimated_hours: durationHours
          } : p
        )
      );

      setSuccessMessage(`Proceso ${event.title} actualizado correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      console.error('Error al mover evento:', err);
      setError(`Error al actualizar el proceso: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para manejar el redimensionamiento de un evento
  const handleEventResize = async ({ event, start, end }) => {
    try {
      console.log('Evento redimensionado:', event);
      console.log('Nueva fecha de inicio:', start);
      console.log('Nueva fecha de fin:', end);

      // Verificar si el proceso está completado
      if (event.resource.status === 'Completado') {
        setError('No se pueden modificar procesos completados');
        return;
      }

      // Verificar que los procesos "En Proceso" no se redimensionen antes de la fecha actual
      if (event.resource.status === 'En Proceso') {
        const now = new Date();
        if (start < now) {
          setError('No se pueden redimensionar procesos "En Proceso" antes de la fecha y hora actual');
          return;
        }
      }

      // Verificar solapamientos
      if (checkForOverlap(event.id, start, end)) {
        setError('No se puede redimensionar el evento a esta posición porque se solapa con otro proceso');
        return;
      }

      setLoading(true);

      // Calcular la duración en horas
      const durationMs = end - start;
      const durationHours = durationMs / (1000 * 60 * 60);

      // Preparar datos para actualizar
      const updateData = {
        start_date: start.toISOString(),
        end_date: end.toISOString(),
        estimated_hours: durationHours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${event.id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === event.id ? {
            ...p,
            start_date: start.toISOString(),
            end_date: end.toISOString(),
            estimated_hours: durationHours
          } : p
        )
      );

      setSuccessMessage(`Duración de ${event.title} actualizada correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      console.error('Error al redimensionar evento:', err);
      setError(`Error al actualizar la duración: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para manejar la reorganización automática de procesos
  const handleReorganizeProcesses = async () => {
    try {
      setLoading(true);

      // Limpiar mensajes anteriores
      setError(null);
      setSuccessMessage(null);

      // Obtener la hora actual
      const now = new Date();

      // Reorganizar procesos
      const reorganized = await reorganizeProcesses(processes, OFFSET_MACHINE_ID, now);

      if (reorganized.length === 0) {
        setSuccessMessage('No hay procesos pendientes o en proceso para mover');
        setLoading(false);

        // Limpiar mensaje después de 5 segundos
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);

        return;
      }

      console.log(`Reorganizando ${reorganized.length} procesos desde ${format(now, 'yyyy-MM-dd HH:mm')}`);

      // Actualizar cada proceso en el backend
      const updatePromises = reorganized.map(async (process) => {
        // Solo actualizar si las fechas han cambiado
        const originalProcess = processes.find(p => p.process_id === process.process_id);
        if (!originalProcess) {
          console.warn(`No se encontró el proceso original con ID ${process.process_id}`);
          return process; // Devolver el proceso sin cambios
        }

        // Verificar si las fechas han cambiado
        if (originalProcess.start_date === process.start_date &&
            originalProcess.end_date === process.end_date) {
          console.log(`El proceso ${process.process_id} no ha cambiado, no se actualiza`);
          return process; // No hay cambios, devolver el proceso original
        }

        // Preparar solo los datos que necesitamos actualizar
        const updateData = {
          start_date: process.start_date,
          end_date: process.end_date,
          estimated_hours: process.estimated_hours
        };

        // Asegurarse de que el ID sea correcto
        const processId = process.process_id;
        console.log(`Actualizando proceso con ID: ${processId}`);

        const url = buildApiUrl(`/production/${processId}`);

        try {
          const response = await ApiInterceptor.fetch(url, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
          });

          if (!response.ok) {
            throw new Error(`Error al actualizar proceso ${processId}: ${response.statusText}`);
          }

          return await response.json();
        } catch (error) {
          console.error(`Error al actualizar proceso ${processId}:`, error);
          throw error;
        }
      });

      try {
        // Esperar a que todas las actualizaciones se completen
        const updatedProcesses = await Promise.all(updatePromises);

        // Actualizar la lista local de procesos
        setProcesses(prevProcesses => {
          const newProcesses = [...prevProcesses];

          // Actualizar cada proceso modificado
          updatedProcesses.forEach(updatedProcess => {
            if (!updatedProcess) return; // Ignorar procesos nulos

            const index = newProcesses.findIndex(p => p.process_id === updatedProcess.process_id);
            if (index !== -1) {
              newProcesses[index] = updatedProcess;
            }
          });

          return newProcesses;
        });

        setSuccessMessage(`Se han movido ${reorganized.length} procesos al siguiente día laborable`);

        // Limpiar mensaje después de 5 segundos
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);
      } catch (error) {
        console.error('Error al procesar las actualizaciones:', error);
        setError(`Error al reorganizar procesos: ${error.message}`);

        // Limpiar mensaje de error después de 10 segundos
        setTimeout(() => {
          setError(null);
        }, 10000);
      }
    } catch (err) {
      console.error('Error al reorganizar procesos:', err);
      setError(`Error al reorganizar procesos: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Personalizar la apariencia de los eventos en el calendario
  const eventPropGetter = (event) => {
    return {
      style: {
        backgroundColor: event.backgroundColor,
        borderLeft: `4px solid ${event.backgroundColor}`,
        color: 'white'
      }
    };
  };

  // Componente personalizado para mostrar eventos en el calendario
  const CustomEvent = ({ event }) => {
    return (
      <Box sx={{ height: '100%', overflow: 'hidden', p: 0.5 }}>
        <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
          {event.resource && event.resource.ot_number ? `OT: ${event.resource.ot_number}` : 'Sin OT'}
        </Typography>
        <Typography variant="caption" sx={{ display: 'block' }}>
          {event.resource && event.resource.name ? event.resource.name : 'Sin nombre'}
        </Typography>
      </Box>
    );
  };

  // Validación de propiedades para CustomEvent
  CustomEvent.propTypes = {
    event: PropTypes.shape({
      resource: PropTypes.shape({
        ot_number: PropTypes.string,
        name: PropTypes.string
      })
    }).isRequired
  };

  // Renderizar pantalla de carga
  if (loading && processes.length === 0) {
    return (
      <Container>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh', flexDirection: 'column' }}>
          <CircularProgress />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Cargando planificación de Prensa B1 - Offset...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Planificación Prensa B1 - Offset
          </Typography>

          <Box>
            <FormControl size="small" sx={{ minWidth: 150, mr: 2 }}>
              <InputLabel>Estado</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Estado"
              >
                <MenuItem value="all">Todos</MenuItem>
                <MenuItem value="Pendiente">Pendiente</MenuItem>
                <MenuItem value="En Proceso">En Proceso</MenuItem>
                <MenuItem value="Completado">Completado</MenuItem>
                <MenuItem value="Cancelado">Cancelado</MenuItem>
              </Select>
            </FormControl>

            <Tooltip title="Mueve TODOS los trabajos pendientes y en proceso al siguiente día laborable, incluso los que ya han comenzado">
              <span>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleReorganizeProcesses}
                  disabled={loading}
                  sx={{ mr: 1 }}
                >
                  Reorganizar
                </Button>
              </span>
            </Tooltip>
          </Box>
        </Box>

        {/* Calendario */}
        <Box sx={{ height: '70vh', mb: 3 }}>
          <DnDCalendar
            localizer={localizer}
            events={filteredEvents}
            startAccessor="start"
            endAccessor="end"
            defaultView={view}
            views={['day', 'week', 'work_week']}
            step={30}
            timeslots={2}
            onNavigate={handleNavigate}
            onView={handleViewChange}
            onSelectEvent={handleSelectEvent}
            eventPropGetter={eventPropGetter}
            components={{
              event: CustomEvent
            }}
            date={selectedDate}
            min={new Date(new Date().setHours(7, 0, 0))}
            max={new Date(new Date().setHours(20, 0, 0))}
            messages={{
              day: 'Día',
              week: 'Semana',
              work_week: 'Semana laboral',
              month: 'Mes',
              previous: 'Anterior',
              next: 'Siguiente',
              today: 'Hoy',
              agenda: 'Agenda',
              showMore: total => `+ Ver más (${total})`
            }}
            resizable
            selectable
            onEventDrop={handleEventDrop}
            onEventResize={handleEventResize}
            popup
            tooltipAccessor={null}
          />
        </Box>

        {/* Mensajes de error y éxito en la parte inferior */}
        <Box sx={{ position: 'fixed', bottom: 20, left: 0, right: 0, zIndex: 1000, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2, width: '80%', maxWidth: '800px' }} onClose={() => setError(null)}>
              <AlertTitle>Error</AlertTitle>
              {error}
            </Alert>
          )}

          {successMessage && (
            <Alert severity="success" sx={{ mb: 2, width: '80%', maxWidth: '800px' }} onClose={() => setSuccessMessage(null)}>
              <AlertTitle>Éxito</AlertTitle>
              {successMessage}
            </Alert>
          )}
        </Box>

        {/* Leyenda */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
          <Typography variant="subtitle2" sx={{ width: '100%', mb: 1 }}>Leyenda:</Typography>

          {/* Estados */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            {Object.entries(STATUS_COLORS).map(([status, color]) => (
              <Box
                key={status}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  backgroundColor: color,
                  color: 'white',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '0.75rem'
                }}
              >
                {status}
              </Box>
            ))}
          </Box>
        </Box>
      </Paper>

      {/* Modal de edición */}
      {selectedProcess && (
        <Dialog
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Detalles del Trabajo: {selectedProcess.name}
          </DialogTitle>

          <DialogContent dividers>
            <Stack spacing={2}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="OT"
                  value={selectedProcess.ot_number || ''}
                  fullWidth
                  InputProps={{ readOnly: true }}
                />

                <FormControl fullWidth>
                  <InputLabel>Estado</InputLabel>
                  <Select
                    value={selectedProcess.status || 'Pendiente'}
                    onChange={handleStatusChange}
                    label="Estado"
                  >
                    <MenuItem value="Pendiente">Pendiente</MenuItem>
                    <MenuItem value="En Proceso">En Proceso</MenuItem>
                    <MenuItem value="Completado">Completado</MenuItem>
                    <MenuItem value="Cancelado">Cancelado</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <TextField
                label="Descripción"
                value={selectedProcess.description || ''}
                fullWidth
                multiline
                rows={2}
                InputProps={{ readOnly: true }}
              />

              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="Cantidad"
                  value={selectedProcess.quantity || ''}
                  fullWidth
                  InputProps={{ readOnly: true }}
                />

                <TextField
                  label="Duración estimada (horas)"
                  value={selectedProcess.estimated_hours || ''}
                  fullWidth
                  onChange={handleDurationChange}
                  type="number"
                  inputProps={{ min: 0.5, step: 0.5 }}
                />
              </Box>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="Fecha y hora de inicio"
                  type="datetime-local"
                  value={format(new Date(selectedProcess.start_date), "yyyy-MM-dd'T'HH:mm")}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  onChange={(e) => {
                    const newStart = new Date(e.target.value);
                    const duration = selectedProcess.estimated_hours;

                    // Calcular nueva fecha de fin
                    const newEnd = new Date(newStart);
                    newEnd.setTime(newStart.getTime() + (duration * 60 * 60 * 1000));

                    setSelectedProcess({
                      ...selectedProcess,
                      start_date: newStart.toISOString(),
                      end_date: newEnd.toISOString()
                    });
                  }}
                />

                <TextField
                  label="Fecha y hora de fin"
                  type="datetime-local"
                  value={format(new Date(selectedProcess.end_date), "yyyy-MM-dd'T'HH:mm")}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  InputProps={{ readOnly: true }}
                />
              </Box>

              {selectedProcess.dependencies && selectedProcess.dependencies.length > 0 && (
                <Box>
                  <Typography variant="subtitle2">Dependencias:</Typography>
                  <Paper variant="outlined" sx={{ p: 1 }}>
                    {selectedProcess.dependencies.map(depId => (
                      <Typography key={depId} variant="body2">
                        • {depId}
                      </Typography>
                    ))}
                  </Paper>
                </Box>
              )}
            </Stack>
          </DialogContent>

          <DialogActions>
            <Button onClick={() => setIsEditModalOpen(false)}>
              Cancelar
            </Button>
            <Button
              onClick={handleSaveProcess}
              variant="contained"
              color="primary"
              disabled={loading}
            >
              {loading ? 'Guardando...' : 'Guardar Cambios'}
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Container>
  );
};

export default OffsetPressPlanner;
