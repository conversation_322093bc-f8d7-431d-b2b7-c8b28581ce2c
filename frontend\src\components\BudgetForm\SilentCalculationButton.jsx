import { useState } from 'react';
import PropTypes from 'prop-types';
import { IconButton, Tooltip, CircularProgress } from '@mui/material';
import CalculateIcon from '@mui/icons-material/Calculate';
import { useSilentSheetCalculation } from '../../services/silentSheetCalculationService';

/**
 * Componente botón para cálculo silencioso de pliegos
 * Ejemplo de uso del servicio externo silentSheetCalculationService
 */
const SilentCalculationButton = ({
  partIndex,
  budgetParts,
  budget,
  budgetId,
  buildApiUrl,
  showSnackbar,
  setBudgetParts,
  setBudget,
  setCurrentCalculatedPart,
  setCalculationResults,
  disabled = false,
  size = 'small',
  color = 'primary',
  config = {
    showModal: false,
    showSuccessMessage: true,
    recalculateShipping: true
  }
}) => {
  const [calculating, setCalculating] = useState(false);

  // Hook personalizado para cálculo silencioso
  const silentCalculation = useSilentSheetCalculation({
    budgetParts,
    budget,
    budgetId,
    buildApiUrl,
    showSnackbar,
    setBudgetParts,
    setBudget,
    setCalculatingSheets: setCalculating,
    setCurrentCalculatedPart,
    setCalculationResults
  });

  // Validar que la parte tenga los datos necesarios
  const part = budgetParts[partIndex];
  const isDisabled = disabled || 
                    calculating || 
                    !part || 
                    !part.machine || 
                    !part.paper || 
                    !part.pageCount || 
                    !budget.copies || 
                    parseInt(budget.copies) <= 0;

  const handleClick = async (e) => {
    e.stopPropagation();
    
    if (isDisabled) return;

    try {
      await silentCalculation(partIndex, config);
    } catch (error) {
      console.error('Error en cálculo silencioso:', error);
      showSnackbar('Error al calcular pliegos: ' + error.message, 'error');
    }
  };

  return (
    <Tooltip title={calculating ? "Calculando..." : "Calcular pliegos"}>
      <span>
        <IconButton
          color={color}
          onClick={handleClick}
          size={size}
          disabled={isDisabled}
          sx={{ mr: 1 }}
        >
          {calculating ? (
            <CircularProgress size={20} color={color} />
          ) : (
            <CalculateIcon />
          )}
        </IconButton>
      </span>
    </Tooltip>
  );
};

SilentCalculationButton.propTypes = {
  partIndex: PropTypes.number.isRequired,
  budgetParts: PropTypes.array.isRequired,
  budget: PropTypes.object.isRequired,
  budgetId: PropTypes.string,
  buildApiUrl: PropTypes.func.isRequired,
  showSnackbar: PropTypes.func.isRequired,
  setBudgetParts: PropTypes.func.isRequired,
  setBudget: PropTypes.func.isRequired,
  setCurrentCalculatedPart: PropTypes.func.isRequired,
  setCalculationResults: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  color: PropTypes.oneOf(['primary', 'secondary', 'error', 'info', 'success', 'warning']),
  config: PropTypes.shape({
    showModal: PropTypes.bool,
    showSuccessMessage: PropTypes.bool,
    recalculateShipping: PropTypes.bool
  })
};

export default SilentCalculationButton;
