import { useState, useEffect, useCallback } from 'react';
import ProductFinishingSelector from '../ProductFinishingSelector';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  Snackbar,
  Switch,
  FormControlLabel,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { buildApiUrl } from '../../config';
import { ApiInterceptor, LogService } from '../../services/productCatalogServices';

// Tipos de encuadernación
const ASSEMBLY_ORDERS = [
  { value: "Gathering", label: "<PERSON>zado (Libros)" },
  { value: "Collecting", label: "Embu<PERSON><PERSON> (Revistas)" },
  { value: "None", label: "Sin encuadernación (Tarjetas, Carteles)" }
];

// Tipos de productos
const PRODUCT_TYPES = [
  { value: "Folleto", label: "Folleto" },
  { value: "Revista", label: "Revista" },
  { value: "Libro", label: "Libro" },
  { value: "Catálogo", label: "Catálogo" },
  { value: "Tarjeta", label: "Tarjeta" },
  { value: "Cartel", label: "Cartel" },
  { value: "Flyer", label: "Flyer" },
  { value: "Otro", label: "Otro" }
];

const ProductCatalog = () => {
  // Estado para los productos
  const [products, setProducts] = useState([]);
  const [showOnlyActive, setShowOnlyActive] = useState(true);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Estado para el formulario de producto
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentProduct, setCurrentProduct] = useState({
    product_id: null,
    name: '',
    type: '',
    assembly_order: 'None',
    description: '',
    default_colors: '4/4',
    default_finishing: [],
    finishing_processes: [],
    active: true,
    parts: []
  });

  // Estado para los procesos de acabado seleccionados
  const [selectedProcesses, setSelectedProcesses] = useState([]);

  // Estado para la parte seleccionada
  const [currentPart, setCurrentPart] = useState(null);
  const [isPartDialogOpen, setIsPartDialogOpen] = useState(false);
  const [isEditingPart, setIsEditingPart] = useState(false);

  // Función para filtrar productos según el estado activo
  const filterProducts = useCallback((productsToFilter) => {
    if (showOnlyActive) {
      return productsToFilter.filter(product => product.active);
    }
    return productsToFilter;
  }, [showOnlyActive]);

  // Función para alternar entre ver todos los productos o solo los activos
  const handleToggleActive = useCallback(() => {
    setShowOnlyActive(!showOnlyActive);

    // Actualizar la lista filtrada cuando cambia el switch
    if (showOnlyActive) {
      // Si estaba mostrando solo activos y ahora quiere ver todos
      setFilteredProducts(products);
    } else {
      // Si estaba mostrando todos y ahora quiere ver solo activos
      setFilteredProducts(products.filter(product => product.active));
    }
  }, [showOnlyActive, products]);

  // Función para obtener productos
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Intentar obtener productos desde la API
      const url = buildApiUrl('/products');
      console.log('Intentando cargar productos desde API:', url);
      const response = await ApiInterceptor.fetch(url);

      if (response.ok) {
        const data = await response.json();
        console.log('Productos obtenidos desde API:', data);

        const productsArray = Array.isArray(data) ? data : [];
        setProducts(productsArray);
        setFilteredProducts(filterProducts(productsArray));

        LogService.logUserAction('fetch_products_api', {
          count: productsArray.length
        });
      } else {
        throw new Error(`Error al obtener productos: ${response.statusText}`);
      }
    } catch (apiError) {
      console.warn('Error al obtener productos desde API:', apiError);

      // Si la API falla, cargar datos locales
      try {
        const localUrl = '/data/products.json';
        console.log('Intentando cargar productos desde:', localUrl);

        // Usar fetch nativo en lugar de ApiInterceptor para evitar problemas
        const localResponse = await fetch(localUrl);

        if (!localResponse.ok) {
          throw new Error(`Error al obtener productos locales: ${localResponse.statusText}`);
        }

        const localData = await localResponse.json();
        console.log('Productos obtenidos desde archivo local:', localData);

        const localProductsArray = Array.isArray(localData) ? localData : [];
        setProducts(localProductsArray);
        setFilteredProducts(filterProducts(localProductsArray));

        LogService.logUserAction('fetch_products_local', {
          count: localProductsArray.length
        });
      } catch (localError) {
        console.error('Error al obtener productos locales:', localError);
        setError(`Error al obtener productos: ${localError.message}`);
        LogService.logError('Error al obtener productos locales', { error: localError.message });
      }
    } finally {
      setLoading(false);
    }
  }, [setProducts, setError, setLoading, filterProducts]);

  // Cargar productos al iniciar
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Función para abrir el diálogo de creación/edición
  const handleOpenDialog = (product = null) => {
    if (product) {
      setIsEditMode(true);
      setCurrentProduct(product);
      // Cargar los procesos de acabado si existen
      if (product.finishing_processes && Array.isArray(product.finishing_processes)) {
        setSelectedProcesses(product.finishing_processes);
      } else {
        setSelectedProcesses([]);
      }
    } else {
      setIsEditMode(false);
      setCurrentProduct({
        product_id: `PROD-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
        name: '',
        type: '',
        work_style: 'Flat',
        assembly_order: 'None',
        description: '',
        default_colors: '4/4',
        default_finishing: [],
        finishing_processes: [],
        active: true,
        parts: []
      });
      setSelectedProcesses([]);
    }
    setIsDialogOpen(true);
  };

  // Función para abrir el diálogo de creación/edición de partes
  const handleOpenPartDialog = (part = null) => {
    console.log('Abriendo diálogo de partes:', part);

    if (part) {
      setIsEditingPart(true);
      setCurrentPart({...part}); // Crear una copia para evitar referencias
    } else {
      setIsEditingPart(false);
      setCurrentPart({
        part_id: `PART-${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
        name: '',
        description: '',
        default_colors: '4/4'
      });
    }

    // Asegurarse de que el diálogo se abra después de actualizar el estado
    setTimeout(() => {
      setIsPartDialogOpen(true);
    }, 0);
  };

  // Función para cerrar el diálogo de partes
  const handleClosePartDialog = () => {
    console.log('Cerrando diálogo de partes');
    setIsPartDialogOpen(false);
    // Limpiar el estado después de un breve retraso para evitar problemas de UI
    setTimeout(() => {
      setCurrentPart(null);
    }, 100);
  };

  // Función para manejar cambios en el formulario de partes
  const handlePartInputChange = (e) => {
    const { name, value } = e.target;
    console.log(`Cambiando ${name} a ${value}`);

    setCurrentPart(prevPart => {
      if (!prevPart) return { [name]: value };
      return {
        ...prevPart,
        [name]: value
      };
    });
  };

  // Función para guardar una parte
  const savePart = () => {
    console.log('Guardando parte:', currentPart);

    if (!currentPart || !currentPart.name) {
      setError('El nombre de la parte es obligatorio');
      return;
    }

    try {
      // Asegurarse de que currentProduct.parts es un array
      const updatedParts = Array.isArray(currentProduct.parts) ? [...currentProduct.parts] : [];

      if (isEditingPart) {
        // Actualizar parte existente
        const index = updatedParts.findIndex(p => p.part_id === currentPart.part_id);
        if (index !== -1) {
          updatedParts[index] = currentPart;
        } else {
          // Si no se encuentra la parte, añadirla como nueva
          updatedParts.push(currentPart);
        }
      } else {
        // Añadir nueva parte
        updatedParts.push(currentPart);
      }

      console.log('Partes actualizadas:', updatedParts);

      // Actualizar el producto con las nuevas partes
      setCurrentProduct(prevProduct => ({
        ...prevProduct,
        parts: updatedParts
      }));

      // Mostrar mensaje de éxito temporal
      setSuccessMessage(`Parte ${isEditingPart ? 'actualizada' : 'añadida'} correctamente. Recuerde guardar el producto para aplicar los cambios.`);

      // Cerrar el diálogo
      handleClosePartDialog();
    } catch (error) {
      console.error('Error al guardar la parte:', error);
      setError(`Error al guardar la parte: ${error.message}`);
    }
  };

  // Función para eliminar una parte
  const deletePart = (partId) => {
    if (!window.confirm('¿Está seguro de que desea eliminar esta parte?')) {
      return;
    }

    const updatedParts = currentProduct.parts.filter(p => p.part_id !== partId);

    setCurrentProduct({
      ...currentProduct,
      parts: updatedParts
    });

    // Mostrar mensaje de éxito temporal
    setSuccessMessage('Parte eliminada correctamente. Recuerde guardar el producto para aplicar los cambios.');
  };

  // Función para cerrar el diálogo
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setError(null);
    // Limpiar los procesos seleccionados al cerrar el diálogo
    setSelectedProcesses([]);
    // No limpiamos currentProduct aquí para evitar errores de renderizado
  };

  // Función para manejar cambios en el formulario
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentProduct({
      ...currentProduct,
      [name]: value
    });
  };

  // Función para guardar un producto
  const saveProduct = async () => {
    try {
      setLoading(true);
      setError(null);

      // Determinar si es un producto nuevo basado en si está en modo de edición
      const isNewProduct = !isEditMode;

      // Validar que todos los campos obligatorios estén presentes
      if (!currentProduct.name || !currentProduct.type || !currentProduct.work_style || !currentProduct.assembly_order || !currentProduct.description) {
        setError('Todos los campos obligatorios deben estar completos');
        setLoading(false);
        return;
      }

      // Actualizar los nombres de los procesos seleccionados en default_finishing
      const processNames = selectedProcesses.map(process => process.name);

      // Asegurarse de que el producto tenga un array de partes válido
      const productToSave = {
        ...currentProduct,
        parts: currentProduct.parts || [],
        default_finishing: processNames,
        finishing_processes: selectedProcesses,
        assembly_order: currentProduct.assembly_order // Asegurarse de que se incluya el tipo de encuadernación
      };

      // Registrar los datos que se van a enviar
      console.log('Datos a enviar:', {
        assembly_order: productToSave.assembly_order,
        default_finishing: productToSave.default_finishing,
        finishing_processes: productToSave.finishing_processes
      });

      console.log('Guardando producto:', productToSave);

      try {
        // Intentar guardar a través de la API
        const url = buildApiUrl(isNewProduct ? '/products' : `/products/${currentProduct.product_id}`);
        const method = isNewProduct ? 'POST' : 'PUT';

        const response = await ApiInterceptor.fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(productToSave)
        });

        if (response.ok) {
          const savedProduct = await response.json();
          console.log(`Producto ${isNewProduct ? 'creado' : 'actualizado'} correctamente:`, savedProduct);

          // Esperar un momento para asegurarnos de que el backend ha guardado el producto
          await new Promise(resolve => setTimeout(resolve, 500));

          // Actualizar la lista de productos
          await fetchProducts();

          // Mostrar mensaje de éxito
          setSuccessMessage(`Producto ${isNewProduct ? 'creado' : 'actualizado'} correctamente con ${currentProduct.parts.length} parte(s)`);

          // Cerrar el diálogo
          handleCloseDialog();

          LogService.logUserAction(isNewProduct ? 'create_product' : 'update_product', {
            product_id: savedProduct.product_id,
            product_name: savedProduct.name
          });

          return;
        }

        // Intentar obtener más detalles del error
        try {
          const errorData = await response.json();
          console.log('Error completo:', errorData);
          if (errorData.detail && Array.isArray(errorData.detail)) {
            // Formatear los errores de validación
            const validationErrors = errorData.detail.map(err =>
              `Campo '${err.loc.join('.')}': ${err.msg}`
            ).join('\n');
            throw new Error(`Error de validación:\n${validationErrors}`);
          } else {
            throw new Error(`Error al ${isNewProduct ? 'crear' : 'actualizar'} el producto: ${errorData.detail || response.statusText}`);
          }
        } catch (jsonError) {
          console.log('Error al parsear la respuesta:', jsonError);
          throw new Error(`Error al ${isNewProduct ? 'crear' : 'actualizar'} el producto: ${response.statusText}`);
        }
      } catch (apiError) {
        console.warn(`Error al ${isNewProduct ? 'crear' : 'actualizar'} el producto a través de la API:`, apiError);
        console.log('Detalles del error:', apiError.message);
        setError(apiError.message);

        // Si la API falla, actualizar localmente
        const updatedProducts = [...products];

        if (isNewProduct) {
          // Generar un nuevo ID de producto
          const newProductId = `PROD-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

          // Añadir el nuevo producto
          const newProduct = {
            ...currentProduct,
            product_id: newProductId
          };

          updatedProducts.push(newProduct);
        } else {
          // Actualizar el producto existente
          const index = updatedProducts.findIndex(p => p.product_id === currentProduct.product_id);

          if (index !== -1) {
            updatedProducts[index] = {
              ...updatedProducts[index],
              ...currentProduct
            };
          } else {
            throw new Error(`Producto con ID ${currentProduct.product_id} no encontrado`);
          }
        }

        // Actualizar el estado
        setProducts(updatedProducts);
        setFilteredProducts(filterProducts(updatedProducts));

        // Guardar en localStorage
        localStorage.setItem('products', JSON.stringify(updatedProducts));

        // Mostrar mensaje de éxito
        setSuccessMessage(`Producto ${isNewProduct ? 'creado' : 'actualizado'} correctamente (local) con ${currentProduct.parts.length} parte(s)`);

        // Cerrar el diálogo
        handleCloseDialog();

        LogService.logUserAction(isNewProduct ? 'create_product_local' : 'update_product_local', {
          product_id: currentProduct.product_id,
          product_name: currentProduct.name
        });
      }
    } catch (err) {
      console.error(`Error al ${!currentProduct.product_id ? 'crear' : 'actualizar'} el producto:`, err);
      setError(`Error al ${!currentProduct.product_id ? 'crear' : 'actualizar'} el producto: ${err.message}`);
      LogService.logError(`Error al ${!currentProduct.product_id ? 'crear' : 'actualizar'} el producto`, { error: err.message });
    } finally {
      setLoading(false);
    }
  };

  // Función para eliminar un producto
  const deleteProduct = async (productId) => {
    if (!window.confirm('¿Está seguro de que desea eliminar este producto?')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      try {
        // Intentar eliminar a través de la API
        const url = buildApiUrl(`/products/${productId}`);
        const response = await ApiInterceptor.fetch(url, {
          method: 'DELETE'
        });

        if (response.ok) {
          console.log('Producto eliminado correctamente');

          // Actualizar la lista de productos
          await fetchProducts();

          // Mostrar mensaje de éxito
          setSuccessMessage('Producto eliminado correctamente');

          LogService.logUserAction('delete_product', {
            product_id: productId
          });

          return;
        }

        throw new Error(`Error al eliminar el producto: ${response.statusText}`);
      } catch (apiError) {
        console.warn('Error al eliminar el producto a través de la API:', apiError);

        // Si la API falla, actualizar localmente
        const updatedProducts = products.filter(p => p.product_id !== productId);

        // Actualizar el estado
        setProducts(updatedProducts);
        setFilteredProducts(filterProducts(updatedProducts));

        // Guardar en localStorage
        localStorage.setItem('products', JSON.stringify(updatedProducts));

        // Mostrar mensaje de éxito
        setSuccessMessage('Producto eliminado correctamente (local)');

        LogService.logUserAction('delete_product_local', {
          product_id: productId
        });
      }
    } catch (err) {
      console.error('Error al eliminar el producto:', err);
      setError(`Error al eliminar el producto: ${err.message}`);
      LogService.logError('Error al eliminar el producto', { error: err.message });
    } finally {
      setLoading(false);
    }
  };

  // Renderizar la tabla de productos
  const renderProductsTable = () => {
    return (
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'primary.main' }}>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Nombre</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Tipo</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Encuadernación</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Colores</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Descripción</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Estado</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredProducts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No hay productos disponibles.
                </TableCell>
              </TableRow>
            ) : (
              filteredProducts.map((product) => (
                <TableRow key={product.product_id}>
                  <TableCell>{product.name}</TableCell>
                  <TableCell>{product.type}</TableCell>
                  <TableCell>
                    {ASSEMBLY_ORDERS.find(ao => ao.value === product.assembly_order)?.label || product.assembly_order}
                  </TableCell>
                  <TableCell>{product.default_colors}</TableCell>
                  <TableCell>{product.description}</TableCell>
                  <TableCell>
                    <Chip
                      label={product.active ? "Activo" : "Inactivo"}
                      color={product.active ? "success" : "default"}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      color="primary"
                      onClick={() => handleOpenDialog(product)}
                      size="small"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      color="error"
                      onClick={() => deleteProduct(product.product_id)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  // Renderizar el formulario de producto
  const renderProductForm = () => {
    // Si no hay producto actual o el diálogo no está abierto, no renderizar nada
    if (!currentProduct || !isDialogOpen) {
      return null;
    }

    return (
      <Dialog open={isDialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {isEditMode ? 'Editar Producto' : 'Nuevo Producto'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="name"
                label="Nombre del Producto"
                value={currentProduct.name || ''}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Tipo de Producto</InputLabel>
                <Select
                  name="type"
                  value={currentProduct.type}
                  onChange={handleInputChange}
                  label="Tipo de Producto"
                >
                  {PRODUCT_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Tipo de Encuadernación</InputLabel>
                <Select
                  name="assembly_order"
                  value={currentProduct.assembly_order}
                  onChange={handleInputChange}
                  label="Tipo de Encuadernación"
                >
                  {ASSEMBLY_ORDERS.map((order) => (
                    <MenuItem key={order.value} value={order.value}>
                      {order.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="default_colors"
                label="Colores por Defecto"
                value={currentProduct.default_colors}
                onChange={handleInputChange}
                fullWidth
                placeholder="Ej: 4/4, 4/0, 1/1"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Descripción"
                value={currentProduct.description}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={currentProduct.active || false}
                    onChange={(e) => setCurrentProduct({
                      ...currentProduct,
                      active: e.target.checked
                    })}
                    color="primary"
                  />
                }
                label="Producto activo"
              />
            </Grid>

            {/* Sección de Partes del Producto */}
            {/* Selector de Acabados */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                Acabados del Producto
              </Typography>
              <Paper elevation={1} sx={{ mb: 2, overflow: 'hidden' }}>
                <ProductFinishingSelector
                  selectedProcesses={selectedProcesses}
                  setSelectedProcesses={setSelectedProcesses}
                />
              </Paper>
            </Grid>

            {/* Partes del Producto */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                Partes del Producto
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => handleOpenPartDialog()}
                  startIcon={<AddIcon />}
                  size="small"
                >
                  Añadir Parte
                </Button>
              </Box>

              {currentProduct.parts && currentProduct.parts.length > 0 ? (
                <TableContainer component={Paper} sx={{ mb: 2 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Nombre</TableCell>
                        <TableCell>Descripción</TableCell>
                        <TableCell>Colores</TableCell>
                        <TableCell>Acciones</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {currentProduct.parts.map((part) => (
                        <TableRow key={part.part_id}>
                          <TableCell>{part.name}</TableCell>
                          <TableCell>{part.description}</TableCell>
                          <TableCell>{part.default_colors}</TableCell>
                          <TableCell>
                            <IconButton
                              color="primary"
                              onClick={() => handleOpenPartDialog(part)}
                              size="small"
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              color="error"
                              onClick={() => deletePart(part.part_id)}
                              size="small"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No hay partes definidas para este producto.
                </Typography>
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancelar</Button>
          <Button
            onClick={saveProduct}
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            disabled={loading || !currentProduct.name || !currentProduct.type}
          >
            {isEditMode ? 'Actualizar' : 'Guardar'}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Catálogo de Productos
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={showOnlyActive}
                onChange={handleToggleActive}
                color="primary"
              />
            }
            label="Mostrar solo activos"
          />
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            NUEVO PRODUCTO
          </Button>
        </Box>
      </Box>

      {loading && <Typography>Cargando...</Typography>}
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      {renderProductsTable()}
      {renderProductForm()}

      {/* Diálogo para editar partes */}
      {isPartDialogOpen && currentPart && (
        <Dialog open={isPartDialogOpen} onClose={handleClosePartDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {isEditingPart ? 'Editar Parte' : 'Nueva Parte'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  name="name"
                  label="Nombre de la Parte"
                  value={currentPart?.name || ''}
                  onChange={handlePartInputChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="description"
                  label="Descripción"
                  value={currentPart?.description || ''}
                  onChange={handlePartInputChange}
                  fullWidth
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="default_colors"
                  label="Colores por Defecto"
                  value={currentPart?.default_colors || '4/4'}
                  onChange={handlePartInputChange}
                  fullWidth
                  placeholder="Ej: 4/4, 4/0, 1/1"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClosePartDialog}>Cancelar</Button>
            <Button
              onClick={savePart}
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              disabled={!currentPart?.name}
            >
              {isEditingPart ? 'Actualizar' : 'Guardar'}
            </Button>
          </DialogActions>
        </Dialog>
      )}

      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage(null)}
        message={successMessage}
      />
    </Paper>
  );
};

export default ProductCatalog;
