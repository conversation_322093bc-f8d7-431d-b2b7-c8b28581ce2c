"""
Utilidades para calcular costes de papel
"""

def calculate_sheet_cost(paper_data):
    """
    Calcula el coste por pliego basado en el coste por tonelada
    
    Args:
        paper_data (dict): Datos del papel que incluyen:
            - dimension_width (float): <PERSON>cho del pliego en mm
            - dimension_height (float): Alto del pliego en mm
            - weight (float): Gramaje del papel en g/m²
            - cost_per_ton (float): Coste por tonelada en €
    
    Returns:
        float: Coste por pliego en €
    """
    # Convertir dimensiones de mm a m
    width_m = paper_data["dimension_width"] / 1000
    height_m = paper_data["dimension_height"] / 1000
    
    # Calcular área del pliego en m²
    area_m2 = width_m * height_m
    
    # Calcular peso del pliego en kg
    # Peso = Área (m²) * Gramaje (g/m²) / 1000 (para convertir g a kg)
    weight_kg = area_m2 * paper_data["weight"] / 1000
    
    # Calcular coste por kg
    cost_per_kg = paper_data["cost_per_ton"] / 1000
    
    # Calcular coste por pliego
    sheet_cost = weight_kg * cost_per_kg
    
    return sheet_cost

def calculate_sheet_cost_for_quantity(paper_data, quantity):
    """
    Calcula el coste total para una cantidad de pliegos
    
    Args:
        paper_data (dict): Datos del papel
        quantity (int): Cantidad de pliegos
    
    Returns:
        float: Coste total para la cantidad especificada
    """
    sheet_cost = calculate_sheet_cost(paper_data)
    return sheet_cost * quantity

def calculate_cost_per_1000(paper_data):
    """
    Calcula el coste por 1000 pliegos
    
    Args:
        paper_data (dict): Datos del papel
    
    Returns:
        float: Coste por 1000 pliegos
    """
    return calculate_sheet_cost_for_quantity(paper_data, 1000)
