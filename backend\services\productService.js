const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const productsFilePath = path.join(__dirname, '../data/products.json');

// Función para leer todos los productos
const getAllProducts = () => {
  try {
    if (!fs.existsSync(productsFilePath)) {
      return [];
    }
    
    const data = fs.readFileSync(productsFilePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error al leer productos:', error);
    return [];
  }
};

// Función para obtener un producto por ID
const getProductById = (id) => {
  const products = getAllProducts();
  return products.find(product => product.id === parseInt(id) || product.id === id);
};

// Función para crear un nuevo producto
const createProduct = (productData) => {
  try {
    const products = getAllProducts();
    
    // Generar un nuevo ID
    const newId = products.length > 0 
      ? Math.max(...products.map(p => typeof p.id === 'number' ? p.id : 0)) + 1 
      : 1;
    
    const newProduct = {
      id: newId,
      ...productData
    };
    
    products.push(newProduct);
    
    fs.writeFileSync(productsFilePath, JSON.stringify(products, null, 2));
    
    return newProduct;
  } catch (error) {
    console.error('Error al crear producto:', error);
    throw error;
  }
};

// Función para actualizar un producto existente
const updateProduct = (id, productData) => {
  try {
    let products = getAllProducts();
    const index = products.findIndex(product => product.id === parseInt(id) || product.id === id);
    
    if (index === -1) {
      throw new Error(`Producto con ID ${id} no encontrado`);
    }
    
    // Actualizar el producto
    products[index] = {
      ...products[index],
      ...productData,
      id: products[index].id // Mantener el ID original
    };
    
    fs.writeFileSync(productsFilePath, JSON.stringify(products, null, 2));
    
    return products[index];
  } catch (error) {
    console.error('Error al actualizar producto:', error);
    throw error;
  }
};

// Función para eliminar un producto
const deleteProduct = (id) => {
  try {
    let products = getAllProducts();
    const index = products.findIndex(product => product.id === parseInt(id) || product.id === id);
    
    if (index === -1) {
      throw new Error(`Producto con ID ${id} no encontrado`);
    }
    
    // Eliminar el producto
    const deletedProduct = products[index];
    products.splice(index, 1);
    
    fs.writeFileSync(productsFilePath, JSON.stringify(products, null, 2));
    
    return deletedProduct;
  } catch (error) {
    console.error('Error al eliminar producto:', error);
    throw error;
  }
};

// Función para obtener productos por tipo
const getProductsByType = (type) => {
  const products = getAllProducts();
  return products.filter(product => product.type === type);
};

// Función para obtener productos por estilo de trabajo
const getProductsByWorkStyle = (workStyle) => {
  const products = getAllProducts();
  return products.filter(product => product.work_style === workStyle);
};

module.exports = {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductsByType,
  getProductsByWorkStyle
};
