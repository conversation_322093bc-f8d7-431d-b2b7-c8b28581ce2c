from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from utils.catalog_manager import load_machines, load_paper_catalog
from utils.logger import log_info, log_error
from utils.paper_calculator import calcular_pliegos
from models.models import EsquemaResponse
import math

router = APIRouter(
    prefix="/v2",
    tags=["digital_calculator"],
    responses={404: {"description": "Máquina no encontrada"}}
)

class DigitalCalculationRequest(BaseModel):
    machine_id: str
    copies: int
    paper_id: Optional[str] = None
    is_duplex: bool = True
    is_color: bool = True
    num_paginas: Optional[int] = None
    ancho_pagina: Optional[float] = None
    alto_pagina: Optional[float] = None
    colors_front: Optional[int] = None
    colors_back: Optional[int] = None
    custom_print_time: Optional[float] = None
    custom_setup_time: Optional[int] = None

class DigitalCalculationResponse(BaseModel):
    machine_id: str
    machine_name: str
    machine_type: str = "Digital"  # Tipo de máquina con valor por defecto
    copies: int
    total_pages: int
    total_sheets: int
    clicks_per_sheet: int
    total_clicks: int
    is_duplex: bool
    is_color: bool
    click_unit_cost: float
    click_cost: float
    paper_cost: Optional[float] = None
    printing_time_minutes: float
    total_time_minutes: float
    total_time_hours: float
    paper_id: Optional[str] = None
    paper_name: Optional[str] = None
    sheet_width_mm: Optional[float] = None
    sheet_height_mm: Optional[float] = None
    a4_per_sheet: int
    print_speed: int
    total_cost: float
    machine_cost: Optional[float] = None
    mejor_combinacion: Optional[Dict[str, Any]] = None
    esquemas_utilizados: Optional[List[Dict[str, Any]]] = None
    # Campos adicionales para hacer la estructura más similar a offset
    paper_data: Optional[Dict[str, Any]] = None
    machine_data: Optional[Dict[str, Any]] = None
    clicks_data: Optional[Dict[str, Any]] = None

# Función auxiliar para calcular cuántas páginas A4 caben en una hoja
def calcular_a4_por_hoja(sheet_width, sheet_height, page_width, page_height):
    # Calcular cuántas páginas caben horizontalmente
    pages_x = int(sheet_width / page_width)
    # Calcular cuántas páginas caben verticalmente
    pages_y = int(sheet_height / page_height)
    # Calcular total de páginas por hoja
    return pages_x * pages_y

@router.post("/calculate-digital", response_model=DigitalCalculationResponse)
async def calculate_digital(request: DigitalCalculationRequest):
    """
    Calcula costes y tiempos para impresión digital.

    Esta versión proporciona cálculos detallados para máquinas digitales, similar a calculate-offset.
    Para máquinas digitales, no se considera tiempo de preparación en los cálculos.

    Parámetros:
    - machine_id: ID de la máquina digital
    - copies: Número de ejemplares
    - paper_id: ID del papel (opcional)
    - is_duplex: Si la impresión es a doble cara (true) o una cara (false)
    - is_color: Si la impresión es a color (true) o blanco y negro (false)
    - num_paginas: Número de páginas del documento
    - ancho_pagina: Ancho de la página en mm
    - alto_pagina: Alto de la página en mm
    - colors_front: Número de colores en el anverso
    - colors_back: Número de colores en el reverso
    - custom_print_time: Tiempo personalizado de impresión en horas
    - custom_setup_time: Tiempo personalizado (opcional, solo para casos especiales)

    Retorna información detallada sobre costes y tiempos de impresión.
    """
    try:
        log_info(f"Solicitud de cálculo digital recibida: {request}")

        # Cargar la máquina del catálogo
        machines = load_machines()
        machine = next((m for m in machines if m.get("machine_id") == request.machine_id or m.get("product_id") == request.machine_id), None)

        if not machine:
            log_error(f"Máquina con ID {request.machine_id} no encontrada")
            raise HTTPException(status_code=404, detail=f"Máquina con ID {request.machine_id} no encontrada")

        # Verificar que es una máquina digital
        if machine.get("type") != "Digital":
            log_error(f"La máquina {request.machine_id} no es de tipo Digital")
            raise HTTPException(status_code=400, detail=f"La máquina {request.machine_id} no es de tipo Digital")

        # Determinar si es impresión a doble cara
        is_duplex = request.is_duplex
        if request.colors_back is not None:
            is_duplex = request.colors_back > 0

        # Determinar si es impresión a color
        is_color = request.is_color
        if request.colors_front is not None or request.colors_back is not None:
            front_colors = request.colors_front or 0
            back_colors = request.colors_back or 0
            is_color = front_colors > 1 or back_colors > 1

        # Obtener coste por click
        click_unit_cost = machine.get("click_color_cost", 0.023) if is_color else machine.get("click_bw_cost", 0.009)

        # Obtener datos del papel si se proporciona paper_id
        paper = None
        paper_name = None
        sheet_width_mm = None
        sheet_height_mm = None
        paper_cost = None

        if request.paper_id:
            papers = load_paper_catalog()
            paper = next((p for p in papers if p.get("product_id") == request.paper_id), None)
            if paper:
                paper_name = paper.get("descriptive_name", "")
                sheet_width_mm = paper.get("dimension_width", 0)
                sheet_height_mm = paper.get("dimension_height", 0)

        # Obtener dimensiones de página
        ancho_pagina = request.ancho_pagina or 210  # A4 por defecto
        alto_pagina = request.alto_pagina or 297    # A4 por defecto

        # Si no se proporcionan dimensiones del papel, usar A3 por defecto
        if not sheet_width_mm or not sheet_height_mm:
            sheet_width_mm = 420  # A3 por defecto
            sheet_height_mm = 297

        # Calcular cuántas páginas A4 caben en una hoja
        a4_per_sheet = calcular_a4_por_hoja(sheet_width_mm, sheet_height_mm, ancho_pagina, alto_pagina)

        # Obtener número de páginas
        num_paginas = request.num_paginas or 1

        # Calcular número de pliegos necesarios
        total_pliegos = math.ceil(num_paginas / (a4_per_sheet * (2 if is_duplex else 1)))

        # Calcular número total de hojas físicas
        total_sheets = total_pliegos * request.copies

        # Calcular número de clicks
        clicks_per_sheet = 2 if is_duplex else 1
        total_clicks = total_sheets * clicks_per_sheet

        # Calcular coste de clicks
        click_cost = total_clicks * click_unit_cost

        # Calcular coste de papel si tenemos la información
        if paper and paper.get("price_per_1000"):
            paper_cost_per_1000 = paper.get("price_per_1000", 0)
            paper_cost = (paper_cost_per_1000 / 1000) * total_sheets

        # Calcular tiempo de impresión
        print_speed = machine.get("speed", 120)  # Hojas por minuto
        if print_speed <= 0:
            print_speed = 120  # Valor por defecto si no está definido o es inválido

        # Si se proporciona un tiempo personalizado, usarlo
        if request.custom_print_time is not None:
            printing_time_minutes = request.custom_print_time * 60  # Convertir de horas a minutos
        else:
            printing_time_minutes = total_sheets / print_speed

        # Tiempo total es igual al tiempo de impresión (no hay tiempo de preparación)
        total_time_minutes = printing_time_minutes
        total_time_hours = total_time_minutes / 60

        # Calcular coste de máquina basado en tiempo
        hourly_cost = machine.get("hourly_cost", 60)
        machine_cost = hourly_cost * total_time_hours

        # Calcular coste total
        total_cost = click_cost + (paper_cost or 0) + machine_cost

        # Preparar la información de esquemas utilizados
        esquemas_utilizados = []
        mejor_combinacion_dict = {
            "total_pliegos": total_pliegos,
            "total_clicks": total_clicks,
            "a4_per_sheet": a4_per_sheet,
            "is_duplex": is_duplex,
            "is_color": is_color,
            "print_speed": print_speed,
            "printing_time_minutes": printing_time_minutes,
            "estimated_time_minutes": printing_time_minutes,
            "estimated_time_hours": total_time_hours
        }

        # Añadir esquema utilizado
        esquema_dict = {
            "nombre": f"F{a4_per_sheet * (2 if is_duplex else 1)}-1",
            "numero_pliegos": total_pliegos,
            "paginas_por_pliego": a4_per_sheet * (2 if is_duplex else 1),
            "disposicion": {
                "paginas_ancho": a4_per_sheet,
                "paginas_alto": 1,
                "orientacion": "vertical_normal"
            },
            "es_tira_retira": False,
            "is_duplex": is_duplex,
            "is_color": is_color,
            "clicks_per_sheet": clicks_per_sheet
        }
        esquemas_utilizados.append(esquema_dict)

        # Construir datos adicionales para hacer la estructura similar a offset
        machine_data = {
            "setup_time": 0,  # Las máquinas digitales no tienen tiempo de preparación
            "printing_time_minutes": round(printing_time_minutes, 2),
            "total_time_minutes": round(total_time_minutes, 2),
            "total_time_hours": round(total_time_hours, 2),
            "hourly_cost": machine.get("hourly_cost", 0),
            "click_unit_cost": click_unit_cost,
            "click_color_cost": machine.get("click_color_cost", 0),
            "speed": print_speed,
            "name": machine.get("name", ""),
            "type": "Digital"  # Añadimos explícitamente el tipo
        }
        
        paper_data = None
        if paper:
            paper_data = {
                "paper_name": paper_name,
                "name": paper_name,
                "descriptive_name": paper.get("descriptive_name", paper_name),
                "paper_cost": round(paper_cost, 2) if paper_cost else 0,
                "sheet_width_mm": sheet_width_mm,
                "sheet_height_mm": sheet_height_mm,
                "dimension_width": sheet_width_mm,
                "dimension_height": sheet_height_mm
            }
        
        clicks_data = {
            "clicks_per_sheet": clicks_per_sheet,
            "total_clicks": total_clicks,
            "click_unit_cost": click_unit_cost,
            "click_cost": round(click_cost, 2)
        }

        # Construir la respuesta
        response = DigitalCalculationResponse(
            machine_id=request.machine_id,
            machine_name=machine.get("name", ""),
            machine_type="Digital",  # Aseguramos que el tipo sea explícito
            copies=request.copies,
            total_pages=num_paginas,
            total_sheets=total_sheets,
            clicks_per_sheet=clicks_per_sheet,
            total_clicks=total_clicks,
            is_duplex=is_duplex,
            is_color=is_color,
            click_unit_cost=click_unit_cost,
            click_cost=round(click_cost, 2),
            paper_cost=round(paper_cost, 2) if paper_cost else None,
            printing_time_minutes=round(printing_time_minutes, 2),
            total_time_minutes=round(total_time_minutes, 2),
            total_time_hours=round(total_time_hours, 2),
            paper_id=request.paper_id,
            paper_name=paper_name,
            sheet_width_mm=sheet_width_mm,
            sheet_height_mm=sheet_height_mm,
            a4_per_sheet=a4_per_sheet,
            print_speed=print_speed,
            machine_cost=round(machine_cost, 2) if machine_cost else None,
            total_cost=round(total_cost, 2),
            mejor_combinacion=mejor_combinacion_dict,
            esquemas_utilizados=esquemas_utilizados,
            # Nuevos campos para hacer la estructura similar a offset
            paper_data=paper_data,
            machine_data=machine_data,
            clicks_data=clicks_data
        )

        log_info(f"Cálculo digital completado: {response}")
        return response

    except HTTPException as e:
        log_error(f"Error HTTP en cálculo digital: {e.detail}")
        raise e
    except Exception as e:
        log_error(f"Error en cálculo digital: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error al calcular: {str(e)}")
