import React from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

/**
 * Componente que muestra información detallada de un cliente
 */
const ClientInfoModal = ({ open, onClose, client }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        Información del Cliente
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        {client && (
          <Grid container spacing={3}>
            {/* Información de la empresa */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Datos de la Empresa
              </Typography>
              <Paper elevation={1} sx={{ p: 2 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  {client.company?.name || 'Sin nombre de empresa'}
                </Typography>

                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>ID Cliente:</strong> {client.client_id}
                </Typography>

                <Typography variant="body2">
                  <strong>Código de Facturación:</strong> {client.billing_code || 'No especificado'}
                </Typography>

                <Typography variant="body2">
                  <strong>ID de Pedido:</strong> {client.order_id || 'No especificado'}
                </Typography>

                <Typography variant="body2">
                  <strong>Estado:</strong> {client.active ? 'Activo' : 'Inactivo'}
                </Typography>

                <Typography variant="body2">
                  <strong>Creado:</strong> {client.created_at || 'No especificado'}
                </Typography>

                <Typography variant="body2">
                  <strong>Última actualización:</strong> {client.updated_at || 'No especificado'}
                </Typography>

                <Typography variant="body2" sx={{ mt: 2, p: 1, bgcolor: 'rgba(0, 150, 136, 0.1)', borderRadius: 1, fontWeight: 'medium' }}>
                  <strong>Descuento aplicable:</strong> {client.discount_percentage ? `${client.discount_percentage}%` : '0%'}
                </Typography>
              </Paper>
            </Grid>

            {/* Dirección */}
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Dirección
              </Typography>
              <Paper elevation={1} sx={{ p: 2 }}>
                {client.company?.address ? (
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Calle"
                        secondary={client.company.address.street || 'No especificada'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Ciudad"
                        secondary={client.company.address.city || 'No especificada'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Región/Provincia"
                        secondary={client.company.address.region || 'No especificada'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Código Postal"
                        secondary={client.company.address.postal_code || 'No especificado'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="País"
                        secondary={client.company.address.country || 'No especificado'}
                      />
                    </ListItem>
                  </List>
                ) : (
                  <Typography variant="body2">No hay información de dirección disponible</Typography>
                )}
              </Paper>
            </Grid>

            {/* Información de contacto */}
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Información de Contacto
              </Typography>
              <Paper elevation={1} sx={{ p: 2 }}>
                {client.contact ? (
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Nombre"
                        secondary={`${client.contact.first_name || ''} ${client.contact.last_name || ''}`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Cargo"
                        secondary={client.contact.position || 'No especificado'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Teléfono"
                        secondary={client.contact.phone || 'No especificado'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Fax"
                        secondary={client.contact.fax || 'No especificado'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Email"
                        secondary={client.contact.email || 'No especificado'}
                      />
                    </ListItem>
                  </List>
                ) : (
                  <Typography variant="body2">No hay información de contacto disponible</Typography>
                )}
              </Paper>
            </Grid>

            {/* Notas */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Notas
              </Typography>
              <Paper elevation={1} sx={{ p: 2 }}>
                <Typography variant="body2">
                  {client.notes || 'No hay notas disponibles para este cliente'}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

ClientInfoModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  client: PropTypes.object
};

export default ClientInfoModal;
