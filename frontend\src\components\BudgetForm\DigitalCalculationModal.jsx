import { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Tooltip
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import RestartAltIcon from '@mui/icons-material/RestartAlt';

/**
 * Modal especializado para mostrar resultados de cálculos de pliegos en máquinas digitales
 */
const DigitalCalculationModal = ({
  open,
  onClose,
  sheetCalculation,
  budget,
  customMachinePrintTime,
  handleCustomMachineTimeChange,
  selectedProcesses = [],
  calculatedProcessCost = 0
}) => {


  // Determinar si los datos provienen del endpoint v2/calculate-digital
  const isDigitalV2 = sheetCalculation?.clicks_data;

  // Efecto para mostrar la respuesta completa del endpoint solo cuando el modal está abierto
  useEffect(() => {
    if (open && sheetCalculation) {
      // Mostrar la respuesta completa del endpoint
      console.log('Datos recibidos en DigitalCalculationModal:', sheetCalculation);

      // Mostrar la respuesta en formato JSON para facilitar la copia
      console.log('Datos en formato JSON:', JSON.stringify(sheetCalculation, null, 2));

      // Mostrar información específica sobre esquemas utilizados
      if (sheetCalculation.esquemas_utilizados && sheetCalculation.esquemas_utilizados.length > 0) {
        console.log('Esquemas utilizados:', sheetCalculation.esquemas_utilizados);
      } else if (sheetCalculation.mejor_combinacion?.esquemas_utilizados && sheetCalculation.mejor_combinacion.esquemas_utilizados.length > 0) {
        console.log('Esquemas utilizados (mejor_combinacion):', sheetCalculation.mejor_combinacion.esquemas_utilizados);
      }
    }
  }, [open, sheetCalculation]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        Resultado del cálculo de pliegos (Digital)
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        {sheetCalculation && (
          <>
            {/* Sección de Resumen */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Resumen
              </Typography>
              <Typography variant="body1">
                <strong>Producto:</strong> {budget.page_count || budget.pageCount} páginas de tamaño {budget.page_size || budget.pageSize} {(budget.page_size || budget.pageSize) === 'Personalizado' ? `(${budget.custom_page_size || budget.customPageSize})` : ''}
              </Typography>
              <Typography variant="body1">
                <strong>Tipo de impresión:</strong> Digital (clicks)
              </Typography>
              <Typography variant="body1">
                <strong>Número de ejemplares:</strong> {
                  (() => {
                    // Intentar obtener el valor de diferentes fuentes
                    const copiesValue = budget.copies ||
                                      sheetCalculation?.copies ||
                                      budget.part?.copies ||
                                      sheetCalculation?.mejor_combinacion?.copies;

                    // Convertir a número si es string, o usar 0 como valor por defecto
                    return copiesValue ? parseInt(copiesValue, 10) : 0;
                  })()
                }
              </Typography>
              <Typography variant="body1">
                <strong>Papel seleccionado:</strong>
                {budget.paper_data ? (
                  <span>
                    {budget.paper_data.descriptive_name || budget.paper_data.name || 'Sin nombre'} ({budget.paper_data.dimension_width} x {budget.paper_data.dimension_height} mm)
                  </span>
                ) : budget.paper ? (
                  <span>
                    {budget.paper.descriptive_name || budget.paper.name || 'Sin nombre'} ({budget.paper.dimension_width} x {budget.paper.dimension_height} mm)
                  </span>
                ) : (
                  'No seleccionado'
                )}
              </Typography>
              <Typography variant="body1">
                <strong>Configuración de colores:</strong> {budget.colorConfig ? `${budget.colorConfig.frontColors}/${budget.colorConfig.backColors}${budget.colorConfig.pantones > 0 ? ` + ${budget.colorConfig.pantones} pantones` : ''}` : 'No especificada'}
              </Typography>
              <Typography variant="body1">
                <strong>Tipo de encuadernado:</strong> {budget.binding_type ?
                  (budget.binding_type === 'gathering' ? 'Alzado' :
                   budget.binding_type === 'collection' ? 'Grapado' :
                   budget.binding_type === 'none' ? 'Sin encuadernado' : budget.binding_type) : 'Alzado (por defecto)'}
              </Typography>
            </Box>

            {/* Sección de Esquema utilizado */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Resumen de clicks
              </Typography>
              <Paper elevation={2} sx={{ p: 2, bgcolor: '#f5f5f5' }}>
                <Typography variant="body1" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                  Total: {sheetCalculation?.total_sheets || 0} pliegos / {sheetCalculation?.total_clicks || 0} clicks
                </Typography>

                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2">
                      <strong>Modo de impresión:</strong> {sheetCalculation?.is_duplex ? 'Doble cara (dúplex)' : 'Una cara (símplex)'}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Tipo de impresión:</strong> {sheetCalculation?.is_color ? 'Color' : 'Blanco y negro'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2">
                      <strong>A4 por pliego:</strong> {sheetCalculation?.a4_per_sheet || 2}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Clicks por pliego:</strong> {sheetCalculation?.clicks_per_sheet || 1}
                    </Typography>
                  </Grid>
                </Grid>

                {/* Tabla de esquemas si están disponibles */}
                {sheetCalculation &&
                 ((sheetCalculation.mejor_combinacion && sheetCalculation.mejor_combinacion.esquemas_utilizados) ||
                  sheetCalculation.esquemas_utilizados) && (
                  <>
                    <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, fontWeight: 'bold' }}>
                      Esquemas de imposición
                    </Typography>
                    <Table size="small">
                      <TableHead>
                        <TableRow sx={{ bgcolor: '#e0e0e0' }}>
                          <TableCell sx={{ fontWeight: 'bold' }}>Esquema</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Pliegos</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Págs/Pliego</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Disposición</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Modo</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Clicks</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {(sheetCalculation.esquemas_utilizados || sheetCalculation.mejor_combinacion.esquemas_utilizados || []).map((esquema, index) => (
                          <TableRow key={index}>
                            <TableCell>{esquema.nombre}</TableCell>
                            <TableCell>{esquema.numero_pliegos}</TableCell>
                            <TableCell>{esquema.paginas_por_pliego}</TableCell>
                            <TableCell>
                              {esquema.disposicion ?
                                `${esquema.disposicion.paginas_ancho} × ${esquema.disposicion.paginas_alto}` :
                                (esquema.paginas_ancho && esquema.paginas_alto ?
                                  `${esquema.paginas_ancho} × ${esquema.paginas_alto}` : 'N/A')}
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                {esquema.is_duplex ? 'Duplex' : 'Simplex'}
                              </Typography>
                              <Typography variant="caption" display="block">
                                {esquema.is_color ? 'Color' : 'Blanco y negro'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              {esquema.clicks_per_sheet !== undefined ? esquema.clicks_per_sheet : '-'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </>
                )}
              </Paper>
            </Box>

            {/* Sección de Cálculo de costes */}
            <Box>
              <Typography variant="h6" gutterBottom>
                Cálculo de costes
              </Typography>
              <Paper elevation={2} sx={{ p: 2, bgcolor: '#f5f5f5' }}>
                {budget && (
                  <>
                    {/* No incluimos las secciones de datos del papel y datos de la máquina */}

                    {/* Sección de COSTE TOTAL DEL TRABAJO con formato similar a la imagen */}
                    <Box sx={{ bgcolor: '#e3f2fd', p: 2, borderRadius: 1 }}>
                      <Typography variant="h6" color="primary" gutterBottom>
                        COSTE TOTAL DEL TRABAJO
                      </Typography>

                      <Grid container spacing={2}>
                        {/* Columna izquierda: Datos de producción */}
                        <Grid item xs={12} md={7}>
                          {(() => {
                            // Usar directamente los valores del endpoint
                            const copies = sheetCalculation?.copies || parseInt(budget.copies || 0, 10);
                            const totalSheets = sheetCalculation?.total_sheets || 0;
                            const totalClicks = sheetCalculation?.total_clicks || 0;

                            // Determinar el tiempo según la fuente de datos disponible
                            let timeMinutes = 0;
                            let timeHours = 0;

                            if (customMachinePrintTime) {
                              timeMinutes = customMachinePrintTime;
                              timeHours = customMachinePrintTime / 60;
                            } else if (sheetCalculation?.printing_time_minutes) {
                              timeMinutes = sheetCalculation.printing_time_minutes;
                              timeHours = sheetCalculation.total_time_hours || (timeMinutes / 60);
                            } else if (sheetCalculation?.total_time_minutes) {
                              timeMinutes = sheetCalculation.total_time_minutes;
                              timeHours = sheetCalculation.total_time_hours || (timeMinutes / 60);
                            }

                            return (
                              <>
                                <Typography variant="body1">
                                  <strong>Ejemplares:</strong> {copies}
                                </Typography>
                                <Typography variant="body1">
                                  <strong>Pliegos totales:</strong> {totalSheets}
                                </Typography>
                                <Typography variant="body1">
                                  <strong>Clicks totales:</strong> {totalClicks}
                                </Typography>
                                <Typography variant="body1">
                                  <strong>Tiempo calculado:</strong> {timeHours.toFixed(2)} horas ({timeMinutes.toFixed(0)} minutos)
                                </Typography>

                                {/* Campo para personalizar el tiempo */}
                                {handleCustomMachineTimeChange && (
                                  <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                                    <TextField
                                      label="Tiempo personalizado (min)"
                                      type="number"
                                      size="small"
                                      value={customMachinePrintTime || ''}
                                      onChange={(e) => handleCustomMachineTimeChange(e.target.value)}
                                      sx={{ mr: 1, width: '180px' }}
                                    />
                                    <Tooltip title="Restablecer tiempo">
                                      <IconButton
                                        size="small"
                                        onClick={() => handleCustomMachineTimeChange(null)}
                                        color="primary"
                                      >
                                        <RestartAltIcon />
                                      </IconButton>
                                    </Tooltip>
                                  </Box>
                                )}
                              </>
                            );
                          })()}

                          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mt: 2 }}>
                            Desglose de costos:
                          </Typography>
                          {(() => {
                            // Usar directamente los valores del endpoint
                            const paperCost = sheetCalculation?.paper_cost || 0;
                            const clickCost = sheetCalculation?.click_cost || 0;
                            const machineCost = sheetCalculation?.machine_cost || 0;
                            const clickUnitCost = sheetCalculation?.click_unit_cost || 0;
                            const totalClicks = sheetCalculation?.total_clicks || 0;

                            return (
                              <>
                                <Typography variant="body2">
                                  <strong>Papel:</strong> {paperCost.toFixed(2)} €
                                </Typography>
                                <Typography variant="body2">
                                  <strong>Clicks:</strong> {clickCost.toFixed(2)} € {totalClicks > 0 && clickUnitCost > 0 && `(${totalClicks} clicks x ${clickUnitCost.toFixed(4)} € por click)`}
                                </Typography>
                                {machineCost > 0 && machineCost !== clickCost && (
                                  <Typography variant="body2">
                                    <strong>Coste adicional máquina:</strong> {machineCost.toFixed(2)} €
                                  </Typography>
                                )}
                              </>
                            );
                          })()}
                        </Grid>

                        {/* Columna derecha: Resumen de costes */}
                        <Grid item xs={12} md={5} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', justifyContent: 'space-between' }}>
                          {(() => {
                            // Usar directamente los valores del endpoint
                            const paperCost = sheetCalculation?.paper_cost || 0;
                            const clickCost = sheetCalculation?.click_cost || 0;
                            const machineCost = sheetCalculation?.machine_cost || 0;
                            const totalCost = sheetCalculation?.total_cost || 0;

                            return (
                              <>
                                <Box>
                                  <Typography variant="body1" align="right">
                                    Papel: {paperCost.toFixed(2)} €
                                  </Typography>
                                  <Typography variant="body1" align="right">
                                    Clicks: {clickCost.toFixed(2)} €
                                  </Typography>
                                  {machineCost > 0 && machineCost !== clickCost && (
                                    <Typography variant="body1" align="right">
                                      Máquina: {machineCost.toFixed(2)} €
                                    </Typography>
                                  )}
                                </Box>

                                <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                                  <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                                    {totalCost.toFixed(2)} €
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Coste total de impresión
                                  </Typography>
                                </Box>
                              </>
                            );
                          })()}
                        </Grid>
                      </Grid>
                    </Box>

                    {/* No incluimos la sección de procesos adicionales ya que el modal es solo para la parte de impresión */}

                    {/* No incluimos la sección de coste total del presupuesto ya que el modal es solo para la parte de impresión */}
                  </>
                )}
              </Paper>
            </Box>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

DigitalCalculationModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  sheetCalculation: PropTypes.object,
  budget: PropTypes.object,
  customMachinePrintTime: PropTypes.number,
  handleCustomMachineTimeChange: PropTypes.func,
  selectedProcesses: PropTypes.array,
  calculatedProcessCost: PropTypes.number
};

export default DigitalCalculationModal;
