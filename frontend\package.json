{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 4005", "build": "node --max-old-space-size=4096 node_modules/vite/bin/vite.js build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.4", "@mui/x-date-pickers": "^8.0.0", "@react-pdf/renderer": "^4.3.0", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "moment": "^2.30.1", "react": "^19.0.0", "react-big-calendar": "^1.18.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.1", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.1.0"}}