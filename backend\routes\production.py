from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Body, HTTPException, Query, status, Depends
from dependencies.auth import get_current_active_user
from models.production import (
    ProductionProcess,
    ProductionProcessCreate,
    ProductionProcessUpdate,
    ProductionProcessType,
    ProductionStatus
)
from utils.logger import log_info, log_error
from utils.production_manager import (
    get_all_production_processes,
    get_production_process_by_id,
    get_production_processes_by_budget_id,
    get_production_processes_by_type,
    get_production_processes_by_status,
    get_production_processes_by_ot_number,
    update_production_process,
    delete_production_process,
    delete_production_processes_by_ot,
    load_production_data,
    save_production_data,
    migrate_to_new_structure
)
from utils.budget_manager import update_budget_status

router = APIRouter(
    prefix="/production",
    tags=["production"],
    responses={404: {"description": "Proceso de producción no encontrado"}}
)

@router.get("/", response_model=List[ProductionProcess])
def get_all_processes(
    budget_id: Optional[str] = None,
    process_type: Optional[str] = None,
    status: Optional[str] = None
):
    """
    Obtiene todos los procesos de producción, con filtros opcionales
    """
    # Si se proporciona budget_id, filtrar por ese presupuesto
    if budget_id:
        return get_production_processes_by_budget_id(budget_id)

    # Si se proporciona process_type, filtrar por ese tipo
    if process_type:
        try:
            process_type_enum = ProductionProcessType(process_type)
            return get_production_processes_by_type(process_type_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Tipo de proceso inválido: {process_type}"
            )

    # Si se proporciona status, filtrar por ese estado
    if status:
        try:
            status_enum = ProductionStatus(status)
            return get_production_processes_by_status(status_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Estado inválido: {status}"
            )

    # Si no se proporciona ningún filtro, devolver todos los procesos
    return get_all_production_processes()

@router.get("/{process_id}", response_model=ProductionProcess)
def get_process(process_id: str):
    """
    Obtiene un proceso de producción por su ID
    """
    process = get_production_process_by_id(process_id)
    if not process:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Proceso de producción con ID {process_id} no encontrado"
        )
    return process

@router.get("/budget/{budget_id}", response_model=List[ProductionProcess])
def get_processes_by_budget(budget_id: str):
    """
    Obtiene todos los procesos de producción asociados a un presupuesto
    """
    return get_production_processes_by_budget_id(budget_id)

@router.get("/ot/{ot_number}", response_model=Dict[str, Any])
def get_processes_by_ot(ot_number: str):
    """
    Obtiene todos los procesos de producción asociados a un número de OT
    y los devuelve en un formato estructurado para generar el PDF de OT

    Args:
        ot_number: Número de OT

    Returns:
        Dict[str, Any]: Datos de la OT con sus procesos
    """
    log_info(f"Solicitud para obtener procesos de la OT: {ot_number}")

    processes = get_production_processes_by_ot_number(ot_number)

    if not processes:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No se encontraron procesos para la OT: {ot_number}"
        )

    # Obtener información básica de la OT del primer proceso
    first_process = processes[0]

    # Crear estructura de respuesta
    ot_data = {
        "ot_id": ot_number,
        "budget_id": first_process.get("budget_id", ""),
        "client_id": first_process.get("client_id", ""),
        "approval_date": first_process.get("created_at", ""),
        "status": "Pendiente",  # Por defecto
        "processes": processes
    }

    # Determinar el estado general de la OT
    if all(p.get("status") == "Completado" for p in processes):
        ot_data["status"] = "Completado"
    elif any(p.get("status") == "En Proceso" for p in processes):
        ot_data["status"] = "En Proceso"
    elif any(p.get("status") == "Cancelado" for p in processes):
        ot_data["status"] = "Cancelado"

    return ot_data

@router.patch("/{process_id}", response_model=ProductionProcess)
def update_process(process_id: str, update_data: ProductionProcessUpdate):
    """
    Actualiza un proceso de producción
    """
    # Convertir el modelo Pydantic a diccionario, excluyendo valores nulos
    update_dict = update_data.dict(exclude_unset=True)

    # Actualizar el proceso
    updated_process = update_production_process(process_id, update_dict)

    if not updated_process:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Proceso de producción con ID {process_id} no encontrado"
        )

    # Si el proceso se ha actualizado a Completado, verificar si todos los procesos de la OT están completados
    if update_dict.get("status") == ProductionStatus.COMPLETED:
        log_info(f"Proceso {process_id} actualizado a Completado. Verificando estado de la OT...")
        ot_number = updated_process["ot_number"] if isinstance(updated_process, dict) else updated_process.ot_number
        if ot_number:
            # Obtener todos los procesos de la OT
            processes = get_production_processes_by_ot_number(ot_number)
            # Verificar si todos están completados o cancelados
            active_processes = [p for p in processes if p.status != ProductionStatus.CANCELLED]
            all_completed = all(p.status == ProductionStatus.COMPLETED for p in active_processes)

            if all_completed and active_processes:
                log_info(f"Todos los procesos activos de la OT {ot_number} están completados. Actualizando estado del presupuesto...")
                # Actualizar el estado del presupuesto
                update_budget_status(ot_number)

    return updated_process

@router.post("/update-budget-status", response_model=Dict)
async def update_budget_status_endpoint(ot_number: str = Body(..., embed=True), new_status: str = Body("Completado", embed=True)):
    """
    Actualiza el estado del presupuesto cuando todos los procesos de una OT estén completados

    Args:
        ot_number: Número de OT del presupuesto a actualizar

    Returns:
        Dict: Resultado de la operación
    """
    log_info(f"Recibida solicitud para actualizar estado del presupuesto para OT: {ot_number} a {new_status}")
    result = update_budget_status(ot_number, new_status)
    return result

@router.delete("/{process_id}", response_model=Dict[str, Any])
def delete_process(process_id: str):
    """
    Elimina un proceso de producción

    Args:
        process_id: ID del proceso a eliminar

    Returns:
        Dict: Mensaje de confirmación
    """
    log_info(f"Solicitud para eliminar proceso de producción: {process_id}")

    # Intentar eliminar el proceso
    result = delete_production_process(process_id)

    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Proceso de producción con ID {process_id} no encontrado"
        )

    return {"message": f"Proceso de producción con ID {process_id} eliminado correctamente"}

@router.post("/assign-machines", response_model=Dict[str, Any])
def assign_machines_to_processes_endpoint(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Asigna máquinas a procesos de acabado existentes que no tienen machine_id.

    Este endpoint ejecuta un script que:
    1. Carga todos los procesos de producción
    2. Carga el catálogo de procesos
    3. Para cada proceso de acabado sin machine_id:
       - Determina el tipo de proceso (Corte, Encuadernación, etc.)
       - Busca el proceso correspondiente en el catálogo
       - Asigna la primera máquina compatible
    4. Guarda los cambios en el archivo de producción

    Returns:
        Dict: Información sobre la operación
    """
    try:
        # Importar el script de asignación de máquinas
        from scripts.assign_machines_to_processes import assign_machines_to_processes

        # Ejecutar la función de asignación
        assign_machines_to_processes()

        return {
            "success": True,
            "message": "Máquinas asignadas correctamente a los procesos de acabado"
        }
    except Exception as e:
        log_error(f"Error al asignar máquinas a procesos: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al asignar máquinas a procesos: {str(e)}"
        )

@router.delete("/ot/{ot_number}", response_model=Dict[str, Any])
def delete_ot_processes(ot_number: str, current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Elimina todos los procesos de producción asociados a un número de OT
    y actualiza el estado del presupuesto correspondiente a "Cancelado"

    Args:
        ot_number: Número de OT cuyos procesos se eliminarán

    Returns:
        Dict: Información sobre la operación
    """
    log_info(f"Solicitud para eliminar todos los procesos de la OT: {ot_number}")

    # Intentar eliminar los procesos de la OT
    result = delete_production_processes_by_ot(ot_number)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=result["message"]
        )

    # Actualizar el estado del presupuesto a "Cancelado"
    log_info(f"Actualizando estado del presupuesto para OT {ot_number} a Cancelado")
    update_result = update_budget_status(ot_number, "Cancelado")

    if not update_result.get("success", False):
        log_error(f"Error al actualizar el estado del presupuesto: {update_result.get('message', '')}")
        # No interrumpimos el flujo si falla la actualización del presupuesto
        result["budget_update"] = {
            "success": False,
            "message": update_result.get('message', 'Error desconocido')
        }
    else:
        result["budget_update"] = {
            "success": True,
            "message": f"Estado del presupuesto para OT {ot_number} actualizado a Cancelado"
        }

    return result

@router.post("/migrate", response_model=Dict[str, Any])
def migrate_production_data(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Migra los datos de producción de la estructura antigua a la nueva estructura jerárquica

    Returns:
        Dict: Información sobre la operación
    """
    log_info("Solicitud para migrar datos de producción a la nueva estructura")

    # Intentar migrar los datos
    result = migrate_to_new_structure()

    return result