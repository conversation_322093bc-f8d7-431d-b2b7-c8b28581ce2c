from fastapi import APIRouter, HTTPException
from typing import List
from models.models import Client, ClientCreate, ClientUpdate
from utils.catalog_manager import load_client_catalog, save_client_catalog

router = APIRouter(
    prefix="/clients",
    tags=["clients"],
    responses={404: {"description": "Cliente no encontrado"}}
)

# Variable global para el catálogo
client_catalog = load_client_catalog()

@router.get("/", response_model=List[Client])
def get_all_clients():
    """
    Obtiene todos los clientes del catálogo
    """
    return client_catalog

@router.get("/{client_id}", response_model=Client)
def get_client_by_id(client_id: str):
    """
    Obtiene un cliente específico por su ID
    """
    for client in client_catalog:
        if client["client_id"] == client_id:
            return client
    raise HTTPException(status_code=404, detail="Cliente no encontrado")

@router.post("/", response_model=Client)
def create_client(client: ClientCreate):
    """
    Crea un nuevo cliente en el catálogo
    """
    # Verificar si ya existe un cliente con el mismo ID
    if any(c["client_id"] == client.client_id for c in client_catalog):
        raise HTTPException(status_code=400, detail="Ya existe un cliente con este ID")
    
    client_dict = client.model_dump()
    client_catalog.append(client_dict)
    save_client_catalog(client_catalog)
    return client_dict

@router.put("/{client_id}", response_model=Client)
def update_client(client_id: str, client_update: ClientUpdate):
    """
    Actualiza un cliente existente en el catálogo
    """
    for client in client_catalog:
        if client["client_id"] == client_id:
            update_data = client_update.model_dump(exclude_unset=True)
            
            # Actualizar campos anidados manteniendo los valores existentes
            if "company" in update_data:
                client["company"].update(update_data.pop("company"))
            if "contact" in update_data:
                client["contact"].update(update_data.pop("contact"))
                
            client.update(update_data)
            client["updated_at"] = "2025-02-19T21:25:53+01:00"  # Actualizar timestamp
            save_client_catalog(client_catalog)
            return client
            
    raise HTTPException(status_code=404, detail="Cliente no encontrado")

@router.delete("/{client_id}")
def delete_client(client_id: str):
    """
    Elimina un cliente del catálogo
    """
    for i, client in enumerate(client_catalog):
        if client["client_id"] == client_id:
            client_catalog.pop(i)
            save_client_catalog(client_catalog)
            return {"message": "Cliente eliminado correctamente"}
            
    raise HTTPException(status_code=404, detail="Cliente no encontrado")
