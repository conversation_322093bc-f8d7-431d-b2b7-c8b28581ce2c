from fastapi import APIRouter, HTTPException, status
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
from models.models import ClickCalculationRequest, ClickCalculationResponse

# Cargar datos
from utils.data_loader import load_papers, load_machines, load_processes, load_consumables

# Importar función para calcular coste de envío
from routes.shipping_cost import calculate_shipping_cost_by_weight

router = APIRouter(
    prefix="/calculations",
    tags=["calculations"],
    responses={404: {"description": "Not found"}}
)

# Descripción del router (no soportada directamente en esta versión de FastAPI)
# "Endpoints para realizar cálculos de costes relacionados con la producción de trabajos de impresión, incluyendo papel, máquinas, planchas, procesos, clicks digitales y envíos"

# Modelo para solicitud de cálculo de coste de papel
class PaperCostRequest(BaseModel):
    paper_id: str
    total_sheets: int



# Modelo para solicitud de cálculo de coste de procesos
class ProcessCostRequest(BaseModel):
    process_id: str
    quantity: int
    unit_type: str  # "hour" o "unit"

# Modelo para solicitud de cálculo de coste de planchas
class PlateCostRequest(BaseModel):
    total_plates: int
    plate_type: Optional[str] = None  # Tipo de plancha (opcional)

# Modelo para solicitud de cálculo de coste total
class TotalCostRequest(BaseModel):
    paper_costs: List[Dict[str, float]]
    machine_costs: List[Dict[str, float]]
    process_costs: List[Dict[str, float]]
    plate_costs: Optional[List[Dict[str, float]]] = None

# Calcular coste de papel
@router.post("/paper-cost", summary="Calcula el coste del papel para un trabajo")
async def calculate_paper_cost(request: PaperCostRequest):
    """
    Calcula el coste del papel basado en el ID del papel y el número total de pliegos.
    Utiliza el precio por 1000 pliegos definido en el catálogo de papeles.

    - **paper_id**: ID del papel en el catálogo
    - **total_sheets**: Número total de pliegos a imprimir

    Retorna el coste del papel, junto con información adicional como el nombre del papel,
    el precio por 1000 pliegos y el número total de pliegos.
    """
    print(f"Recibida solicitud de cálculo de papel: {request}")
    papers = load_papers()
    print(f"Papeles cargados: {len(papers)}")

    # Buscar el papel por ID (puede ser paper_id o product_id)
    paper = next((p for p in papers if p.get("paper_id") == request.paper_id or p.get("product_id") == request.paper_id), None)
    if not paper:
        print(f"Papel con ID {request.paper_id} no encontrado")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Papel con ID {request.paper_id} no encontrado"
        )

    print(f"Papel encontrado: {paper}")

    # Calcular coste (precio por 1000 pliegos / 1000 * total de pliegos)
    price_per_1000 = paper.get("price_per_1000", 0)
    cost = (price_per_1000 / 1000) * request.total_sheets

    result = {
        "paper_id": request.paper_id,
        "paper_name": paper.get("descriptive_name", ""),
        "total_sheets": request.total_sheets,
        "price_per_1000": price_per_1000,
        "cost": round(cost, 2)
    }

    print(f"Resultado del cálculo de papel: {result}")
    return result



# Calcular coste de planchas
@router.post("/plate-cost", summary="Calcula el coste de las planchas para un trabajo offset")
async def calculate_plate_cost(request: PlateCostRequest):
    """
    Calcula el coste de las planchas para un trabajo de impresión offset.
    Utiliza el catálogo de consumibles para obtener el coste unitario de las planchas.

    - **total_plates**: Número total de planchas necesarias
    - **plate_type**: Tipo específico de plancha (opcional)

    Si no se especifica un tipo de plancha, se utilizará la primera plancha disponible en el catálogo.
    Retorna el coste total de las planchas, junto con información sobre el tipo de plancha utilizado.
    """
    print(f"Recibida solicitud de cálculo de planchas: {request}")
    consumables = load_consumables()
    print(f"Consumibles cargados: {len(consumables)}")

    # Filtrar consumibles por tipo "Plancha"
    plates = [c for c in consumables if c["type"] == "Plancha"]
    print(f"Planchas encontradas: {len(plates)}")

    if not plates:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No se encontraron planchas en el catálogo de consumibles"
        )

    # Si se especifica un tipo de plancha, buscar esa plancha específica
    if request.plate_type:
        plate = next((p for p in plates if p["consumable_id"] == request.plate_type), None)
        if not plate:
            # Si no se encuentra la plancha específica, usar la primera disponible
            plate = plates[0]
            print(f"Plancha específica no encontrada, usando: {plate['name']}")
    else:
        # Si no se especifica un tipo, usar la primera plancha disponible
        plate = plates[0]
        print(f"Usando plancha por defecto: {plate['name']}")

    # Calcular coste de las planchas
    cost_per_plate = plate.get("unit_cost", 0)
    total_cost = cost_per_plate * request.total_plates

    result = {
        "plate_id": plate["consumable_id"],
        "plate_name": plate["name"],
        "total_plates": request.total_plates,
        "cost_per_plate": cost_per_plate,
        "cost": round(total_cost, 2)
    }

    print(f"Resultado del cálculo de planchas: {result}")
    return result

# Calcular coste de proceso
@router.post("/process-cost", summary="Calcula el coste de un proceso de acabado")
async def calculate_process_cost(request: ProcessCostRequest):
    """
    Calcula el coste de un proceso de acabado (como encuadernación, troquelado, etc.)
    basado en el tipo de proceso y la cantidad.

    - **process_id**: ID del proceso en el catálogo
    - **quantity**: Cantidad (horas o unidades)
    - **unit_type**: Tipo de unidad ("hour" para horas o "unit" para unidades)

    Dependiendo del tipo de unidad, se utilizará el coste por hora o el coste por unidad
    definido en el catálogo de procesos.
    """
    processes = load_processes()

    # Buscar el proceso por ID (puede ser process_id o product_id)
    process = next((p for p in processes if p.get("process_id") == request.process_id or p.get("product_id") == request.process_id), None)
    if not process:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Proceso con ID {request.process_id} no encontrado"
        )

    # Obtener el coste unitario según el tipo de unidad
    unit_cost = process.get("cost_per_hour" if request.unit_type == "hour" else "cost_per_unit", 0)

    # Calcular coste total
    total_cost = unit_cost * request.quantity

    return {
        "process_id": request.process_id,
        "process_name": process.get("name", ""),
        "quantity": request.quantity,
        "unit_type": request.unit_type,
        "unit_cost": unit_cost,
        "total_cost": round(total_cost, 2)
    }

# Calcular coste total
@router.post("/total-cost", summary="Calcula el coste total de un trabajo combinando todos los costes")
async def calculate_total_cost(request: TotalCostRequest):
    """
    Calcula el coste total de un trabajo sumando los costes de papel, máquina, procesos y planchas.

    - **paper_costs**: Lista de costes de papel
    - **machine_costs**: Lista de costes de máquina
    - **process_costs**: Lista de costes de procesos
    - **plate_costs**: Lista de costes de planchas (opcional)

    Retorna un desglose de los costes por categoría y el coste total del trabajo.
    Útil para obtener un resumen completo de los costes de producción.
    """
    # Sumar todos los costes
    paper_cost = sum(item.get("cost", 0) for item in request.paper_costs)
    machine_cost = sum(item.get("cost", 0) for item in request.machine_costs)
    process_cost = sum(item.get("cost", 0) for item in request.process_costs)

    # Sumar coste de planchas si existe
    plate_cost = 0
    if request.plate_costs:
        plate_cost = sum(item.get("cost", 0) for item in request.plate_costs)

    total_cost = paper_cost + machine_cost + process_cost + plate_cost

    return {
        "paper_cost": round(paper_cost, 2),
        "machine_cost": round(machine_cost, 2),
        "process_cost": round(process_cost, 2),
        "plate_cost": round(plate_cost, 2),
        "total_cost": round(total_cost, 2)
    }

# Calcular todos los costes para una parte
class PartCostRequest(BaseModel):
    paper_id: str
    machine_id: str
    total_sheets: int
    total_plates: Optional[int] = None
    custom_print_time: Optional[float] = None
    color_sides: Optional[int] = None  # Número de caras impresas a color
    bw_sides: Optional[int] = None  # Número de caras impresas en blanco y negro
    processes: List[Dict[str, Any]] = []

@router.post("/part-cost", summary="Calcula el coste total de una parte de un trabajo")
async def calculate_part_cost(request: PartCostRequest):
    """
    Calcula el coste total de una parte de un trabajo (como cubierta o interior),
    combinando los costes de papel, máquina, planchas y procesos.

    - **paper_id**: ID del papel en el catálogo
    - **machine_id**: ID de la máquina en el catálogo
    - **total_sheets**: Número total de pliegos
    - **total_plates**: Número total de planchas (opcional, solo para máquinas offset)
    - **custom_print_time**: Tiempo personalizado de impresión (opcional)
    - **color_sides**: Número de caras impresas a color (opcional)
    - **bw_sides**: Número de caras impresas en blanco y negro (opcional)
    - **processes**: Lista de procesos de acabado (opcional)

    Este endpoint es útil para calcular el coste de cada parte de un trabajo
    (por ejemplo, cubierta e interior de un libro) de forma independiente.
    """
    # Calcular coste de papel
    paper_cost_result = await calculate_paper_cost(PaperCostRequest(
        paper_id=request.paper_id,
        total_sheets=request.total_sheets
    ))

    # NOTA: El cálculo de máquina ahora se debe hacer usando los endpoints V2:
    # /v2/calculate-offset para máquinas offset
    # /v2/calculate-digital para máquinas digitales
    machine_cost_result = {"total_cost": 0}  # Placeholder - usar endpoints V2

    # Verificar si la máquina es digital
    machines = load_machines()
    machine = next((m for m in machines if m.get("machine_id") == request.machine_id or m.get("product_id") == request.machine_id), None)
    is_digital = machine and machine.get("type") == "Digital"

    # Calcular coste de planchas solo si no es una máquina digital y se especifica el número de planchas
    plate_cost_result = None
    if not is_digital and request.total_plates is not None and request.total_plates > 0:
        plate_cost_result = await calculate_plate_cost(PlateCostRequest(
            total_plates=request.total_plates
        ))

    # Calcular costes de procesos
    process_costs = []
    for process in request.processes:
        process_cost_result = await calculate_process_cost(ProcessCostRequest(
            process_id=process.get("process_id"),
            quantity=process.get("quantity", 0),
            unit_type=process.get("unit_type", "unit")
        ))
        process_costs.append(process_cost_result)

    # Calcular coste total
    total_cost = paper_cost_result.get("cost", 0) + machine_cost_result.get("total_cost", 0)

    # Añadir coste de planchas si existe
    if plate_cost_result:
        total_cost += plate_cost_result.get("cost", 0)

    # Añadir costes de procesos
    total_cost += sum(p.get("total_cost", 0) for p in process_costs)

    result = {
        "paper_cost": paper_cost_result,
        "machine_cost": machine_cost_result,
        "process_costs": process_costs,
        "total_cost": round(total_cost, 2)
    }

    # Añadir coste de planchas al resultado si existe
    if plate_cost_result:
        result["plate_cost"] = plate_cost_result

    return result

# Modelo para solicitud de cálculo de coste de envío
class ShippingCostRequest(BaseModel):
    weight_kg: float
    country: str = "España"
    client_id: Optional[str] = None

# Calcular clicks para máquinas digitales
@router.post("/digital-clicks", response_model=ClickCalculationResponse, summary="Calcula el número de clicks y coste para máquinas digitales")
async def calculate_digital_clicks(request: ClickCalculationRequest):
    """
    Calcula el número de clicks (impresiones) y el coste para máquinas de impresión digital.

    - **machine_id**: ID de la máquina digital en el catálogo
    - **sheets**: Número de pliegos por copia
    - **copies**: Número de copias a imprimir
    - **is_duplex**: Indica si la impresión es a doble cara (true) o a una cara (false)
    - **is_color**: Indica si la impresión es a color (true) o en blanco y negro (false)

    Parámetros adicionales para cálculo más preciso:
    - **paper_id**: ID del papel en el catálogo
    - **folding_scheme**: Esquema de plegado (ej: "F4-1")
    - **pages_per_sheet**: Páginas por pliego según el esquema
    - **page_width_mm**: Ancho de la página en mm
    - **page_height_mm**: Alto de la página en mm
    - **sheet_width_mm**: Ancho del pliego en mm
    - **sheet_height_mm**: Alto del pliego en mm

    Para impresiones a doble cara (duplex), cada pliego cuenta como 2 clicks.
    Para impresiones a una cara (simplex), cada pliego cuenta como 1 click.
    El coste se calcula multiplicando el número total de clicks por el coste unitario
    del click (que varía según sea a color o en blanco y negro).
    """
    print(f"Recibida solicitud de cálculo de clicks: {request}")

    # Cargar máquinas
    machines = load_machines()

    # Buscar la máquina por ID
    machine = next((m for m in machines if m.get("machine_id") == request.machine_id or m.get("product_id") == request.machine_id), None)
    if not machine:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Máquina con ID {request.machine_id} no encontrada"
        )

    # Verificar que la máquina es digital
    if machine.get("type") != "Digital":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"La máquina con ID {request.machine_id} no es una máquina digital"
        )

    # Calcular el número total de pliegos
    total_sheets = request.sheets * request.copies

    # Determinar el número de clicks por pliego (1 para simplex, 2 para duplex)
    clicks_per_sheet = 2 if request.is_duplex else 1

    # Calcular el número total de clicks
    total_clicks = total_sheets * clicks_per_sheet

    # Obtener el coste por click según si es color o B/N
    click_unit_cost = machine.get("click_color_cost", 0.15) if request.is_color else machine.get("click_bw_cost", 0.05)

    # Calcular el coste total
    total_cost = total_clicks * click_unit_cost

    # Variables para información del papel
    paper_id = None
    paper_name = None
    sheet_width_mm = None
    sheet_height_mm = None

    # Si se proporciona paper_id, obtener información del papel
    if request.paper_id:
        papers = load_papers()
        paper = next((p for p in papers if p.get("paper_id") == request.paper_id), None)
        if paper:
            paper_id = paper.get("paper_id")
            paper_name = paper.get("descriptive_name")
            sheet_width_mm = paper.get("dimension_width")
            sheet_height_mm = paper.get("dimension_height")

    # Si no se obtuvo información del papel por ID, usar los valores proporcionados directamente
    if not sheet_width_mm and request.sheet_width_mm:
        sheet_width_mm = request.sheet_width_mm
    if not sheet_height_mm and request.sheet_height_mm:
        sheet_height_mm = request.sheet_height_mm

    # Si aún no tenemos dimensiones, usar las dimensiones máximas de la máquina
    if not sheet_width_mm or not sheet_height_mm:
        sheet_width_mm = machine.get("max_width", 330)  # Ancho máximo en mm (por defecto SRA3)
        sheet_height_mm = machine.get("max_height", 488)  # Alto máximo en mm (por defecto SRA3)

    # Dimensiones de página
    page_width_mm = request.page_width_mm or 210  # Por defecto A4
    page_height_mm = request.page_height_mm or 297  # Por defecto A4

    print(f"Dimensiones del pliego: {sheet_width_mm}x{sheet_height_mm}mm")
    print(f"Dimensiones de la página: {page_width_mm}x{page_height_mm}mm")

    # Determinar páginas por pliego basado en el esquema de plegado o calcular
    pages_per_sheet = None
    folding_scheme = None

    if request.folding_scheme and request.pages_per_sheet:
        # Usar la información proporcionada
        folding_scheme = request.folding_scheme
        pages_per_sheet = request.pages_per_sheet
        print(f"Usando esquema de plegado proporcionado: {folding_scheme} con {pages_per_sheet} páginas por pliego")
    else:
        # Calcular cuántas páginas caben en el pliego
        # Verificar si las dimensiones son suficientes para múltiples páginas por cara
        # Consideramos diferentes orientaciones (vertical/horizontal)

        # Calcular cuántas páginas caben a lo ancho y alto del pliego
        pages_width = int(sheet_width_mm / page_width_mm)
        pages_height = int(sheet_height_mm / page_height_mm)

        # Probar con orientación rotada
        pages_width_rotated = int(sheet_width_mm / page_height_mm)
        pages_height_rotated = int(sheet_height_mm / page_width_mm)

        # Tomar la mejor combinación
        if pages_width * pages_height > pages_width_rotated * pages_height_rotated:
            pages_per_sheet = pages_width * pages_height
            print(f"Caben {pages_width}x{pages_height} páginas por cara (orientación normal)")
        else:
            pages_per_sheet = pages_width_rotated * pages_height_rotated
            print(f"Caben {pages_width_rotated}x{pages_height_rotated} páginas por cara (orientación rotada)")

        # Si no cabe ninguna página completa, asumimos al menos 1
        if pages_per_sheet < 1:
            pages_per_sheet = 1
            print("ADVERTENCIA: Las dimensiones del pliego son menores que la página, asumiendo 1 página por cara")

    # Calcular el total de páginas (no pliegos)
    total_pages = total_sheets * pages_per_sheet
    if request.is_duplex:
        total_pages *= 2  # Multiplicar por 2 si es dúplex (páginas en ambas caras)

    print(f"Total de páginas: {total_pages}")

    # Obtener la velocidad de la máquina en A4/minuto
    print_speed = machine.get("speed", 100)  # Velocidad por defecto: 100 A4/minuto

    # Calcular el tiempo estimado basado en el número de páginas y la velocidad
    # Ajustamos la velocidad según el tamaño de página (si no es A4)
    page_size_factor = 1.0  # Factor por defecto para A4

    # Si la página es significativamente diferente de A4, ajustar el factor
    a4_area = 210 * 297  # Área de A4 en mm²
    page_area = page_width_mm * page_height_mm  # Área de la página actual en mm²

    if page_area > 0:
        page_size_factor = a4_area / page_area

    # Velocidad ajustada según el tamaño de página
    adjusted_speed = print_speed * page_size_factor

    print(f"Velocidad de la máquina: {print_speed} A4/min, Factor de ajuste: {page_size_factor}, Velocidad ajustada: {adjusted_speed} páginas/min")

    # Calcular el tiempo estimado en minutos
    estimated_time_minutes = total_pages / adjusted_speed if adjusted_speed > 0 else 0

    # Convertir a horas
    estimated_time_hours = estimated_time_minutes / 60

    print(f"Tiempo estimado: {estimated_time_minutes} minutos ({estimated_time_hours} horas)")

    # Preparar la respuesta
    result = ClickCalculationResponse(
        machine_id=request.machine_id,
        machine_name=machine.get("name", ""),
        sheets=request.sheets,
        copies=request.copies,
        total_sheets=total_sheets,
        clicks_per_sheet=clicks_per_sheet,
        total_clicks=total_clicks,
        is_color=request.is_color,
        is_duplex=request.is_duplex,
        click_unit_cost=click_unit_cost,
        total_cost=round(total_cost, 2),

        # Información del papel
        paper_id=paper_id,
        paper_name=paper_name,
        sheet_width_mm=sheet_width_mm,
        sheet_height_mm=sheet_height_mm,

        # Información del esquema de plegado
        folding_scheme=folding_scheme,
        pages_per_sheet=pages_per_sheet,

        # Información del cálculo de tiempo
        a4_per_sheet=pages_per_sheet,  # Usamos pages_per_sheet en lugar de a4_per_sheet
        print_speed=print_speed,
        total_pages=total_pages,
        estimated_time_minutes=round(estimated_time_minutes, 2),
        estimated_time_hours=round(estimated_time_hours, 2)
    )

    print(f"Resultado del cálculo de clicks: {result}")
    return result

# Calcular coste de envío
@router.post("/shipping-cost", summary="Calcula el coste de envío basado en el peso y el país de destino")
async def calculate_shipping_cost(request: ShippingCostRequest):
    """
    Calcula el coste de envío de un trabajo basado en el peso total y el país de destino.

    - **weight_kg**: Peso total del envío en kilogramos
    - **country**: País de destino (por defecto: "España")
    - **client_id**: ID del cliente (opcional, para obtener el país automáticamente)

    El coste de envío se calcula según una tabla de tarifas basada en el peso.
    Para envíos internacionales, se aplica un factor de distancia adicional.
    Si se proporciona un client_id, se utilizará el país asociado a ese cliente.
    """
    print(f"Recibida solicitud de cálculo de envío: {request}")

    # Validar el peso
    if request.weight_kg <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="El peso debe ser mayor que 0"
        )

    # Si se proporciona un client_id, obtener el país del cliente
    country = request.country
    if request.client_id:
        from utils.data_loader import load_clients
        clients = load_clients()
        client = next((c for c in clients if c.get("client_id") == request.client_id), None)
        
        # El país está anidado en la estructura company.address.country
        if client and client.get("company") and client["company"].get("address") and client["company"]["address"].get("country"):
            country = client["company"]["address"]["country"]
            print(f"País del cliente {request.client_id}: {country}")
            
            # Corregir codificación para países con caracteres especiales si es necesario
            if country == "EspaÃ±a":
                country = "España"
                
            print(f"País normalizado: {country}")

    # Calcular el coste de envío
    shipping_cost = calculate_shipping_cost_by_weight(request.weight_kg, country)

    # Determinar el factor de distancia
    distance_factor = 1.0
    if country != "España":
        distance_factor = 2.5

    result = {
        "weight_kg": request.weight_kg,
        "country": country,
        "shipping_cost": round(shipping_cost, 2),
        "distance_factor": distance_factor
    }

    # Mostrar el resultado en formato JSON para mejor visualización
    import json
    print("\nResultado del cálculo de envío (JSON):")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    print()
    return result
