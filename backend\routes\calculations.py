from fastapi import APIRouter, HTTPException, status
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
# from models.models import ClickCalculationRequest, ClickCalculationResponse  # Eliminado - no se usa

# Cargar datos
from utils.data_loader import load_papers, load_processes

# Importar función para calcular coste de envío
from routes.shipping_cost import calculate_shipping_cost_by_weight

router = APIRouter(
    prefix="/calculations",
    tags=["calculations"],
    responses={404: {"description": "Not found"}}
)

# Descripción del router (no soportada directamente en esta versión de FastAPI)
# "Endpoints para realizar cálculos de costes relacionados con la producción de trabajos de impresión, incluyendo papel, máquinas, planchas, procesos, clicks digitales y envíos"

# Modelo para solicitud de cálculo de coste de papel
class PaperCostRequest(BaseModel):
    paper_id: str
    total_sheets: int



# Modelo para solicitud de cálculo de coste de procesos
class ProcessCostRequest(BaseModel):
    process_id: str
    quantity: int
    unit_type: str  # "hour" o "unit"



# Modelo para solicitud de cálculo de coste total - ELIMINADO
# Usar endpoints V2 que incluyen cálculo completo de costes

# Calcular coste de papel
@router.post("/paper-cost", summary="Calcula el coste del papel para un trabajo")
async def calculate_paper_cost(request: PaperCostRequest):
    """
    Calcula el coste del papel basado en el ID del papel y el número total de pliegos.
    Utiliza el precio por 1000 pliegos definido en el catálogo de papeles.

    - **paper_id**: ID del papel en el catálogo
    - **total_sheets**: Número total de pliegos a imprimir

    Retorna el coste del papel, junto con información adicional como el nombre del papel,
    el precio por 1000 pliegos y el número total de pliegos.
    """
    print(f"Recibida solicitud de cálculo de papel: {request}")
    papers = load_papers()
    print(f"Papeles cargados: {len(papers)}")

    # Buscar el papel por ID (puede ser paper_id o product_id)
    paper = next((p for p in papers if p.get("paper_id") == request.paper_id or p.get("product_id") == request.paper_id), None)
    if not paper:
        print(f"Papel con ID {request.paper_id} no encontrado")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Papel con ID {request.paper_id} no encontrado"
        )

    print(f"Papel encontrado: {paper}")

    # Calcular coste (precio por 1000 pliegos / 1000 * total de pliegos)
    price_per_1000 = paper.get("price_per_1000", 0)
    cost = (price_per_1000 / 1000) * request.total_sheets

    result = {
        "paper_id": request.paper_id,
        "paper_name": paper.get("descriptive_name", ""),
        "total_sheets": request.total_sheets,
        "price_per_1000": price_per_1000,
        "cost": round(cost, 2)
    }

    print(f"Resultado del cálculo de papel: {result}")
    return result





# Calcular coste de proceso
@router.post("/process-cost", summary="Calcula el coste de un proceso de acabado")
async def calculate_process_cost(request: ProcessCostRequest):
    """
    Calcula el coste de un proceso de acabado (como encuadernación, troquelado, etc.)
    basado en el tipo de proceso y la cantidad.

    - **process_id**: ID del proceso en el catálogo
    - **quantity**: Cantidad (horas o unidades)
    - **unit_type**: Tipo de unidad ("hour" para horas o "unit" para unidades)

    Dependiendo del tipo de unidad, se utilizará el coste por hora o el coste por unidad
    definido en el catálogo de procesos.
    """
    processes = load_processes()

    # Buscar el proceso por ID (puede ser process_id o product_id)
    process = next((p for p in processes if p.get("process_id") == request.process_id or p.get("product_id") == request.process_id), None)
    if not process:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Proceso con ID {request.process_id} no encontrado"
        )

    # Obtener el coste unitario según el tipo de unidad
    unit_cost = process.get("cost_per_hour" if request.unit_type == "hour" else "cost_per_unit", 0)

    # Calcular coste total
    total_cost = unit_cost * request.quantity

    return {
        "process_id": request.process_id,
        "process_name": process.get("name", ""),
        "quantity": request.quantity,
        "unit_type": request.unit_type,
        "unit_cost": unit_cost,
        "total_cost": round(total_cost, 2)
    }



# Calcular todos los costes para una parte
class PartCostRequest(BaseModel):
    paper_id: str
    machine_id: str
    total_sheets: int
    total_plates: Optional[int] = None
    custom_print_time: Optional[float] = None
    color_sides: Optional[int] = None  # Número de caras impresas a color
    bw_sides: Optional[int] = None  # Número de caras impresas en blanco y negro
    processes: List[Dict[str, Any]] = []

@router.post("/part-cost", summary="Calcula el coste total de una parte de un trabajo")
async def calculate_part_cost(request: PartCostRequest):
    """
    Calcula el coste total de una parte de un trabajo (como cubierta o interior),
    combinando los costes de papel, máquina, planchas y procesos.

    - **paper_id**: ID del papel en el catálogo
    - **machine_id**: ID de la máquina en el catálogo
    - **total_sheets**: Número total de pliegos
    - **total_plates**: Número total de planchas (opcional, solo para máquinas offset)
    - **custom_print_time**: Tiempo personalizado de impresión (opcional)
    - **color_sides**: Número de caras impresas a color (opcional)
    - **bw_sides**: Número de caras impresas en blanco y negro (opcional)
    - **processes**: Lista de procesos de acabado (opcional)

    Este endpoint es útil para calcular el coste de cada parte de un trabajo
    (por ejemplo, cubierta e interior de un libro) de forma independiente.
    """
    # Calcular coste de papel
    paper_cost_result = await calculate_paper_cost(PaperCostRequest(
        paper_id=request.paper_id,
        total_sheets=request.total_sheets
    ))

    # NOTA: El cálculo de máquina ahora se debe hacer usando los endpoints V2:
    # /v2/calculate-offset para máquinas offset
    # /v2/calculate-digital para máquinas digitales
    machine_cost_result = {"total_cost": 0}  # Placeholder - usar endpoints V2

    # Coste de planchas eliminado - usar endpoints V2 que incluyen cálculo de planchas
    plate_cost_result = None

    # Calcular costes de procesos
    process_costs = []
    for process in request.processes:
        process_cost_result = await calculate_process_cost(ProcessCostRequest(
            process_id=process.get("process_id"),
            quantity=process.get("quantity", 0),
            unit_type=process.get("unit_type", "unit")
        ))
        process_costs.append(process_cost_result)

    # Calcular coste total
    total_cost = paper_cost_result.get("cost", 0) + machine_cost_result.get("total_cost", 0)

    # Añadir coste de planchas si existe
    if plate_cost_result:
        total_cost += plate_cost_result.get("cost", 0)

    # Añadir costes de procesos
    total_cost += sum(p.get("total_cost", 0) for p in process_costs)

    result = {
        "paper_cost": paper_cost_result,
        "machine_cost": machine_cost_result,
        "process_costs": process_costs,
        "total_cost": round(total_cost, 2)
    }

    # Añadir coste de planchas al resultado si existe
    if plate_cost_result:
        result["plate_cost"] = plate_cost_result

    return result

# Modelo para solicitud de cálculo de coste de envío
class ShippingCostRequest(BaseModel):
    weight_kg: float
    country: str = "España"
    client_id: Optional[str] = None



# Calcular coste de envío
@router.post("/shipping-cost", summary="Calcula el coste de envío basado en el peso y el país de destino")
async def calculate_shipping_cost(request: ShippingCostRequest):
    """
    Calcula el coste de envío de un trabajo basado en el peso total y el país de destino.

    - **weight_kg**: Peso total del envío en kilogramos
    - **country**: País de destino (por defecto: "España")
    - **client_id**: ID del cliente (opcional, para obtener el país automáticamente)

    El coste de envío se calcula según una tabla de tarifas basada en el peso.
    Para envíos internacionales, se aplica un factor de distancia adicional.
    Si se proporciona un client_id, se utilizará el país asociado a ese cliente.
    """
    print(f"Recibida solicitud de cálculo de envío: {request}")

    # Validar el peso
    if request.weight_kg <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="El peso debe ser mayor que 0"
        )

    # Si se proporciona un client_id, obtener el país del cliente
    country = request.country
    if request.client_id:
        from utils.data_loader import load_clients
        clients = load_clients()
        client = next((c for c in clients if c.get("client_id") == request.client_id), None)
        
        # El país está anidado en la estructura company.address.country
        if client and client.get("company") and client["company"].get("address") and client["company"]["address"].get("country"):
            country = client["company"]["address"]["country"]
            print(f"País del cliente {request.client_id}: {country}")
            
            # Corregir codificación para países con caracteres especiales si es necesario
            if country == "EspaÃ±a":
                country = "España"
                
            print(f"País normalizado: {country}")

    # Calcular el coste de envío
    shipping_cost = calculate_shipping_cost_by_weight(request.weight_kg, country)

    # Determinar el factor de distancia
    distance_factor = 1.0
    if country != "España":
        distance_factor = 2.5

    result = {
        "weight_kg": request.weight_kg,
        "country": country,
        "shipping_cost": round(shipping_cost, 2),
        "distance_factor": distance_factor
    }

    # Mostrar el resultado en formato JSON para mejor visualización
    import json
    print("\nResultado del cálculo de envío (JSON):")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    print()
    return result
