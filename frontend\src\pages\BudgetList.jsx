import { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { buildApiUrl } from '../config';
import { useAuth } from '../contexts/AuthContext';
import BudgetDetailsDialog from '../components/BudgetDetailsDialog';
import ConfigService from '../services/ConfigService';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Menu,
  MenuItem,
  Chip,
  TextField,
  InputAdornment,
  Box,
  Grid,
  Snackbar,
  Alert,
  FormControl,
  InputLabel,
  Select,
  Divider,
  Switch,
  FormControlLabel
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FilterListIcon from '@mui/icons-material/FilterList';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DataObjectIcon from '@mui/icons-material/DataObject';
import JDFGenerator from '../components/JDFGenerator';
import BudgetPDF from '../components/BudgetPDF';
import BudgetJsonViewer from '../components/BudgetJsonViewer';
import DescriptionTooltip from '../components/DescriptionTooltip';
import ClientInfoTooltip from '../components/ClientInfoTooltip';
// JDFSchemaService ya no es necesario

// URL de la API importada desde config.js

// Estados disponibles para los presupuestos
const BUDGET_STATUS = [
  { value: 'Pendiente', color: 'warning' },
  { value: 'Actualizado', color: 'secondary' },
  { value: 'Aprobado', color: 'success' },
  { value: 'Enviado', color: 'primary' },
  { value: 'Rechazado', color: 'error' },
  { value: 'Completado', color: 'info' },
  { value: 'Facturado', color: 'default' }
];

// Tipos de trabajo disponibles
const JOB_TYPES = [
  'Folleto',
  'Libro',
  'Revista',
  'Catálogo',
  'Tarjeta',
  'Cartel',
  'Otro'
];

const BudgetList = ({ onEditBudget }) => {
  // Obtener el token de autenticación
  const { token } = useAuth();

  // Estados para los datos
  const [budgets, setBudgets] = useState([]);
  const [filteredBudgets, setFilteredBudgets] = useState([]);
  const [clients, setClients] = useState([]);

  // Referencias para controlar las llamadas a la API
  const fetchedBudgetsRef = useRef(false);
  const fetchedClientsRef = useRef(false);

  // Estado para los filtros
  const [filters, setFilters] = useState({
    client: '',
    status: '',
    jobType: '',
    searchTerm: ''
  });

  // Estado para el diálogo de confirmación de eliminación
  const [deleteDialog, setDeleteDialog] = useState({
    open: false,
    budgetId: null
  });

  // Estado para el diálogo de PDF
  const [pdfDialog, setPdfDialog] = useState({
    open: false,
    budgetId: null
  });

  // Estado para el diálogo de generación de JSON JDF
  const [jdfDialog, setJdfDialog] = useState({
    open: false,
    budgetId: null,
    budget: null
  });

  // Estado para el diálogo de visualización de JSON
  const [jsonDialog, setJsonDialog] = useState({
    open: false,
    budgetId: null
  });

  // Estado para el diálogo de detalles del presupuesto
  const [detailsDialog, setDetailsDialog] = useState({
    open: false,
    budgetId: null,
    budget: null
  });

  // Estado para el diálogo de confirmación de duplicación
  const [duplicateDialog, setDuplicateDialog] = useState({
    open: false,
    budgetId: null
  });

  // Estado para el snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });



  // Estado para el menú de cambio de estado
  const [statusMenu, setStatusMenu] = useState({
    open: false,
    anchorEl: null,
    budgetId: null,
    currentStatus: ''
  });

  // Estado para mostrar u ocultar los filtros
  const [showFilters, setShowFilters] = useState(false);

  // Estado para ocultar presupuestos facturados (activado por defecto)
  const [hideBilled, setHideBilled] = useState(true);

  // Estados para la paginación
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Estado para la configuración del sistema
  const [config, setConfig] = useState(null);

  // Función para mostrar mensajes en el snackbar
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // Función para cerrar el snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Función para abrir el menú de cambio de estado
  const handleOpenStatusMenu = (event, budgetId, currentStatus) => {
    event.stopPropagation();
    setStatusMenu({
      open: true,
      anchorEl: event.currentTarget,
      budgetId,
      currentStatus
    });
  };

  // Función para cerrar el menú de cambio de estado
  const handleCloseStatusMenu = () => {
    setStatusMenu({
      open: false,
      anchorEl: null,
      budgetId: null,
      currentStatus: ''
    });
  };

  // Función para cambiar el estado de un presupuesto desde el menú
  const handleChangeStatus = (newStatus) => {
    if (statusMenu.budgetId && newStatus !== statusMenu.currentStatus) {
      updateBudgetStatus(statusMenu.budgetId, newStatus);
    }
    handleCloseStatusMenu();
  };

  // Función para obtener los presupuestos desde la API
  const fetchBudgets = useCallback(async () => {
    try {
      // Primero obtenemos la lista básica de presupuestos
      const response = await fetch(buildApiUrl('/budgets'), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!response.ok) throw new Error('Error al obtener los presupuestos');
      const basicBudgetsList = await response.json();
      
      // Ahora obtenemos los detalles completos de cada presupuesto
      const detailedBudgets = [];
      for (const budget of basicBudgetsList) {
        try {
          const detailResponse = await fetch(buildApiUrl(`/budgets/${budget.budget_id}`), {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          if (detailResponse.ok) {
            const detailedBudget = await detailResponse.json();
            detailedBudgets.push(detailedBudget);
          } else {
            // Si falla, usamos el presupuesto básico
            detailedBudgets.push(budget);
          }
        } catch (detailErr) {
          console.error(`Error obteniendo detalles para ${budget.budget_id}:`, detailErr);
          detailedBudgets.push(budget);
        }
      }
      
      console.log('Presupuestos detallados cargados:', detailedBudgets);
      // Verificar la estructura del created_by en los presupuestos
      detailedBudgets.forEach(budget => {
        console.log(`Presupuesto ${budget.budget_id} - created_by:`, budget.created_by);
      });
      
      setBudgets(detailedBudgets);
    } catch (err) {
      console.error('Error fetching budgets:', err);
      showSnackbar(err.message, 'error');
    }
  }, [token]);

  // Función para obtener los clientes desde la API
  const fetchClients = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/clients'), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!response.ok) throw new Error('Error al obtener los clientes');
      const data = await response.json();
      setClients(data.filter(client => client.active));
    } catch (err) {
      console.error('Error fetching clients:', err);
      showSnackbar(err.message, 'error');
    }
  }, [token]);

  // Función para obtener la configuración del sistema
  const fetchConfig = useCallback(async () => {
    try {
      const configData = await ConfigService.getConfig();
      setConfig(configData);
    } catch (err) {
      console.error('Error fetching config:', err);
      // Usar valores por defecto si no se puede obtener la configuración
      setConfig({ profit_percentage: 30 });
    }
  }, []);

  // Función para filtrar presupuestos según los criterios de filtro
  const filterBudgets = useCallback(() => {
    if (!budgets) return;

    let filteredResults = [...budgets];

    // Ocultar presupuestos facturados si está activado el switch
    if (hideBilled) {
      filteredResults = filteredResults.filter(budget => budget.status !== 'Facturado');
    }

    // Filtrar por cliente
    if (filters.client && filters.client !== '') {
      filteredResults = filteredResults.filter(budget =>
        budget.client_id === filters.client
      );
    }

    // Filtrar por estado
    if (filters.status && filters.status !== '') {
      filteredResults = filteredResults.filter(budget =>
        budget.status === filters.status
      );
    }

    // Filtrar por tipo de trabajo
    if (filters.jobType && filters.jobType !== '') {
      filteredResults = filteredResults.filter(budget =>
        budget.job_type === filters.jobType
      );
    }

    // Filtrar por término de búsqueda (en descripción o número de OT)
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filteredResults = filteredResults.filter(budget =>
        budget.description.toLowerCase().includes(searchLower) ||
        budget.ot_number.toLowerCase().includes(searchLower)
      );
    }

    // Ordenar los presupuestos por fecha de creación (los más nuevos primero)
    filteredResults.sort((a, b) => {
      const dateA = new Date(a.created_at || 0);
      const dateB = new Date(b.created_at || 0);
      return dateB - dateA; // Orden descendente (más nuevos primero)
    });

    setFilteredBudgets(filteredResults);
    setPage(0); // Resetear a la primera página cuando cambian los filtros
  }, [budgets, filters, hideBilled]);

  // Función para manejar cambios en los filtros
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters({
      ...filters,
      [name]: value
    });
  };

  // Función para resetear los filtros
  const resetFilters = () => {
    setFilters({
      client: '',
      status: '',
      jobType: '',
      searchTerm: ''
    });
    setPage(0); // Resetear a la primera página cuando se limpian los filtros
  };

  // Función para manejar el cambio de página
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Función para manejar el cambio de filas por página
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Función para abrir el diálogo de confirmación de eliminación
  const handleOpenDeleteDialog = (budgetId) => {
    setDeleteDialog({
      open: true,
      budgetId
    });
  };

  // Función para cerrar el diálogo de confirmación de eliminación
  const handleCloseDeleteDialog = () => {
    setDeleteDialog({
      open: false,
      budgetId: null
    });
  };

  // Función para abrir el diálogo de PDF
  const handleOpenPdfDialog = (budgetId) => {
    setPdfDialog({
      open: true,
      budgetId
    });
  };

  // Función para cerrar el diálogo de PDF
  const handleClosePdfDialog = () => {
    setPdfDialog({
      open: false,
      budgetId: null
    });
  };

  // Función para abrir el diálogo de generación de JSON JDF
  // La función handleOpenJdfDialog ha sido eliminada ya que no se utiliza

  // Función para cerrar el diálogo de generación de JSON JDF
  const handleCloseJdfDialog = () => {
    setJdfDialog({
      open: false,
      budgetId: null,
      budget: null
    });
  };

  // Función para abrir el diálogo de visualización de JSON
  const handleOpenJsonViewer = (budgetId) => {
    setJsonDialog({
      open: true,
      budgetId
    });
  };

  // Función para cerrar el diálogo de visualización de JSON
  const handleCloseJsonViewer = () => {
    setJsonDialog({
      open: false,
      budgetId: null
    });
  };

  // La función handleGenerateJDFSchema ha sido eliminada ya que no se utiliza



  // Función para abrir el diálogo de detalles del presupuesto
  const handleOpenDetailsDialog = async (budgetId) => {
    try {
      // Obtener los datos completos del presupuesto
      const response = await fetch(buildApiUrl(`/budgets/${budgetId}`), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!response.ok) throw new Error('Error al obtener los detalles del presupuesto');
      const budgetData = await response.json();

      setDetailsDialog({
        open: true,
        budgetId,
        budget: budgetData
      });
    } catch (err) {
      console.error('Error fetching budget details:', err);
      showSnackbar(err.message, 'error');
    }
  };

  // Función para cerrar el diálogo de detalles del presupuesto
  const handleCloseDetailsDialog = () => {
    setDetailsDialog({
      open: false,
      budgetId: null,
      budget: null
    });
  };

  // Función para abrir el diálogo de confirmación de duplicación
  const handleOpenDuplicateDialog = (budgetId) => {
    setDuplicateDialog({
      open: true,
      budgetId
    });
  };

  // Función para cerrar el diálogo de confirmación de duplicación
  const handleCloseDuplicateDialog = () => {
    setDuplicateDialog({
      open: false,
      budgetId: null
    });
  };

  // Función para eliminar un presupuesto
  const deleteBudget = async () => {
    try {
      const response = await fetch(buildApiUrl(`/budgets/${deleteDialog.budgetId}`), {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) throw new Error('Error al eliminar el presupuesto');

      // Actualizar la lista de presupuestos
      setBudgets(budgets.filter(budget => budget.budget_id !== deleteDialog.budgetId));
      showSnackbar('Presupuesto eliminado correctamente');
    } catch (err) {
      console.error('Error deleting budget:', err);
      showSnackbar(err.message, 'error');
    } finally {
      handleCloseDeleteDialog();
    }
  };

  // Función para duplicar un presupuesto
  const duplicateBudget = async () => {
    try {
      const response = await fetch(buildApiUrl(`/budgets/${duplicateDialog.budgetId}/duplicate`), {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) throw new Error('Error al duplicar el presupuesto');

      const duplicatedBudget = await response.json();

      // Actualizar la lista de presupuestos
      await fetchBudgets();
      showSnackbar(`Presupuesto duplicado correctamente con OT: ${duplicatedBudget.ot_number}`);
    } catch (err) {
      console.error('Error duplicating budget:', err);
      showSnackbar(err.message, 'error');
    } finally {
      handleCloseDuplicateDialog();
    }
  };

  // Función para actualizar el estado de un presupuesto
  const updateBudgetStatus = async (budgetId, newStatus) => {
    try {
      // Encontrar el presupuesto a actualizar
      const budgetToUpdate = budgets.find(budget => budget.budget_id === budgetId);
      if (!budgetToUpdate) return;

      // Crear una copia con el nuevo estado
      const updatedBudget = {
        ...budgetToUpdate,
        status: newStatus
      };

      // Enviar la actualización a la API
      const response = await fetch(buildApiUrl(`/budgets/${budgetId}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updatedBudget)
      });

      if (!response.ok) throw new Error('Error al actualizar el estado del presupuesto');

      // Actualizar la lista de presupuestos
      const data = await response.json();
      setBudgets(budgets.map(budget =>
        budget.budget_id === budgetId ? data : budget
      ));

      showSnackbar(`Estado actualizado a "${newStatus}"`);
    } catch (err) {
      console.error('Error updating budget status:', err);
      showSnackbar(err.message, 'error');
    }
  };

  // Función para obtener el cliente a partir de su ID
  const getClient = (clientId) => {
    return clients.find(c => c.client_id === clientId) || null;
  };

  // Función para formatear la fecha
  const formatDate = (dateString) => {
    if (!dateString) return 'Fecha no disponible';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch {
      return 'Fecha inválida';
    }
  };

  // Obtener el coste total de un presupuesto
  const getBudgetTotalCost = (budget) => {
    // Verificar si tenemos la estructura de costes y devolver el total
    if (budget && budget.costs && budget.costs.summary && typeof budget.costs.summary.total === 'number') {
      return budget.costs.summary.total;
    }
    
    // Para presupuestos existentes, usar total_cost como fallback
    if (budget && typeof budget.total_cost === 'number') {
      return budget.total_cost;
    }
    
    // Si no existe ninguna estructura anterior, calcular la suma de los costes de todas las partes
    if (budget && Array.isArray(budget.parts) && budget.parts.length > 0) {
      // Sumar los costes de todas las partes
      const partsCost = budget.parts.reduce((sum, part) => {
        return sum + (typeof part.total_cost === 'number' ? part.total_cost : 0);
      }, 0);
      
      // Añadir el coste de los procesos adicionales si existe
      const processCost = budget.process_total_cost || 0;
      
      // Añadir el coste de envío si existe
      const shippingCost = budget.shipping_cost || 0;
      
      return partsCost + processCost + shippingCost;
    }
    
    // Si no existe ninguna estructura de costes, devolver 0
    return 0;
  };

  // Calcular el precio de lista (costo + porcentaje de beneficio)
  const calculateListPrice = (cost) => {
    if (!config) return cost;
    const profitPercentage = config.profit_percentage || 30; // Valor por defecto: 30%
    return cost * (1 + profitPercentage / 100);
  };

  // Calcular el PVP (precio de lista - descuento del cliente)
  const calculatePVP = (listPrice, budget) => {
    if (!budget) return listPrice;

    // Primero intentar obtener el descuento de client_data en el presupuesto (datos históricos)
    if (budget.client_data && budget.client_data.discount_percentage !== undefined) {
      const discountPercentage = budget.client_data.discount_percentage || 0;
      return listPrice * (1 - discountPercentage / 100);
    }

    // Si no hay client_data, buscar el cliente actual
    if (!budget.client_id) return listPrice;
    const client = clients.find(c => c.client_id === budget.client_id);
    if (!client) return listPrice;

    const discountPercentage = client.discount_percentage || 0;
    return listPrice * (1 - discountPercentage / 100);
  };

  // Efecto para cargar los presupuestos, clientes y configuración al montar el componente
  useEffect(() => {
    if (!fetchedBudgetsRef.current) {
      fetchBudgets();
      fetchedBudgetsRef.current = true;
    }

    if (!fetchedClientsRef.current) {
      fetchClients();
      fetchedClientsRef.current = true;
    }

    // Cargar la configuración del sistema
    fetchConfig();
  }, [fetchBudgets, fetchClients, fetchConfig]);

  // Efecto para filtrar los presupuestos cuando cambian los filtros o el estado de hideBilled
  useEffect(() => {
    filterBudgets();
  }, [filterBudgets, hideBilled]);

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="h4" sx={{ mr: 2 }}>
            Presupuestos
          </Typography>

          <FormControlLabel
            control={
              <Switch
                checked={hideBilled}
                onChange={(e) => setHideBilled(e.target.checked)}
                color="primary"
              />
            }
            label="Ocultar facturados"
            sx={{ ml: 2 }}
          />
        </Box>

        <Box>
          <Button
            variant="contained"
            color="primary"
            onClick={() => onEditBudget(null)}
            sx={{ mr: 1 }}
          >
            Nuevo Presupuesto
          </Button>

          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? 'Ocultar Filtros' : 'Mostrar Filtros'}
          </Button>
        </Box>
      </Box>

      {/* Sección de filtros */}
      {showFilters && (
        <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                name="searchTerm"
                label="Buscar"
                variant="outlined"
                value={filters.searchTerm}
                onChange={handleFilterChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Cliente</InputLabel>
                <Select
                  name="client"
                  value={filters.client}
                  onChange={handleFilterChange}
                  label="Cliente"
                >
                  <MenuItem value="">Todos</MenuItem>
                  {clients.map(client => (
                    <MenuItem key={client.client_id} value={client.client_id}>
                      {client.company?.name || 'Cliente sin nombre'}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Estado</InputLabel>
                <Select
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  label="Estado"
                >
                  <MenuItem value="">Todos</MenuItem>
                  {BUDGET_STATUS.map(status => (
                    <MenuItem key={status.value} value={status.value}>
                      {status.value}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Tipo de Trabajo</InputLabel>
                <Select
                  name="jobType"
                  value={filters.jobType}
                  onChange={handleFilterChange}
                  label="Tipo de Trabajo"
                >
                  <MenuItem value="">Todos</MenuItem>
                  {JOB_TYPES.map(type => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                onClick={resetFilters}
              >
                Limpiar Filtros
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Tabla de presupuestos */}
      <TableContainer component={Paper} elevation={1} sx={{ width: '100%', overflowX: 'auto', boxSizing: 'border-box' }}>
        <Table sx={{ minWidth: 650 }}>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>OT</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold', width: '25%' }}>Descripción</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Cliente</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Tipo</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Cantidad</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Coste Total</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>PVP</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Estado</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Usuario</TableCell>
              <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Fecha</TableCell>
              <TableCell align="center" sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredBudgets.length > 0 ? (
              filteredBudgets
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map(budget => (
                <TableRow key={budget.budget_id}>
                  <TableCell sx={{ fontSize: '0.85rem' }}>{budget.ot_number.replace('OT-', '')}</TableCell>
                  <TableCell sx={{ fontSize: '0.85rem', maxWidth: '300px' }}>
                    <DescriptionTooltip description={budget.description} />
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.85rem' }}>
                    <ClientInfoTooltip client={getClient(budget.client_id) || budget.client_data} />
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.85rem' }}>{budget.job_type}</TableCell>
                  <TableCell sx={{ fontSize: '0.85rem', whiteSpace: 'nowrap' }}>{budget.quantity.toLocaleString()}</TableCell>
                  <TableCell sx={{ fontSize: '0.85rem' }}>
                    {new Intl.NumberFormat('es-ES', {
                      style: 'currency',
                      currency: 'EUR'
                    }).format(getBudgetTotalCost(budget))}
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
                    {(() => {
                      // Obtener el coste total usando la función centralizada
                      const totalCost = getBudgetTotalCost(budget);

                      // Calcular el precio de lista
                      const listPrice = calculateListPrice(totalCost);

                      // Calcular el PVP usando los datos del presupuesto
                      const pvp = calculatePVP(listPrice, budget);

                      return pvp > 0 ? (
                        <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
                          {new Intl.NumberFormat('es-ES', {
                            style: 'currency',
                            currency: 'EUR'
                          }).format(pvp)}
                        </Typography>
                      ) : (
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
                          No calculado
                        </Typography>
                      );
                    })()}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Haz clic para cambiar el estado">
                      <Chip
                        label={budget.status}
                        color={BUDGET_STATUS.find(s => s.value === budget.status)?.color || 'default'}
                        size="small"
                        sx={{
                          fontSize: '0.8rem',
                          cursor: 'pointer',
                          fontWeight: 'bold',
                          '&:hover': {
                            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                            transform: 'translateY(-1px)'
                          },
                          transition: 'all 0.2s'
                        }}
                        onClick={(e) => handleOpenStatusMenu(e, budget.budget_id, budget.status)}
                      />
                    </Tooltip>
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.85rem' }}>
                    {budget.created_by?.username || 'No disponible'}
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.85rem' }}>{formatDate(budget.created_at)}</TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                      <Tooltip title="Ver detalles">
                        <span>
                          <IconButton
                            color="info"
                            size="small"
                            onClick={() => handleOpenDetailsDialog(budget.budget_id)}
                            sx={{ padding: '4px' }}
                          >
                            <VisibilityIcon sx={{ fontSize: '1rem' }} />
                          </IconButton>
                        </span>
                      </Tooltip>

                      <Tooltip title="Editar">
                        <span>
                          <IconButton
                            color="primary"
                            size="small"
                            onClick={() => onEditBudget(budget.budget_id)}
                            sx={{ padding: '4px' }}
                          >
                            <EditIcon sx={{ fontSize: '1rem' }} />
                          </IconButton>
                        </span>
                      </Tooltip>

                      <Tooltip title="Duplicar presupuesto">
                        <span>
                          <IconButton
                            color="success"
                            size="small"
                            onClick={() => handleOpenDuplicateDialog(budget.budget_id)}
                            sx={{ padding: '4px' }}
                          >
                            <ContentCopyIcon sx={{ fontSize: '1rem' }} />
                          </IconButton>
                        </span>
                      </Tooltip>

                      <Tooltip title="Generar PDF">
                        <span>
                          <IconButton
                            color="secondary"
                            size="small"
                            onClick={() => handleOpenPdfDialog(budget.budget_id)}
                            sx={{ padding: '4px' }}
                          >
                            <PictureAsPdfIcon sx={{ fontSize: '1rem' }} />
                          </IconButton>
                        </span>
                      </Tooltip>

                      <Tooltip title="Ver JSON">
                        <span>
                          <IconButton
                            color="info"
                            size="small"
                            onClick={() => handleOpenJsonViewer(budget.budget_id)}
                            sx={{ padding: '4px' }}
                          >
                            <DataObjectIcon sx={{ fontSize: '1rem' }} />
                          </IconButton>
                        </span>
                      </Tooltip>

                      <Tooltip title="Eliminar">
                        <span>
                          <IconButton
                            color="error"
                            size="small"
                            onClick={() => handleOpenDeleteDialog(budget.budget_id)}
                            sx={{ padding: '4px' }}
                          >
                            <DeleteIcon sx={{ fontSize: '1rem' }} />
                          </IconButton>
                        </span>
                      </Tooltip>


                    </Box>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={10} align="center">
                  <Typography variant="body1" sx={{ py: 2, fontSize: '0.9rem' }}>
                    No se encontraron presupuestos
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Paginación */}
      <TablePagination
        rowsPerPageOptions={[5, 10, 15, 25, 50]}
        component="div"
        count={filteredBudgets.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Filas por página:"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
      />

      {/* Diálogo de confirmación para eliminar presupuesto */}
      <Dialog
        open={deleteDialog.open}
        onClose={handleCloseDeleteDialog}
      >
        <DialogTitle>Confirmar eliminación</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Estás seguro de que deseas eliminar este presupuesto? Esta acción no se puede deshacer.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Cancelar
          </Button>
          <Button onClick={deleteBudget} color="error">
            Eliminar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para mostrar el PDF */}
      {pdfDialog.open && (
        <BudgetPDF
          budgetId={pdfDialog.budgetId}
          onClose={handleClosePdfDialog}
        />
      )}

      {/* Diálogo para generar JSON JDF */}
      <Dialog
        open={jdfDialog.open}
        onClose={handleCloseJdfDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Generador de JDF</DialogTitle>
        <DialogContent>
          {jdfDialog.budget && (
            <JDFGenerator
              budget={jdfDialog.budget}
              onClose={handleCloseJdfDialog}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseJdfDialog}>Cerrar</Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para visualizar el JSON */}
      {jsonDialog.open && (
        <BudgetJsonViewer
          budgetId={jsonDialog.budgetId}
          onClose={handleCloseJsonViewer}
        />
      )}

      {/* Diálogo de detalles del presupuesto */}
      <BudgetDetailsDialog
        open={detailsDialog.open}
        budget={detailsDialog.budget}
        onClose={handleCloseDetailsDialog}
        onGeneratePdf={(budgetId) => handleOpenPdfDialog(budgetId)}
        onStatusChange={(budgetId, newStatus) => updateBudgetStatus(budgetId, newStatus)}
        showSnackbar={showSnackbar}
      />

      {/* Diálogo de confirmación de duplicación */}
      <Dialog
        open={duplicateDialog.open}
        onClose={handleCloseDuplicateDialog}
      >
        <DialogTitle>Duplicar Presupuesto</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Estás seguro de que deseas duplicar este presupuesto? Se creará una copia con un nuevo número de OT.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDuplicateDialog}>Cancelar</Button>
          <Button
            onClick={duplicateBudget}
            color="primary"
            variant="contained"
            startIcon={<ContentCopyIcon />}
          >
            Duplicar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Menú de cambio de estado */}
      <Menu
        anchorEl={statusMenu.anchorEl}
        open={statusMenu.open}
        onClose={handleCloseStatusMenu}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 180,
            overflow: 'visible',
            mt: 1.5,
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              left: '50%',
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translate(-50%, -50%) rotate(45deg)',
              zIndex: 0,
            },
          },
        }}
      >
        <Typography variant="subtitle2" sx={{ px: 2, py: 1, fontWeight: 'bold', color: 'primary.main' }}>
          Cambiar estado a:
        </Typography>
        <Divider sx={{ mb: 1 }} />
        {BUDGET_STATUS.map((status) => (
          <MenuItem
            key={status.value}
            onClick={() => handleChangeStatus(status.value)}
            selected={status.value === statusMenu.currentStatus}
            sx={{
              fontSize: '0.9rem',
              minWidth: 150,
              my: 0.5,
              mx: 1,
              borderRadius: 1,
              '&.Mui-selected': {
                backgroundColor: `${status.color}.light`,
              },
              '&:hover': {
                backgroundColor: `${status.color}.light`,
              }
            }}
          >
            <Chip
              label={status.value}
              color={status.color}
              size="small"
              sx={{
                fontSize: '0.8rem',
                minWidth: 100,
                fontWeight: 'bold',
                py: 0.5
              }}
            />
          </MenuItem>
        ))}
      </Menu>

      {/* Snackbar para mensajes */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

// Validación de props
BudgetList.propTypes = {
  onEditBudget: PropTypes.func
};

// Props por defecto
BudgetList.defaultProps = {
  onEditBudget: () => {}
};

export default BudgetList;
