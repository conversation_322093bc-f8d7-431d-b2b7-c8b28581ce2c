from pathlib import Path
import json
from fastapi import HTTPException

# Rutas de los catálogos
CATALOG_PATH = Path("config/paper_catalog.json")
MACHINE_CATALOG_PATH = Path("config/machine_catalog.json")
CLIENT_CATALOG_PATH = Path("config/client_catalog.json")
PROCESS_CATALOG_PATH = Path("config/process_catalog.json")
PRODUCT_CATALOG_PATH = Path("config/product_catalog.json")

# Funciones para el catálogo de papeles
def load_paper_catalog():
    try:
        if CATALOG_PATH.exists():
            with open(CATALOG_PATH, "r", encoding="utf-8") as f:
                return json.load(f)
        # Si el archivo no existe, crear uno vacío
        save_paper_catalog([])
        return []
    except Exception as e:
        print(f"Error loading paper catalog: {str(e)}")
        return []

def save_paper_catalog(catalog):
    try:
        CATALOG_PATH.parent.mkdir(parents=True, exist_ok=True)
        with open(CATALOG_PATH, "w", encoding="utf-8") as f:
            json.dump(catalog, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving paper catalog: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error al guardar el catálogo: {str(e)}"
        )

# Funciones para el catálogo de máquinas
def load_machine_catalog():
    if MACHINE_CATALOG_PATH.exists():
        with open(MACHINE_CATALOG_PATH, "r", encoding="utf-8") as f:
            return json.load(f)
    return []

# Alias para mantener compatibilidad
load_machines = load_machine_catalog

def save_machine_catalog(catalog):
    MACHINE_CATALOG_PATH.parent.mkdir(parents=True, exist_ok=True)
    with open(MACHINE_CATALOG_PATH, "w", encoding="utf-8") as f:
        json.dump(catalog, f, indent=2, ensure_ascii=False)

# Funciones para el catálogo de clientes
def load_client_catalog():
    try:
        if CLIENT_CATALOG_PATH.exists():
            with open(CLIENT_CATALOG_PATH, "r", encoding="utf-8") as f:
                return json.load(f)
        # Si el archivo no existe, crear uno vacío
        save_client_catalog([])
        return []
    except Exception as e:
        print(f"Error loading client catalog: {str(e)}")
        return []

def save_client_catalog(catalog):
    try:
        CLIENT_CATALOG_PATH.parent.mkdir(parents=True, exist_ok=True)
        with open(CLIENT_CATALOG_PATH, "w", encoding="utf-8") as f:
            json.dump(catalog, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving client catalog: {str(e)}")

# Funciones para el catálogo de procesos
def load_process_catalog():
    try:
        if PROCESS_CATALOG_PATH.exists():
            with open(PROCESS_CATALOG_PATH, "r", encoding="utf-8") as f:
                return json.load(f)
        # Si el archivo no existe, crear uno vacío
        save_process_catalog([])
        return []
    except Exception as e:
        print(f"Error loading process catalog: {str(e)}")
        return []

def save_process_catalog(catalog):
    try:
        PROCESS_CATALOG_PATH.parent.mkdir(parents=True, exist_ok=True)
        with open(PROCESS_CATALOG_PATH, "w", encoding="utf-8") as f:
            json.dump(catalog, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving process catalog: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error al guardar el catálogo de procesos: {str(e)}"
        )

# Funciones para el catálogo de productos
def load_product_catalog():
    try:
        if PRODUCT_CATALOG_PATH.exists():
            with open(PRODUCT_CATALOG_PATH, "r", encoding="utf-8") as f:
                return json.load(f)
        # Si el archivo no existe, crear uno vacío
        save_product_catalog([])
        return []
    except Exception as e:
        print(f"Error loading product catalog: {str(e)}")
        return []

def save_product_catalog(catalog):
    try:
        PRODUCT_CATALOG_PATH.parent.mkdir(parents=True, exist_ok=True)
        with open(PRODUCT_CATALOG_PATH, "w", encoding="utf-8") as f:
            json.dump(catalog, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving product catalog: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error al guardar el catálogo de productos: {str(e)}"
        )
