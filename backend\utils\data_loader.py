import json
import os
from typing import List, Dict, Any

# Rutas a los archivos de datos
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
CONFIG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config")

# Archivos en la carpeta data
BUDGETS_FILE = os.path.join(DATA_DIR, "budgets.json")

# Archivos en la carpeta config
PAPERS_FILE = os.path.join(CONFIG_DIR, "paper_catalog.json")
MACHINES_FILE = os.path.join(CONFIG_DIR, "machine_catalog.json")
CLIENTS_FILE = os.path.join(CONFIG_DIR, "client_catalog.json")
PROCESSES_FILE = os.path.join(CONFIG_DIR, "process_catalog.json")
PRODUCTS_FILE = os.path.join(CONFIG_DIR, "product_catalog.json")
CONSUMABLES_FILE = os.path.join(CONFIG_DIR, "consumibles_catalog.json")

def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Carga un archivo JSON y devuelve su contenido.
    Si el archivo no existe, devuelve una lista vacía.
    """
    try:
        if os.path.exists(file_path):
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        else:
            print(f"Archivo no encontrado: {file_path}")
            return []
    except Exception as e:
        print(f"Error al cargar el archivo {file_path}: {e}")
        return []

def save_json_file(file_path: str, data: List[Dict[str, Any]]) -> None:
    """
    Guarda datos en un archivo JSON.
    """
    try:
        # Asegurarse de que el directorio existe
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
    except Exception as e:
        print(f"Error al guardar el archivo {file_path}: {e}")

# Funciones para cargar datos específicos
def load_papers() -> List[Dict[str, Any]]:
    return load_json_file(PAPERS_FILE)

def load_machines() -> List[Dict[str, Any]]:
    return load_json_file(MACHINES_FILE)

def load_clients() -> List[Dict[str, Any]]:
    return load_json_file(CLIENTS_FILE)

def load_budgets() -> List[Dict[str, Any]]:
    return load_json_file(BUDGETS_FILE)

def load_processes() -> List[Dict[str, Any]]:
    return load_json_file(PROCESSES_FILE)

def load_products() -> List[Dict[str, Any]]:
    return load_json_file(PRODUCTS_FILE)

def load_consumables() -> List[Dict[str, Any]]:
    return load_json_file(CONSUMABLES_FILE)

# Funciones para guardar datos específicos
def save_papers(data: List[Dict[str, Any]]) -> None:
    save_json_file(PAPERS_FILE, data)

def save_machines(data: List[Dict[str, Any]]) -> None:
    save_json_file(MACHINES_FILE, data)

def save_clients(data: List[Dict[str, Any]]) -> None:
    save_json_file(CLIENTS_FILE, data)

def save_budgets(data: List[Dict[str, Any]]) -> None:
    save_json_file(BUDGETS_FILE, data)

def save_processes(data: List[Dict[str, Any]]) -> None:
    save_json_file(PROCESSES_FILE, data)

def save_products(data: List[Dict[str, Any]]) -> None:
    save_json_file(PRODUCTS_FILE, data)

def save_consumables(data: List[Dict[str, Any]]) -> None:
    save_json_file(CONSUMABLES_FILE, data)
