import React, { useState } from 'react';
import { Box, Paper, Typography, useTheme, ToggleButton, ToggleButtonGroup } from '@mui/material';

/**
 * Componente para visualizar gráficamente un pliego con sus páginas, marcas y áreas de desperdicio
 */
const SheetPreview = ({ 
  anchoPliego, 
  altoPliego, 
  anchoPagina, 
  altoPagina, 
  paginasAncho, 
  paginasAlto, 
  sangrado = 3, 
  pinzas = 10, 
  margenLateral = 5, 
  margenSuperior = 5, 
  margenInferior = 5, 
  marcasRegistro = true, 
  tirasControl = true,
  esTiraRetira = false,
  sheetType = 'Flat',
  pageLayout = null,
  esquemaNombre = '',
  medianil = margenLateral * 2,  // Calle o medianil entre dípticos (margenLateral + margenLateral)
}) => {
  const theme = useTheme();
  
  // Estado para controlar si se muestra la cara o el dorso en esquemas tira-retira
  const [lado, setLado] = useState('cara');
  
  // Para esquemas tipo F (fold), usamos directamente 2x2 para F8-7
  let paginasAnchoAjustado, paginasAltoAjustado;
  
  if (esTiraRetira || sheetType === 'WorkAndTurn') {
    // Para el esquema F8-7 (8 páginas), la disposición es exactamente 2x2 por cara
    if (paginasAncho === 4 && paginasAlto === 2) {
      // Este es el caso del F8-7
      paginasAnchoAjustado = 2;
      paginasAltoAjustado = 2;
    } else {
      // Para otros esquemas tira-retira, dividimos las páginas entre cara y dorso
      paginasAnchoAjustado = paginasAncho;
      paginasAltoAjustado = paginasAlto;
      
      if (paginasAncho > paginasAlto) {
        paginasAnchoAjustado = Math.floor(paginasAncho / 2);
      } else {
        paginasAltoAjustado = Math.floor(paginasAlto / 2);
      }
    }
  } else {
    // Para esquemas normales (no tira-retira), usamos los valores originales
    paginasAnchoAjustado = paginasAncho;
    paginasAltoAjustado = paginasAlto;
  }
  
  // Manejar cambio entre cara y dorso
  const handleLadoChange = (event, nuevoLado) => {
    if (nuevoLado !== null) {
      setLado(nuevoLado);
    }
  };
  
  // Calculamos la escala para que el pliego se ajuste al contenedor
  const maxWidth = 500;
  const maxHeight = 400;
  const escalaAncho = maxWidth / anchoPliego;
  const escalaAlto = maxHeight / altoPliego;
  const escala = Math.min(escalaAncho, escalaAlto, 1); // Limitamos la escala a 1:1 como máximo
  
  // Dimensiones escaladas
  const anchoEscalado = anchoPliego * escala;
  const altoEscalado = altoPliego * escala;
  const anchoPaginaEscalado = anchoPagina * escala;
  const altoPaginaEscalado = altoPagina * escala;
  const sangradoEscalado = sangrado * escala;
  const pinzasEscalado = pinzas * escala;
  const margenLateralEscalado = margenLateral * escala;
  const margenSuperiorEscalado = margenSuperior * escala;
  const margenInferiorEscalado = margenInferior * escala;
  const medianilEscalado = medianil * escala;
  
  // Determinamos si es un esquema tipo F (fold) basado en el nombre
  const esEsquemaTipoF = esquemaNombre && esquemaNombre.startsWith('F');
  
  // Calculamos el espacio disponible dentro del pliego (descontando márgenes)
  const anchoDisponible = anchoEscalado - (margenLateralEscalado * 2);
  const altoDisponible = altoEscalado - margenSuperiorEscalado - margenInferiorEscalado - pinzasEscalado;
  
  // Calculamos el espacio entre páginas
  // Para esquemas tipo F, no hay espacio entre páginas que van unidas por el lomo,
  // pero sí hay un medianil (calle) entre dípticos
  let espacioHorizontal, espacioVertical;
  
  if (esEsquemaTipoF) {
    // Para esquemas tipo F, calculamos el espacio disponible teniendo en cuenta:
    // - No hay espacio entre páginas de un mismo díptico (unidas por el lomo)
    // - Hay un medianil (calle) de 10mm entre dípticos (5mm + 5mm)
    // - Mantenemos espacio en los bordes
    
    // Calculamos cuántos dípticos hay horizontalmente (cada díptico tiene 2 páginas)
    const dipticosHorizontales = Math.ceil(paginasAnchoAjustado / 2);
    // Espacio total ocupado por páginas
    const espacioPaginasHorizontal = anchoPaginaEscalado * paginasAnchoAjustado;
    // Espacio total ocupado por medianiles entre dípticos
    const espacioMedianilesHorizontal = medianilEscalado * (dipticosHorizontales > 1 ? dipticosHorizontales - 1 : 0);
    // Espacio restante para los bordes
    const espacioBordesHorizontal = anchoDisponible - espacioPaginasHorizontal - espacioMedianilesHorizontal;
    // Dividimos el espacio de los bordes entre los dos lados
    espacioHorizontal = Math.max(0, espacioBordesHorizontal / 2);
    
    // Hacemos lo mismo para el eje vertical
    const dipticosVerticales = Math.ceil(paginasAltoAjustado / 2);
    const espacioPaginasVertical = altoPaginaEscalado * paginasAltoAjustado;
    const espacioMedianilesVertical = medianilEscalado * (dipticosVerticales > 1 ? dipticosVerticales - 1 : 0);
    const espacioBordesVertical = altoDisponible - espacioPaginasVertical - espacioMedianilesVertical;
    espacioVertical = Math.max(0, espacioBordesVertical / 2);
  } else {
    // Para otros tipos de esquemas, distribuimos el espacio uniformemente
    espacioHorizontal = (anchoDisponible - (anchoPaginaEscalado * paginasAnchoAjustado)) / (paginasAnchoAjustado + 1);
    espacioVertical = (altoDisponible - (altoPaginaEscalado * paginasAltoAjustado)) / (paginasAltoAjustado + 1);
  }
  
  // Generamos las páginas
  const paginas = [];
  for (let fila = 0; fila < paginasAltoAjustado; fila++) {
    for (let col = 0; col < paginasAnchoAjustado; col++) {
      let x, y;
      
      if (esEsquemaTipoF) {
        // Para esquemas tipo F, las páginas van unidas de 2 en 2 por el lomo
        // Calculamos la posición teniendo en cuenta:
        // - Hay un margen lateral para cada página
        // - El medianil (calle) entre dípticos está formado por los márgenes laterales de las páginas adyacentes
        
        // Identificamos a qué díptico pertenece la página horizontalmente
        const dipticoHorizontal = Math.floor(col / 2);
        // Identificamos la posición dentro del díptico (0 = izquierda, 1 = derecha)
        const posicionEnDiptico = col % 2;
        
        // Calculamos la posición X teniendo en cuenta:
        // - Espacio horizontal (distribuido en los bordes)
        // - Posición del díptico
        // - Posición dentro del díptico
        // - Margen lateral para cada página
        
        // Espacio base (desde el borde izquierdo del pliego hasta la primera página)
        let xBase = margenLateralEscalado + espacioHorizontal;
        
        // Espacio adicional según el díptico y la posición en el díptico
        if (dipticoHorizontal === 0) {
          // Primer díptico
          if (posicionEnDiptico === 0) {
            // Primera página del primer díptico (más a la izquierda)
            x = xBase;
          } else {
            // Segunda página del primer díptico
            x = xBase + anchoPaginaEscalado;
          }
        } else {
          // Segundo díptico o posteriores
          // Calculamos el espacio ocupado por los dípticos anteriores
          const espacioDipticosAnteriores = dipticoHorizontal * (anchoPaginaEscalado * 2);
          // Añadimos el espacio del medianil (margenLateral * 2 para cada medianil)
          const espacioMedianiles = dipticoHorizontal * medianilEscalado;
          
          if (posicionEnDiptico === 0) {
            // Primera página del díptico (izquierda)
            x = xBase + espacioDipticosAnteriores + espacioMedianiles;
          } else {
            // Segunda página del díptico (derecha)
            x = xBase + espacioDipticosAnteriores + espacioMedianiles + anchoPaginaEscalado;
          }
        }
        
        // Hacemos lo mismo para el eje vertical
        const dipticoVertical = Math.floor(fila / 2);
        const posicionVerticalEnDiptico = fila % 2;
        
        // Espacio base (desde el borde superior del pliego hasta la primera página)
        let yBase = margenSuperiorEscalado + pinzasEscalado + espacioVertical;
        
        // Espacio adicional según el díptico vertical y la posición en el díptico
        if (dipticoVertical === 0) {
          // Primer díptico vertical
          if (posicionVerticalEnDiptico === 0) {
            // Primera página del primer díptico vertical (arriba)
            y = yBase;
          } else {
            // Segunda página del primer díptico vertical (abajo)
            y = yBase + altoPaginaEscalado;
          }
        } else {
          // Segundo díptico vertical o posteriores
          // Calculamos el espacio ocupado por los dípticos anteriores
          const espacioDipticosVerticalesAnteriores = dipticoVertical * (altoPaginaEscalado * 2);
          // Añadimos el espacio del medianil (margenLateral * 2 para cada medianil)
          const espacioMedianilesVerticales = dipticoVertical * medianilEscalado;
          
          if (posicionVerticalEnDiptico === 0) {
            // Primera página del díptico vertical (arriba)
            y = yBase + espacioDipticosVerticalesAnteriores + espacioMedianilesVerticales;
          } else {
            // Segunda página del díptico vertical (abajo)
            y = yBase + espacioDipticosVerticalesAnteriores + espacioMedianilesVerticales + altoPaginaEscalado;
          }
        }
      } else {
        // Para otros tipos de esquemas, usamos el espaciado uniforme
        x = margenLateralEscalado + espacioHorizontal + col * (anchoPaginaEscalado + espacioHorizontal);
        y = margenSuperiorEscalado + pinzasEscalado + espacioVertical + fila * (altoPaginaEscalado + espacioVertical);
      }
      
      // Calcular el número de página
      let numeroPagina;
      
      // Si tenemos la información de page_layout del esquema, la usamos
      if (pageLayout && lado) {
        try {
          // Obtener la matriz de páginas para el lado actual ("front" o "back")
          const ladoKey = lado === 'cara' ? 'front' : 'back';
          if (pageLayout[ladoKey] && 
              pageLayout[ladoKey][fila] && 
              typeof pageLayout[ladoKey][fila][col] !== 'undefined') {
            numeroPagina = pageLayout[ladoKey][fila][col];
          } else {
            // Si no encontramos la página en el layout, usamos el cálculo por defecto
            const paginasPorCara = paginasAnchoAjustado * paginasAltoAjustado;
            if (lado === 'cara') {
              numeroPagina = fila * paginasAnchoAjustado + col + 1;
            } else {
              numeroPagina = paginasPorCara + (paginasAltoAjustado - fila - 1) * paginasAnchoAjustado + (paginasAnchoAjustado - col - 1) + 1;
            }
          }
        } catch (error) {
          console.error('Error al obtener numeración de página desde page_layout:', error);
          numeroPagina = fila * paginasAnchoAjustado + col + 1;
        }
      } else {
        // Si no tenemos page_layout, usamos el cálculo por defecto
        const paginasPorCara = paginasAnchoAjustado * paginasAltoAjustado;
        
        if (esTiraRetira || sheetType === 'WorkAndTurn') {
          if (lado === 'cara') {
            numeroPagina = fila * paginasAnchoAjustado + col + 1;
          } else {
            // En el dorso, las páginas se numeran de forma inversa para tira-retira
            numeroPagina = paginasPorCara + (paginasAltoAjustado - fila - 1) * paginasAnchoAjustado + (paginasAnchoAjustado - col - 1) + 1;
          }
        } else {
          numeroPagina = fila * paginasAnchoAjustado + col + 1;
        }
      }
      
      paginas.push(
        <Box
          key={`pagina-${fila}-${col}`}
          sx={{
            position: 'absolute',
            left: x - sangradoEscalado,
            top: y - sangradoEscalado,
            width: anchoPaginaEscalado + (sangradoEscalado * 2),
            height: altoPaginaEscalado + (sangradoEscalado * 2),
            bgcolor: theme.palette.grey[200],
            border: `${sangradoEscalado}px solid ${theme.palette.warning.light}`,
            boxSizing: 'content-box',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {numeroPagina}
          </Typography>
        </Box>
      );
    }
  }
  
  // Generamos los márgenes laterales de las páginas que forman los medianiles
  const margenes = [];
  if (esEsquemaTipoF) {
    // Recorremos todas las páginas para mostrar sus márgenes laterales
    for (let fila = 0; fila < paginasAltoAjustado; fila++) {
      for (let col = 0; col < paginasAnchoAjustado; col++) {
        // Identificamos a qué díptico pertenece la página horizontalmente
        const dipticoHorizontal = Math.floor(col / 2);
        // Identificamos la posición dentro del díptico (0 = izquierda, 1 = derecha)
        const posicionEnDiptico = col % 2;
        
        // Calculamos la posición base de la página (igual que en el cálculo de páginas)
        let xBase = margenLateralEscalado + espacioHorizontal;
        let x;
        
        if (dipticoHorizontal === 0) {
          // Primer díptico
          if (posicionEnDiptico === 0) {
            // Primera página del primer díptico (más a la izquierda)
            x = xBase;
          } else {
            // Segunda página del primer díptico
            x = xBase + anchoPaginaEscalado;
          }
        } else {
          // Segundo díptico o posteriores
          const espacioDipticosAnteriores = dipticoHorizontal * (anchoPaginaEscalado * 2);
          const espacioMedianiles = dipticoHorizontal * medianilEscalado;
          
          if (posicionEnDiptico === 0) {
            // Primera página del díptico (izquierda)
            x = xBase + espacioDipticosAnteriores + espacioMedianiles;
          } else {
            // Segunda página del díptico (derecha)
            x = xBase + espacioDipticosAnteriores + espacioMedianiles + anchoPaginaEscalado;
          }
        }
        
        // Calculamos la posición Y de la página
        const dipticoVertical = Math.floor(fila / 2);
        const posicionVerticalEnDiptico = fila % 2;
        let yBase = margenSuperiorEscalado + pinzasEscalado + espacioVertical;
        let y;
        
        if (dipticoVertical === 0) {
          if (posicionVerticalEnDiptico === 0) {
            y = yBase;
          } else {
            y = yBase + altoPaginaEscalado;
          }
        } else {
          const espacioDipticosVerticalesAnteriores = dipticoVertical * (altoPaginaEscalado * 2);
          const espacioMedianilesVerticales = dipticoVertical * medianilEscalado;
          
          if (posicionVerticalEnDiptico === 0) {
            y = yBase + espacioDipticosVerticalesAnteriores + espacioMedianilesVerticales;
          } else {
            y = yBase + espacioDipticosVerticalesAnteriores + espacioMedianilesVerticales + altoPaginaEscalado;
          }
        }
        
        // Margen izquierdo de la página (excepto si es la primera página de un díptico)
        const margenLateralEscaladoMitad = margenLateralEscalado / 2;
        
        if (posicionEnDiptico === 1) { // Es la segunda página de un díptico (tiene margen izquierdo)
          margenes.push(
            <Box
              key={`margen-izq-${fila}-${col}`}
              sx={{
                position: 'absolute',
                left: x - margenLateralEscaladoMitad,
                top: y,
                width: margenLateralEscaladoMitad,
                height: altoPaginaEscalado,
                bgcolor: 'rgba(255, 165, 0, 0.2)',  // Color naranja semi-transparente
                border: '1px dashed rgba(255, 165, 0, 0.5)',
                borderRight: 'none',
                zIndex: 1,  // Para que aparezca por encima de las páginas
              }}
            />
          );
        }
        
        // Margen derecho de la página (excepto si es la segunda página de un díptico)
        if (posicionEnDiptico === 0) { // Es la primera página de un díptico (tiene margen derecho)
          margenes.push(
            <Box
              key={`margen-der-${fila}-${col}`}
              sx={{
                position: 'absolute',
                left: x + anchoPaginaEscalado,
                top: y,
                width: margenLateralEscaladoMitad,
                height: altoPaginaEscalado,
                bgcolor: 'rgba(255, 165, 0, 0.2)',  // Color naranja semi-transparente
                border: '1px dashed rgba(255, 165, 0, 0.5)',
                borderLeft: 'none',
                zIndex: 1,  // Para que aparezca por encima de las páginas
              }}
            />
          );
        }
        
        // Añadimos etiquetas de texto para los medianiles horizontales
        if (posicionEnDiptico === 0 && col < paginasAnchoAjustado - 1 && (col + 1) % 2 === 1) {
          const xMedianil = x + anchoPaginaEscalado + margenLateralEscaladoMitad / 2;
          margenes.push(
            <Typography
              key={`texto-medianil-h-${fila}-${col}`}
              variant="caption"
              color="orange"
              sx={{
                position: 'absolute',
                left: xMedianil,
                top: y + altoPaginaEscalado / 2,
                transform: 'rotate(90deg)',
                whiteSpace: 'nowrap',
                zIndex: 2,
              }}
            >
              {margenLateral}+{margenLateral}={medianil}mm
            </Typography>
          );
        }
      }
    }
  }
  }
  
  // Área de pinzas
  const areaPinzas = pinzas > 0 ? (
    <Box
      key="pinzas"
      sx={{
        position: 'absolute',
        left: 0,
        top: 0,
        width: anchoEscalado,
        height: pinzasEscalado,
        bgcolor: 'rgba(0, 128, 255, 0.2)',
        border: '1px dashed rgba(0, 128, 255, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1, // Para que aparezca por encima de las páginas
      }}
    >
      <Typography variant="caption" color="primary">
        Pinzas ({pinzas} mm)
      </Typography>
    </Box>
  ) : null;
  
  // Marcas de registro
  const marcas = [];
  if (marcasRegistro) {
    const tamanoMarca = Math.min(20, Math.max(10, Math.min(anchoEscalado, altoEscalado) * 0.05));
    
    // Esquina superior izquierda
    marcas.push(
      <Box
        key="marca-sup-izq"
        sx={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: tamanoMarca,
          height: tamanoMarca,
          bgcolor: theme.palette.grey[300],
          border: `1px solid ${theme.palette.grey[500]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2, // Para que aparezca por encima de todo
        }}
      >
        <Box sx={{ width: '50%', height: '50%', border: '1px solid black', borderRadius: '50%' }} />
      </Box>
    );
    
    // Esquina superior derecha
    marcas.push(
      <Box
        key="marca-sup-der"
        sx={{
          position: 'absolute',
          right: 0,
          top: 0,
          width: tamanoMarca,
          height: tamanoMarca,
          bgcolor: theme.palette.grey[300],
          border: `1px solid ${theme.palette.grey[500]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2,
        }}
      >
        <Box sx={{ width: '50%', height: '50%', border: '1px solid black', borderRadius: '50%' }} />
      </Box>
    );
    
    // Esquina inferior izquierda
    marcas.push(
      <Box
        key="marca-inf-izq"
        sx={{
          position: 'absolute',
          left: 0,
          bottom: 0,
          width: tamanoMarca,
          height: tamanoMarca,
          bgcolor: theme.palette.grey[300],
          border: `1px solid ${theme.palette.grey[500]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2,
        }}
      >
        <Box sx={{ width: '50%', height: '50%', border: '1px solid black', borderRadius: '50%' }} />
      </Box>
    );
    
    // Esquina inferior derecha
    marcas.push(
      <Box
        key="marca-inf-der"
        sx={{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: tamanoMarca,
          height: tamanoMarca,
          bgcolor: theme.palette.grey[300],
          border: `1px solid ${theme.palette.grey[500]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2,
        }}
      >
        <Box sx={{ width: '50%', height: '50%', border: '1px solid black', borderRadius: '50%' }} />
      </Box>
    );
  }
  
  // Tira de control
  const tiraControl = tirasControl ? (
    <Box
      key="tira-control"
      sx={{
        position: 'absolute',
        left: margenLateralEscalado,
        bottom: margenInferiorEscalado / 2,
        width: anchoEscalado - margenLateralEscalado * 2,
        height: margenInferiorEscalado / 2,
        bgcolor: theme.palette.grey[300],
        border: `1px solid ${theme.palette.grey[500]}`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1,
      }}
    >
      <Typography variant="caption" color="text.secondary">
        Tira de control
      </Typography>
    </Box>
  ) : null;
  
  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: 2, 
        mb: 3,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}
    >
      <Typography variant="h6" gutterBottom>
        Visualización del Pliego {esquemaNombre && `- Esquema ${esquemaNombre}`}
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Dimensiones del pliego: {anchoPliego} × {altoPliego} mm
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Dimensiones de página: {anchoPagina} × {altoPagina} mm
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Disposición: {paginasAnchoAjustado} × {paginasAltoAjustado} páginas {esTiraRetira || sheetType === 'WorkAndTurn' ? 'por cara' : ''}
        </Typography>
        {(esTiraRetira || sheetType === 'WorkAndTurn') && (
          <Box sx={{ mt: 1, display: 'flex', justifyContent: 'center' }}>
            <ToggleButtonGroup
              value={lado}
              exclusive
              onChange={handleLadoChange}
              size="small"
              aria-label="lado del pliego"
            >
              <ToggleButton value="cara" aria-label="cara">
                Cara (Anverso)
              </ToggleButton>
              <ToggleButton value="dorso" aria-label="dorso">
                Dorso (Reverso)
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>
        )}
      </Box>
      
      <Box
        sx={{
          position: 'relative',
          width: anchoEscalado,
          height: altoEscalado,
          bgcolor: theme.palette.grey[100],
          border: `1px solid ${theme.palette.grey[400]}`,
          overflow: 'hidden',
        }}
      >
        {/* Área de pinzas */}
        {areaPinzas}
        
        {/* Medianiles (calles) entre dípticos */}
        {medianiles}
        
        {/* Tira de control */}
        {tiraControl}
        
        {/* Marcas de registro */}
        {marcas}
        
        {/* Páginas */}
        {paginas}
      </Box>
      
      <Box sx={{ mt: 2, display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.grey[100], border: `1px solid ${theme.palette.grey[400]}`, mr: 1 }} />
          <Typography variant="caption">Pliego</Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.grey[200], border: `${Math.max(1, sangradoEscalado)}px solid ${theme.palette.warning.light}`, mr: 1 }} />
          <Typography variant="caption">Página con sangrado</Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, bgcolor: 'rgba(0, 128, 255, 0.2)', border: '1px dashed rgba(0, 128, 255, 0.5)', mr: 1 }} />
          <Typography variant="caption">Pinzas</Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.grey[300], border: `1px solid ${theme.palette.grey[500]}`, mr: 1 }} />
          <Typography variant="caption">Marcas/Tiras</Typography>
        </Box>
        
        {esEsquemaTipoF && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: 16, height: 16, bgcolor: 'rgba(255, 165, 0, 0.2)', border: '1px dashed rgba(255, 165, 0, 0.5)', mr: 1 }} />
            <Typography variant="caption">Medianil ({margenLateral}+{margenLateral}={medianil} mm)</Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default SheetPreview;
