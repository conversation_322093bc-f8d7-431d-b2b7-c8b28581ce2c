from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from typing import List, Dict, Any, Optional
from datetime import datetime

from models.activity_log import ActivityLog, ActivityType
from services.activity_log_service import ActivityLogService
from dependencies.auth import get_current_active_user, get_admin_user

router = APIRouter(
    prefix="/activity-logs",
    tags=["activity-logs"],
    responses={401: {"description": "No autorizado"}}
)

# Crear instancia del servicio
activity_log_service = ActivityLogService()

@router.get("/", response_model=List[ActivityLog])
async def get_activity_logs(
    limit: int = Query(100, ge=1, le=1000),
    skip: int = Query(0, ge=0),
    user_id: Optional[str] = None,
    activity_type: Optional[ActivityType] = None,
    entity_id: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: Dict[str, Any] = Depends(get_admin_user)
):
    """
    Obtiene los registros de actividad con filtros opcionales (solo administradores).
    """
    logs = activity_log_service.get_logs(
        limit=limit,
        skip=skip,
        user_id=user_id,
        activity_type=activity_type,
        entity_id=entity_id,
        start_date=start_date,
        end_date=end_date
    )
    
    return logs

@router.get("/{log_id}", response_model=ActivityLog)
async def get_activity_log(
    log_id: str,
    current_user: Dict[str, Any] = Depends(get_admin_user)
):
    """
    Obtiene un registro de actividad por su ID (solo administradores).
    """
    log = activity_log_service.get_log_by_id(log_id)
    
    if not log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registro de actividad no encontrado"
        )
    
    return log

@router.delete("/clear")
async def clear_all_logs(
    current_user: Dict[str, Any] = Depends(get_admin_user)
):
    """
    Elimina todos los registros de actividad (solo administradores).
    """
    count = activity_log_service.clear_all_logs()
    
    # Registrar esta acción
    activity_log_service.add_log(
        user_id=current_user["user_id"],
        username=current_user["username"],
        activity_type=ActivityType.USER_DELETE,
        description=f"El usuario {current_user['username']} ha eliminado todos los registros de actividad ({count} registros)"
    )
    
    return {"message": f"Se han eliminado {count} registros de actividad"}

# Función auxiliar para registrar actividad
def log_activity(
    request: Request,
    user_id: str,
    username: str,
    activity_type: ActivityType,
    description: str,
    entity_id: Optional[str] = None,
    additional_data: Optional[dict] = None
):
    """
    Registra una actividad del usuario.
    """
    client_ip = request.client.host if request.client else None
    
    activity_log_service.add_log(
        user_id=user_id,
        username=username,
        activity_type=activity_type,
        description=description,
        entity_id=entity_id,
        ip_address=client_ip,
        additional_data=additional_data
    )
