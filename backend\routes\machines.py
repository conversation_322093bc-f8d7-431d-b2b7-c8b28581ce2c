from fastapi import APIRouter, HTTPException
from typing import List
from models.models import Machine, MachineCreate, MachineUpdate
from utils.catalog_manager import load_machine_catalog, save_machine_catalog

router = APIRouter(
    prefix="/machines",
    tags=["machines"],
    responses={404: {"description": "Máquina no encontrada"}}
)

# Variable global para el catálogo
machine_catalog = load_machine_catalog()

@router.get("/", response_model=List[Machine])
def get_all_machines():
    """
    Obtiene todas las máquinas del catálogo
    """
    return machine_catalog

@router.get("/{machine_id}", response_model=Machine)
def get_machine_by_id(machine_id: str):
    """
    Obtiene una máquina específica por su ID
    """
    machine = next((m for m in machine_catalog if m["machine_id"] == machine_id), None)
    if not machine:
        raise HTTPException(status_code=404, detail="Máquina no encontrada")
    return machine

@router.post("/", response_model=Machine)
def create_machine(machine: MachineCreate):
    """
    Crea una nueva máquina en el catálogo
    """
    if any(m["machine_id"] == machine.machine_id for m in machine_catalog):
        raise HTTPException(status_code=400, detail="Ya existe una máquina con ese ID")
    
    machine_dict = machine.model_dump()
    machine_catalog.append(machine_dict)
    save_machine_catalog(machine_catalog)
    return machine_dict

@router.put("/{machine_id}", response_model=Machine)
def update_machine(machine_id: str, machine_update: MachineUpdate):
    """
    Actualiza una máquina existente en el catálogo
    """
    machine_idx = next((i for i, m in enumerate(machine_catalog) if m["machine_id"] == machine_id), None)
    if machine_idx is None:
        raise HTTPException(status_code=404, detail="Máquina no encontrada")
    
    current_machine = machine_catalog[machine_idx]
    update_data = machine_update.model_dump(exclude_unset=True)
    
    updated_machine = {**current_machine, **update_data}
    machine_catalog[machine_idx] = updated_machine
    save_machine_catalog(machine_catalog)
    return updated_machine

@router.delete("/{machine_id}")
def delete_machine(machine_id: str):
    """
    Elimina una máquina del catálogo
    """
    machine_idx = next((i for i, m in enumerate(machine_catalog) if m["machine_id"] == machine_id), None)
    if machine_idx is None:
        raise HTTPException(status_code=404, detail="Máquina no encontrada")
    
    machine_catalog.pop(machine_idx)
    save_machine_catalog(machine_catalog)
    return {"message": "Máquina eliminada correctamente"}
