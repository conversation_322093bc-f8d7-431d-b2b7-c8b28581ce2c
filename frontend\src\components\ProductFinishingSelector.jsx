import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Typography,
  Box,
  Chip,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Collapse
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CloseIcon from '@mui/icons-material/Close';
import { buildApiUrl } from '../config';

const ProductFinishingSelector = ({
  selectedProcesses,
  setSelectedProcesses
}) => {
  const [processes, setProcesses] = useState([]);
  const [expanded, setExpanded] = useState(false);

  // Cargar los procesos (acabados) disponibles
  useEffect(() => {
    const fetchProcesses = async () => {
      try {
        const apiUrl = buildApiUrl('/processes/');
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error('Error al obtener los procesos');
        const data = await response.json();

        // Filtrar solo procesos de acabado, corte, plegado, encuadernación, etc.
        const filteredProcesses = data.filter(process =>
          process.type && ['Corte', 'Plegado', 'Encuadernación', 'Acabado', 'Barnizado', 'Laminado', 'Troquelado'].includes(process.type)
        );

        setProcesses(filteredProcesses);
      } catch (error) {
        console.error('Error al cargar los procesos:', error);
      }
    };

    fetchProcesses();
  }, []);

  // Manejar la selección de un proceso (acabado)
  const handleProcessSelect = (process) => {
    // Verificar si el proceso ya está seleccionado
    if (selectedProcesses.some(p => p.process_id === process.process_id)) {
      return; // Si ya está seleccionado, no hacer nada
    }

    console.log('Seleccionando proceso:', process);

    // Añadir el proceso a la lista de seleccionados
    const newProcess = {
      process_id: process.process_id,
      name: process.name,
      type: process.type,
      unit_cost: process.unit_cost || 0,
      unit_type: process.unit_type || 'Unidad'
    };

    const updatedProcesses = [...selectedProcesses, newProcess];
    setSelectedProcesses(updatedProcesses);

    // Registrar los procesos seleccionados
    console.log('Procesos seleccionados actualizados:', updatedProcesses);
  };

  // Manejar la eliminación de un proceso
  const handleProcessRemove = (processId) => {
    const updatedProcesses = selectedProcesses.filter(p => p.process_id !== processId);
    setSelectedProcesses(updatedProcesses);

    // Registrar los procesos seleccionados después de eliminar
    console.log('Procesos seleccionados después de eliminar:', updatedProcesses);
  };

  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          p: 1,
          cursor: 'pointer',
          borderBottom: expanded ? '1px solid #e0e0e0' : 'none'
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <IconButton
          size="small"
          sx={{ mr: 1 }}
          onClick={(e) => {
            e.stopPropagation();
            setExpanded(!expanded);
          }}
        >
          {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
        <Typography variant="h6" component="div" sx={{ ml: 1 }}>
          Acabados ({selectedProcesses.length} seleccionados)
        </Typography>
      </Box>
      <Collapse in={expanded}>
        <Box sx={{ p: 2 }}>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Selecciona los acabados necesarios para este producto
          </Typography>

          {/* Chips para seleccionar procesos */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
            {processes.map((process) => (
              <Chip
                key={process.process_id}
                label={`${process.name} (${process.unit_cost ? process.unit_cost.toFixed(2) : '0.00'} €/${process.unit_type || 'Unidad'})`}
                onClick={() => handleProcessSelect(process)}
                onDelete={selectedProcesses.some(p => p.process_id === process.process_id) ?
                  () => handleProcessRemove(process.process_id) : undefined}
                color={selectedProcesses.some(p => p.process_id === process.process_id) ? "primary" : "default"}
                variant={selectedProcesses.some(p => p.process_id === process.process_id) ? "filled" : "outlined"}
                sx={{ m: 0.5 }}
              />
            ))}
          </Box>

          {/* Tabla de procesos seleccionados */}
          {selectedProcesses.length > 0 && (
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Acabado</TableCell>
                  <TableCell>Tipo</TableCell>
                  <TableCell align="right">Coste unitario</TableCell>
                  <TableCell></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {selectedProcesses.map((process) => (
                  <TableRow key={process.process_id}>
                    <TableCell>{process.name}</TableCell>
                    <TableCell>{process.type}</TableCell>
                    <TableCell align="right">{process.unit_cost.toFixed(2)} €/{process.unit_type || 'Unidad'}</TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={() => handleProcessRemove(process.process_id)}
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </Box>
      </Collapse>
    </Box>
  );
};

ProductFinishingSelector.propTypes = {
  selectedProcesses: PropTypes.array.isRequired,
  setSelectedProcesses: PropTypes.func.isRequired
};

export default ProductFinishingSelector;
