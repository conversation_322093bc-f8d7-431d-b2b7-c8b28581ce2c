// services/CatalogService.js
import ApiService from './ApiService';
import LogService from './logService';

/**
 * Servicio para gestionar los catálogos de la aplicación
 */
class CatalogService {
  /**
   * Obtiene todos los elementos de un catálogo
   *
   * @param {string} catalogType - Tipo de catálogo (papers, machines, clients, products, processes, consumables)
   * @param {object} options - Opciones adicionales
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise<Array>} - Promesa con los elementos del catálogo
   */
  static async getAll(catalogType, options = {}, token = null) {
    try {
      const response = await ApiService.get(`/${catalogType}`, options, token);
      
      if (!response.ok) {
        throw new Error(`Error al obtener el catálogo de ${catalogType}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      LogService.logUserAction(`fetch_${catalogType}_catalog`, {
        count: data.length
      });
      
      return data;
    } catch (error) {
      LogService.logError(`Error al obtener el catálogo de ${catalogType}`, {
        error: error.message
      });
      
      throw error;
    }
  }
  
  /**
   * Obtiene un elemento específico de un catálogo por su ID
   *
   * @param {string} catalogType - Tipo de catálogo (papers, machines, clients, products, processes, consumables)
   * @param {string} id - ID del elemento
   * @param {object} options - Opciones adicionales
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise<object>} - Promesa con el elemento del catálogo
   */
  static async getById(catalogType, id, options = {}, token = null) {
    try {
      const response = await ApiService.get(`/${catalogType}/${id}`, options, token);
      
      if (!response.ok) {
        throw new Error(`Error al obtener el elemento ${id} del catálogo de ${catalogType}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      LogService.logError(`Error al obtener el elemento ${id} del catálogo de ${catalogType}`, {
        error: error.message
      });
      
      throw error;
    }
  }
  
  /**
   * Crea un nuevo elemento en un catálogo
   *
   * @param {string} catalogType - Tipo de catálogo (papers, machines, clients, products, processes, consumables)
   * @param {object} data - Datos del nuevo elemento
   * @param {object} options - Opciones adicionales
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise<object>} - Promesa con el elemento creado
   */
  static async create(catalogType, data, options = {}, token = null) {
    try {
      const response = await ApiService.post(`/${catalogType}`, data, options, token);
      
      if (!response.ok) {
        throw new Error(`Error al crear elemento en el catálogo de ${catalogType}: ${response.statusText}`);
      }
      
      const createdItem = await response.json();
      
      LogService.logUserAction(`create_${catalogType}_item`, {
        id: createdItem.id || createdItem[`${catalogType.slice(0, -1)}_id`]
      });
      
      return createdItem;
    } catch (error) {
      LogService.logError(`Error al crear elemento en el catálogo de ${catalogType}`, {
        error: error.message,
        data
      });
      
      throw error;
    }
  }
  
  /**
   * Actualiza un elemento existente en un catálogo
   *
   * @param {string} catalogType - Tipo de catálogo (papers, machines, clients, products, processes, consumables)
   * @param {string} id - ID del elemento a actualizar
   * @param {object} data - Datos actualizados
   * @param {object} options - Opciones adicionales
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise<object>} - Promesa con el elemento actualizado
   */
  static async update(catalogType, id, data, options = {}, token = null) {
    try {
      const response = await ApiService.put(`/${catalogType}/${id}`, data, options, token);
      
      if (!response.ok) {
        throw new Error(`Error al actualizar el elemento ${id} del catálogo de ${catalogType}: ${response.statusText}`);
      }
      
      const updatedItem = await response.json();
      
      LogService.logUserAction(`update_${catalogType}_item`, {
        id
      });
      
      return updatedItem;
    } catch (error) {
      LogService.logError(`Error al actualizar el elemento ${id} del catálogo de ${catalogType}`, {
        error: error.message,
        data
      });
      
      throw error;
    }
  }
  
  /**
   * Elimina un elemento de un catálogo
   *
   * @param {string} catalogType - Tipo de catálogo (papers, machines, clients, products, processes, consumables)
   * @param {string} id - ID del elemento a eliminar
   * @param {object} options - Opciones adicionales
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise<object>} - Promesa con la respuesta de la eliminación
   */
  static async delete(catalogType, id, options = {}, token = null) {
    try {
      const response = await ApiService.delete(`/${catalogType}/${id}`, options, token);
      
      if (!response.ok) {
        throw new Error(`Error al eliminar el elemento ${id} del catálogo de ${catalogType}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      LogService.logUserAction(`delete_${catalogType}_item`, {
        id
      });
      
      return result;
    } catch (error) {
      LogService.logError(`Error al eliminar el elemento ${id} del catálogo de ${catalogType}`, {
        error: error.message
      });
      
      throw error;
    }
  }
}

export default CatalogService;
