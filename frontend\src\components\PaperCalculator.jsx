import React, { useState, useEffect } from 'react';
import { API_URL } from '../config';
import SheetPreview from './SheetPreview';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  CircularProgress, 
  Container, 
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider, 
  FormControl, 
  FormControlLabel, 
  FormHelperText,
  FormLabel, 
  Grid, 
  IconButton,
  InputLabel, 
  LinearProgress, 
  MenuItem, 
  Paper, 
  Radio, 
  RadioGroup, 
  Select, 
  Switch,
  TextField, 
  Tooltip,
  Typography, 
  useTheme,
  Alert
} from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CloseIcon from '@mui/icons-material/Close';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment,
  Chip
} from '@mui/material';

const PAGE_FORMATS = [
  { id: 'A5', width: 148, height: 210, name: 'A5 (148 × 210 mm)' },
  { id: 'A4', width: 210, height: 297, name: 'A4 (210 × 297 mm)' },
  { id: 'A3', width: 297, height: 420, name: 'A3 (297 × 420 mm)' },
  { id: 'SRA3', width: 320, height: 450, name: 'SRA3 (320 × 450 mm)' },
];

const PaperCalculator = () => {
  const [papers, setPapers] = useState([]);
  const [selectedPaper, setSelectedPaper] = useState(null);
  const [selectedFormat, setSelectedFormat] = useState('A4');
  const [formData, setFormData] = useState({
    numPaginas: 40,
    anchoPagina: 210,
    altoPagina: 297,
    anchoPliego: '',
    altoPliego: '',
    frontColors: '4',  // CMYK por defecto
    backColors: '4',   // CMYK por defecto
    machineType: 'Offset', // Tipo de máquina por defecto
    copies: 1 // Número de copias por defecto
  });
  const [resultado, setResultado] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  
  // Estado para el modal de detalles del pliego
  const [openDetailsModal, setOpenDetailsModal] = useState(false);
  
  // Estado para los parámetros de cálculo de desperdicio
  const [wasteParams, setWasteParams] = useState({
    sangrado: 3.0,
    pinzas: 10.0,
    margen_lateral: 5.0,
    margen_superior: 5.0,
    margen_inferior: 5.0,
    marcas_registro: true,
    tiras_control: true
  });
  
  // Estado para controlar la visualización del pliego
  const [showSheetPreview, setShowSheetPreview] = useState(false);
  const [selectedEsquema, setSelectedEsquema] = useState(null);

  useEffect(() => {
    fetchPapers();
  }, []);

  const fetchPapers = async () => {
    try {
      const response = await fetch(`${API_URL}/papers/`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al obtener el catálogo de papeles');
      }
      const data = await response.json();
      setPapers(data);
      // Seleccionar el primer papel por defecto si existe
      if (data.length > 0) {
        setSelectedPaper(data[0]);
        setFormData(prev => ({
          ...prev,
          anchoPliego: data[0].dimension_width,
          altoPliego: data[0].dimension_height
        }));
      }
    } catch (err) {
      console.error('Error al cargar papeles:', err);
      setError(err.message);
    }
  };

  const handlePaperChange = (event) => {
    const paper = papers.find(p => p.product_id === event.target.value);
    setSelectedPaper(paper);
    setFormData(prev => ({
      ...prev,
      anchoPliego: paper.dimension_width,
      altoPliego: paper.dimension_height
    }));
  };

  const handleFormatChange = (event) => {
    const format = PAGE_FORMATS.find(f => f.id === event.target.value);
    setSelectedFormat(event.target.value);
    setFormData(prev => ({
      ...prev,
      anchoPagina: format.width,
      altoPagina: format.height
    }));
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const formatoPaginaSeleccionado = {
        ancho: parseFloat(formData.anchoPagina),
        alto: parseFloat(formData.altoPagina)
      };

      if (!formatoPaginaSeleccionado.ancho || !formatoPaginaSeleccionado.alto) {
        throw new Error('Formato de página no válido');
      }

      // Validar que todos los campos numéricos sean válidos
      if (isNaN(parseInt(formData.numPaginas)) ||
          isNaN(parseFloat(formData.anchoPliego)) ||
          isNaN(parseFloat(formData.altoPliego))) {
        throw new Error('Por favor, complete todos los campos con valores numéricos válidos');
      }

      // Crear el objeto de solicitud
      const requestData = {
        num_paginas: parseInt(formData.numPaginas),
        ancho_pagina: formatoPaginaSeleccionado.ancho,
        alto_pagina: formatoPaginaSeleccionado.alto,
        ancho_pliego: parseFloat(formData.anchoPliego),
        alto_pliego: parseFloat(formData.altoPliego),
        front_colors: parseInt(formData.frontColors),
        back_colors: parseInt(formData.backColors),
        machine_type: formData.machineType,
        copies: parseInt(formData.copies) || 1,
        // Parámetros de desperdicio
        waste_params: {
          sangrado: wasteParams.sangrado,
          pinzas: wasteParams.pinzas,
          margen_lateral: wasteParams.margen_lateral,
          margen_superior: wasteParams.margen_superior,
          margen_inferior: wasteParams.margen_inferior,
          marcas_registro: wasteParams.marcas_registro,
          tiras_control: wasteParams.tiras_control
        }
      };
      
      // Imprimir la solicitud en formato JSON en la consola
      console.log('Solicitud a /calcular-pliegos:');
      console.log(JSON.stringify(requestData, null, 2));

      const response = await fetch(`${API_URL}/calcular-pliegos`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al calcular los pliegos');
      }

      const data = await response.json();
      
      // Imprimir la respuesta en formato JSON en la consola
      console.log('Respuesta de /calcular-pliegos:');
      console.log(JSON.stringify(data, null, 2));
      
      setResultado(data);
      setError(null);
      setLoading(false);
    } catch (err) {
      console.error('Error en la solicitud:', err);
      setError(err.message);
      setResultado(null);
      setLoading(false);
    }
  };

  const EsquemaRow = ({ esquema, machineType }) => {
    // Calcular clicks por esquema si es máquina digital
    const clicksPorEsquema = machineType === 'Digital' ?
      esquema.numero_pliegos * (esquema.es_tira_retira ? 2 : 1) : 0;

    // Obtener el color de fondo según el tipo de pliego
    const getSheetTypeColor = (sheetType) => {
      switch (sheetType) {
        case 'Flat':
          return '#e3f2fd'; // Azul claro
        case 'WorkAndTurn':
          return '#e8f5e9'; // Verde claro
        case 'WorkAndBack':
          return '#fff3e0'; // Naranja claro
        case 'Perfecting':
          return '#f3e5f5'; // Púrpura claro
        default:
          return 'transparent';
      }
    };

    // Obtener la descripción del tipo de pliego
    const getSheetTypeDescription = (sheetType) => {
      switch (sheetType) {
        case 'Flat':
          return 'Una cara';
        case 'WorkAndTurn':
          return 'Tira-retira (mismas planchas)';
        case 'WorkAndBack':
          return 'Diferentes planchas';
        case 'Perfecting':
          return 'Ambas caras a la vez';
        default:
          return '';
      }
    };
    
    // Función para mostrar la visualización del pliego
    const handleShowPreview = () => {
      setSelectedEsquema(esquema);
      setShowSheetPreview(true);
    };

    return (
      <TableRow sx={{ backgroundColor: esquema.sheet_type ? getSheetTypeColor(esquema.sheet_type) : 'transparent' }}>
        <TableCell>{esquema.nombre}</TableCell>
        <TableCell>{esquema.numero_pliegos}</TableCell>
        <TableCell>{esquema.paginas_por_pliego}</TableCell>
        <TableCell>
          {esquema.disposicion.paginas_ancho} × {esquema.disposicion.paginas_alto}
          <IconButton 
            size="small" 
            color="primary" 
            onClick={handleShowPreview} 
            title="Visualizar pliego"
            sx={{ ml: 1 }}
          >
            <VisibilityIcon fontSize="small" />
          </IconButton>
        </TableCell>
        <TableCell>
          {esquema.numero_pliegos * esquema.paginas_por_pliego}
        </TableCell>
        <TableCell>
          {esquema.sheet_type ? (
            <Chip
              size="small"
              label={esquema.sheet_type}
              sx={{
                backgroundColor: getSheetTypeColor(esquema.sheet_type),
                border: '1px solid',
                borderColor: 'primary.main',
                fontSize: '0.7rem'
              }}
              title={getSheetTypeDescription(esquema.sheet_type)}
            />
          ) : (
            esquema.es_tira_retira && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                T/R
              </span>
            )
          )}
          {esquema.needs_two_passes && (
            <Typography variant="caption" display="block" color="warning.main" sx={{ mt: 0.5 }}>
              2 pasadas
            </Typography>
          )}
        </TableCell>
        <TableCell>
          {machineType === 'Digital' ?
            clicksPorEsquema :
            (esquema.plates_needed !== undefined ? esquema.plates_needed : esquema.planchas_necesarias)}
        </TableCell>
      </TableRow>
    );
  };

  const handleOpenDetailsModal = () => {
    setOpenDetailsModal(true);
  };

  const handleCloseDetailsModal = () => {
    setOpenDetailsModal(false);
  };

  const handleWasteParamChange = (event) => {
    const { name, value, type, checked } = event.target;
    setWasteParams({
      ...wasteParams,
      [name]: type === 'checkbox' ? checked : parseFloat(value)
    });
  };

  const handleApplyWasteParams = () => {
    if (resultado) {
      handleSubmit(new Event('submit'));
    }
    handleCloseDetailsModal();
  };

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 800, mx: 'auto', mt: 4 }}>
      <Typography variant="h4" gutterBottom>
        Calculadora de Pliegos
      </Typography>

      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3 }}>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
          <Tooltip title="Configurar parámetros de desperdicio">
            <IconButton color="primary" onClick={handleOpenDetailsModal}>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>

        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom component="div">Formato de Página</Typography>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel id="format-select-label">Tamaño de Página</InputLabel>
            <Select
              labelId="format-select-label"
              id="format-select"
              value={selectedFormat}
              onChange={handleFormatChange}
              label="Tamaño de Página"
            >
              {PAGE_FORMATS.map((format) => (
                <MenuItem key={format.id} value={format.id}>
                  {format.name}
                </MenuItem>
              ))}
            </Select>
            <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <span>Dimensiones:</span>
              <span>{formData.anchoPagina} × {formData.altoPagina} mm</span>
            </Box>
          </FormControl>

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Ancho de Página"
                name="anchoPagina"
                value={formData.anchoPagina}
                onChange={handleChange}
                type="number"
                InputProps={{
                  endAdornment: <InputAdornment position="end">mm</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Alto de Página"
                name="altoPagina"
                value={formData.altoPagina}
                onChange={handleChange}
                type="number"
                InputProps={{
                  endAdornment: <InputAdornment position="end">mm</InputAdornment>,
                }}
              />
            </Grid>
          </Grid>
        </Paper>

        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom component="div">Detalles del Pliego</Typography>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel id="paper-select-label">Papel</InputLabel>
            <Select
              labelId="paper-select-label"
              id="paper-select"
              value={selectedPaper?.product_id || ''}
              onChange={handlePaperChange}
              label="Papel"
            >
              {papers.map((paper) => (
                <MenuItem key={paper.product_id} value={paper.product_id}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <span>{paper.descriptive_name}</span>
                      <Chip
                        size="small"
                        label={paper.inStock ? "En Stock" : "Sin Stock"}
                        color={paper.inStock ? "success" : "error"}
                        sx={{ ml: 1 }}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                      {paper.dimension_width} × {paper.dimension_height} mm
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
            {selectedPaper && (
              <Box sx={{ mt: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                  <span>Categoría:</span>
                  <span>{selectedPaper.category}</span>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                  <span>Gramaje:</span>
                  <span>{selectedPaper.weight} g/m²</span>
                </Box>
                {selectedPaper.manufacturer && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span>Fabricante:</span>
                    <span>{selectedPaper.manufacturer}</span>
                  </Box>
                )}
              </Box>
            )}
          </FormControl>

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Ancho del Pliego"
                name="anchoPliego"
                value={formData.anchoPliego}
                onChange={handleChange}
                type="number"
                InputProps={{
                  endAdornment: <InputAdornment position="end">mm</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Alto del Pliego"
                name="altoPliego"
                value={formData.altoPliego}
                onChange={handleChange}
                type="number"
                InputProps={{
                  endAdornment: <InputAdornment position="end">mm</InputAdornment>,
                }}
              />
            </Grid>
          </Grid>
        </Paper>

        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom component="div">Número de Páginas y Ejemplares</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Número de Páginas"
                name="numPaginas"
                value={formData.numPaginas}
                onChange={handleChange}
                type="number"
                helperText={
                  <Box component="span" sx={{ display: 'block' }}>
                    El número de páginas debe ser par
                  </Box>
                }
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Número de Ejemplares"
                name="copies"
                value={formData.copies}
                onChange={handleChange}
                type="number"
                inputProps={{ min: 1 }}
                helperText={
                  <Box component="span" sx={{ display: 'block' }}>
                    Cantidad de ejemplares a imprimir
                  </Box>
                }
              />
            </Grid>
          </Grid>
        </Paper>

        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom component="div">Configuración de Colores</Typography>

          <Grid container spacing={3}>
            {/* Colores Anverso */}
            <Grid item xs={12} md={6}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Colores Anverso (Cara)</FormLabel>
                <RadioGroup
                  row
                  name="frontColors"
                  value={formData.frontColors}
                  onChange={handleChange}
                >
                  <FormControlLabel value="4" control={<Radio />} label="4 colores (CMYK)" />
                  <FormControlLabel value="1" control={<Radio />} label="1 color" />
                </RadioGroup>
              </FormControl>
            </Grid>

            {/* Colores Reverso */}
            <Grid item xs={12} md={6}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Colores Reverso</FormLabel>
                <RadioGroup
                  row
                  name="backColors"
                  value={formData.backColors}
                  onChange={handleChange}
                >
                  <FormControlLabel value="4" control={<Radio />} label="4 colores (CMYK)" />
                  <FormControlLabel value="1" control={<Radio />} label="1 color" />
                  <FormControlLabel value="0" control={<Radio />} label="Sin impresión" />
                </RadioGroup>
              </FormControl>
            </Grid>
          </Grid>

          <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Configuración actual: {formData.frontColors}/{formData.backColors}
            </Typography>
            <Typography variant="body2">
              {formData.frontColors === '4' ? '4 colores CMYK en anverso' : '1 color en anverso'} /
              {formData.backColors === '4' ? '4 colores CMYK en reverso' :
               formData.backColors === '1' ? '1 color en reverso' : 'Sin impresión en reverso'}
            </Typography>
          </Box>
        </Paper>

        <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom component="div">Tipo de Máquina</Typography>
          <FormControl component="fieldset" fullWidth>
            <RadioGroup
              row
              name="machineType"
              value={formData.machineType}
              onChange={handleChange}
            >
              <FormControlLabel value="Offset" control={<Radio />} label="Offset" />
              <FormControlLabel value="Digital" control={<Radio />} label="Digital" />
            </RadioGroup>
            <FormHelperText>
              {formData.machineType === 'Digital' ?
                'Para máquinas digitales, se calculará el número de clicks en lugar de planchas' :
                'Para máquinas offset, se calculará el número de planchas necesarias'}
            </FormHelperText>
          </FormControl>
        </Paper>

        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, gap: 2 }}>
          <Button 
            type="submit" 
            variant="contained" 
            color="primary" 
            size="large"
            disabled={loading}
            sx={{ minWidth: 200 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Calcular'}
          </Button>
          
          <Tooltip title="Configurar parámetros de desperdicio">
            <IconButton 
              color="primary" 
              onClick={handleOpenDetailsModal}
              sx={{ ml: 1 }}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {resultado && (
        <Box sx={{ mt: 4 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom component="div">
              Resumen
            </Typography>
            <Typography variant="body1">
              <strong>Configuración de colores:</strong> {formData.frontColors}/{formData.backColors}
              ({formData.frontColors === '4' ? 'CMYK' : '1 color'} en anverso /
              {formData.backColors === '4' ? 'CMYK' : formData.backColors === '1' ? '1 color' : 'Sin impresión'} en reverso)
            </Typography>
            <Typography variant="body1">
              {formData.machineType === 'Digital' ? (
                <><strong>Total clicks:</strong> {resultado.mejor_combinacion.total_clicks}</>
              ) : (
                <><strong>Total planchas necesarias:</strong> {resultado.mejor_combinacion.total_planchas}</>
              )}
            </Typography>
          </Box>

          <Typography variant="h6" gutterBottom component="div" sx={{ mt: 3 }}>
            Mejor Combinación
          </Typography>

          {resultado.mejor_combinacion.total_passes && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                <strong>Total de pasadas por máquina:</strong> {resultado.mejor_combinacion.total_passes} pasadas
                {resultado.mejor_combinacion.total_passes > resultado.mejor_combinacion.total_pliegos && (
                  <span> (algunos pliegos requieren dos pasadas)</span>
                )}
              </Typography>
            </Alert>
          )}

          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Esquema</TableCell>
                  <TableCell>Pliegos</TableCell>
                  <TableCell>Págs/Pliego</TableCell>
                  <TableCell>Disposición</TableCell>
                  <TableCell>Total Págs</TableCell>
                  <TableCell>Tipo Pliego</TableCell>
                  <TableCell>{formData.machineType === 'Digital' ? 'Clicks' : 'Planchas'}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {resultado.mejor_combinacion.esquemas_utilizados.map((esquema, index) => (
                  <EsquemaRow key={index} esquema={esquema} machineType={formData.machineType} />
                ))}
                <TableRow>
                  <TableCell colSpan={4} align="right">
                    <Box component="span" sx={{ fontWeight: 'bold' }}>
                      Totales:
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box component="span" sx={{ fontWeight: 'bold' }}>
                      {formData.numPaginas}
                    </Box>
                  </TableCell>
                  <TableCell />
                  <TableCell>
                    <Box component="span" sx={{ fontWeight: 'bold' }}>
                      {formData.machineType === 'Digital' ?
                        resultado.mejor_combinacion.total_clicks :
                        resultado.mejor_combinacion.total_planchas}
                    </Box>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>

          {/* Análisis de desperdicio de papel */}
          {resultado.mejor_combinacion.porcentaje_desperdicio !== undefined && (
            <Box sx={{ mt: 4 }}>
              <Typography variant="h6" gutterBottom component="div">
                Análisis de Desperdicio de Papel
              </Typography>
              
              <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Aprovechamiento del papel
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <LinearProgress 
                          variant="determinate" 
                          value={resultado.mejor_combinacion.porcentaje_aprovechamiento} 
                          sx={{ 
                            height: 10, 
                            borderRadius: 5,
                            backgroundColor: '#f5f5f5',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: resultado.mejor_combinacion.porcentaje_aprovechamiento > 70 ? 'success.main' :
                                             resultado.mejor_combinacion.porcentaje_aprovechamiento > 50 ? 'warning.main' : 'error.main'
                            }
                          }}
                        />
                      </Box>
                      <Box sx={{ minWidth: 35 }}>
                        <Typography variant="body2" color="text.secondary">
                          {`${Math.round(resultado.mejor_combinacion.porcentaje_aprovechamiento)}%`}
                        </Typography>
                      </Box>
                    </Box>
                    
                    <TableContainer>
                      <Table size="small">
                        <TableBody>
                          <TableRow>
                            <TableCell component="th" scope="row">Área total del pliego</TableCell>
                            <TableCell align="right">{resultado.mejor_combinacion.area_pliego_mm2.toLocaleString()} mm²</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell component="th" scope="row">Área utilizada</TableCell>
                            <TableCell align="right">{resultado.mejor_combinacion.area_utilizada_mm2.toLocaleString()} mm²</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell component="th" scope="row">Área desperdiciada</TableCell>
                            <TableCell align="right">{resultado.mejor_combinacion.area_desperdiciada_mm2.toLocaleString()} mm²</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell component="th" scope="row">Porcentaje de desperdicio</TableCell>
                            <TableCell align="right">{resultado.mejor_combinacion.porcentaje_desperdicio.toFixed(2)}%</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    {resultado.mejor_combinacion.recomendaciones_desperdicio && resultado.mejor_combinacion.recomendaciones_desperdicio.length > 0 && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Recomendaciones para reducir el desperdicio
                        </Typography>
                        {resultado.mejor_combinacion.recomendaciones_desperdicio.map((recomendacion, index) => (
                          <Alert key={index} severity="info" sx={{ mb: 1 }}>
                            {recomendacion}
                          </Alert>
                        ))}
                      </Box>
                    )}
                  </Grid>
                </Grid>
              </Paper>
            </Box>
          )}
          
          {/* Leyenda de tipos de pliego */}
          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Leyenda de tipos de pliego:
            </Typography>
            <Grid container spacing={1}>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ width: 16, height: 16, bgcolor: '#e3f2fd', mr: 1, border: '1px solid', borderColor: 'primary.main' }}></Box>
                  <Typography variant="body2"><strong>Flat:</strong> Impresión a una cara</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ width: 16, height: 16, bgcolor: '#e8f5e9', mr: 1, border: '1px solid', borderColor: 'primary.main' }}></Box>
                  <Typography variant="body2"><strong>WorkAndTurn:</strong> Tira-retira (mismas planchas)</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ width: 16, height: 16, bgcolor: '#fff3e0', mr: 1, border: '1px solid', borderColor: 'primary.main' }}></Box>
                  <Typography variant="body2"><strong>WorkAndBack:</strong> Diferentes planchas</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ width: 16, height: 16, bgcolor: '#f3e5f5', mr: 1, border: '1px solid', borderColor: 'primary.main' }}></Box>
                  <Typography variant="body2"><strong>Perfecting:</strong> Ambas caras a la vez</Typography>
                </Box>
              </Grid>
            </Grid>
            <Typography variant="body2" sx={{ mt: 1 }}>
              El tipo de pliego óptimo se determina automáticamente según el esquema de plegado,
              el número de colores y las capacidades de la máquina.
            </Typography>
          </Box>
        </Box>
      )}

      {/* Modal para editar parámetros de desperdicio */}
      <Dialog open={openDetailsModal} onClose={handleCloseDetailsModal} maxWidth="sm" fullWidth>
        <DialogTitle>Configuración de Parámetros de Desperdicio</DialogTitle>
        <DialogContent>
          <Typography variant="subtitle2" color="textSecondary" sx={{ mb: 2 }}>
            Estos parámetros se utilizan para calcular el desperdicio de papel en cada trabajo de impresión.
          </Typography>
          
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Sangrado (mm)"
                type="number"
                name="sangrado"
                value={wasteParams.sangrado}
                onChange={handleWasteParamChange}
                fullWidth
                InputProps={{
                  inputProps: { min: 0, step: 0.5 }
                }}
                helperText="Sangrado alrededor de cada página"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Pinzas (mm)"
                type="number"
                name="pinzas"
                value={wasteParams.pinzas}
                onChange={handleWasteParamChange}
                fullWidth
                InputProps={{
                  inputProps: { min: 0, step: 0.5 }
                }}
                helperText="Espacio para que la máquina sujete el papel"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Margen Lateral (mm)"
                type="number"
                name="margen_lateral"
                value={wasteParams.margen_lateral}
                onChange={handleWasteParamChange}
                fullWidth
                InputProps={{
                  inputProps: { min: 0, step: 0.5 }
                }}
                helperText="Margen en ambos lados"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Margen Superior (mm)"
                type="number"
                name="margen_superior"
                value={wasteParams.margen_superior}
                onChange={handleWasteParamChange}
                fullWidth
                InputProps={{
                  inputProps: { min: 0, step: 0.5 }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Margen Inferior (mm)"
                type="number"
                name="margen_inferior"
                value={wasteParams.margen_inferior}
                onChange={handleWasteParamChange}
                fullWidth
                InputProps={{
                  inputProps: { min: 0, step: 0.5 }
                }}
              />
            </Grid>
          </Grid>
          
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={wasteParams.marcas_registro}
                  onChange={handleWasteParamChange}
                  name="marcas_registro"
                  color="primary"
                />
              }
              label="Incluir marcas de registro"
            />
            <Typography variant="caption" color="textSecondary" display="block">
              Las marcas de registro ocupan aproximadamente 20x20mm en cada esquina
            </Typography>
          </Box>
          
          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={wasteParams.tiras_control}
                  onChange={handleWasteParamChange}
                  name="tiras_control"
                  color="primary"
                />
              }
              label="Incluir tiras de control"
            />
            <Typography variant="caption" color="textSecondary" display="block">
              Las tiras de control ocupan aproximadamente 10mm a lo ancho del pliego
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetailsModal}>Cancelar</Button>
          <Button onClick={handleApplyWasteParams} variant="contained" color="primary">
            Aplicar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para visualizar el pliego */}
      <Dialog 
        open={showSheetPreview} 
        onClose={() => setShowSheetPreview(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Visualización del Pliego
          <IconButton
            aria-label="close"
            onClick={() => setShowSheetPreview(false)}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {selectedEsquema && (
            <SheetPreview
              anchoPliego={parseFloat(formData.anchoPliego)}
              altoPliego={parseFloat(formData.altoPliego)}
              anchoPagina={parseFloat(formData.anchoPagina)}
              altoPagina={parseFloat(formData.altoPagina)}
              paginasAncho={selectedEsquema.disposicion.paginas_ancho}
              paginasAlto={selectedEsquema.disposicion.paginas_alto}
              sangrado={wasteParams.sangrado}
              pinzas={wasteParams.pinzas}
              margenLateral={wasteParams.margen_lateral}
              margenSuperior={wasteParams.margen_superior}
              margenInferior={wasteParams.margen_inferior}
              marcasRegistro={wasteParams.marcas_registro}
              tirasControl={wasteParams.tiras_control}
              esTiraRetira={selectedEsquema.es_tira_retira}
              sheetType={selectedEsquema.sheet_type}
              pageLayout={selectedEsquema.page_layout}
              esquemaNombre={selectedEsquema.nombre}
              orientacion={selectedEsquema.disposicion?.orientacion || 'Rotate0'}
            />
          )}
        </DialogContent>
      </Dialog>
    </Paper>
  );
};

export default PaperCalculator;
