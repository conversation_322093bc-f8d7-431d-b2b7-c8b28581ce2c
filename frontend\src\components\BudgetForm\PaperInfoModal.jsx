import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  CircularProgress,
  Alert
} from '@mui/material';
import DescriptionIcon from '@mui/icons-material/Description';
import { buildApiUrl } from '../../config';

/**
 * Componente modal para crear un nuevo tipo de papel
 */
const PaperInfoModal = ({ open, onClose, onPaperCreated }) => {
  // Estado para el nuevo papel
  const [newPaper, setNewPaper] = useState({
    descriptive_name: '',
    weight: '',
    dimension_width: '',
    dimension_height: '',
    price_per_1000: '',
    cost_per_ton: '', // Precio por tonelada
    category: 'Offset', // Valor por defecto
    color: 'Blanco', // Valor por defecto
    media_type: 'Paper',
    finish: 'Natural',
    thickness: '',
    grainDirection: 'Long',
    manufacturer: 'Genérico',
    inStock: true
  });

  // Estado para el proceso de creación
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Función para calcular el precio por millar a partir del precio por tonelada
  const calculatePricePerThousand = (costPerTon, weight, width, height) => {
    // Convertir a números
    const w = parseFloat(weight);
    const widthM = parseFloat(width) / 1000; // Convertir mm a metros
    const heightM = parseFloat(height) / 1000; // Convertir mm a metros
    const costPerTonValue = parseFloat(costPerTon);
    
    // Verificar que todos los valores sean válidos
    if (isNaN(w) || isNaN(widthM) || isNaN(heightM) || isNaN(costPerTonValue) || 
        w <= 0 || widthM <= 0 || heightM <= 0 || costPerTonValue <= 0) {
      return '';
    }
    
    // Cálculo: (costo por tonelada * peso en g/m² * ancho en m * alto en m) / 1000
    const areaInSquareMeters = widthM * heightM;
    const weightPerSheet = (w * areaInSquareMeters) / 1000; // Peso en kg por pliego
    const pricePerSheet = (costPerTonValue * weightPerSheet) / 1000; // Precio por pliego
    const pricePerThousand = pricePerSheet * 1000; // Precio por millar
    
    // Redondear a 2 decimales
    return pricePerThousand.toFixed(2);
  };
  
  // Función para calcular el precio por tonelada a partir del precio por millar
  const calculateCostPerTon = (pricePerThousand, weight, width, height) => {
    // Convertir a números
    const w = parseFloat(weight);
    const widthM = parseFloat(width) / 1000; // Convertir mm a metros
    const heightM = parseFloat(height) / 1000; // Convertir mm a metros
    const pricePerThousandValue = parseFloat(pricePerThousand);
    
    // Verificar que todos los valores sean válidos
    if (isNaN(w) || isNaN(widthM) || isNaN(heightM) || isNaN(pricePerThousandValue) || 
        w <= 0 || widthM <= 0 || heightM <= 0 || pricePerThousandValue <= 0) {
      return '';
    }
    
    // Cálculo inverso: (precio por millar * 1000) / (peso en g/m² * ancho en m * alto en m)
    const areaInSquareMeters = widthM * heightM;
    const weightPerSheet = (w * areaInSquareMeters) / 1000; // Peso en kg por pliego
    const pricePerSheet = pricePerThousandValue / 1000; // Precio por pliego
    const costPerTon = (pricePerSheet * 1000) / weightPerSheet; // Precio por tonelada
    
    // Redondear a 2 decimales
    return costPerTon.toFixed(2);
  };
  
  // Manejar cambios en los campos
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Actualizar el estado con el nuevo valor
    setNewPaper(prev => {
      const updatedPaper = { ...prev, [name]: value };
      
      // Si se cambia el precio por tonelada, calcular el precio por millar
      if (name === 'cost_per_ton' && value) {
        const pricePerThousand = calculatePricePerThousand(
          value, 
          updatedPaper.weight, 
          updatedPaper.dimension_width, 
          updatedPaper.dimension_height
        );
        
        if (pricePerThousand) {
          updatedPaper.price_per_1000 = pricePerThousand;
        }
      }
      
      // Si se cambia el precio por millar, calcular el precio por tonelada
      if (name === 'price_per_1000' && value) {
        const costPerTon = calculateCostPerTon(
          value, 
          updatedPaper.weight, 
          updatedPaper.dimension_width, 
          updatedPaper.dimension_height
        );
        
        if (costPerTon) {
          updatedPaper.cost_per_ton = costPerTon;
        }
      }
      
      // Si se cambia alguno de los valores que afectan al cálculo (peso, dimensiones)
      // y ya hay un precio por tonelada, recalcular el precio por millar
      if ((name === 'weight' || name === 'dimension_width' || name === 'dimension_height') && 
          updatedPaper.cost_per_ton && value) {
        const pricePerThousand = calculatePricePerThousand(
          updatedPaper.cost_per_ton, 
          updatedPaper.weight, 
          updatedPaper.dimension_width, 
          updatedPaper.dimension_height
        );
        
        if (pricePerThousand) {
          updatedPaper.price_per_1000 = pricePerThousand;
        }
      }
      
      return updatedPaper;
    });
  };

  // Manejar envío del formulario
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsCreating(true);
    setError(null);
    setSuccess(false);

    try {
      // Validar campos requeridos
      if (!newPaper.descriptive_name || !newPaper.weight || 
          !newPaper.dimension_width || !newPaper.dimension_height || 
          !newPaper.price_per_1000) {
        throw new Error('Todos los campos marcados con * son obligatorios');
      }

      // Crear objeto con los datos del nuevo papel
      const paperData = {
        descriptive_name: newPaper.descriptive_name,
        weight: parseFloat(newPaper.weight),
        dimension_width: parseFloat(newPaper.dimension_width),
        dimension_height: parseFloat(newPaper.dimension_height),
        price_per_1000: parseFloat(newPaper.price_per_1000),
        cost_per_ton: newPaper.cost_per_ton ? parseFloat(newPaper.cost_per_ton) : 0,
        category: newPaper.category,
        color: newPaper.color,
        media_type: newPaper.media_type,
        finish: newPaper.finish,
        thickness: newPaper.thickness ? parseFloat(newPaper.thickness) : parseFloat(newPaper.weight) * 0.9, // Estimación si no se proporciona
        grainDirection: newPaper.grainDirection,
        manufacturer: newPaper.manufacturer,
        inStock: newPaper.inStock,
        notes: "",
        // Generar un ID provisional (el servidor lo reemplazará)
        product_id: `Pap-temp-${Date.now()}`
      };

      // Enviar datos al servidor
      const apiUrl = buildApiUrl('/papers/');
      
      // Log para depuración
      console.log('Enviando datos al servidor:', paperData);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(paperData)
      });
      
      // Log para depuración de la respuesta
      console.log('Respuesta del servidor:', response.status, response.statusText);

      if (!response.ok) {
        const errorData = await response.json();
        
        // Log completo del error para depuración
        console.log('Error completo del servidor:', errorData);
        
        // Manejar diferentes formatos de errores de la API
        if (errorData.detail) {
          // Si es un mensaje simple
          throw new Error(errorData.detail);
        } else if (typeof errorData === 'object') {
          // Si es un objeto con múltiples errores de validación
          const errorMessages = [];
          
          Object.entries(errorData).forEach(([field, errors]) => {
            if (Array.isArray(errors)) {
              errorMessages.push(`${field}: ${errors.join(', ')}`);
            } else if (typeof errors === 'string') {
              errorMessages.push(`${field}: ${errors}`);
            } else if (typeof errors === 'object') {
              errorMessages.push(`${field}: ${JSON.stringify(errors)}`);
            }
          });
          
          if (errorMessages.length > 0) {
            throw new Error(errorMessages.join('\n'));
          } else {
            // Si no se pudo extraer mensajes de error pero hay un objeto de error
            throw new Error(`Error de validación: ${JSON.stringify(errorData)}`);
          }
        }
        
        // Si no se puede determinar el formato del error
        throw new Error('Error al crear el papel. Verifica los datos ingresados.');
      }

      // Obtener el papel creado
      const createdPaper = await response.json();
      
      // Mostrar mensaje de éxito
      setSuccess(true);
      
      // Notificar al componente padre
      if (onPaperCreated) {
        onPaperCreated(createdPaper);
      }
      
      // Resetear el formulario
      setNewPaper({
        descriptive_name: '',
        weight: '',
        dimension_width: '',
        dimension_height: '',
        price_per_1000: '',
        cost_per_ton: '',
        category: 'Offset',
        color: 'Blanco',
        media_type: 'Paper',
        finish: 'Natural',
        thickness: '',
        grainDirection: 'Long',
        manufacturer: 'Genérico',
        inStock: true
      });
      
      // Cerrar el modal después de un breve retraso
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Error al crear el papel:', error);
      setError(error.message);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <DescriptionIcon sx={{ mr: 1, color: '#1976d2' }} />
          <Typography variant="h6">Crear Nuevo Papel</Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error.split('\n').map((line, index) => (
              <div key={index}>{line}</div>
            ))}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Papel creado correctamente
          </Alert>
        )}
        
        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            {/* Nombre descriptivo */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nombre descriptivo *"
                name="descriptive_name"
                value={newPaper.descriptive_name}
                onChange={handleChange}
                required
                helperText="Ejemplo: Estucado Brillo 135g"
              />
            </Grid>
            
            {/* Gramaje */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Gramaje (g/m²) *"
                name="weight"
                type="number"
                value={newPaper.weight}
                onChange={handleChange}
                required
                inputProps={{ min: 1 }}
                helperText="Peso en gramos por metro cuadrado"
              />
            </Grid>
            
            {/* Dimensiones */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Ancho (mm) *"
                name="dimension_width"
                type="number"
                value={newPaper.dimension_width}
                onChange={handleChange}
                required
                inputProps={{ min: 1 }}
                helperText="Ancho del pliego en milímetros"
              />
            </Grid>
            
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Alto (mm) *"
                name="dimension_height"
                type="number"
                value={newPaper.dimension_height}
                onChange={handleChange}
                required
                inputProps={{ min: 1 }}
                helperText="Alto del pliego en milímetros"
              />
            </Grid>
            
            {/* Precios */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Precio por tonelada (€)"
                name="cost_per_ton"
                type="number"
                value={newPaper.cost_per_ton}
                onChange={handleChange}
                inputProps={{ min: 0, step: 0.01 }}
                helperText="Precio por tonelada (calcula automáticamente el precio por millar)"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Precio por 1000 pliegos (€) *"
                name="price_per_1000"
                type="number"
                value={newPaper.price_per_1000}
                onChange={handleChange}
                required
                inputProps={{ min: 0, step: 0.01 }}
                helperText="Precio por millar de pliegos"
              />
            </Grid>
            
            {/* Categoría de papel */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Categoría</InputLabel>
                <Select
                  name="category"
                  value={newPaper.category}
                  onChange={handleChange}
                  label="Categoría"
                >
                  <MenuItem value="Offset">Offset</MenuItem>
                  <MenuItem value="Estucado">Estucado</MenuItem>
                  <MenuItem value="Reciclado">Reciclado</MenuItem>
                  <MenuItem value="Autocopiativo">Autocopiativo</MenuItem>
                  <MenuItem value="Cartulina">Cartulina</MenuItem>
                  <MenuItem value="Kraft">Kraft</MenuItem>
                  <MenuItem value="Especial">Especial</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {/* Color */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Color</InputLabel>
                <Select
                  name="color"
                  value={newPaper.color}
                  onChange={handleChange}
                  label="Color"
                >
                  <MenuItem value="Blanco">Blanco</MenuItem>
                  <MenuItem value="Marfil">Marfil</MenuItem>
                  <MenuItem value="Natural">Natural</MenuItem>
                  <MenuItem value="Amarillo">Amarillo</MenuItem>
                  <MenuItem value="Verde">Verde</MenuItem>
                  <MenuItem value="Azul">Azul</MenuItem>
                  <MenuItem value="Rosa">Rosa</MenuItem>
                  <MenuItem value="Rojo">Rojo</MenuItem>
                  <MenuItem value="Negro">Negro</MenuItem>
                  <MenuItem value="Otro">Otro</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {/* Acabado */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Acabado</InputLabel>
                <Select
                  name="finish"
                  value={newPaper.finish}
                  onChange={handleChange}
                  label="Acabado"
                >
                  <MenuItem value="Natural">Natural</MenuItem>
                  <MenuItem value="Mate">Mate</MenuItem>
                  <MenuItem value="Brillo">Brillo</MenuItem>
                  <MenuItem value="Satinado">Satinado</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {/* Grosor */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Grosor (micras)"
                name="thickness"
                type="number"
                value={newPaper.thickness}
                onChange={handleChange}
                inputProps={{ min: 1 }}
                helperText="Dejar en blanco para calcular automáticamente"
              />
            </Grid>
            
            {/* Fabricante */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Fabricante"
                name="manufacturer"
                value={newPaper.manufacturer}
                onChange={handleChange}
                helperText="Nombre del fabricante del papel"
              />
            </Grid>
          </Grid>
        </form>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancelar
        </Button>
        <Button 
          onClick={handleSubmit} 
          color="primary" 
          variant="contained" 
          disabled={isCreating}
          startIcon={isCreating ? <CircularProgress size={20} /> : null}
        >
          {isCreating ? 'Creando...' : 'Crear Papel'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

PaperInfoModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onPaperCreated: PropTypes.func
};

export default PaperInfoModal;
