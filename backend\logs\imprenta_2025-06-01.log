2025-06-01 08:23:39 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 08:23:39 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 08:23:41 - INFO - Iniciando la aplicación FastAPI
2025-06-01 08:41:04 - INFO - Request: 127.0.0.1 - GET /docs
2025-06-01 08:41:04 - INFO - Request: 127.0.0.1 - GET /docs - Status: 200
2025-06-01 08:41:04 - INFO - Request: 127.0.0.1 - GET /openapi.json
2025-06-01 08:41:04 - INFO - Request: 127.0.0.1 - GET /openapi.json - Status: 200
2025-06-01 08:46:39 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 08:46:39 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 08:46:39 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 08:46:39 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 08:46:45 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 08:46:45 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 08:46:45 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 08:46:45 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 08:46:48 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 08:46:48 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 08:46:58 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 08:46:58 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 08:47:08 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 08:47:08 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 08:51:41 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos-digital
2025-06-01 08:51:41 - INFO - Calculando pliegos digital para: num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 ancho_pliego=488.0 alto_pliego=330.0 front_colors=4 back_colors=4 copies=100 binding_type='gathering'
2025-06-01 08:51:41 - INFO - Calculando pliegos digital para 40 páginas, dimensiones: 210.0x297.0mm, pliego: 488.0x330.0mm
2025-06-01 08:51:41 - INFO - Colores: 4/4, Duplex: True, Color: True
2025-06-01 08:51:41 - INFO - Caben 2 A4 por cara en el pliego
2025-06-01 08:51:41 - INFO - Total de A4 por pliego: 4 (2 por cara × 2 caras)
2025-06-01 08:51:41 - INFO - Resultado del cálculo de pliegos digital: 10 pliegos, 2000 clicks
2025-06-01 08:51:41 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos-digital - Status: 200
2025-06-01 08:58:32 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 08:58:32 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 08:58:32 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 08:58:32 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 08:58:38 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 08:58:38 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 08:58:38 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 08:58:38 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 08:58:41 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 08:58:41 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:02:09 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:02:09 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:02:57 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:02:57 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:03:13 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:03:13 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:06:52 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:06:52 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 09:06:52 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:07:32 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:07:32 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 09:07:32 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:07:33 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:07:33 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 09:07:33 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:08:11 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:08:11 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 09:08:11 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:08:12 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:08:12 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 09:08:12 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:22:02 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:22:02 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 09:22:02 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:22:15 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:22:15 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 09:22:15 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:22:48 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 09:22:48 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 09:22:48 - INFO - Iniciando la aplicación FastAPI
2025-06-01 09:22:53 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 09:22:53 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 09:28:38 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 09:28:38 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 401
2025-06-01 09:28:38 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 09:28:38 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 401
2025-06-01 09:28:43 - INFO - Request: 127.0.0.1 - POST /auth/login
2025-06-01 09:28:43 - INFO - Request: 127.0.0.1 - POST /auth/login - Status: 200
2025-06-01 09:28:43 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 09:28:43 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /budgets
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /budgets - Status: 307
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /clients
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /clients - Status: 307
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /budgets/
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /budgets/ - Status: 200
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B - Status: 200
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-01 09:28:44 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /products_t=1748762926204
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /products_t=1748762926204 - Status: 307
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /products_t=1748762926219
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /products_t=1748762926219 - Status: 307
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /products/_t=1748762926204
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /products/_t=1748762926219
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /products/_t=1748762926204 - Status: 200
2025-06-01 09:28:46 - INFO - Request: 127.0.0.1 - GET /products/_t=1748762926219 - Status: 200
2025-06-01 09:28:50 - INFO - Request: 127.0.0.1 - GET /products_t=1748762930011
2025-06-01 09:28:50 - INFO - Request: 127.0.0.1 - GET /products_t=1748762930011 - Status: 307
2025-06-01 09:28:50 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 09:28:50 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 09:28:50 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 09:28:50 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 09:28:50 - INFO - Request: 127.0.0.1 - GET /products/_t=1748762930011
2025-06-01 09:28:50 - INFO - Request: 127.0.0.1 - GET /products/_t=1748762930011 - Status: 200
2025-06-01 09:29:05 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset
2025-06-01 09:29:05 - INFO - Solicitud de cálculo offset v2 recibida: machine_id='MAQ-001' copies=1000 num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 colors_front=4 colors_back=4 paper_id='Pap-003' ancho_pliego=1000.0 alto_pliego=700.0 custom_setup_time=None custom_sheets_per_hour=None custom_maculatura=None
2025-06-01 09:29:05 - INFO - Usando máquina MAQ-001 con 8 cuerpos de impresión
2025-06-01 09:29:05 - INFO - Cálculo de pliegos completado: mejor_combinacion=CombinacionResponse(esquemas_utilizados=[EsquemaResponse(nombre='F16-7', numero_pliegos=2, paginas_por_pliego=16, disposicion=DisposicionResponse(paginas_ancho=4, paginas_alto=2, orientacion='vertical_normal'), es_tira_retira=False, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=16, needs_two_passes=False, page_layout=None), EsquemaResponse(nombre='F8-7', numero_pliegos=1, paginas_por_pliego=8, disposicion=DisposicionResponse(paginas_ancho=2, paginas_alto=2, orientacion='horizontal_tira_retira'), es_tira_retira=True, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=8, needs_two_passes=False, page_layout=None)], total_pliegos=3, total_planchas=24, total_passes=2, total_clicks=0, a4_per_sheet=None, print_speed=None, setup_time=None, sheets_per_hour=None, estimated_time_minutes=180.0, estimated_time_hours=3.0, area_pliego_mm2=None, area_utilizada_mm2=None, area_desperdiciada_mm2=None, porcentaje_desperdicio=None, porcentaje_aprovechamiento=None, recomendaciones_desperdicio=None)
2025-06-01 09:29:05 - INFO - Cálculo offset v2 completado. total_sheets=3, copies=1000, total_physical_sheets=2500
2025-06-01 09:29:05 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset - Status: 200
2025-06-01 09:29:05 - INFO - Request: 127.0.0.1 - POST /calculations/shipping-cost
2025-06-01 09:29:05 - INFO - Request: 127.0.0.1 - POST /calculations/shipping-cost - Status: 200
2025-06-01 09:30:54 - INFO - Request: 127.0.0.1 - POST /budgets
2025-06-01 09:30:54 - INFO - Request: 127.0.0.1 - POST /budgets - Status: 307
2025-06-01 09:30:54 - INFO - Request: 127.0.0.1 - POST /budgets/
2025-06-01 09:30:56 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:30:56 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:30:56 - INFO - Request: 127.0.0.1 - POST /budgets/ - Status: 201
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /clients
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets - Status: 307
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /clients - Status: 307
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets/
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets/ - Status: 200
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B - Status: 200
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F
2025-06-01 09:30:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F - Status: 200
2025-06-01 09:31:00 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F
2025-06-01 09:31:00 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F - Status: 200
2025-06-01 09:31:00 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:00 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:00 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:00 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:00 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:00 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:02 - INFO - Request: 127.0.0.1 - PUT /budgets/PRES-927C6F
2025-06-01 09:31:03 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:03 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:03 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:03 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:03 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:03 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:03 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:03 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:03 - INFO - Usando tiempo de cálculo de pliegos para Revista: 1.64 horas
2025-06-01 09:31:03 - INFO - Asignando máquina MAQ-008 al proceso de acabado Corte (Guillotina)
2025-06-01 09:31:03 - INFO - Asignando máquina MAQ-012 al proceso de acabado Encuadernación Grapada
2025-06-01 09:31:03 - INFO - Presupuesto PRES-927C6F añadido a producción con OT OT-25064105 y 3 procesos
2025-06-01 09:31:04 - INFO - Request: 127.0.0.1 - PUT /budgets/PRES-927C6F - Status: 200
2025-06-01 09:31:04 - INFO - Request: 127.0.0.1 - PUT /budgets/PRES-927C6F
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - PUT /budgets/PRES-927C6F - Status: 200
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:05 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:06 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:06 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:06 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:06 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:06 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:06 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:08 - INFO - Request: 127.0.0.1 - POST /external/send-budget-jdf/PRES-927C6F
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:31:09 - INFO - Request: 127.0.0.1 - GET /json-ot/generate/PRES-927C6F
2025-06-01 09:31:10 - INFO - JSON_OT generado para el presupuesto PRES-927C6F utilizando plantilla Jinja2
2025-06-01 09:31:10 - INFO - Request: 127.0.0.1 - GET /json-ot/generate/PRES-927C6F - Status: 200
2025-06-01 09:31:10 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F
2025-06-01 09:31:10 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F - Status: 401
2025-06-01 09:31:15 - INFO - Request: 127.0.0.1 - POST /external/send-budget-jdf/PRES-927C6F - Status: 200
2025-06-01 09:31:15 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:31:15 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:31:15 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:31:15 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:31:15 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:31:15 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:34:13 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:34:13 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:34:13 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:34:13 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:34:13 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:34:13 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:41:29 - INFO - Request: 127.0.0.1 - POST /external/send-budget-jdf/PRES-927C6F
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /json-ot/generate/PRES-927C6F
2025-06-01 09:41:30 - INFO - JSON_OT generado para el presupuesto PRES-927C6F utilizando plantilla Jinja2
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /json-ot/generate/PRES-927C6F - Status: 200
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F
2025-06-01 09:41:30 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F - Status: 401
2025-06-01 09:41:34 - INFO - Request: 127.0.0.1 - POST /external/send-budget-jdf/PRES-927C6F - Status: 200
2025-06-01 09:41:34 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:41:34 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:41:34 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:41:34 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:41:34 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:41:34 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 09:43:12 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 09:43:23 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F
2025-06-01 09:43:23 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F - Status: 200
2025-06-01 09:43:23 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F
2025-06-01 09:43:23 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F - Status: 200
2025-06-01 10:00:10 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 10:00:10 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 10:00:11 - INFO - Iniciando la aplicación FastAPI
2025-06-01 10:00:16 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F
2025-06-01 10:00:16 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-927C6F - Status: 200
2025-06-01 10:00:16 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 10:00:16 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 10:00:16 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 10:00:16 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 10:00:16 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3
2025-06-01 10:00:16 - INFO - Request: 127.0.0.1 - GET /clients/Customer-3 - Status: 200
2025-06-01 10:00:24 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 10:00:24 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 10:13:50 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 10:13:50 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 10:13:50 - INFO - Iniciando la aplicación FastAPI
2025-06-01 10:16:57 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 10:16:57 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 10:16:57 - INFO - Iniciando la aplicación FastAPI
2025-06-01 10:18:54 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 10:18:54 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 10:18:55 - INFO - Iniciando la aplicación FastAPI
2025-06-01 10:21:31 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 10:21:31 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 10:21:31 - INFO - Iniciando la aplicación FastAPI
2025-06-01 10:28:29 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 10:28:29 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 10:28:29 - INFO - Iniciando la aplicación FastAPI
2025-06-01 10:36:59 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 10:36:59 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 10:36:59 - INFO - Iniciando la aplicación FastAPI
2025-06-01 13:54:48 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 13:54:48 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 13:54:48 - INFO - Iniciando la aplicación FastAPI
2025-06-01 13:58:33 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 13:58:33 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 13:58:33 - INFO - Iniciando la aplicación FastAPI
2025-06-01 14:08:02 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 14:08:02 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 14:08:02 - INFO - Iniciando la aplicación FastAPI
2025-06-01 14:09:23 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:09:23 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:10:33 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:10:33 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:11:16 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:11:16 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:11:24 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:11:24 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:11:48 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:11:48 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:14:10 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 14:14:10 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 14:14:10 - INFO - Iniciando la aplicación FastAPI
2025-06-01 14:14:17 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:14:17 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:14:34 - INFO - Request: 127.0.0.1 - GET /
2025-06-01 14:14:34 - INFO - Request: 127.0.0.1 - GET / - Status: 200
2025-06-01 14:14:57 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:14:57 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:15:49 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:15:49 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:16:14 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:16:14 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:17:03 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 14:17:03 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 14:17:04 - INFO - Iniciando la aplicación FastAPI
2025-06-01 14:17:06 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:17:06 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:19:32 - INFO - Request: 127.0.0.1 - GET /docs
2025-06-01 14:19:32 - INFO - Request: 127.0.0.1 - GET /docs - Status: 200
2025-06-01 14:19:32 - INFO - Request: 127.0.0.1 - GET /openapi.json
2025-06-01 14:19:32 - INFO - Request: 127.0.0.1 - GET /openapi.json - Status: 200
2025-06-01 14:19:54 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:19:54 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:35:02 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 14:35:02 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 14:35:02 - INFO - Iniciando la aplicación FastAPI
2025-06-01 14:35:28 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:35:28 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 14:35:37 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 14:35:37 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 14:35:37 - INFO - Iniciando la aplicación FastAPI
2025-06-01 14:35:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 14:35:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 17:35:31 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 17:35:31 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 17:35:31 - INFO - Iniciando la aplicación FastAPI
2025-06-01 17:35:44 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 17:35:44 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 17:35:44 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 17:35:44 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 17:35:48 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 17:35:48 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 17:35:48 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 17:35:48 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 17:35:53 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:35:53 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 17:36:15 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:36:15 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 17:36:57 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:36:57 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 17:37:53 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 17:37:53 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 17:37:53 - INFO - Iniciando la aplicación FastAPI
2025-06-01 17:37:56 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:37:56 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:38:36 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 17:38:36 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 17:38:37 - INFO - Iniciando la aplicación FastAPI
2025-06-01 17:38:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:38:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:38:56 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:38:56 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 17:39:03 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:39:03 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:39:14 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:39:14 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 17:39:17 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:39:17 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 17:39:26 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:39:26 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 400
2025-06-01 17:39:29 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:39:29 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /budgets
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /budgets - Status: 307
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /clients
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /clients - Status: 307
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /budgets/
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /budgets/ - Status: 200
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B - Status: 200
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-01 17:39:57 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /products_t=1748792400419
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /products_t=1748792400419 - Status: 307
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /products_t=1748792400428
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /products_t=1748792400428 - Status: 307
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /products/_t=1748792400419
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /products/_t=1748792400428
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /products/_t=1748792400419 - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /products/_t=1748792400428 - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-01 17:40:00 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-01 17:40:01 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-01 17:40:01 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /budgets
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /clients
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /budgets - Status: 307
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /clients - Status: 307
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /budgets/
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /config
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /budgets/ - Status: 200
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B - Status: 200
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-01 17:40:08 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-01 17:40:31 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 17:40:31 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 17:40:31 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 17:40:31 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 17:40:34 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:40:34 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:45:20 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:45:20 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:45:20 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:47:35 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 17:47:35 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 17:47:35 - INFO - Iniciando la aplicación FastAPI
2025-06-01 17:47:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:47:40 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:47:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:48:39 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:48:39 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:48:39 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:49:12 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:49:12 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:49:12 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:53:10 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:53:10 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:53:10 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:54:27 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:54:27 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:54:27 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:54:51 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:54:51 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:54:51 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:55:21 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:55:21 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:55:21 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:56:00 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:56:00 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:56:00 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:56:47 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:56:47 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:56:47 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:57:54 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:57:54 - INFO - Esquema tira-retira detectado: F8-7. Ajustando disposición de 2x2 a 2x1 por cara
2025-06-01 17:57:54 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 17:58:40 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 17:58:40 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 17:58:41 - INFO - Iniciando la aplicación FastAPI
2025-06-01 17:58:46 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 17:58:46 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:00:08 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:00:08 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:00:29 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:00:29 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:03:52 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:03:52 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:04:15 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:04:15 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:04:24 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:04:25 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:04:46 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 18:04:46 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 18:04:46 - INFO - Iniciando la aplicación FastAPI
2025-06-01 18:05:05 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:05:05 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:05:20 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:05:20 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:08:30 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:08:30 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:09:18 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:09:18 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:09:22 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:09:22 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:09:53 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:09:53 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:10:33 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 18:10:33 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 18:10:33 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 18:10:33 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 18:10:36 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 18:10:36 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 18:10:36 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 18:10:36 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 18:10:39 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:10:39 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:12:46 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:12:46 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:13:43 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 18:13:43 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 18:13:43 - INFO - Iniciando la aplicación FastAPI
2025-06-01 18:13:50 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:13:50 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:14:05 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:14:05 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:14:31 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:14:31 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:15:09 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:15:09 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:15:18 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:15:18 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:15:48 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:15:48 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:15:52 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:15:52 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:16:27 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:16:27 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:16:31 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:16:31 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:16:47 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:16:47 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:17:16 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:17:16 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:46:06 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:46:06 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 18:46:57 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 18:46:57 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 20:11:28 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 20:11:28 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 20:11:29 - INFO - Iniciando la aplicación FastAPI
2025-06-01 20:11:43 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 20:11:43 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 20:15:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 20:15:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 20:16:04 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 20:16:04 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 20:16:04 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 20:16:04 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 20:46:16 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 20:46:16 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 20:47:07 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 20:47:07 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 20:50:07 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 20:50:07 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 20:51:13 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 20:51:13 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 20:51:27 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 20:51:27 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 20:51:27 - INFO - Iniciando la aplicación FastAPI
2025-06-01 20:51:42 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 20:51:42 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 20:52:17 - INFO - Request: 127.0.0.1 - POST /auth/login
2025-06-01 20:52:17 - INFO - Request: 127.0.0.1 - POST /auth/login - Status: 200
2025-06-01 20:52:17 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 20:52:17 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 20:52:20 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 20:52:20 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 20:52:20 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 20:52:20 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 20:52:25 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 20:52:25 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:04:28 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 21:04:28 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 21:04:28 - INFO - Iniciando la aplicación FastAPI
2025-06-01 21:04:32 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:04:32 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:04:46 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:04:46 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:05:01 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 21:05:01 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 21:05:01 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-01 21:05:01 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-01 21:05:06 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 21:05:06 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 21:05:06 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 21:05:06 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 21:05:12 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:05:12 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:06:45 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:06:45 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:09:42 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 21:09:42 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 21:09:42 - INFO - Iniciando la aplicación FastAPI
2025-06-01 21:09:45 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:09:45 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:10:07 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:10:07 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:10:20 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:10:20 - INFO - Esquema tira-retira detectado: F16-7. Ajustando disposición de 4x2 a 2x2 por cara
2025-06-01 21:10:20 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:10:31 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:10:31 - INFO - Esquema tira-retira detectado: F16-7. Ajustando disposición de 4x2 a 2x2 por cara
2025-06-01 21:10:31 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:11:33 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:11:33 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:11:44 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:11:44 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:12:20 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:12:20 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:13:19 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:13:19 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:15:31 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:15:31 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /machines
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /machines - Status: 307
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /machines
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /machines - Status: 307
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-01 21:16:43 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 21:16:45 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-001
2025-06-01 21:16:45 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-001 - Status: 200
2025-06-01 21:16:49 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset
2025-06-01 21:16:49 - INFO - Solicitud de cálculo offset v2 recibida: machine_id='MAQ-001' copies=500 num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 colors_front=4 colors_back=4 paper_id='Pap-001' ancho_pliego=None alto_pliego=None custom_setup_time=None custom_sheets_per_hour=None custom_maculatura=None
2025-06-01 21:16:49 - INFO - Dimensiones del pliego obtenidas del papel: 1000.0x700.0mm
2025-06-01 21:16:49 - INFO - Usando máquina MAQ-001 con 8 cuerpos de impresión
2025-06-01 21:16:49 - INFO - Cálculo de pliegos completado: mejor_combinacion=CombinacionResponse(esquemas_utilizados=[EsquemaResponse(nombre='F16-7', numero_pliegos=2, paginas_por_pliego=16, disposicion=DisposicionResponse(paginas_ancho=4, paginas_alto=2, orientacion='Rotate0'), es_tira_retira=False, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=16, needs_two_passes=False, page_layout=None), EsquemaResponse(nombre='F8-7', numero_pliegos=1, paginas_por_pliego=8, disposicion=DisposicionResponse(paginas_ancho=2, paginas_alto=2, orientacion='Rotate90'), es_tira_retira=True, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=8, needs_two_passes=False, page_layout=None)], total_pliegos=3, total_planchas=24, total_passes=2, total_clicks=0, a4_per_sheet=None, print_speed=None, setup_time=None, sheets_per_hour=None, estimated_time_minutes=90.0, estimated_time_hours=1.5, area_pliego_mm2=None, area_utilizada_mm2=None, area_desperdiciada_mm2=None, porcentaje_desperdicio=None, porcentaje_aprovechamiento=None, recomendaciones_desperdicio=None)
2025-06-01 21:16:49 - INFO - Cálculo offset v2 completado. total_sheets=3, copies=500, total_physical_sheets=1250
2025-06-01 21:16:49 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset - Status: 200
2025-06-01 21:26:35 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-003
2025-06-01 21:26:35 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-003 - Status: 200
2025-06-01 21:26:38 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset
2025-06-01 21:26:38 - INFO - Solicitud de cálculo offset v2 recibida: machine_id='MAQ-003' copies=500 num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 colors_front=4 colors_back=4 paper_id='Pap-001' ancho_pliego=None alto_pliego=None custom_setup_time=None custom_sheets_per_hour=None custom_maculatura=None
2025-06-01 21:26:38 - INFO - Dimensiones del pliego obtenidas del papel: 1000.0x700.0mm
2025-06-01 21:26:38 - INFO - Usando máquina MAQ-003 con 4 cuerpos de impresión
2025-06-01 21:26:38 - INFO - Cálculo de pliegos completado: mejor_combinacion=CombinacionResponse(esquemas_utilizados=[EsquemaResponse(nombre='F16-7', numero_pliegos=2, paginas_por_pliego=16, disposicion=DisposicionResponse(paginas_ancho=4, paginas_alto=2, orientacion='Rotate0'), es_tira_retira=False, sheet_type=<SheetType.WORK_AND_BACK: 'WorkAndBack'>, plates_needed=16, needs_two_passes=True, page_layout=None), EsquemaResponse(nombre='F8-7', numero_pliegos=1, paginas_por_pliego=8, disposicion=DisposicionResponse(paginas_ancho=2, paginas_alto=2, orientacion='Rotate90'), es_tira_retira=True, sheet_type=<SheetType.WORK_AND_TURN: 'WorkAndTurn'>, plates_needed=4, needs_two_passes=False, page_layout=None)], total_pliegos=3, total_planchas=20, total_passes=5, total_clicks=0, a4_per_sheet=None, print_speed=None, setup_time=None, sheets_per_hour=None, estimated_time_minutes=90.0, estimated_time_hours=1.5, area_pliego_mm2=None, area_utilizada_mm2=None, area_desperdiciada_mm2=None, porcentaje_desperdicio=None, porcentaje_aprovechamiento=None, recomendaciones_desperdicio=None)
2025-06-01 21:26:38 - INFO - Cálculo offset v2 completado. total_sheets=3, copies=500, total_physical_sheets=1250
2025-06-01 21:26:38 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset - Status: 200
2025-06-01 21:27:28 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-001
2025-06-01 21:27:28 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-001 - Status: 200
2025-06-01 21:27:29 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset
2025-06-01 21:27:29 - INFO - Solicitud de cálculo offset v2 recibida: machine_id='MAQ-001' copies=500 num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 colors_front=4 colors_back=4 paper_id='Pap-001' ancho_pliego=None alto_pliego=None custom_setup_time=None custom_sheets_per_hour=None custom_maculatura=None
2025-06-01 21:27:29 - INFO - Dimensiones del pliego obtenidas del papel: 1000.0x700.0mm
2025-06-01 21:27:29 - INFO - Usando máquina MAQ-001 con 8 cuerpos de impresión
2025-06-01 21:27:29 - INFO - Cálculo de pliegos completado: mejor_combinacion=CombinacionResponse(esquemas_utilizados=[EsquemaResponse(nombre='F16-7', numero_pliegos=2, paginas_por_pliego=16, disposicion=DisposicionResponse(paginas_ancho=4, paginas_alto=2, orientacion='Rotate0'), es_tira_retira=False, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=16, needs_two_passes=False, page_layout=None), EsquemaResponse(nombre='F8-7', numero_pliegos=1, paginas_por_pliego=8, disposicion=DisposicionResponse(paginas_ancho=2, paginas_alto=2, orientacion='Rotate90'), es_tira_retira=True, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=8, needs_two_passes=False, page_layout=None)], total_pliegos=3, total_planchas=24, total_passes=2, total_clicks=0, a4_per_sheet=None, print_speed=None, setup_time=None, sheets_per_hour=None, estimated_time_minutes=90.0, estimated_time_hours=1.5, area_pliego_mm2=None, area_utilizada_mm2=None, area_desperdiciada_mm2=None, porcentaje_desperdicio=None, porcentaje_aprovechamiento=None, recomendaciones_desperdicio=None)
2025-06-01 21:27:29 - INFO - Cálculo offset v2 completado. total_sheets=3, copies=500, total_physical_sheets=1250
2025-06-01 21:27:29 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset - Status: 200
2025-06-01 21:30:20 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 21:30:20 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 21:30:20 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-01 21:30:20 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-01 21:30:29 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 21:30:29 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 21:30:29 - INFO - Iniciando la aplicación FastAPI
2025-06-01 21:30:35 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:30:35 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:31:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:31:40 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:31:59 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:31:59 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 21:32:21 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-01 21:32:21 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-01 22:10:28 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-01 22:10:28 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-01 22:10:28 - INFO - Iniciando la aplicación FastAPI
