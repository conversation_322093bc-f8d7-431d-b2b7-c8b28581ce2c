/**
 * Servicio para el cálculo de pliegos y costes en máquinas digitales
 * Este archivo contiene funciones para calcular pliegos, clicks y costes
 * utilizando el endpoint /v2/calculate-digital
 */
import { buildApiUrl } from '../config';
import { generatePartJobSummary } from './jobSummaryService';

/**
 * Calcula los pliegos y clicks para una parte específica del presupuesto usando una máquina digital
 * @param {Object} params - Parámetros para el cálculo
 * @param {Object} params.part - Parte del presupuesto a calcular
 * @param {number} params.copies - Número de copias a imprimir
 * @param {Function} params.buildApiUrl - Función para construir URLs de API
 * @param {Function} params.showSnackbar - Función para mostrar notificaciones
 * @param {Function} params.setCalculatingSheets - Función para actualizar el estado de cálculo
 * @param {Function} params.setBudgetParts - Función para actualizar las partes del presupuesto
 * @param {Function} params.setBudget - Función para actualizar el presupuesto
 * @param {Function} params.setCurrentCalculatedPart - Función para establecer la parte calculada actual
 * @param {Function} params.setSheetCalculationModal - Función para mostrar/ocultar el modal de cálculo
 * @param {Function} params.setSheetCalculation - Función para establecer los datos del cálculo de pliegos
 * @param {Array} params.budgetParts - Array con todas las partes del presupuesto
 * @param {Object} params.budget - Objeto con los datos del presupuesto
 * @param {number} params.partIndex - Índice de la parte en el array de partes
 * @returns {Promise<Object>} - Resultado del cálculo
 */
export const calculateDigitalSheets = async ({
  part,
  copies,
  buildApiUrl,
  showSnackbar,
  setCalculatingSheets,
  setBudgetParts,
  setBudget,
  setCurrentCalculatedPart,
  setSheetCalculationModal,
  setSheetCalculation,
  budgetParts,
  budget,
  partIndex
}) => {
  // Validar que tenemos todos los datos necesarios
  if (!part || !part.paper || !part.machine || !part.pageCount) {
    showSnackbar('Selecciona papel, máquina y número de páginas para esta parte', 'warning');
    return null;
  }

  // Validar que la máquina es de tipo digital
  if (part.machine.type !== 'Digital') {
    showSnackbar('Esta función solo puede usarse con máquinas digitales', 'warning');
    return null;
  }

  setCalculatingSheets(true);

  try {
    // Determinar el tamaño de página
    let pageSize;

    if (part.pageSize === 'Personalizado') {
      // Si es personalizado, usar el valor del campo customPageSize
      const dimensions = part.customPageSize.split('x').map(dim => parseFloat(dim.trim()));
      if (dimensions.length !== 2 || isNaN(dimensions[0]) || isNaN(dimensions[1])) {
        showSnackbar('Formato de tamaño personalizado incorrecto. Usa el formato "ancho x alto"', 'error');
        setCalculatingSheets(false);
        return null;
      }
      pageSize = {
        width: dimensions[0],
        height: dimensions[1]
      };
    } else {
      // Si es un tamaño estándar, usar las dimensiones predefinidas
      switch (part.pageSize) {
        case 'A4':
          pageSize = { width: 210, height: 297 };
          break;
        case 'A5':
          pageSize = { width: 148, height: 210 };
          break;
        case 'A3':
          pageSize = { width: 297, height: 420 };
          break;
        case 'Carta':
          pageSize = { width: 216, height: 279 };
          break;
        case 'Legal':
          pageSize = { width: 216, height: 356 };
          break;
        default:
          showSnackbar('Tamaño de página no reconocido', 'error');
          setCalculatingSheets(false);
          return null;
      }
    }

    // Asegurarse de que todos los valores sean números
    const numPaginas = parseInt(part.pageCount);
    const anchoPagina = parseFloat(pageSize.width);
    const altoPagina = parseFloat(pageSize.height);

    // Validar que todos los valores sean números válidos
    if (isNaN(numPaginas) || isNaN(anchoPagina) || isNaN(altoPagina)) {
      showSnackbar('Algunos valores no son números válidos', 'error');
      setCalculatingSheets(false);
      return null;
    }

    // Validar que el número de páginas sea par (requerido por el backend)
    if (numPaginas % 2 !== 0) {
      showSnackbar('El número de páginas debe ser par', 'warning');
      setCalculatingSheets(false);
      return null;
    }

    // Determinar si es impresión a color y dúplex
    const isDuplex = part.colorConfig?.backColors > 0; // Si hay colores en el reverso, es duplex
    const isColor = part.colorConfig?.frontColors > 0 || part.colorConfig?.backColors > 0; // Si hay colores en anverso o reverso, es color

    // Preparar los datos para el endpoint v2/calculate-digital
    const requestBody = {
      machine_id: part.machine?.machine_id || part.machine?.product_id,
      copies: parseInt(copies) || 1,
      is_duplex: isDuplex,
      is_color: isColor,
      paper_id: part.paper?.paper_id || part.paper?.product_id,
      custom_print_time: part.customPrintTime,

      // Parámetros para el cálculo
      num_paginas: numPaginas,
      ancho_pagina: anchoPagina,
      alto_pagina: altoPagina
    };

    // Mostrar en consola los parámetros que se envían al endpoint
    console.log('Parámetros enviados al endpoint /v2/calculate-digital:', requestBody);
    console.log('JSON enviado al endpoint /v2/calculate-digital:', JSON.stringify(requestBody, null, 2));

    // Llamar a la API para calcular pliegos
    const response = await fetch(buildApiUrl('/v2/calculate-digital'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error en la respuesta de la API:', errorText);
      throw new Error(`Error al calcular pliegos: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    // Mostrar en consola la respuesta completa del endpoint
    console.log('Respuesta completa del endpoint /v2/calculate-digital:', data);
    console.log('JSON de respuesta del endpoint /v2/calculate-digital:', JSON.stringify(data, null, 2));

    // Extraer costes directamente de la respuesta
    const paperCost = data.paper_cost || 0;
    const machineCost = data.machine_cost || 0;
    const clickCost = data.click_cost || 0;

    // Pasar directamente los datos del endpoint al modal, añadiendo solo lo que falte
    const normalizedData = {
      // Asegurarnos de que las propiedades esenciales estén presentes
      copies: copies,
      // Mantener la estructura de mejor_combinacion para compatibilidad
      mejor_combinacion: {
        total_pliegos: data.total_sheets || 0,
        total_clicks: data.total_clicks || 0,
        esquemas_utilizados: data.esquemas_utilizados || [],
        is_duplex: data.is_duplex || false,
        is_color: data.is_color || false,
        print_speed: data.print_speed || 0,
        estimated_time_minutes: data.printing_time_minutes || 0,
        estimated_time_hours: data.total_time_hours || 0,
        a4_per_sheet: data.a4_per_sheet || 0,
        copies: copies
      },
      // Añadir todos los datos del endpoint directamente
      ...data,
      // Añadir datos de la máquina en la estructura esperada por el modal
      machine_data: {
        setup_time: data.setup_time_minutes || 0,
        printing_time_minutes: data.printing_time_minutes || 0,
        total_time_minutes: data.total_time_minutes || 0,
        total_time_hours: data.total_time_hours || 0,
        hourly_cost: data.machine_cost ? (data.machine_cost / data.total_time_hours) : 0,
        click_unit_cost: data.click_unit_cost || 0,
        click_color_cost: data.click_color_cost || 0,
        speed: data.print_speed || 0,
        name: data.machine_name || ''
      },
      // Añadir datos del papel en la estructura esperada por el modal
      paper_data: {
        paper_name: data.paper_name || '',
        name: data.paper_name || '',
        descriptive_name: data.paper_name || '',
        paper_cost: data.paper_cost || 0,
        sheet_width_mm: data.sheet_width_mm || 0,
        sheet_height_mm: data.sheet_height_mm || 0,
        dimension_width: data.sheet_width_mm || 0,
        dimension_height: data.sheet_height_mm || 0
      },
      // Datos de clicks
      clicks_data: {
        clicks_per_sheet: data.clicks_per_sheet || 0,
        total_clicks: data.total_clicks || 0,
        click_unit_cost: data.click_unit_cost || 0,
        click_cost: data.click_cost || 0
      },
      // Costos
      paper_cost: data.paper_cost || 0,
      click_cost: data.click_cost || 0,
      machine_cost: data.machine_cost || 0,
      total_cost: data.total_cost || 0
    };



    // Generar el resumen del trabajo y añadirlo a la descripción
    const jobSummary = generatePartJobSummary(part, data, budget, copies);
    const currentDesc = budget.description || '';

    // Actualizar la descripción con el resumen del trabajo
    setBudget({
      ...budget,
      description: currentDesc + (currentDesc ? '\n\n' : '') + jobSummary
    });

    // Actualizar la parte con los resultados del cálculo
    const updatedParts = [...budgetParts];
    updatedParts[partIndex] = {
      ...part,
      sheetCalculation: normalizedData,
      paperCost,
      machineCost,
      clickCost,
      totalCost: paperCost + machineCost + clickCost
    };

    setBudgetParts(updatedParts);

    // Mostrar en consola los datos normalizados que se pasan al componente
    console.log('Datos normalizados para el componente:', normalizedData);
    console.log('JSON de datos normalizados:', JSON.stringify(normalizedData, null, 2));

    // Verificar si setSheetCalculation está disponible antes de llamarlo
    if (typeof setSheetCalculation === 'function') {
      setSheetCalculation(normalizedData);
    }

    // Actualizar la parte actual y mostrar el modal
    if (typeof setCurrentCalculatedPart === 'function') {
      setCurrentCalculatedPart(part);
    }

    // Mostrar el modal si la función está disponible
    if (typeof setSheetCalculationModal === 'function') {
      setSheetCalculationModal(true);
    }

    // Finalizar el estado de cálculo
    if (typeof setCalculatingSheets === 'function') {
      setCalculatingSheets(false);
    }

    return normalizedData;
  } catch (error) {
    console.error('Error al calcular pliegos digital:', error);
    showSnackbar(`Error al calcular pliegos: ${error.message}`, 'error');
    setCalculatingSheets(false);
    return null;
  }
};

export default {
  calculateDigitalSheets
};
