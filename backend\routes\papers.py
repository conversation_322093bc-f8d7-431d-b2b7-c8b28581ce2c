from fastapi import APIRouter, HTTPException
from typing import List
from models.models import Paper, PaperCreate, PaperUpdate
from utils.catalog_manager import load_paper_catalog, save_paper_catalog

router = APIRouter(
    prefix="/papers",
    tags=["papers"],
    responses={404: {"description": "Papel no encontrado"}}
)

# Variable global para el catálogo
paper_catalog = load_paper_catalog()

@router.get("/", response_model=List[Paper])
def get_all_papers():
    try:
        global paper_catalog
        paper_catalog = load_paper_catalog()
        return paper_catalog
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error al obtener el catálogo de papeles: {str(e)}"
        )

@router.get("/{paper_id}", response_model=Paper)
def get_paper_by_id(paper_id: str):
    for paper in paper_catalog:
        if paper["product_id"] == paper_id:
            return paper
    raise HTTPException(status_code=404, detail="Papel no encontrado")

@router.post("/", response_model=Paper)
def create_paper(paper: PaperCreate):
    if any(p["product_id"] == paper.product_id for p in paper_catalog):
        raise HTTPException(
            status_code=400,
            detail=f"Ya existe un papel con el ID {paper.product_id}"
        )
    
    new_paper = paper.model_dump()
    paper_catalog.append(new_paper)
    save_paper_catalog(paper_catalog)
    return new_paper

@router.put("/{paper_id}", response_model=Paper)
def update_paper(paper_id: str, paper_update: PaperUpdate):
    for paper in paper_catalog:
        if paper["product_id"] == paper_id:
            update_data = paper_update.model_dump(exclude_unset=True)
            paper.update(update_data)
            save_paper_catalog(paper_catalog)
            return paper
    raise HTTPException(status_code=404, detail="Papel no encontrado")

@router.delete("/{paper_id}")
def delete_paper(paper_id: str):
    for i, paper in enumerate(paper_catalog):
        if paper["product_id"] == paper_id:
            paper_catalog.pop(i)
            save_paper_catalog(paper_catalog)
            return {"message": f"Papel {paper_id} eliminado correctamente"}
    raise HTTPException(status_code=404, detail="Papel no encontrado")
