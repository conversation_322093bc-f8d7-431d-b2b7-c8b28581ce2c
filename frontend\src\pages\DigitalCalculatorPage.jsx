import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Snackbar,
  CircularProgress,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  TableHead,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Switch,
  FormControlLabel
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CalculateIcon from '@mui/icons-material/Calculate';
import PrintIcon from '@mui/icons-material/Print';
import TimerIcon from '@mui/icons-material/Timer';
import SpeedIcon from '@mui/icons-material/Speed';
import EuroIcon from '@mui/icons-material/Euro';
import TuneIcon from '@mui/icons-material/Tune';
import SettingsIcon from '@mui/icons-material/Settings';
import digitalCalculatorService from '../services/digitalCalculatorService';

const DigitalCalculatorPage = () => {
  // Estado para las máquinas digitales
  const [machines, setMachines] = useState([]);
  const [selectedMachine, setSelectedMachine] = useState('');
  const [machineDetails, setMachineDetails] = useState(null);

  // Estado para los papeles
  const [papers, setPapers] = useState([]);
  const [selectedPaper, setSelectedPaper] = useState('');

  // Estado para el formulario
  const [formData, setFormData] = useState({
    // Campos comunes
    copies: 500,
    color_config: '4/4', // Opciones: '4/4', '4/0', '1/1', '1/0'
    custom_print_time: '',

    // Campos para el cálculo
    num_paginas: 40,
    ancho_pagina: 210,
    alto_pagina: 297
  });

  // Estado para los resultados
  const [calculationResult, setCalculationResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showCustomParams, setShowCustomParams] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Cargar máquinas al montar el componente
  useEffect(() => {
    fetchMachines();
  }, []);

  // Cargar detalles de la máquina seleccionada y papeles compatibles
  useEffect(() => {
    console.log('useEffect - selectedMachine cambió a:', selectedMachine);

    if (selectedMachine && selectedMachine !== '') {
      console.log('Cargando detalles y papeles para máquina:', selectedMachine);
      fetchMachineDetails(selectedMachine);
      fetchPapers(selectedMachine); // Cargar papeles compatibles con la máquina seleccionada
    } else {
      console.log('No hay máquina seleccionada, cargando todos los papeles');
      setMachineDetails(null);
      fetchPapers(); // Cargar todos los papeles si no hay máquina seleccionada
    }
  }, [selectedMachine]);

  // Función para cargar las máquinas digitales
  const fetchMachines = async () => {
    try {
      const digitalMachines = await digitalCalculatorService.getDigitalMachines();
      setMachines(digitalMachines);
    } catch (error) {
      console.error('Error al cargar las máquinas:', error);
      setSnackbar({
        open: true,
        message: 'Error al cargar las máquinas. Por favor, inténtelo de nuevo.',
        severity: 'error'
      });
    }
  };

  // Función para cargar los papeles, opcionalmente filtrados por máquina
  const fetchPapers = async (machineId = null) => {
    try {
      console.log('Cargando papeles para máquina:', machineId);

      const papers = await digitalCalculatorService.getPapers(machineId);
      console.log('Papeles recibidos en el componente:', papers.length);

      setPapers(papers);

      // Mostrar mensaje informativo sobre el filtrado de papeles
      if (machineId && papers.length > 0) {
        setSnackbar({
          open: true,
          message: `Se muestran ${papers.length} papeles compatibles con la máquina seleccionada.`,
          severity: 'info'
        });
      }

      // Si hay una máquina seleccionada y el papel actual no es compatible, resetear la selección
      if (machineId && selectedPaper) {
        const isPaperCompatible = papers.some(paper =>
          paper.product_id === selectedPaper || paper.paper_id === selectedPaper
        );

        console.log('Papel seleccionado compatible:', isPaperCompatible);

        if (!isPaperCompatible) {
          setSelectedPaper('');
          setSnackbar({
            open: true,
            message: 'El papel seleccionado no es compatible con esta máquina. Se ha resetado la selección.',
            severity: 'warning'
          });
        }
      }
    } catch (error) {
      console.error('Error al cargar los papeles:', error);
      setPapers([]);
      setSnackbar({
        open: true,
        message: 'Error al cargar los papeles. Por favor, inténtelo de nuevo.',
        severity: 'error'
      });
    }
  };

  // Función para cargar los detalles de una máquina
  const fetchMachineDetails = async (machineId) => {
    try {
      const machineDetails = await digitalCalculatorService.getDigitalMachineDetails(machineId);
      setMachineDetails(machineDetails);
    } catch (error) {
      console.error('Error al cargar los detalles de la máquina:', error);
      setSnackbar({
        open: true,
        message: 'Error al cargar los detalles de la máquina. Por favor, inténtelo de nuevo.',
        severity: 'error'
      });
    }
  };

  // Función para manejar cambios en el formulario
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Para campos numéricos, convertir a número
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? '' : Number(value)
      });
    } else if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: checked
      });
    } else if (name === 'color_config') {
      // Asegurarse de que color_config siempre tenga un valor válido
      setFormData({
        ...formData,
        [name]: value || '4/4'  // Si value es undefined o vacío, usar '4/4'
      });
      console.log(`Configuración de colores actualizada a: ${value || '4/4'}`);
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Función para manejar el cambio de máquina
  const handleMachineChange = (e) => {
    const newMachineId = e.target.value;
    console.log('Máquina seleccionada:', newMachineId);

    setSelectedMachine(newMachineId);

    // Limpiar los campos personalizados pero mantener la configuración de colores
    setFormData({
      ...formData,
      custom_setup_time: '',
      custom_print_time: '',
      // Asegurarse de que color_config siempre tenga un valor válido
      color_config: formData.color_config || '4/4'
    });

    // Resetear la selección de papel
    setSelectedPaper('');
  };

  // Función para manejar el cambio de papel
  const handlePaperChange = (e) => {
    setSelectedPaper(e.target.value);
  };

  // Función para manejar el cambio de switch
  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };

  // Función para calcular costes y tiempos
  const handleCalculate = async () => {
    if (!selectedMachine) {
      setSnackbar({
        open: true,
        message: 'Por favor, seleccione una máquina',
        severity: 'warning'
      });
      return;
    }

    try {
      setLoading(true);

      // Convertir la configuración de colores en front_colors y back_colors
      let front_colors, back_colors;

      switch (formData.color_config) {
        case '4/4':
          front_colors = 4;
          back_colors = 4;
          break;
        case '4/0':
          front_colors = 4;
          back_colors = 0;
          break;
        case '1/1':
          front_colors = 1;
          back_colors = 1;
          break;
        case '1/0':
          front_colors = 1;
          back_colors = 0;
          break;
        default:
          front_colors = 4;
          back_colors = 4;
      }

      // Obtener las dimensiones del pliego del papel seleccionado
      let ancho_pliego = 0;
      let alto_pliego = 0;

      // Si hay un papel seleccionado, usar sus dimensiones
      if (selectedPaper && papers.length > 0) {
        const selectedPaperObj = papers.find(paper =>
          paper.product_id === selectedPaper || paper.paper_id === selectedPaper
        );

        if (selectedPaperObj) {
          ancho_pliego = selectedPaperObj.dimension_width || 0;
          alto_pliego = selectedPaperObj.dimension_height || 0;
          console.log(`Usando dimensiones del papel seleccionado: ${ancho_pliego}x${alto_pliego}mm`);
        }
      }

      // Si no hay papel seleccionado o no se encontraron dimensiones, usar valores por defecto para SRA3
      if (ancho_pliego === 0 || alto_pliego === 0) {
        ancho_pliego = 320;
        alto_pliego = 450;
        console.log(`Usando dimensiones por defecto para SRA3: ${ancho_pliego}x${alto_pliego}mm`);
      }

      // Preparar los datos para el cálculo
      const requestData = {
        machine_id: selectedMachine,
        copies: parseInt(formData.copies),
        front_colors: front_colors,
        back_colors: back_colors,
        paper_id: selectedPaper || undefined,
        num_paginas: parseInt(formData.num_paginas),
        ancho_pagina: parseFloat(formData.ancho_pagina),
        alto_pagina: parseFloat(formData.alto_pagina),
        ancho_pliego: ancho_pliego,
        alto_pliego: alto_pliego,
        binding_type: "gathering" // Tipo de encuadernado por defecto
      };

      // Añadir campos personalizados si tienen valor
      if (formData.custom_setup_time !== '') {
        requestData.custom_setup_time = parseInt(formData.custom_setup_time);
      }

      if (formData.custom_print_time !== '') {
        requestData.custom_print_time = parseFloat(formData.custom_print_time);
      }

      // Mostrar los datos enviados en formato JSON para facilitar la depuración
      console.log('Enviando datos para cálculo digital:', requestData);
      console.log('JSON enviado:', JSON.stringify(requestData, null, 2));

      // Realizar la solicitud al endpoint
      const rawResult = await digitalCalculatorService.calculateDigital(requestData);
      console.log('Resultado del cálculo:', rawResult);
      console.log('JSON recibido:', JSON.stringify(rawResult, null, 2));

      if (!rawResult) {
        throw new Error('No se recibió respuesta del servidor');
      }

      // Adaptar la respuesta para que sea compatible con la interfaz
      // La respuesta actual solo tiene mejor_combinacion, pero necesitamos más campos
      const result = {
        // Usar los datos de mejor_combinacion
        ...rawResult.mejor_combinacion,

        // Añadir campos adicionales para la interfaz
        total_pages: parseInt(formData.num_paginas),
        copies: parseInt(formData.copies),
        total_pliegos: rawResult.mejor_combinacion.total_pliegos,

        // Calcular tiempos (valores aproximados)
        printing_time_minutes: 0,
        total_time_minutes: 0,
        total_time_hours: 0,

        // Calcular costes (valores aproximados)
        click_unit_cost: machineDetails?.click_color_cost || 0.15,
        click_cost: 0,
        total_cost: 0,

        // Añadir información del papel
        paper_name: selectedPaper ? papers.find(p => p.product_id === selectedPaper)?.name || 'Papel personalizado' : 'Papel por defecto',

        // Velocidad de impresión
        print_speed: machineDetails?.speed || 100
      };

      // Calcular clicks y costes
      if (result.total_clicks) {
        // Usar el click_unit_cost según si es color o B/N
        result.click_unit_cost = result.is_color
          ? (machineDetails?.click_color_cost || 0.15)
          : (machineDetails?.click_bw_cost || 0.05);

        // Calcular coste de clicks
        result.click_cost = result.total_clicks * result.click_unit_cost;

        // Calcular tiempo de impresión (aproximado)
        const a4_per_minute = machineDetails?.speed || 100;
        const total_a4_pages = result.total_pages * parseInt(formData.copies);
        result.printing_time_minutes = total_a4_pages / a4_per_minute;
        result.total_time_minutes = result.printing_time_minutes;
        result.total_time_hours = result.total_time_minutes / 60;

        // Calcular coste total
        result.total_cost = result.click_cost;
      }

      setCalculationResult(result);

      // Mostrar mensaje de éxito con detalles
      setSnackbar({
        open: true,
        message: `Cálculo realizado con éxito. Total: ${result.total_pliegos} pliegos, ${result.total_clicks} clicks`,
        severity: 'success'
      });
    } catch (error) {
      console.error('Error al calcular:', error);

      // Mostrar mensaje de error detallado
      let errorMessage = 'Error desconocido';

      if (error.response && error.response.data) {
        errorMessage = error.response.data.detail || JSON.stringify(error.response.data);
      } else if (error.message) {
        errorMessage = error.message;
      }

      console.error('Detalles del error:', error);

      // Limpiar el resultado anterior si hay un error
      setCalculationResult(null);

      setSnackbar({
        open: true,
        message: `Error al calcular: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Función para cerrar el snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Función para resetear el formulario
  const handleReset = () => {
    setFormData({
      // Campos comunes
      copies: 500,
      color_config: '4/4', // Color duplex por defecto
      custom_setup_time: '',
      custom_print_time: '',

      // Campos para el cálculo
      num_paginas: 40,
      ancho_pagina: 210,
      alto_pagina: 297
    });
    setSelectedMachine('');
    setSelectedPaper('');
    setCalculationResult(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <CalculateIcon sx={{ mr: 1 }} />
        Calculadora Avanzada de Digital
      </Typography>

      <Grid container spacing={3}>
        {/* Columna izquierda - Formulario */}
        <Grid item xs={12} md={6}>
          <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Parámetros de Cálculo
            </Typography>

            {calculationResult && (
              <Paper elevation={2} sx={{ p: 2, mb: 3, bgcolor: '#f9f9f9' }}>
                <Typography variant="h6" gutterBottom sx={{ borderBottom: '1px solid #ddd', pb: 1 }}>
                  Esquema utilizado
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    Total: {calculationResult.total_pliegos} pliegos / {calculationResult.total_clicks} clicks
                  </Typography>
                  <Typography variant="body2">
                    Configuración: {calculationResult.is_color ? 'Color' : 'Blanco y negro'}, {calculationResult.is_duplex ? 'Doble cara' : 'Una cara'}
                  </Typography>
                  <Typography variant="body2">
                    Total de páginas por ejemplar: {calculationResult.total_pages} páginas ({calculationResult.a4_per_sheet} páginas A4 por pliego)
                  </Typography>
                </Box>

                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                      <TableRow>
                        <TableCell>Esquema</TableCell>
                        <TableCell>Pliegos</TableCell>
                        <TableCell>Págs/Pliego</TableCell>
                        <TableCell>Disposición</TableCell>
                        <TableCell>Tipo Pliego</TableCell>
                        <TableCell>Clicks</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {calculationResult.esquemas_utilizados && calculationResult.esquemas_utilizados.map((esquema, index) => (
                        <TableRow key={index} sx={{ bgcolor: calculationResult.is_color ? '#fff8e1' : '#f1f8e9' }}>
                          <TableCell>{esquema.nombre}</TableCell>
                          <TableCell>{esquema.numero_pliegos}</TableCell>
                          <TableCell>{esquema.paginas_por_pliego}</TableCell>
                          <TableCell>
                            {esquema.disposicion ? `${esquema.disposicion.paginas_ancho} x ${esquema.disposicion.paginas_alto}` : ''}
                          </TableCell>
                          <TableCell>
                            {calculationResult.is_duplex ? 'Duplex' : 'Simplex'}
                            <br />
                            <Typography variant="caption" color="text.secondary">
                              {calculationResult.is_color ? 'Color' : 'B/N'}
                            </Typography>
                          </TableCell>
                          <TableCell>{esquema.numero_pliegos * (calculationResult.is_duplex ? 2 : 1) * parseInt(formData.copies)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            )}

            <Grid container spacing={2}>
              {/* Selección de máquina */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <FormControl fullWidth>
                    <InputLabel id="machine-select-label">Máquina Digital</InputLabel>
                    <Select
                      labelId="machine-select-label"
                      id="machine-select"
                      value={selectedMachine}
                      label="Máquina Digital"
                      onChange={handleMachineChange}
                    >
                      <MenuItem value="">
                        <em>Seleccione una máquina</em>
                      </MenuItem>
                      {machines && machines.length > 0 ? machines.map((machine) => (
                        <MenuItem key={machine.machine_id} value={machine.machine_id}>
                          {machine.name} ({machine.model})
                        </MenuItem>
                      )) : <MenuItem disabled>No hay máquinas digitales disponibles</MenuItem>}
                    </Select>
                    <FormHelperText>Seleccione la máquina digital para el cálculo</FormHelperText>
                  </FormControl>

                  <Button
                    sx={{ ml: 1, minWidth: 'auto', mt: 1 }}
                    color="primary"
                    variant={showCustomParams ? "contained" : "outlined"}
                    onClick={() => setShowCustomParams(!showCustomParams)}
                    disabled={!selectedMachine}
                    title="Parámetros personalizados"
                  >
                    <TuneIcon />
                  </Button>
                </Box>
              </Grid>

              {/* Parámetros personalizados */}
              {showCustomParams && selectedMachine && (
                <Grid item xs={12}>
                  <Paper elevation={1} sx={{ p: 2, bgcolor: '#f8f9fa', mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <SettingsIcon sx={{ mr: 1, fontSize: '1rem' }} />
                      Parámetros Personalizados
                    </Typography>

                    <Grid container spacing={2}>


                      {/* Tiempo de impresión personalizado */}
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Tiempo de impresión"
                          name="custom_print_time"
                          type="number"
                          value={formData.custom_print_time}
                          onChange={handleInputChange}
                          InputProps={{
                            inputProps: { min: 0, step: 0.1 },
                            endAdornment: <InputAdornment position="end">horas</InputAdornment>,
                          }}
                          helperText="Tiempo personalizado de impresión"
                          size="small"
                        />
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              )}

              {/* Selección de papel (opcional) */}
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="paper-select-label">Papel (opcional)</InputLabel>
                  <Select
                    labelId="paper-select-label"
                    id="paper-select"
                    value={selectedPaper}
                    label="Papel (opcional)"
                    onChange={handlePaperChange}
                  >
                    <MenuItem value="">
                      <em>Sin papel</em>
                    </MenuItem>
                    {papers && papers.length > 0 ? papers.map((paper) => (
                      <MenuItem
                        key={paper.product_id}
                        value={paper.product_id}
                        sx={selectedMachine ? { fontWeight: 'bold', color: 'primary.main' } : {}}
                      >
                        {paper.descriptive_name || paper.name}
                        ({paper.dimension_width}x{paper.dimension_height}mm, {paper.weight}g)
                        {selectedMachine && ' ✓'}
                      </MenuItem>
                    )) : <MenuItem disabled>
                      {selectedMachine
                        ? "No hay papeles compatibles con esta máquina"
                        : "No hay papeles disponibles"}
                    </MenuItem>}
                  </Select>
                  <FormHelperText>
                    {selectedMachine
                      ? "Solo se muestran papeles compatibles con la máquina seleccionada"
                      : "Seleccione una máquina para filtrar papeles compatibles"}
                  </FormHelperText>
                </FormControl>
              </Grid>

              {/* Número de ejemplares */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Número de ejemplares"
                  name="copies"
                  type="number"
                  value={formData.copies}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 1 }
                  }}
                  helperText="Número de copias a imprimir"
                />
              </Grid>

              {/* Número de páginas */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Número de páginas"
                  name="num_paginas"
                  type="number"
                  value={formData.num_paginas}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 1 }
                  }}
                  helperText="Número total de páginas"
                />
              </Grid>

              {/* Ancho de página */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Ancho de página (mm)"
                  name="ancho_pagina"
                  type="number"
                  value={formData.ancho_pagina}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 1 },
                    endAdornment: <InputAdornment position="end">mm</InputAdornment>,
                  }}
                  helperText="Ancho de la página en milímetros (ej: 210 para A4)"
                />
              </Grid>

              {/* Alto de página */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Alto de página (mm)"
                  name="alto_pagina"
                  type="number"
                  value={formData.alto_pagina}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 1 },
                    endAdornment: <InputAdornment position="end">mm</InputAdornment>,
                  }}
                  helperText="Alto de la página en milímetros (ej: 297 para A4)"
                />
              </Grid>

              {/* Configuración de colores */}
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="color-config-label">Configuración de colores</InputLabel>
                  <Select
                    labelId="color-config-label"
                    id="color-config"
                    name="color_config"
                    value={formData.color_config || '4/4'} /* Valor por defecto si es undefined */
                    label="Configuración de colores"
                    onChange={handleInputChange}
                  >
                    <MenuItem value="4/4">Color Duplex (4/4)</MenuItem>
                    <MenuItem value="4/0">Color Simplex (4/0)</MenuItem>
                    <MenuItem value="1/1">B/N Duplex (1/1)</MenuItem>
                    <MenuItem value="1/0">B/N Simplex (1/0)</MenuItem>
                  </Select>
                  <FormHelperText>
                    {formData.color_config === '4/4' && 'Impresión a doble cara a color (CMYK)'}
                    {formData.color_config === '4/0' && 'Impresión a una cara a color (CMYK)'}
                    {formData.color_config === '1/1' && 'Impresión a doble cara en blanco y negro'}
                    {formData.color_config === '1/0' && 'Impresión a una cara en blanco y negro'}
                  </FormHelperText>
                </FormControl>
              </Grid>
            </Grid>
          </Paper>

          {/* Botones de acción */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleReset}
              disabled={loading}
            >
              Resetear
            </Button>

            <Button
              variant="contained"
              color="primary"
              onClick={handleCalculate}
              disabled={loading || !selectedMachine}
              startIcon={loading ? <CircularProgress size={24} /> : <CalculateIcon />}
            >
              {loading ? 'Calculando...' : 'Calcular'}
            </Button>
          </Box>
        </Grid>

        {/* Columna derecha - Resultados */}
        <Grid item xs={12} md={6}>
          {machineDetails && (
            <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <PrintIcon sx={{ mr: 1 }} />
                Información de la Máquina
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Nombre:</Typography>
                  <Typography variant="body1">{machineDetails.name}</Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Modelo:</Typography>
                  <Typography variant="body1">{machineDetails.model}</Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Coste por hora:</Typography>
                  <Typography variant="body1">{machineDetails.hourly_cost} €/h</Typography>
                </Grid>



                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Velocidad:</Typography>
                  <Typography variant="body1">
                    {machineDetails.speed ? `${machineDetails.speed} A4/minuto` : 'No definida'}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Coste por click (color):</Typography>
                  <Typography variant="body1">
                    {machineDetails.click_color_cost ? `${machineDetails.click_color_cost.toFixed(4)} €` : '0.15 €'}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Coste por click (B/N):</Typography>
                  <Typography variant="body1">
                    {machineDetails.click_bw_cost ? `${machineDetails.click_bw_cost.toFixed(4)} €` : '0.05 €'}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          )}

          {calculationResult && (
            <Paper elevation={3} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <EuroIcon sx={{ mr: 1 }} />
                Resultados del Cálculo
              </Typography>

              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center' }}>
                    <TimerIcon sx={{ mr: 1, fontSize: '1.2rem' }} />
                    Tiempos de Impresión
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableBody>

                        <TableRow>
                          <TableCell component="th" scope="row">Tiempo de impresión:</TableCell>
                          <TableCell align="right">{calculationResult.printing_time_minutes ? calculationResult.printing_time_minutes.toFixed(2) : '0.00'} minutos</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Tiempo total:</TableCell>
                          <TableCell align="right">
                            <Typography fontWeight="bold">
                              {calculationResult.total_time_minutes ? calculationResult.total_time_minutes.toFixed(2) : '0.00'} minutos
                            </Typography>
                            <Typography variant="caption" display="block" color="text.secondary">
                              {calculationResult.total_time_hours ? calculationResult.total_time_hours.toFixed(2) : '0.00'} horas
                            </Typography>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>



              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center' }}>
                    <SpeedIcon sx={{ mr: 1, fontSize: '1.2rem' }} />
                    Detalles de Impresión
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableBody>
                        <TableRow>
                          <TableCell component="th" scope="row">Páginas por ejemplar:</TableCell>
                          <TableCell align="right">{calculationResult.total_pages}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Páginas A4 por pliego:</TableCell>
                          <TableCell align="right">{calculationResult.a4_per_sheet}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Pliegos por ejemplar:</TableCell>
                          <TableCell align="right">
                            {calculationResult.total_pliegos}
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Total de pliegos:</TableCell>
                          <TableCell align="right">{calculationResult.total_sheets}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Clicks por pliego:</TableCell>
                          <TableCell align="right">{calculationResult.clicks_per_sheet}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Total de clicks:</TableCell>
                          <TableCell align="right">{calculationResult.total_clicks}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Tipo de impresión:</TableCell>
                          <TableCell align="right">
                            {calculationResult.is_duplex ? 'Doble cara (duplex)' : 'Una cara (simplex)'},
                            {calculationResult.is_color ? ' Color' : ' Blanco y negro'}
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Velocidad de impresión:</TableCell>
                          <TableCell align="right">{calculationResult.print_speed} A4/minuto</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>

              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center' }}>
                    <EuroIcon sx={{ mr: 1, fontSize: '1.2rem' }} />
                    Costes
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                          <TableCell>Concepto</TableCell>
                          <TableCell align="right">Coste</TableCell>
                          <TableCell align="right">Detalles</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell component="th" scope="row">Coste por click:</TableCell>
                          <TableCell align="right">{calculationResult.click_unit_cost ? calculationResult.click_unit_cost.toFixed(4) : '0.0000'} €</TableCell>
                          <TableCell align="right">
                            {calculationResult.is_color ? 'Color' : 'Blanco y negro'}
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Coste de clicks:</TableCell>
                          <TableCell align="right">{calculationResult.click_cost ? calculationResult.click_cost.toFixed(2) : '0.00'} €</TableCell>
                          <TableCell align="right">
                            {calculationResult.total_clicks || 0} clicks × {calculationResult.click_unit_cost ? calculationResult.click_unit_cost.toFixed(4) : '0.0000'} €
                          </TableCell>
                        </TableRow>
                        {calculationResult.machine_cost && (
                          <TableRow>
                            <TableCell component="th" scope="row">Coste de máquina:</TableCell>
                            <TableCell align="right">{calculationResult.machine_cost ? calculationResult.machine_cost.toFixed(2) : '0.00'} €</TableCell>
                            <TableCell align="right">
                              {calculationResult.total_time_hours ? calculationResult.total_time_hours.toFixed(2) : '0.00'} horas × {machineDetails?.hourly_cost || 0} €/h
                            </TableCell>
                          </TableRow>
                        )}
                        {calculationResult.paper_cost && (
                          <TableRow>
                            <TableCell component="th" scope="row">Coste de papel:</TableCell>
                            <TableCell align="right">{calculationResult.paper_cost ? calculationResult.paper_cost.toFixed(2) : '0.00'} €</TableCell>
                            <TableCell align="right">
                              {calculationResult.paper_name}
                            </TableCell>
                          </TableRow>
                        )}
                        <TableRow sx={{ bgcolor: '#e3f2fd' }}>
                          <TableCell component="th" scope="row">
                            <Typography fontWeight="bold">COSTE TOTAL:</Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography fontWeight="bold">{calculationResult.total_cost ? calculationResult.total_cost.toFixed(2) : '0.00'} €</Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="caption">
                              {calculationResult.copies} ejemplares
                            </Typography>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Coste por ejemplar:</TableCell>
                          <TableCell align="right">
                            {calculationResult.total_cost && calculationResult.copies ? (calculationResult.total_cost / calculationResult.copies).toFixed(4) : '0.0000'} €
                          </TableCell>
                          <TableCell></TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Snackbar para mensajes */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DigitalCalculatorPage;
