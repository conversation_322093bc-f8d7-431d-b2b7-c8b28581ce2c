# Budget Form Change Service

## Descripción

El `budgetFormChangeService` es un servicio especializado que extrae la lógica compleja de manejo de cambios en el formulario del componente `BudgetForm`. Este servicio gestiona todos los cambios de campos, validaciones, y actualizaciones de estado relacionadas con el formulario de presupuesto.

## Motivación

La función `handleChange` original en `BudgetForm` tenía más de 228 líneas de código con múltiples responsabilidades:
- Actualización de campos básicos
- Marcado de partes como desactualizadas
- Carga de información de productos
- Configuración de acabados predeterminados
- Creación de partes basadas en productos
- Parseo de configuraciones de colores

Al extraer esta lógica a un servicio especializado, conseguimos:
- **Mejor modularización**: Separación clara de responsabilidades
- **Reutilización**: Funciones disponibles para otros componentes
- **Testabilidad**: Funciones más pequeñas y fáciles de probar
- **Mantenibilidad**: Código más organizado y legible

## Estructura del Servicio

### Funciones Principales

#### `handleFormChange(params)`
Función principal que maneja todos los cambios en el formulario, delegando a funciones específicas según el tipo de campo.

**Parámetros:**
```javascript
{
  event: Event,                    // Evento del formulario
  budget: Object,                  // Estado actual del presupuesto
  budgetParts: Array,             // Partes del presupuesto
  processes: Array,               // Procesos disponibles
  isEditMode: boolean,            // Modo edición
  setBudget: Function,            // Setter para presupuesto
  setBudgetParts: Function,       // Setter para partes
  setProductConfig: Function,     // Setter para configuración de producto
  setSelectedProcesses: Function, // Setter para procesos seleccionados
  setCalculatedProcessCost: Function, // Setter para costo calculado
  createEmptyPart: Function,      // Función para crear parte vacía
  showSnackbar: Function          // Función para mostrar mensajes
}
```

#### `handleJobTypeChange(params)`
Maneja específicamente el cambio del tipo de trabajo, cargando información del producto desde el catálogo.

### Funciones de Gestión de Estado

#### `updateBudgetField(name, value, setBudget)`
Actualiza un campo específico del presupuesto de forma inmutable.

#### `markPartsAsOutdated(budgetParts, setBudgetParts, showSnackbar)`
Marca las partes con cálculos previos como desactualizadas cuando cambian las copias.

### Funciones de Procesamiento

#### `parseColorConfig(colorString)`
Parsea una cadena de configuración de colores (ej: "4/4") a un objeto estructurado.

#### `calculateProcessesTotalCost(processes, copies)`
Calcula el costo total de un array de procesos basado en cantidades y costos unitarios.

#### `prepareProcessesWithQuantities(processes, copies)`
Prepara procesos añadiendo cantidades basadas en el número de copias.

### Funciones de Creación de Partes

#### `createPartsFromProduct(product, createEmptyPart, parseColorConfig)`
Crea partes del presupuesto basadas en las partes definidas en un producto del catálogo.

#### `createDefaultPart(product, createEmptyPart, parseColorConfig)`
Crea una parte por defecto cuando el producto no tiene partes específicas.

#### `loadDefaultFinishing(product, processes, copies, setSelectedProcesses, setCalculatedProcessCost)`
Carga los acabados predeterminados de un producto y calcula sus costos.

## Uso en BudgetForm

### Antes (Código Original)
```javascript
const handleChange = (e) => {
  // 228+ líneas de lógica compleja
  const { name, value } = e.target;
  setBudget(prev => ({ ...prev, [name]: value }));
  
  if (name === 'copies' && isEditMode) {
    // Lógica para marcar partes como desactualizadas
  }
  
  if (name === 'jobType' && value) {
    // Lógica compleja para cargar producto
    // Crear partes
    // Configurar acabados
    // etc...
  }
};
```

### Después (Con Servicio)
```javascript
const handleChange = async (e) => {
  try {
    await budgetFormChangeService.handleFormChange({
      event: e,
      budget,
      budgetParts,
      processes,
      isEditMode,
      setBudget,
      setBudgetParts,
      setProductConfig,
      setSelectedProcesses,
      setCalculatedProcessCost,
      createEmptyPart,
      showSnackbar
    });
  } catch (error) {
    console.error('Error al manejar cambio en el formulario:', error);
    showSnackbar(`Error al procesar el cambio: ${error.message}`, 'error');
  }
};
```

## Beneficios Obtenidos

### 1. **Reducción Dramática de Complejidad**
- BudgetForm: De ~228 líneas a ~15 líneas
- Función más legible y mantenible

### 2. **Modularización Granular**
- Cada responsabilidad en su propia función
- Fácil identificación de problemas
- Reutilización de componentes

### 3. **Mejor Manejo de Errores**
- Try-catch centralizado
- Mensajes de error específicos
- Logging detallado

### 4. **Testabilidad Mejorada**
- 20+ tests unitarios
- Cobertura de casos edge
- Fácil mockeo de dependencias

## Flujo de Trabajo del Servicio

```
handleFormChange()
├── updateBudgetField()           // Siempre se ejecuta
├── name === 'copies' && isEditMode
│   └── markPartsAsOutdated()     // Marca partes como desactualizadas
└── name === 'jobType' && value
    └── handleJobTypeChange()
        ├── ProductService.getProductByType()
        ├── createPartsFromProduct() || createDefaultPart()
        └── loadDefaultFinishing()
            ├── prepareProcessesWithQuantities()
            └── calculateProcessesTotalCost()
```

## Ejemplo de Uso Independiente

```javascript
import budgetFormChangeService from '../services/budgetFormChangeService';

// Usar solo el parseo de colores
const colorConfig = budgetFormChangeService.parseColorConfig('4/0');
// { frontColors: 4, backColors: 0, pantones: 0 }

// Calcular costo de procesos
const totalCost = budgetFormChangeService.calculateProcessesTotalCost(processes, 500);

// Crear partes desde producto
const parts = budgetFormChangeService.createPartsFromProduct(
  product, 
  createEmptyPart, 
  budgetFormChangeService.parseColorConfig
);
```

## Integración con Otros Servicios

### ProductService
- Obtiene información de productos del catálogo
- Usado en `handleJobTypeChange`

### Funciones del BudgetForm
- `createEmptyPart`: Para crear nuevas partes
- `showSnackbar`: Para mostrar mensajes al usuario
- Setters de estado: Para actualizar el estado del componente

## Testing

El servicio incluye tests comprehensivos:

```javascript
describe('budgetFormChangeService', () => {
  test('should update budget field correctly', () => { ... });
  test('should mark parts as outdated', () => { ... });
  test('should parse color config', () => { ... });
  test('should handle job type change', () => { ... });
  // ... 20+ tests más
});
```

## Consideraciones de Implementación

### Inmutabilidad
- Todas las actualizaciones de estado son inmutables
- Uso de spread operator para preservar datos existentes

### Manejo de Errores
- Try-catch en operaciones asíncronas
- Mensajes descriptivos para el usuario
- Logging para debugging

### Performance
- Funciones puras donde es posible
- Evita re-renders innecesarios
- Operaciones optimizadas

## Próximos Pasos

1. **Añadir más validaciones** específicas por tipo de campo
2. **Implementar cache** para productos frecuentemente usados
3. **Crear hooks personalizados** para uso en React
4. **Añadir TypeScript** para mejor tipado
5. **Extraer más lógica** de BudgetForm siguiendo este patrón

La refactorización ha sido exitosa, mejorando significativamente la modularización del código mientras mantiene toda la funcionalidad original y añadiendo robustez con tests comprehensivos.
