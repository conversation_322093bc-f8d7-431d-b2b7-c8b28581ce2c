import { buildApiUrl } from '../config';
import { ApiInterceptor, LogService } from './simplifiedServices';

class JDFService {
  /**
   * Genera un JDF a partir de un presupuesto
   * @param {string} budgetId - ID del presupuesto
   * @returns {Promise<Object>} - Objeto JDF generado
   */
  static async generateJDF(budgetId) {
    try {
      const url = buildApiUrl(`/budgets/${budgetId}/jdf`);
      const response = await ApiInterceptor.fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al generar JDF');
      }

      const data = await response.json();

      LogService.logUserAction('generate_jdf', {
        budget_id: budgetId,
        success: true
      });

      return data;
    } catch (error) {
      console.error('Error al generar JDF:', error);

      LogService.logError('Error al generar JDF', {
        budget_id: budgetId,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Obtiene un JDF por ID de presupuesto
   * @param {string} budgetId - ID del presupuesto
   * @returns {Promise<Object>} - Objeto JDF
   */
  static async getJDFByBudgetId(budgetId) {
    try {
      const url = buildApiUrl(`/budgets/${budgetId}/jdf`);
      const response = await ApiInterceptor.fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al obtener JDF');
      }

      const data = await response.json();

      LogService.logUserAction('get_jdf', {
        budget_id: budgetId,
        success: true
      });

      return data;
    } catch (error) {
      console.error('Error al obtener JDF:', error);

      LogService.logError('Error al obtener JDF', {
        budget_id: budgetId,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Determina el estilo de trabajo adecuado según el tipo de producto
   * @param {string} productType - Tipo de producto
   * @returns {string} - Estilo de trabajo (WorkAndBack, Perfecting, WorkAndTurn, Flat)
   */
  static getWorkStyleByProductType(productType) {
    switch (productType) {
      case "Revista":
      case "Libro":
      case "Catálogo":
        return "WorkAndBack";
      case "Folleto":
        return "WorkAndTurn";
      case "Flyer":
      case "Cartel":
        return "Flat";
      case "Tarjeta":
        return "Perfecting";
      default:
        return "Flat";
    }
  }

  /**
   * Obtiene la descripción del estilo de trabajo
   * @param {string} workStyle - Estilo de trabajo
   * @returns {string} - Descripción del estilo de trabajo
   */
  static getWorkStyleDescription(workStyle) {
    switch (workStyle) {
      case "WorkAndBack":
        return "Tira y Retira";
      case "Perfecting":
        return "Perfecting";
      case "WorkAndTurn":
        return "Volteo";
      case "Flat":
        return "Plano";
      default:
        return "Desconocido";
    }
  }

  /**
   * Obtiene un JDF con el esquema completo por ID de presupuesto
   * @param {string} budgetId - ID del presupuesto
   * @returns {Promise<Object>} - Objeto JDF con el esquema completo
   */
  static async getJDFSchemaByBudgetId(budgetId) {
    try {
      const url = buildApiUrl(`/budgets/schema/${budgetId}`);
      const response = await ApiInterceptor.fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al obtener esquema JDF');
      }

      const data = await response.json();

      LogService.logUserAction('get_jdf_schema', {
        budget_id: budgetId,
        success: true
      });

      return data;
    } catch (error) {
      console.error('Error al obtener esquema JDF:', error);

      LogService.logError('Error al obtener esquema JDF', {
        budget_id: budgetId,
        error: error.message
      });

      throw error;
    }
  }
}

export default JDFService;
