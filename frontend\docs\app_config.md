# Componente de Configuración del Sistema

## Descripción

El componente `AppConfig` permite gestionar la configuración global de la aplicación, incluyendo parámetros como el porcentaje de beneficio, información de la empresa, configuración de presupuestos y producción.

## Estructura del Archivo de Configuración

La configuración se almacena en un archivo JSON en el backend (`backend/config/app_config.json`) con la siguiente estructura:

```json
{
  "profit_percentage": 30,
  "default_waste_percentage": 5,
  "default_vat_percentage": 21,
  "default_currency": "€",
  "default_country": "España",
  "default_language": "es-ES",
  "default_date_format": "DD/MM/YYYY",
  "default_time_format": "HH:mm",
  "default_timezone": "Europe/Madrid",
  "company_info": {
    "name": "Imprenta",
    "address": "Calle Principal 123",
    "city": "Madrid",
    "postal_code": "28001",
    "phone": "+34 91 123 4567",
    "email": "<EMAIL>",
    "website": "www.imprenta.com",
    "tax_id": "B12345678"
  },
  "budget_settings": {
    "default_status": "Pendiente",
    "auto_update_status": true,
    "expiration_days": 30
  },
  "production_settings": {
    "default_time_slot": 30,
    "working_hours": {
      "start": "08:00",
      "end": "18:00"
    },
    "working_days": [1, 2, 3, 4, 5]
  }
}
```

## Parámetros de Configuración

### Configuración General

- **profit_percentage**: Porcentaje de beneficio que se añade al coste total para calcular el precio de venta.
- **default_waste_percentage**: Porcentaje de merma que se aplica por defecto en los cálculos.
- **default_vat_percentage**: Porcentaje de IVA que se aplica por defecto.
- **default_currency**: Símbolo de moneda que se muestra en los precios.
- **default_country**: País que se usa por defecto en los cálculos de envío.
- **default_language**: Código de idioma (ej: es-ES, en-US).
- **default_date_format**: Formato de fecha (ej: DD/MM/YYYY).
- **default_time_format**: Formato de hora (ej: HH:mm).
- **default_timezone**: Zona horaria (ej: Europe/Madrid).

### Información de la Empresa

- **company_info.name**: Nombre de la empresa.
- **company_info.address**: Dirección de la empresa.
- **company_info.city**: Ciudad donde se encuentra la empresa.
- **company_info.postal_code**: Código postal de la empresa.
- **company_info.phone**: Teléfono de contacto de la empresa.
- **company_info.email**: Email de contacto de la empresa.
- **company_info.website**: Sitio web de la empresa.
- **company_info.tax_id**: CIF/NIF de la empresa.

### Configuración de Presupuestos

- **budget_settings.default_status**: Estado que se asigna a los nuevos presupuestos.
- **budget_settings.auto_update_status**: Si se actualiza automáticamente el estado de los presupuestos.
- **budget_settings.expiration_days**: Número de días que un presupuesto es válido.

### Configuración de Producción

- **production_settings.default_time_slot**: Duración en minutos de cada intervalo en el planificador.
- **production_settings.working_hours.start**: Hora de inicio de la jornada laboral.
- **production_settings.working_hours.end**: Hora de fin de la jornada laboral.
- **production_settings.working_days**: Días de la semana laborables (1=Lunes, 7=Domingo).

## API de Configuración

El backend proporciona los siguientes endpoints para gestionar la configuración:

- **GET /config**: Obtiene toda la configuración.
- **GET /config/{key}**: Obtiene un valor específico de la configuración.
- **PUT /config**: Actualiza la configuración completa o parcial.
- **PUT /config/{key}**: Actualiza un valor específico de la configuración.

## Servicio de Configuración

El frontend incluye un servicio `ConfigService` que proporciona métodos para interactuar con la API de configuración:

- **getConfig()**: Obtiene toda la configuración.
- **getConfigValue(key)**: Obtiene un valor específico de la configuración.
- **updateConfig(configData)**: Actualiza la configuración completa o parcial.
- **updateConfigValue(key, value)**: Actualiza un valor específico de la configuración.

## Uso del Componente

El componente `AppConfig` se puede acceder desde el menú de configuración en la barra de navegación. Permite editar todos los parámetros de configuración y guardar los cambios.

## Integración con Otros Componentes

La configuración se utiliza en varios componentes de la aplicación:

- El porcentaje de beneficio se utiliza en los cálculos de precios de venta.
- La información de la empresa se utiliza en los documentos generados (presupuestos, facturas, etc.).
- La configuración de presupuestos se utiliza en la creación y gestión de presupuestos.
- La configuración de producción se utiliza en la planificación de la producción.

## Ejemplo de Uso

```jsx
// Obtener toda la configuración
const config = await ConfigService.getConfig();
console.log('Porcentaje de beneficio:', config.profit_percentage);

// Obtener un valor específico
const profitPercentage = await ConfigService.getConfigValue('profit_percentage');
console.log('Porcentaje de beneficio:', profitPercentage);

// Actualizar un valor específico
await ConfigService.updateConfigValue('profit_percentage', 35);
console.log('Porcentaje de beneficio actualizado a 35%');
```
