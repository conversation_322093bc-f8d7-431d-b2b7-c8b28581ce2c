import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Download as DownloadIcon,
  LocalShipping as LocalShippingIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { format, parseISO, subDays, startOfDay, endOfDay, isWithinInterval } from 'date-fns';
import { es } from 'date-fns/locale';
import { buildApiUrl } from '../config';

const ShippingHistory = () => {
  // Estados
  const [shippingRecords, setShippingRecords] = useState([]);
  const [filteredRecords, setFilteredRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [carrierFilter, setCarrierFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [showOnlyInProcess, setShowOnlyInProcess] = useState(true);
  const [carriers, setCarriers] = useState([]);

  // Estados para el diálogo de cambio de estado
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [recordToUpdateStatus, setRecordToUpdateStatus] = useState(null);
  const [newStatus, setNewStatus] = useState('');
  const [statusUpdateLoading, setStatusUpdateLoading] = useState(false);
  const [statusUpdateError, setStatusUpdateError] = useState(null);
  const [statusUpdateSuccess, setStatusUpdateSuccess] = useState(false);

  // Estados para el diálogo de eliminación
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState(null);
  const [deleteSuccess, setDeleteSuccess] = useState(false);

  // Cargar los registros de envío al montar el componente
  useEffect(() => {
    fetchShippingRecords();
  }, []);

  // Filtrar los registros cuando cambian los filtros
  useEffect(() => {
    filterRecords();
  }, [shippingRecords, searchTerm, statusFilter, carrierFilter, dateFilter, showOnlyInProcess]);

  // Función para obtener los registros de envío
  const fetchShippingRecords = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(buildApiUrl('/shipping'));

      if (!response.ok) {
        throw new Error(`Error al obtener los registros de envío: ${response.statusText}`);
      }

      const data = await response.json();
      setShippingRecords(data);

      // Extraer transportistas únicos para el filtro
      const uniqueCarriers = [...new Set(data.map(record => record.carrier))].filter(Boolean);
      setCarriers(uniqueCarriers);

    } catch (err) {
      console.error('Error al cargar los registros de envío:', err);
      setError(`Error al cargar los registros de envío: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Función para filtrar los registros
  const filterRecords = () => {
    let filtered = [...shippingRecords];

    // Filtrar por término de búsqueda
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(record =>
        record.ot_number.toLowerCase().includes(term) ||
        record.client_name.toLowerCase().includes(term) ||
        (record.tracking_number && record.tracking_number.toLowerCase().includes(term))
      );
    }

    // Filtrar por estado
    if (statusFilter !== 'all') {
      filtered = filtered.filter(record => record.delivery_status === statusFilter);
    }

    // Filtrar solo los envíos "En Proceso" si el interruptor está activado
    if (showOnlyInProcess) {
      filtered = filtered.filter(record => record.delivery_status === 'En Proceso');
    }

    // Filtrar por transportista
    if (carrierFilter !== 'all') {
      filtered = filtered.filter(record => record.carrier === carrierFilter);
    }

    // Filtrar por fecha
    if (dateFilter !== 'all') {
      const today = new Date();
      let startDate;

      switch (dateFilter) {
        case 'today':
          startDate = startOfDay(today);
          break;
        case 'week':
          startDate = subDays(today, 7);
          break;
        case 'month':
          startDate = subDays(today, 30);
          break;
        default:
          startDate = null;
      }

      if (startDate) {
        filtered = filtered.filter(record => {
          try {
            const recordDate = parseISO(record.shipping_date);
            return isWithinInterval(recordDate, {
              start: startDate,
              end: endOfDay(today)
            });
          } catch (error) {
            return false;
          }
        });
      }
    }

    setFilteredRecords(filtered);
    setPage(0); // Resetear a la primera página cuando cambian los filtros
  };

  // Función para manejar el cambio de página
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Función para manejar el cambio de filas por página
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Función para manejar la búsqueda
  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
  };

  // Función para limpiar todos los filtros
  const handleClearFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setCarrierFilter('all');
    setDateFilter('all');
  };

  // Función para abrir el diálogo de detalles
  const handleOpenDialog = (record) => {
    setSelectedRecord(record);
    setIsDialogOpen(true);
  };

  // Función para cerrar el diálogo de detalles
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedRecord(null);
  };

  // Función para abrir el diálogo de cambio de estado
  const handleOpenStatusDialog = (record) => {
    setRecordToUpdateStatus(record);
    setNewStatus(record.delivery_status);
    setIsStatusDialogOpen(true);
    setStatusUpdateError(null);
    setStatusUpdateSuccess(false);
  };

  // Función para cerrar el diálogo de cambio de estado
  const handleCloseStatusDialog = () => {
    setIsStatusDialogOpen(false);
    setRecordToUpdateStatus(null);
    setStatusUpdateError(null);
  };

  // Función para actualizar el estado de un envío
  const handleUpdateStatus = async () => {
    if (!recordToUpdateStatus || !newStatus) return;

    try {
      setStatusUpdateLoading(true);
      setStatusUpdateError(null);
      setStatusUpdateSuccess(false);

      const response = await fetch(buildApiUrl(`/shipping/${recordToUpdateStatus.shipping_id}`), {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          delivery_status: newStatus
        })
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar el estado: ${response.statusText}`);
      }

      const updatedRecord = await response.json();

      // Actualizar el registro en la lista local
      setShippingRecords(prevRecords =>
        prevRecords.map(record =>
          record.shipping_id === updatedRecord.shipping_id ? updatedRecord : record
        )
      );

      setStatusUpdateSuccess(true);

      // Cerrar el diálogo después de 1.5 segundos
      setTimeout(() => {
        handleCloseStatusDialog();
      }, 1500);

    } catch (err) {
      console.error('Error al actualizar el estado:', err);
      setStatusUpdateError(err.message);
    } finally {
      setStatusUpdateLoading(false);
    }
  };

  // Función para descargar el albarán
  const handleDownloadDeliveryNote = (shippingId) => {
    // Usar el endpoint especializado para descargar albaranes por ID de envío
    const downloadUrl = buildApiUrl(`/uploads/download-delivery-note/${shippingId}`);
    console.log('Descargando albarán para envío:', shippingId);
    console.log('URL de descarga:', downloadUrl);

    // Abrir la URL en una nueva pestaña
    window.open(downloadUrl, '_blank');
  };

  // Función para abrir el diálogo de confirmación de eliminación
  const handleOpenDeleteDialog = (record) => {
    setRecordToDelete(record);
    setIsDeleteDialogOpen(true);
    setDeleteError(null);
    setDeleteSuccess(false);
  };

  // Función para cerrar el diálogo de confirmación de eliminación
  const handleCloseDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setRecordToDelete(null);
    setDeleteError(null);
  };

  // Función para eliminar un envío
  const handleDeleteShipping = async () => {
    if (!recordToDelete) return;

    try {
      setDeleteLoading(true);
      setDeleteError(null);
      setDeleteSuccess(false);

      const response = await fetch(buildApiUrl(`/shipping/${recordToDelete.shipping_id}`), {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error al eliminar el envío: ${response.statusText}`);
      }

      // Eliminar el registro de la lista local
      const updatedRecords = shippingRecords.filter(record =>
        record.shipping_id !== recordToDelete.shipping_id
      );

      setShippingRecords(updatedRecords);
      setFilteredRecords(prevFiltered =>
        prevFiltered.filter(record =>
          record.shipping_id !== recordToDelete.shipping_id
        )
      );

      setDeleteSuccess(true);

      // Cerrar el diálogo después de 1.5 segundos
      setTimeout(() => {
        handleCloseDeleteDialog();
      }, 1500);

    } catch (err) {
      console.error('Error al eliminar el envío:', err);
      setDeleteError(err.message);
    } finally {
      setDeleteLoading(false);
    }
  };

  // Renderizar la fecha formateada
  const formatDate = (dateString) => {
    try {
      return format(parseISO(dateString), 'dd/MM/yyyy HH:mm', { locale: es });
    } catch (error) {
      return 'Fecha inválida';
    }
  };

  // Renderizar el estado del envío con un chip de color clickeable
  const renderDeliveryStatus = (record) => {
    const status = record.delivery_status;
    let color = 'default';

    switch (status) {
      case 'En Proceso':
        color = 'primary';
        break;
      case 'Entregado':
        color = 'success';
        break;
      case 'Devuelto':
        color = 'error';
        break;
      case 'Cancelado':
        color = 'warning';
        break;
      default:
        color = 'default';
    }

    return (
      <Tooltip title="Haz clic para cambiar el estado">
        <Chip
          label={status}
          color={color}
          size="small"
          variant="filled"
          onClick={() => handleOpenStatusDialog(record)}
          sx={{ cursor: 'pointer' }}
        />
      </Tooltip>
    );
  };

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Historial de Envíos
        </Typography>
        
        <Box>
          <FormControlLabel
            control={
              <Switch
                checked={showOnlyInProcess}
                onChange={(e) => setShowOnlyInProcess(e.target.checked)}
                color="primary"
                size="small"
              />
            }
            label="Solo mostrar envíos en proceso"
            sx={{ mr: 2 }}
          />
          
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchShippingRecords}
            disabled={loading}
          >
            Actualizar
          </Button>
        </Box>
      </Box>

      {/* Filtros */}
      <Paper sx={{ p: 2, mb: 3, width: '100%', boxSizing: 'border-box' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Filtros</Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<ClearIcon />}
            onClick={handleClearFilters}
            disabled={!searchTerm && statusFilter === 'all' && carrierFilter === 'all' && dateFilter === 'all'}
          >
            Limpiar Filtros
          </Button>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center', mb: 2 }}>
          <TextField
            label="Buscar"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearch}
            placeholder="OT, cliente o seguimiento"
            sx={{ minWidth: 250 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: searchTerm && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={() => setSearchTerm('')}>
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              )
            }}
          />

          <TextField
            select
            label="Estado"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            sx={{ minWidth: 150 }}
            size="small"
          >
            <MenuItem value="all">Todos</MenuItem>
            <MenuItem value="En Proceso">En Proceso</MenuItem>
            <MenuItem value="Entregado">Entregado</MenuItem>
            <MenuItem value="Devuelto">Devuelto</MenuItem>
            <MenuItem value="Cancelado">Cancelado</MenuItem>
          </TextField>

          <TextField
            select
            label="Transportista"
            value={carrierFilter}
            onChange={(e) => setCarrierFilter(e.target.value)}
            sx={{ minWidth: 150 }}
            size="small"
          >
            <MenuItem value="all">Todos</MenuItem>
            {carriers.map(carrier => (
              <MenuItem key={carrier} value={carrier}>{carrier}</MenuItem>
            ))}
          </TextField>

          <TextField
            select
            label="Fecha"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            sx={{ minWidth: 150 }}
            size="small"
          >
            <MenuItem value="all">Todas</MenuItem>
            <MenuItem value="today">Hoy</MenuItem>
            <MenuItem value="week">Última semana</MenuItem>
            <MenuItem value="month">Último mes</MenuItem>
          </TextField>
        </Box>
      </Paper>

      {/* Mensajes de error */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tabla de registros */}
      <Paper sx={{ width: '100%', boxSizing: 'border-box' }}>
        <TableContainer sx={{ width: '100%', boxSizing: 'border-box' }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>OT</TableCell>
                <TableCell>Cliente</TableCell>
                <TableCell>Fecha de Envío</TableCell>
                <TableCell>Transportista</TableCell>
                <TableCell>Seguimiento</TableCell>
                <TableCell>Estado</TableCell>
                <TableCell align="center">Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <CircularProgress size={40} sx={{ my: 2 }} />
                    <Typography variant="body2">Cargando registros de envío...</Typography>
                  </TableCell>
                </TableRow>
              ) : filteredRecords.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <Typography variant="body2">
                      No se encontraron registros de envío
                      {searchTerm && ` que coincidan con "${searchTerm}"`}
                      {statusFilter !== 'all' && ` con estado "${statusFilter}"`}
                      {carrierFilter !== 'all' && ` con transportista "${carrierFilter}"`}
                      {dateFilter !== 'all' && ` en el período seleccionado`}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredRecords
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((record) => (
                    <TableRow key={record.shipping_id} hover>
                      <TableCell>{record.ot_number}</TableCell>
                      <TableCell>{record.client_name}</TableCell>
                      <TableCell>{formatDate(record.shipping_date)}</TableCell>
                      <TableCell>{record.carrier}</TableCell>
                      <TableCell>
                        {record.tracking_number || <Typography variant="body2" color="text.secondary">No disponible</Typography>}
                      </TableCell>
                      <TableCell>{renderDeliveryStatus(record)}</TableCell>
                      <TableCell align="center">
                        <Tooltip title="Ver detalles">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleOpenDialog(record)}
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Descargar albarán">
                          <IconButton
                            size="small"
                            color="secondary"
                            onClick={() => handleDownloadDeliveryNote(record.shipping_id)}
                          >
                            <DownloadIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Eliminar envío">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleOpenDeleteDialog(record)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Paginación */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredRecords.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Filas por página:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
        />
      </Paper>

      {/* Diálogo de detalles */}
      <Dialog
        open={isDialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        {selectedRecord && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocalShippingIcon sx={{ mr: 1 }} />
                Detalles del Envío - {selectedRecord.ot_number}
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={2}>
                {/* Información del pedido */}
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Información del Pedido
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Número de OT</Typography>
                  <Typography variant="body1" gutterBottom>{selectedRecord.ot_number}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Cliente</Typography>
                  <Typography variant="body1" gutterBottom>{selectedRecord.client_name}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Fecha de Envío</Typography>
                  <Typography variant="body1" gutterBottom>{formatDate(selectedRecord.shipping_date)}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Estado</Typography>
                  <Box sx={{ mt: 0.5 }}>{renderDeliveryStatus(selectedRecord)}</Box>
                </Grid>

                {/* Información del transportista */}
                <Grid item xs={12} sx={{ mt: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Información del Transportista
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Transportista</Typography>
                  <Typography variant="body1" gutterBottom>{selectedRecord.carrier}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Número de Seguimiento</Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedRecord.tracking_number || 'No disponible'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Número de Bultos</Typography>
                  <Typography variant="body1" gutterBottom>{selectedRecord.packages}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Peso</Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedRecord.weight ? `${selectedRecord.weight} kg` : 'No especificado'}
                  </Typography>
                </Grid>

                {/* Dirección de entrega */}
                <Grid item xs={12} sx={{ mt: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Dirección de Entrega
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2">Dirección</Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedRecord.delivery_address.street}, {selectedRecord.delivery_address.city}, {selectedRecord.delivery_address.postal_code}, {selectedRecord.delivery_address.country}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Persona de Contacto</Typography>
                  <Typography variant="body1" gutterBottom>{selectedRecord.contact_person}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2">Teléfono de Contacto</Typography>
                  <Typography variant="body1" gutterBottom>{selectedRecord.contact_phone}</Typography>
                </Grid>

                {/* Observaciones */}
                {selectedRecord.notes && (
                  <>
                    <Grid item xs={12} sx={{ mt: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Observaciones
                      </Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body1">{selectedRecord.notes}</Typography>
                    </Grid>
                  </>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button
                startIcon={<DownloadIcon />}
                color="secondary"
                onClick={() => handleDownloadDeliveryNote(selectedRecord.shipping_id)}
              >
                Descargar Albarán
              </Button>
              <Button onClick={handleCloseDialog}>Cerrar</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Diálogo para cambiar el estado del envío */}
      <Dialog open={isStatusDialogOpen} onClose={handleCloseStatusDialog} maxWidth="xs" fullWidth>
        <DialogTitle>Cambiar estado del envío</DialogTitle>
        <DialogContent>
          {recordToUpdateStatus && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                OT: {recordToUpdateStatus.ot_number}
              </Typography>
              <Typography variant="subtitle2" gutterBottom>
                Cliente: {recordToUpdateStatus.client_name || 'Cliente sin nombre'}
              </Typography>
              <Typography variant="subtitle2" gutterBottom>
                Transportista: {recordToUpdateStatus.carrier}
              </Typography>
              <Typography variant="subtitle2" gutterBottom>
                Fecha de envío: {formatDate(recordToUpdateStatus.shipping_date)}
              </Typography>

              <FormControl fullWidth margin="normal">
                <InputLabel id="shipping-status-select-label">Estado</InputLabel>
                <Select
                  labelId="shipping-status-select-label"
                  value={newStatus}
                  onChange={(e) => setNewStatus(e.target.value)}
                  label="Estado"
                  fullWidth
                >
                  <MenuItem value="En Proceso">En Proceso</MenuItem>
                  <MenuItem value="Entregado">Entregado</MenuItem>
                  <MenuItem value="Devuelto">Devuelto</MenuItem>
                  <MenuItem value="Cancelado">Cancelado</MenuItem>
                </Select>
              </FormControl>

              {statusUpdateError && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {statusUpdateError}
                </Alert>
              )}

              {statusUpdateSuccess && (
                <Alert severity="success" sx={{ mt: 2 }}>
                  Estado actualizado correctamente
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseStatusDialog} color="inherit">
            Cancelar
          </Button>
          <Button
            onClick={handleUpdateStatus}
            color="primary"
            variant="contained"
            disabled={statusUpdateLoading || statusUpdateSuccess}
          >
            {statusUpdateLoading ? <CircularProgress size={24} /> : 'Guardar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo para confirmar eliminación de envío */}
      <Dialog open={isDeleteDialogOpen} onClose={handleCloseDeleteDialog} maxWidth="xs" fullWidth>
        <DialogTitle>Eliminar envío</DialogTitle>
        <DialogContent>
          {recordToDelete && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body1" gutterBottom>
                ¿Estás seguro de que deseas eliminar este envío?
              </Typography>

              <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                <Typography variant="subtitle2" gutterBottom>
                  OT: {recordToDelete.ot_number}
                </Typography>
                <Typography variant="subtitle2" gutterBottom>
                  Cliente: {recordToDelete.client_name || 'Cliente sin nombre'}
                </Typography>
                <Typography variant="subtitle2" gutterBottom>
                  Transportista: {recordToDelete.carrier}
                </Typography>
                <Typography variant="subtitle2" gutterBottom>
                  Fecha de envío: {formatDate(recordToDelete.shipping_date)}
                </Typography>
                <Typography variant="subtitle2" gutterBottom>
                  Estado: {recordToDelete.delivery_status}
                </Typography>
              </Box>

              <Typography variant="body2" color="error" sx={{ mt: 2 }}>
                Esta acción no se puede deshacer.
              </Typography>

              {deleteError && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {deleteError}
                </Alert>
              )}

              {deleteSuccess && (
                <Alert severity="success" sx={{ mt: 2 }}>
                  Envío eliminado correctamente
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="inherit">
            Cancelar
          </Button>
          <Button
            onClick={handleDeleteShipping}
            color="error"
            variant="contained"
            disabled={deleteLoading || deleteSuccess}
          >
            {deleteLoading ? <CircularProgress size={24} /> : 'Eliminar'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default ShippingHistory;
