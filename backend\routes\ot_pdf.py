from fastapi import APIRouter, HTTPException, status, Body
from fastapi.responses import FileResponse
from typing import Dict, Any, List, Optional
import os
from datetime import datetime
from pydantic import BaseModel
from utils.logger import log_info, log_error
from fpdf import FPDF

router = APIRouter(
    prefix="/uploads",
    tags=["uploads"],
    responses={404: {"description": "Not found"}}
)

# Directorio para guardar los PDFs de OT
OT_PDF_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "ots")
os.makedirs(OT_PDF_DIR, exist_ok=True)

class OTData(BaseModel):
    ot_id: str
    budget_id: str
    client_id: str
    approval_date: str
    status: str
    processes: List[Dict[str, Any]]

class ClientData(BaseModel):
    client_id: str
    company: Optional[Dict[str, Any]] = None
    contact_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None

class BudgetData(BaseModel):
    budget_id: str
    description: Optional[str] = None
    parts: Optional[List[Dict[str, Any]]] = None

class OTPDFData(BaseModel):
    ot_data: OTData
    client_data: Optional[ClientData] = None
    budget_data: Optional[BudgetData] = None

@router.post("/save-ot-pdf/{ot_number}", response_model=Dict[str, Any])
async def save_ot_pdf(
    ot_number: str,
    data: OTPDFData = Body(...)
):
    """
    Genera y guarda un PDF de OT en el servidor.

    Args:
        ot_number: Número de OT
        data: Datos para generar el PDF

    Returns:
        Dict: Información sobre el archivo guardado
    """
    try:
        # Crear el nombre del archivo
        filename = f"OT_{ot_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        file_path = os.path.join(OT_PDF_DIR, filename)

        # Generar el PDF
        generate_ot_pdf(file_path, data)

        log_info(f"PDF de OT {ot_number} guardado en {file_path}")

        return {
            "success": True,
            "message": f"PDF de OT {ot_number} guardado correctamente",
            "file_path": file_path,
            "filename": filename
        }
    except Exception as e:
        log_error(f"Error al guardar PDF de OT {ot_number}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al guardar PDF de OT: {str(e)}"
        )

def generate_ot_pdf(file_path: str, data: OTPDFData):
    """
    Genera un PDF de OT utilizando FPDF

    Args:
        file_path: Ruta donde guardar el PDF
        data: Datos para generar el PDF
    """
    try:
        # Crear el PDF
        pdf = FPDF()
        pdf.add_page()

        # Ruta al logo
        logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "images", "trikyprinter-logo.png")

        # Verificar si el logo existe
        if os.path.exists(logo_path):
            # Añadir logo en la esquina superior izquierda
            pdf.image(logo_path, x=10, y=10, w=50)

            # Información de la empresa en la esquina superior derecha
            pdf.set_font("Arial", "", 8)
            pdf.set_xy(150, 10)
            pdf.cell(50, 5, "TrikyPrinter", 0, 2, "R")
            pdf.cell(50, 5, "c/Costa Rica 11", 0, 2, "R")
            pdf.cell(50, 5, "28016 Madrid", 0, 2, "R")
            pdf.cell(50, 5, "Tel: +34 91 000 00 00", 0, 2, "R")
            pdf.cell(50, 5, "Email: <EMAIL>", 0, 2, "R")

            # Mover el cursor después del logo
            pdf.set_y(40)
        else:
            # Si no se encuentra el logo, usar texto
            pdf.set_font("Arial", "B", 16)
            pdf.cell(190, 10, "TrikyPrinter", 0, 1, "L")
            pdf.set_font("Arial", "", 8)
            pdf.cell(190, 5, "c/Costa Rica 11, 28016 Madrid", 0, 1, "L")
            pdf.cell(190, 5, "Tel: +34 91 000 00 00 - Email: <EMAIL>", 0, 1, "L")
            pdf.ln(5)

        # Configurar fuentes para el título
        pdf.set_font("Arial", "B", 16)

        # Título
        pdf.cell(190, 10, f"ORDEN DE TRABAJO: {data.ot_data.ot_id}", 0, 1, "C")
        pdf.ln(5)

        # Información general
        pdf.set_font("Arial", "B", 12)
        pdf.cell(190, 10, "INFORMACIÓN GENERAL", 0, 1)
        pdf.set_font("Arial", "", 10)

        # Cliente
        client_name = "Cliente sin nombre"
        if data.client_data and data.client_data.company and data.client_data.company.get("name"):
            client_name = data.client_data.company.get("name")
        pdf.cell(190, 10, f"Cliente: {client_name}", 0, 1)

        # Presupuesto
        pdf.cell(190, 10, f"Presupuesto: {data.ot_data.budget_id}", 0, 1)

        # Descripción
        description = "Sin descripción"
        if data.budget_data and data.budget_data.description:
            description = data.budget_data.description
        pdf.cell(190, 10, f"Descripción: {description}", 0, 1)

        # Fecha de aprobación
        approval_date = "No definida"
        if data.ot_data.approval_date:
            try:
                date = datetime.fromisoformat(data.ot_data.approval_date.replace("Z", "+00:00"))
                approval_date = date.strftime("%d/%m/%Y")
            except:
                pass
        pdf.cell(190, 10, f"Fecha de aprobación: {approval_date}", 0, 1)

        # Estado
        pdf.cell(190, 10, f"Estado: {data.ot_data.status}", 0, 1)
        pdf.ln(5)

        # Procesos de producción
        pdf.set_font("Arial", "B", 12)
        pdf.cell(190, 10, "PROCESOS DE PRODUCCIÓN", 0, 1)

        # Agrupar procesos por tipo
        processes_by_type = {}
        for process in data.ot_data.processes:
            process_type = process.get("process_type", "Desconocido")
            if process_type not in processes_by_type:
                processes_by_type[process_type] = []
            processes_by_type[process_type].append(process)

        # Ordenar los tipos de procesos
        process_types_order = {"Impresión": 1, "Acabado": 2, "Manipulado": 3, "Envío": 4}
        process_types = sorted(processes_by_type.keys(), key=lambda x: process_types_order.get(x, 99))

        for process_type in process_types:
            # Título del tipo de proceso
            pdf.set_font("Arial", "B", 11)
            pdf.cell(190, 10, process_type, 0, 1)

            # Cabecera de la tabla
            pdf.set_font("Arial", "B", 10)
            pdf.cell(50, 10, "Proceso", 1, 0, "C")
            pdf.cell(30, 10, "Cantidad", 1, 0, "C")
            pdf.cell(30, 10, "Máquina", 1, 0, "C")
            pdf.cell(40, 10, "Fecha Inicio", 1, 0, "C")
            pdf.cell(40, 10, "Fecha Fin", 1, 1, "C")

            # Datos de los procesos
            pdf.set_font("Arial", "", 10)
            for process in processes_by_type[process_type]:
                pdf.cell(50, 10, process.get("name", ""), 1, 0)
                pdf.cell(30, 10, str(process.get("quantity", "")), 1, 0, "C")
                pdf.cell(30, 10, process.get("machine_id", "No asignada"), 1, 0, "C")

                # Formatear fechas
                start_date = "No definida"
                if process.get("start_date"):
                    try:
                        date = datetime.fromisoformat(process["start_date"].replace("Z", "+00:00"))
                        start_date = date.strftime("%d/%m/%Y")
                    except:
                        pass
                pdf.cell(40, 10, start_date, 1, 0, "C")

                end_date = "No definida"
                if process.get("end_date"):
                    try:
                        date = datetime.fromisoformat(process["end_date"].replace("Z", "+00:00"))
                        end_date = date.strftime("%d/%m/%Y")
                    except:
                        pass
                pdf.cell(40, 10, end_date, 1, 1, "C")

            # Detalles de los procesos
            pdf.ln(5)
            for process in processes_by_type[process_type]:
                pdf.set_font("Arial", "B", 10)
                pdf.cell(190, 10, process.get("name", ""), 0, 1)

                pdf.set_font("Arial", "", 10)
                pdf.cell(190, 10, f"Descripción: {process.get('description', 'Sin descripción')}", 0, 1)
                pdf.cell(190, 10, f"Notas: {process.get('notes', 'Sin notas adicionales')}", 0, 1)
                pdf.ln(5)

        # Especificaciones técnicas
        if data.budget_data and data.budget_data.parts and len(data.budget_data.parts) > 0:
            pdf.set_font("Arial", "B", 12)
            pdf.cell(190, 10, "ESPECIFICACIONES TÉCNICAS", 0, 1)

            for part in data.budget_data.parts:
                pdf.set_font("Arial", "B", 10)
                pdf.cell(190, 10, part.get("name", ""), 0, 1)

                pdf.set_font("Arial", "", 10)
                if part.get("paper_data"):
                    paper_data = part["paper_data"]
                    pdf.cell(190, 10, f"Papel: {paper_data.get('name', '')}, {paper_data.get('weight', '')}g, {paper_data.get('size', '')}", 0, 1)

                if part.get("machine_data"):
                    machine_data = part["machine_data"]
                    pdf.cell(190, 10, f"Máquina: {machine_data.get('name', '')}", 0, 1)

                if part.get("colors"):
                    pdf.cell(190, 10, f"Colores: {part.get('colors', '')}", 0, 1)

                pdf.ln(5)

        # Pie de página
        pdf.set_y(-30)
        pdf.set_font("Arial", "I", 8)
        pdf.cell(0, 10, f"Documento generado el {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}", 0, 1, "C")

        # Añadir línea separadora
        pdf.line(10, pdf.get_y(), 200, pdf.get_y())
        pdf.ln(2)

        # Información de la empresa en el pie de página
        pdf.cell(0, 5, "TrikyPrinter - c/Costa Rica 11, 28016 Madrid - CIF: B12345678", 0, 1, "C")
        pdf.cell(0, 5, "Tel: +34 91 000 00 00 - Email: <EMAIL> - www.trikyprinter.com", 0, 1, "C")

        # Guardar el PDF
        pdf.output(file_path)

        return True

    except Exception as e:
        log_error(f"Error al generar PDF de OT: {str(e)}")
        raise e

@router.get("/ot-pdf/{filename}", response_class=FileResponse)
async def get_ot_pdf(
    filename: str
):
    """
    Obtiene un PDF de OT guardado en el servidor.

    Args:
        filename: Nombre del archivo PDF

    Returns:
        FileResponse: Archivo PDF
    """
    file_path = os.path.join(OT_PDF_DIR, filename)

    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Archivo PDF de OT no encontrado: {filename}"
        )

    return FileResponse(
        file_path,
        media_type="application/pdf",
        filename=filename
    )

@router.get("/list-ot-pdfs", response_model=List[Dict[str, Any]])
async def list_ot_pdfs():
    """
    Lista todos los PDFs de OT guardados en el servidor.

    Returns:
        List[Dict]: Lista de archivos PDF de OT
    """
    try:
        files = []
        for filename in os.listdir(OT_PDF_DIR):
            if filename.startswith("OT_") and filename.endswith(".pdf"):
                file_path = os.path.join(OT_PDF_DIR, filename)
                files.append({
                    "filename": filename,
                    "file_path": file_path,
                    "created_at": datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()
                })

        # Ordenar por fecha de creación (más recientes primero)
        files.sort(key=lambda x: x["created_at"], reverse=True)

        return files
    except Exception as e:
        log_error(f"Error al listar PDFs de OT: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al listar PDFs de OT: {str(e)}"
        )
