"""
Utilidades para serializar esquemas de plegado para la API
"""
from folding_schemes import get_scheme

def get_serializable_page_layout(scheme_name):
    """
    Obtiene el page_layout de un esquema de plegado en un formato serializable para JSON.
    
    Args:
        scheme_name: Nombre del esquema de plegado
        
    Returns:
        Un diccionario con el page_layout o None si no se encuentra el esquema
    """
    try:
        scheme = get_scheme(scheme_name)
        if not scheme:
            return None
            
        # Convertir el page_layout a un formato serializable
        serializable_layout = {}
        
        # Procesar el lado "front"
        if "front" in scheme.page_layout:
            serializable_layout["front"] = []
            for row in scheme.page_layout["front"]:
                serializable_layout["front"].append(list(row))
                
        # Procesar el lado "back"
        if "back" in scheme.page_layout:
            serializable_layout["back"] = []
            for row in scheme.page_layout["back"]:
                serializable_layout["back"].append(list(row))
                
        return serializable_layout
    except Exception as e:
        print(f"Error al serializar page_layout para {scheme_name}: {str(e)}")
        return None
