from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import uuid
import json
from models.models import Budget
from routes.budgets import load_budgets
from routes.papers import get_paper_by_id
from routes.machines import get_machine_by_id
from routes.clients import get_client_by_id

router = APIRouter(
    tags=["jdf_generator"],
    responses={404: {"description": "Recurso no encontrado"}}
)

# Definir modelos Pydantic para la respuesta JDF
class SheetConfig(BaseModel):
    sheet_name: str
    bindery_signature_name: str
    paper_ref: str
    fold_catalog: str
    work_style: str

class StrippingParams(BaseModel):
    signature_name: str
    sheets: List[SheetConfig]

class Signature(BaseModel):
    signature_ID: str
    job_part_id_name: str
    press_name: str
    stripping_params: StrippingParams

class PaperConfig(BaseModel):
    weight: float
    dimension_width: float
    dimension_height: float
    media_type: str
    product_id: str
    thickness: float
    descriptive_name: str

class CustomerInfo(BaseModel):
    company_name: Optional[str] = None
    contact_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None

class JDFResponse(BaseModel):
    job_id: str
    descriptive_name: str
    customer_info: Optional[CustomerInfo] = None
    signatures: List[Signature] = []
    paper_configs: List[PaperConfig] = []
    runlists: Optional[List[Dict[str, Any]]] = None

@router.get("/budgets/{budget_id}/jdf", response_model=Dict[str, Any])
async def generate_jdf_from_budget(budget_id: str):
    """
    Genera un documento JDF a partir de un presupuesto existente.
    """
    # Obtener el presupuesto
    budgets = load_budgets()
    budget_data = None

    for budget in budgets:
        if budget["budget_id"] == budget_id:
            budget_data = budget
            break

    if not budget_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Presupuesto con ID {budget_id} no encontrado"
        )

    # Obtener datos adicionales necesarios
    try:
        paper_data = budget_data.get("paper_data") if budget_data.get("paper_data") else await get_paper_by_id(budget_data["paper_id"])
        machine_data = budget_data.get("machine_data") if budget_data.get("machine_data") else await get_machine_by_id(budget_data["machine_id"])
        client_data = budget_data.get("client_data") if budget_data.get("client_data") else await get_client_by_id(budget_data["client_id"])
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al obtener datos relacionados: {str(e)}"
        )

    # Verificar que tenemos todos los datos necesarios
    if not paper_data or not machine_data:
        # En lugar de lanzar un error, creamos una respuesta básica sin signaturas
        return {
            "job_id": budget_data.get("ot_number", ""),
            "descriptive_name": budget_data.get("description", ""),
            "customer_info": {
                "company_name": client_data.get("company", {}).get("name") if client_data and "company" in client_data else "No disponible",
                "contact_name": client_data.get("contact", {}).get("name") if client_data and "contact" in client_data else "No disponible",
                "email": client_data.get("contact", {}).get("email") if client_data and "contact" in client_data else "",
                "phone": client_data.get("contact", {}).get("phone") if client_data and "contact" in client_data else ""
            },
            "signatures": [],
            "paper_configs": []
        }

    # Generar el documento JDF
    signatures = []
    paper_configs = []

    # Añadir configuración de papel
    paper_config = {
        "weight": paper_data.get("weight", 0),
        "dimension_width": paper_data.get("dimension_width", 0),
        "dimension_height": paper_data.get("dimension_height", 0),
        "media_type": paper_data.get("media_type", ""),
        "product_id": paper_data.get("product_id", ""),
        "thickness": paper_data.get("thickness", 0),
        "descriptive_name": paper_data.get("descriptive_name", "")
    }
    paper_configs.append(paper_config)

    # Verificar si hay cálculo de pliegos
    sheet_calculation = budget_data.get("sheet_calculation", {})
    mejor_combinacion = sheet_calculation.get("mejor_combinacion", {})

    # Si hay cálculo de pliegos, procesamos las signaturas
    if mejor_combinacion and "esquemas_utilizados" in mejor_combinacion:
        # Crear una sola signatura para todos los esquemas
        signature_id = "Sig-1"
        job_part_id_name = "producto"  # Una sola parte del producto
        press_name = machine_data.get("name", "")

        # Crear hojas para cada esquema utilizado
        sheets = []
        for idx, esquema in enumerate(mejor_combinacion["esquemas_utilizados"]):
            # Determinar el estilo de trabajo basado en si es tira/retira
            work_style = esquema.get("work_style", "WorkAndTurn" if esquema.get("es_tira_retira", False) else "WorkAndBack")

            # Crear una hoja para cada pliego en el esquema
            for sheet_idx in range(esquema.get("numero_pliegos", 1)):
                sheet_name = f"Sheet-1-{len(sheets) + 1}"  # Numeración secuencial para todas las hojas
                bindery_signature_name = f"BSN-1-{len(sheets) + 1}"

                # Usar el nombre exacto del esquema como fold_catalog
                fold_catalog = esquema.get("nombre", "")

                sheet = {
                    "sheet_name": sheet_name,
                    "bindery_signature_name": bindery_signature_name,
                    "paper_ref": paper_data.get("product_id", ""),
                    "fold_catalog": fold_catalog,
                    "work_style": work_style,
                    "strip_cell_params": {
                        "trim_size_width": budget_data.get("page_size", {}).get("width", 210),
                        "trim_size_height": budget_data.get("page_size", {}).get("height", 297)
                    }
                }
                sheets.append(sheet)

        # Añadir la signatura con todas las hojas
        signature = {
            "signature_ID": signature_id,
            "job_part_id_name": job_part_id_name,
            "press_name": press_name,
            "stripping_params": {
                "signature_name": signature_id,
                "sheets": sheets
            }
        }
        signatures.append(signature)

    # Crear y devolver la respuesta JDF
    jdf_response = {
        "job_id": budget_data.get("ot_number", ""),
        "descriptive_name": budget_data.get("description", ""),
        "customer_info": {
            "company_name": client_data.get("company", {}).get("name") if client_data and "company" in client_data else "No disponible",
            "contact_name": client_data.get("contact", {}).get("name") if client_data and "contact" in client_data else "No disponible",
            "email": client_data.get("contact", {}).get("email") if client_data and "contact" in client_data else "",
            "phone": client_data.get("contact", {}).get("phone") if client_data and "contact" in client_data else ""
        },
        "signatures": signatures,
        "paper_configs": paper_configs
    }

    # Añadir runlists si hay un PDF asociado al presupuesto
    pdf_filename = budget_data.get("pdf_filename")
    page_count = budget_data.get("page_count")

    if pdf_filename and page_count:
        # Construir la URL del PDF (usando la URL base de la aplicación)
        # En producción, esto debería ser la URL pública donde se almacenan los PDFs
        pdf_url = f"http://192.168.11.105:3001/{pdf_filename}"

        # Crear el rango de páginas (0 ~ pages-1)
        page_range = f"0 ~ {page_count - 1}"

        # Añadir la sección runlists al JDF
        jdf_response["runlists"] = [
            {
                "runlist_id": "RL-1d",
                "pages": page_count,
                "page_range": page_range,
                "signature_ref": "Sig-1",  # Referencia a la signatura creada anteriormente
                "pdf_url": pdf_url
            }
        ]

    return jdf_response

@router.post("/budgets/jdf", response_model=Dict[str, Any])
async def generate_jdf_from_budget_data(budget: Budget):
    """
    Genera un documento JDF a partir de los datos de un presupuesto sin necesidad de guardarlo.
    """
    # Convertir el modelo Pydantic a diccionario
    budget_data = budget.dict()

    # Obtener datos adicionales necesarios
    try:
        paper_data = budget_data.get("paper_data") if budget_data.get("paper_data") else await get_paper_by_id(budget_data["paper_id"])
        machine_data = budget_data.get("machine_data") if budget_data.get("machine_data") else await get_machine_by_id(budget_data["machine_id"])
        client_data = budget_data.get("client_data") if budget_data.get("client_data") else await get_client_by_id(budget_data["client_id"])
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al obtener datos relacionados: {str(e)}"
        )

    # Verificar que tenemos todos los datos necesarios
    if not paper_data or not machine_data:
        # En lugar de lanzar un error, creamos una respuesta básica sin signaturas
        return {
            "job_id": budget_data.get("ot_number", ""),
            "descriptive_name": budget_data.get("description", ""),
            "customer_info": {
                "company_name": client_data.get("company", {}).get("name") if client_data and "company" in client_data else "No disponible",
                "contact_name": client_data.get("contact", {}).get("name") if client_data and "contact" in client_data else "No disponible",
                "email": client_data.get("contact", {}).get("email") if client_data and "contact" in client_data else "",
                "phone": client_data.get("contact", {}).get("phone") if client_data and "contact" in client_data else ""
            },
            "signatures": [],
            "paper_configs": []
        }

    # Generar el documento JDF
    signatures = []
    paper_configs = []

    # Añadir configuración de papel
    paper_config = {
        "weight": paper_data.get("weight", 0),
        "dimension_width": paper_data.get("dimension_width", 0),
        "dimension_height": paper_data.get("dimension_height", 0),
        "media_type": paper_data.get("media_type", ""),
        "product_id": paper_data.get("product_id", ""),
        "thickness": paper_data.get("thickness", 0),
        "descriptive_name": paper_data.get("descriptive_name", "")
    }
    paper_configs.append(paper_config)

    # Verificar si hay cálculo de pliegos
    sheet_calculation = budget_data.get("sheet_calculation", {})
    mejor_combinacion = sheet_calculation.get("mejor_combinacion", {})

    # Si hay cálculo de pliegos, procesamos las signaturas
    if mejor_combinacion and "esquemas_utilizados" in mejor_combinacion:
        # Crear una sola signatura para todos los esquemas
        signature_id = "Sig-1"
        job_part_id_name = "producto"  # Una sola parte del producto
        press_name = machine_data.get("name", "")

        # Crear hojas para cada esquema utilizado
        sheets = []
        for idx, esquema in enumerate(mejor_combinacion["esquemas_utilizados"]):
            # Determinar el estilo de trabajo basado en si es tira/retira
            work_style = esquema.get("work_style", "WorkAndTurn" if esquema.get("es_tira_retira", False) else "WorkAndBack")

            # Crear una hoja para cada pliego en el esquema
            for sheet_idx in range(esquema.get("numero_pliegos", 1)):
                sheet_name = f"Sheet-1-{len(sheets) + 1}"  # Numeración secuencial para todas las hojas
                bindery_signature_name = f"BSN-1-{len(sheets) + 1}"

                # Usar el nombre exacto del esquema como fold_catalog
                fold_catalog = esquema.get("nombre", "")

                sheet = {
                    "sheet_name": sheet_name,
                    "bindery_signature_name": bindery_signature_name,
                    "paper_ref": paper_data.get("product_id", ""),
                    "fold_catalog": fold_catalog,
                    "work_style": work_style,
                    "strip_cell_params": {
                        "trim_size_width": budget_data.get("page_size", {}).get("width", 210),
                        "trim_size_height": budget_data.get("page_size", {}).get("height", 297)
                    }
                }
                sheets.append(sheet)

        # Añadir la signatura con todas las hojas
        signature = {
            "signature_ID": signature_id,
            "job_part_id_name": job_part_id_name,
            "press_name": press_name,
            "stripping_params": {
                "signature_name": signature_id,
                "sheets": sheets
            }
        }
        signatures.append(signature)

    # Crear y devolver la respuesta JDF
    jdf_response = {
        "job_id": budget_data.get("ot_number", ""),
        "descriptive_name": budget_data.get("description", ""),
        "customer_info": {
            "company_name": client_data.get("company", {}).get("name") if client_data and "company" in client_data else "No disponible",
            "contact_name": client_data.get("contact", {}).get("name") if client_data and "contact" in client_data else "No disponible",
            "email": client_data.get("contact", {}).get("email") if client_data and "contact" in client_data else "",
            "phone": client_data.get("contact", {}).get("phone") if client_data and "contact" in client_data else ""
        },
        "signatures": signatures,
        "paper_configs": paper_configs
    }

    # Añadir runlists si hay un PDF asociado al presupuesto
    pdf_filename = budget_data.get("pdf_filename")
    page_count = budget_data.get("page_count")

    if pdf_filename and page_count:
        # Construir la URL del PDF (usando la URL base de la aplicación)
        # En producción, esto debería ser la URL pública donde se almacenan los PDFs
        pdf_url = f"http://192.168.11.105:3001/{pdf_filename}"

        # Crear el rango de páginas (0 ~ pages-1)
        page_range = f"0 ~ {page_count - 1}"

        # Añadir la sección runlists al JDF
        jdf_response["runlists"] = [
            {
                "runlist_id": "RL-1d",
                "pages": page_count,
                "page_range": page_range,
                "signature_ref": "Sig-1",  # Referencia a la signatura creada anteriormente
                "pdf_url": pdf_url
            }
        ]

    return jdf_response


@router.get("/{budget_id}/jdf-schema", response_model=dict)
async def generate_jdf_schema(budget_id: str):
    """
    Genera un JSON con el esquema JDF a partir de un presupuesto.

    Args:
        budget_id: ID del presupuesto

    Returns:
        dict: JSON con el esquema JDF
    """
    return await generate_jdf_schema_v2(budget_id)


@router.get("/schema/{budget_id}", response_model=dict)
async def generate_jdf_schema_v2(budget_id: str):
    """
    Genera un JSON con el esquema JDF a partir de un presupuesto.

    Args:
        budget_id: ID del presupuesto

    Returns:
        dict: JSON con el esquema JDF
    """
    # Cargar el presupuesto
    budgets = load_budgets()
    budget_data = None

    for budget in budgets:
        if budget["budget_id"] == budget_id:
            budget_data = budget
            break

    if not budget_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Presupuesto con ID {budget_id} no encontrado"
        )

    # Cargar datos relacionados
    paper_data = get_paper_by_id(budget_data.get("paper_id", ""))
    machine_data = get_machine_by_id(budget_data.get("machine_id", ""))
    client_data = get_client_by_id(budget_data.get("client_id", ""))

    # Inicializar el JDF según el esquema requerido
    jdf = {
        "job_id": budget_data.get("ot_number", "").replace("OT-", ""),  # Eliminar prefijo OT-
        "descriptive_name": budget_data.get("description", ""),
        "author": "JDFast",
        "agent_name": "JDFast",
        "agent_version": "3.1",
        "comment": f"Generado a partir del presupuesto {budget_id}",
        "product_type": budget_data.get("job_type", ""),
        "binding_side": "Left",  # Valor por defecto
        "signatures": [],
        "paper_configs": []
    }

    # Añadir configuración de papel
    if paper_data:
        paper_config = {
            "weight": paper_data.get("weight", 0),
            "dimension_width": paper_data.get("dimension_width", 0),
            "dimension_height": paper_data.get("dimension_height", 0),
            "media_type": "Paper",
            "product_id": f"Pap-{paper_data.get('paper_id', '001')}",
            "thickness": paper_data.get("thickness", 0),
            "descriptive_name": paper_data.get("descriptive_name", "")
        }
        jdf["paper_configs"].append(paper_config)

    # Mapeo de estilos de trabajo
    work_style_map = {
        "tira_retira": "WorkAndBack",
        "tira y retira": "WorkAndTurn",
        "perfecting": "Perfecting",
        "flat": "Flat"
    }

    # Procesar las partes del presupuesto como signaturas
    if "parts" in budget_data and budget_data["parts"]:
        for idx, part in enumerate(budget_data["parts"]):
            # Crear una signatura para cada parte
            signature_id = f"Str-{idx+1}"
            job_part_name = part.get("name", f"Parte {idx+1}")

            # Determinar el estilo de trabajo basado en los datos del presupuesto
            work_style = "Flat"  # Valor por defecto

            # Verificar si hay información de tira/retira en el cálculo de pliegos
            if "sheet_calculation" in part and "mejor_combinacion" in part["sheet_calculation"]:
                mejor_combinacion = part["sheet_calculation"]["mejor_combinacion"]
                if "esquemas_utilizados" in mejor_combinacion and len(mejor_combinacion["esquemas_utilizados"]) > 0:
                    # Verificar si alguno de los esquemas es tira/retira
                    for esquema in mejor_combinacion["esquemas_utilizados"]:
                        if esquema.get("es_tira_retira", False):
                            work_style = "tira_retira"
                            break

            # Si hay configuración explícita de estilo de trabajo, usarla
            if "product_config" in budget_data and "work_style" in budget_data["product_config"]:
                work_style = budget_data["product_config"]["work_style"]
            elif "work_style" in part:
                work_style = part["work_style"]
            elif "color_config" in part:
                # Si hay configuración de colores, verificar si es tira/retira
                color_config = part["color_config"]
                if color_config.get("frontColors", 0) > 0 and color_config.get("backColors", 0) > 0:
                    work_style = "tira_retira"

            # Mapear el estilo de trabajo a los valores permitidos
            mapped_work_style = work_style_map.get(work_style.lower(), "Flat")

            # Crear la estructura de la signatura
            signature = {
                "signature_ID": signature_id,
                "job_part_id_name": job_part_name,
                "press_name": machine_data.get("name", "Prensa B1") if machine_data else "Prensa B1",
                "assembly_order": "Gathering",
                "stripping_params": {
                    "signature_name": f"Sig-{idx+1}",
                    "sheets": []
                }
            }

            # Añadir hojas a la signatura
            sheet = {
                "sheet_name": f"Sheet-{idx+1}-1",
                "bindery_signature_name": f"BSN-{idx+1}-1",
                "paper_ref": f"Pap-{paper_data.get('paper_id', '001')}" if paper_data else "Pap-001",
                "fold_catalog": "F16-7",  # Valor por defecto
                "work_style": mapped_work_style
            }

            # Añadir parámetros de imposición si están disponibles
            if "page_size" in part:
                strip_cell_params = {
                    "trim_size_width": part["page_size"].get("width", 0),
                    "trim_size_height": part["page_size"].get("height", 0),
                    "bleed_face": 3,  # Valores por defecto
                    "bleed_foot": 3,
                    "bleed_head": 3,
                    "bleed_spine": 3,
                    "orientation": "Rotate0",
                    "relative_box_x1": 0,
                    "relative_box_y1": 0,
                    "relative_box_x2": 1,
                    "relative_box_y2": 1
                }
                sheet["strip_cell_params"] = strip_cell_params

            signature["stripping_params"]["sheets"].append(sheet)
            jdf["signatures"].append(signature)
    else:
        # Si no hay partes, crear una signatura por defecto
        signature = {
            "signature_ID": "Str-1",
            "job_part_id_name": "Principal",
            "press_name": machine_data.get("name", "Prensa B1") if machine_data else "Prensa B1",
            "assembly_order": "Gathering",
            "stripping_params": {
                "signature_name": "Sig-1",
                "sheets": [{
                    "sheet_name": "Sheet-1-1",
                    "bindery_signature_name": "BSN-1-1",
                    "paper_ref": f"Pap-{paper_data.get('paper_id', '001')}" if paper_data else "Pap-001",
                    "fold_catalog": "F16-7",
                    "work_style": "Flat",
                    "strip_cell_params": {
                        "trim_size_width": budget_data.get("page_size", {}).get("width", 210),
                        "trim_size_height": budget_data.get("page_size", {}).get("height", 297),
                        "bleed_face": 3,
                        "bleed_foot": 3,
                        "bleed_head": 3,
                        "bleed_spine": 3,
                        "orientation": "Rotate0",
                        "relative_box_x1": 0,
                        "relative_box_y1": 0,
                        "relative_box_x2": 1,
                        "relative_box_y2": 1
                    }
                }]
            }
        }
        jdf["signatures"].append(signature)

    # Añadir información del cliente
    if client_data:
        jdf["customer_info"] = {
            "customer_id": client_data.get("client_id", ""),
            "company_name": client_data.get("company", {}).get("name", "") if "company" in client_data else "",
            "country": client_data.get("address", {}).get("country", "") if "address" in client_data else "",
            "region": client_data.get("address", {}).get("region", "") if "address" in client_data else "",
            "city": client_data.get("address", {}).get("city", "") if "address" in client_data else "",
            "street": client_data.get("address", {}).get("street", "") if "address" in client_data else "",
            "postal_code": client_data.get("address", {}).get("postal_code", "") if "address" in client_data else "",
            "first_name": client_data.get("contact", {}).get("first_name", "") if "contact" in client_data else "",
            "family_name": client_data.get("contact", {}).get("last_name", "") if "contact" in client_data else "",
            "phone": client_data.get("contact", {}).get("phone", "") if "contact" in client_data else "",
            "email": client_data.get("contact", {}).get("email", "") if "contact" in client_data else ""
        }

    # Añadir runlists si hay un PDF asociado
    pdf_filename = budget_data.get("pdf_file")
    page_count = budget_data.get("page_count")

    if pdf_filename and page_count:
        # Construir la URL del PDF
        pdf_url = f"http://localhost:3005/uploads/{pdf_filename}"

        jdf["runlists"] = [{
            "runlist_id": "RL-1d",
            "pages": page_count,
            "page_range": f"0 ~ {page_count-1}",
            "signature_ref": "Str-1",  # Referencia a la primera signatura
            "pdf_url": pdf_url
        }]

    return jdf
