import { buildApiUrl } from '../config';

/**
 * Obtiene los presupuestos que pueden ser facturados (Completados o Enviados y no facturados)
 * @returns {Promise<Array>} Lista de presupuestos facturables
 */
export const getBillableBudgets = async () => {
  try {
    // Obtener el token de autenticación del localStorage
    const token = localStorage.getItem('auth_token');

    if (!token) {
      throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
    }

    const response = await fetch(buildApiUrl('/invoices/billable'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Error al obtener presupuestos facturables: ${response.statusText}`);
    }

    const data = await response.json();

    // Calcular el PVP para cada presupuesto
    return data.map(budget => {
      // Calcular importe total (costo)
      let totalCost = 0;

      // Sumar costos de partes
      const parts = budget.parts || [];
      for (const part of parts) {
        totalCost += part.total_cost || 0;
      }

      // Sumar costos de procesos
      totalCost += budget.process_total_cost || 0;

      // Sumar costo de envío
      totalCost += budget.shipping_cost || 0;

      // Calcular precio de lista (con margen de beneficio)
      const profitPercentage = 30; // Porcentaje de beneficio por defecto
      const listPrice = totalCost * (1 + profitPercentage / 100);

      // Aplicar descuento del cliente si existe
      const clientData = budget.client_data || {};
      const discountPercentage = clientData.discount_percentage || 0;
      let finalPrice = listPrice;

      if (discountPercentage > 0) {
        const discountAmount = listPrice * (discountPercentage / 100);
        finalPrice = listPrice - discountAmount;
      }

      // Añadir los campos calculados al presupuesto
      return {
        ...budget,
        total_cost: totalCost,
        list_price: listPrice,
        pvp: finalPrice
      };
    });
  } catch (error) {
    console.error('Error en getBillableBudgets:', error);
    throw error;
  }
};

/**
 * Genera una factura para un presupuesto específico
 * @param {string} budgetId ID del presupuesto
 * @returns {Promise<Object>} Información de la factura generada
 */
export const generateInvoice = async (budgetId) => {
  try {
    // Obtener el token de autenticación del localStorage
    const token = localStorage.getItem('auth_token');

    if (!token) {
      throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
    }

    console.log(`Generando factura para el presupuesto con ID: ${budgetId}`);

    // Intentar obtener información del presupuesto primero para verificar que existe
    const budgetResponse = await fetch(buildApiUrl(`/budgets/${budgetId}`), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!budgetResponse.ok) {
      throw new Error(`Error al obtener el presupuesto: ${budgetResponse.statusText}`);
    }

    const budget = await budgetResponse.json();
    console.log(`Presupuesto encontrado: ${budget.budget_id}, Cliente: ${budget.client_id}`);

    const response = await fetch(buildApiUrl(`/invoices/${budgetId}/generate`), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Error al generar factura: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error en generateInvoice:', error);
    throw error;
  }
};

/**
 * Lista las facturas generadas
 * @param {number} year Año de las facturas (opcional)
 * @param {number} month Mes de las facturas (opcional)
 * @returns {Promise<Array>} Lista de facturas
 */
export const listInvoices = async (year, month) => {
  try {
    // Obtener el token de autenticación del localStorage
    const token = localStorage.getItem('auth_token');

    if (!token) {
      throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
    }

    let url = buildApiUrl('/invoices/list');
    const params = new URLSearchParams();

    if (year) {
      params.append('year', year);
    }

    if (month) {
      params.append('month', month);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Error al listar facturas: ${response.statusText}`);
    }

    const data = await response.json();

    // Obtener información detallada de los presupuestos asociados a las facturas
    const budgetsResponse = await fetch(buildApiUrl('/budgets'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!budgetsResponse.ok) {
      throw new Error(`Error al obtener presupuestos: ${budgetsResponse.statusText}`);
    }

    const budgets = await budgetsResponse.json();

    // Enriquecer los datos de las facturas con información adicional de los presupuestos
    return data.map(invoice => {
      const relatedBudget = budgets.find(b => b.budget_id === invoice.budget_id);

      if (relatedBudget) {
        return {
          ...invoice,
          client_id: relatedBudget.client_id,
          created_at: relatedBudget.created_at,
          completed_at: relatedBudget.completed_at,
          shipped_at: relatedBudget.shipped_at,
          client_data: relatedBudget.client_data,
          client_company: relatedBudget.client_data?.company?.name || 'Cliente no especificado',
          description: relatedBudget.description,
          job_type: relatedBudget.job_type,
          quantity: relatedBudget.quantity,
          ot_number: relatedBudget.ot_number
        };
      }

      return invoice;
    });
  } catch (error) {
    console.error('Error en listInvoices:', error);
    throw error;
  }
};

/**
 * Descarga una factura en PDF
 * @param {string} invoicePath Ruta al archivo de factura
 * @returns {Promise<Blob>} Blob con el contenido del PDF
 */
export const downloadInvoice = async (invoicePath) => {
  try {
    // Obtener el token de autenticación del localStorage
    const token = localStorage.getItem('auth_token');

    if (!token) {
      throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
    }

    // Extraer el nombre del archivo de la ruta
    // Primero verificamos si la ruta contiene una ruta completa del sistema
    let fileName;

    if (invoicePath.includes('facturas')) {
      // Extraer el nombre del archivo con formato FACT-YYYYMM-XXXX.pdf
      const matches = invoicePath.match(/FACT-\d{6}-\d{4}\.pdf$/);
      if (matches) {
        fileName = matches[0];
      } else {
        // Si no coincide con el patrón, usar el último segmento de la ruta
        fileName = invoicePath.split(/[\\\/]/).pop();
      }
    } else {
      // Si no es una ruta de factura, simplemente usar el último segmento
      fileName = invoicePath.split(/[\\\/]/).pop();
    }

    console.log(`Intentando descargar archivo: ${fileName} desde la ruta: ${invoicePath}`);

    // Construir la URL para descargar el archivo
    const url = buildApiUrl(`/uploads/download/${fileName}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`Error al descargar factura: ${response.statusText}`);
    }

    const blob = await response.blob();
    return blob;
  } catch (error) {
    console.error('Error en downloadInvoice:', error);
    throw error;
  }
};
