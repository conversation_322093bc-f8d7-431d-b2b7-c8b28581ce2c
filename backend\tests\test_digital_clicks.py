"""
Script para probar el cálculo de clicks digitales y tiempo estimado
"""
import sys
import os
import json

# Añadir el directorio raíz al path para poder importar los módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.models import ClickCalculationRequest
from routes.calculations import calculate_digital_clicks

async def test_digital_clicks():
    """
    Prueba el cálculo de clicks digitales y tiempo estimado
    """
    # Caso de prueba 1: Impresión dúplex a color
    request1 = ClickCalculationRequest(
        machine_id="MAQ-016",  # Fujifilm Revoria PC1120
        sheets=20,
        copies=200,
        is_color=True,
        is_duplex=True
    )
    
    # Ejecutar el cálculo
    result1 = await calculate_digital_clicks(request1)
    
    # Imprimir el resultado
    print("\nCaso 1: Impresión dúplex a color")
    print(json.dumps(result1.dict(), indent=2))
    
    # Caso de prueba 2: Impresión simplex a color
    request2 = ClickCalculationRequest(
        machine_id="MAQ-016",  # Fujifilm Revoria PC1120
        sheets=20,
        copies=200,
        is_color=True,
        is_duplex=False
    )
    
    # Ejecutar el cálculo
    result2 = await calculate_digital_clicks(request2)
    
    # Imprimir el resultado
    print("\nCaso 2: Impresión simplex a color")
    print(json.dumps(result2.dict(), indent=2))

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_digital_clicks())
