# Componentes

Este documento describe los componentes principales del frontend de la aplicación Imprenta.

## Índice

1. [Introducción](#introducción)
2. [Componentes de Catálogo](#componentes-de-catálogo)
3. [Componentes de Presupuesto](#componentes-de-presupuesto)
4. [Componentes de Cálculo](#componentes-de-cálculo)
5. [Componentes Comunes](#componentes-comunes)

## Introducción

Los componentes son elementos de la interfaz de usuario reutilizables. Están organizados por funcionalidad y se encuentran en el directorio `src/components/`.

## Componentes de Catálogo

### PaperCatalog

El componente `PaperCatalog` proporciona una interfaz para gestionar el catálogo de papeles:

- **Funcionalidades**:
  - Listar todos los papeles
  - Añadir un nuevo papel
  - Editar un papel existente
  - Eliminar un papel
  - Filtrar y buscar papeles

- **Props**:
  - Ninguna

- **Estado**:
  - `papers`: Lista de papeles
  - `selectedPaper`: Papel seleccionado para edición
  - `isDialogOpen`: Estado del diálogo de edición
  - `filters`: Filtros aplicados a la lista

- **Ejemplo de uso**:
  ```jsx
  <PaperCatalog />
  ```

### MachineCatalog

El componente `MachineCatalog` proporciona una interfaz para gestionar el catálogo de máquinas:

- **Funcionalidades**:
  - Listar todas las máquinas
  - Añadir una nueva máquina
  - Editar una máquina existente
  - Eliminar una máquina
  - Filtrar y buscar máquinas

- **Props**:
  - Ninguna

- **Estado**:
  - `machines`: Lista de máquinas
  - `selectedMachine`: Máquina seleccionada para edición
  - `isDialogOpen`: Estado del diálogo de edición
  - `filters`: Filtros aplicados a la lista

- **Ejemplo de uso**:
  ```jsx
  <MachineCatalog />
  ```

### ClientCatalog

El componente `ClientCatalog` proporciona una interfaz para gestionar el catálogo de clientes:

- **Funcionalidades**:
  - Listar todos los clientes
  - Añadir un nuevo cliente
  - Editar un cliente existente
  - Eliminar un cliente
  - Filtrar y buscar clientes

- **Props**:
  - Ninguna

- **Estado**:
  - `clients`: Lista de clientes
  - `selectedClient`: Cliente seleccionado para edición
  - `isDialogOpen`: Estado del diálogo de edición
  - `filters`: Filtros aplicados a la lista

- **Ejemplo de uso**:
  ```jsx
  <ClientCatalog />
  ```

### ProcessCatalog

El componente `ProcessCatalog` proporciona una interfaz para gestionar el catálogo de procesos:

- **Funcionalidades**:
  - Listar todos los procesos
  - Añadir un nuevo proceso
  - Editar un proceso existente
  - Eliminar un proceso
  - Filtrar y buscar procesos

- **Props**:
  - Ninguna

- **Estado**:
  - `processes`: Lista de procesos
  - `selectedProcess`: Proceso seleccionado para edición
  - `isDialogOpen`: Estado del diálogo de edición
  - `filters`: Filtros aplicados a la lista

- **Ejemplo de uso**:
  ```jsx
  <ProcessCatalog />
  ```

## Componentes de Presupuesto

### BudgetForm

El componente `BudgetForm` proporciona un formulario para crear y editar presupuestos:

- **Funcionalidades**:
  - Crear un nuevo presupuesto
  - Editar un presupuesto existente
  - Calcular pliegos mediante el componente SheetCalculationModal
  - Calcular costes (papel, máquina, acabados)
  - Subir archivos PDF con el componente PdfUploader
  - Seleccionar tipo de trabajo con el componente JobTypeSelector
  - Gestionar acabados con el componente FinishingSelector
  - Configurar colores de impresión (4/0, 4/4, etc.)
  - Redirección automática al listado de presupuestos tras actualizar un presupuesto
  - Visualización prominente del coste total
  - Personalización del tiempo de impresión de la máquina
  - Actualización automática del estado a "Actualizado" cuando se modifica un presupuesto existente

- **Props**:
  - `budgetId`: ID del presupuesto a editar (opcional)
  - `onSave`: Función a ejecutar después de guardar (opcional)

- **Estado**:
  - `budget`: Datos del presupuesto
  - `clients`: Lista de clientes
  - `papers`: Lista de papeles
  - `machines`: Lista de máquinas
  - `processes`: Lista de procesos disponibles
  - `selectedProcesses`: Lista de procesos seleccionados para el presupuesto
  - `sheetCalculation`: Resultado del cálculo de pliegos
  - `isCalculating`: Estado del cálculo
  - `selectedPdf`: Archivo PDF seleccionado
  - `pdfInfo`: Información del PDF (tamaño, páginas, etc.)
  - `customMachinePrintTime`: Tiempo personalizado para la máquina
  - `colorConfig`: Configuración de colores (frontColors, backColors, pantones)
  - `calculatedPaperCost`: Coste calculado del papel
  - `calculatedMachineCost`: Coste calculado de la máquina
  - `calculatedProcessCost`: Coste calculado de los acabados
  - `calculatedTotalCost`: Coste total calculado

- **Componentes relacionados**:
  - `JobTypeSelector`: Selector de tipo de trabajo
  - `FinishingSelector`: Selector de acabados
  - `SheetCalculationModal`: Modal para cálculo de pliegos
  - `PdfUploader`: Componente para subir y mostrar información de PDFs
  - `MachineInfoModal`: Modal con información de la máquina
  - `ShippingCostSection`: Componente recién creado para mostrar explícitamente el coste de envío y el peso total del papel

- **Ejemplo de uso**:
  ```jsx
  <BudgetForm budgetId="PRES-123456" onSave={() => console.log('Presupuesto guardado')} />
  ```

### BudgetList (en la carpeta pages)

El componente `BudgetList` proporciona una lista de presupuestos con opciones de filtrado y búsqueda:

- **Funcionalidades**:
  - Listar todos los presupuestos
  - Filtrar y buscar presupuestos
  - Ver detalles de un presupuesto
  - Editar un presupuesto
  - Eliminar un presupuesto
  - Duplicar un presupuesto
  - Generar PDF
  - Generar JDF
  - Ver JSON
  - Cambiar el estado de un presupuesto directamente haciendo clic en el chip de estado
  - Mostrar un menú desplegable con las opciones de estado al hacer clic en el chip
  - Visualización mejorada con chips de estado sólidos (filled) para mejor visibilidad
  - Estados disponibles:
    - Pendiente (color warning - amarillo): Estado inicial
    - Actualizado (color secondary - morado): Cuando se modifica un presupuesto existente
    - Aprobado (color success - verde): Cuando el cliente aprueba el presupuesto
    - Enviado (color primary - azul): Cuando se envía el presupuesto al cliente
    - Rechazado (color error - rojo): Cuando el cliente rechaza el presupuesto
    - Completado (color info - celeste): Cuando el trabajo se ha completado

- **Props**:
  - `onEditBudget`: Función a ejecutar al editar un presupuesto

- **Estado**:
  - `budgets`: Lista de presupuestos
  - `filteredBudgets`: Lista de presupuestos filtrados
  - `clients`: Lista de clientes
  - `filters`: Filtros aplicados a la lista
  - `statusMenu`: Estado del menú de cambio de estado (nuevo)
  - `detailsDialog`: Estado del diálogo de detalles
  - `deleteDialog`: Estado del diálogo de eliminación
  - `duplicateDialog`: Estado del diálogo de duplicación
  - `pdfDialog`: Estado del diálogo de PDF
  - `jdfDialog`: Estado del diálogo de JDF
  - `jsonDialog`: Estado del diálogo de JSON

- **Ejemplo de uso**:
  ```jsx
  <BudgetList onEditBudget={(budgetId) => console.log(`Editando presupuesto ${budgetId}`)} />
  ```

### BudgetPDF

El componente `BudgetPDF` proporciona una interfaz para generar y visualizar un PDF de un presupuesto:

- **Funcionalidades**:
  - Generar un PDF de un presupuesto
  - Visualizar el PDF
  - Descargar el PDF

- **Props**:
  - `budgetId`: ID del presupuesto
  - `onClose`: Función a ejecutar al cerrar el diálogo

- **Estado**:
  - `budget`: Datos del presupuesto
  - `client`: Datos del cliente
  - `paper`: Datos del papel
  - `machine`: Datos de la máquina
  - `loading`: Estado de carga

- **Ejemplo de uso**:
  ```jsx
  <BudgetPDF budgetId="PRES-123456" onClose={() => console.log('Diálogo cerrado')} />
  ```

### BudgetJDFGenerator

El componente `BudgetJDFGenerator` proporciona una interfaz para generar un archivo JDF de un presupuesto:

- **Funcionalidades**:
  - Generar un archivo JDF de un presupuesto
  - Visualizar el JDF
  - Descargar el JDF

- **Props**:
  - `budgetId`: ID del presupuesto
  - `onClose`: Función a ejecutar al cerrar el diálogo

- **Estado**:
  - `budget`: Datos del presupuesto
  - `jdfContent`: Contenido del JDF
  - `loading`: Estado de carga

- **Ejemplo de uso**:
  ```jsx
  <BudgetJDFGenerator budgetId="PRES-123456" onClose={() => console.log('Diálogo cerrado')} />
  ```

### BudgetJsonViewer

El componente `BudgetJsonViewer` proporciona una interfaz para visualizar el JSON de un presupuesto:

- **Funcionalidades**:
  - Visualizar el JSON de un presupuesto
  - Copiar el JSON al portapapeles

- **Props**:
  - `budgetId`: ID del presupuesto
  - `onClose`: Función a ejecutar al cerrar el diálogo

- **Estado**:
  - `budget`: Datos del presupuesto
  - `loading`: Estado de carga

- **Ejemplo de uso**:
  ```jsx
  <BudgetJsonViewer budgetId="PRES-123456" onClose={() => console.log('Diálogo cerrado')} />
  ```

## Componentes de Cálculo

### PaperCalculator

El componente `PaperCalculator` proporciona una calculadora de papel:

- **Funcionalidades**:
  - Calcular el coste del papel
  - Calcular el número de pliegos
  - Calcular el peso del papel

- **Props**:
  - Ninguna

- **Estado**:
  - `formData`: Datos del formulario
  - `result`: Resultado del cálculo

- **Ejemplo de uso**:
  ```jsx
  <PaperCalculator />
  ```

### PaperSheetCalculator

El componente `PaperSheetCalculator` proporciona una calculadora de pliegos:

- **Funcionalidades**:
  - Calcular la disposición de páginas en pliegos
  - Visualizar los esquemas posibles
  - Calcular el número total de pliegos y planchas

- **Props**:
  - Ninguna

- **Estado**:
  - `formData`: Datos del formulario
  - `result`: Resultado del cálculo
  - `loading`: Estado de carga

- **Ejemplo de uso**:
  ```jsx
  <PaperSheetCalculator />
  ```

## Componentes Modulares de Presupuesto

### JobTypeSelector

El componente `JobTypeSelector` proporciona una interfaz para seleccionar el tipo de trabajo y configurar los colores de impresión:

- **Funcionalidades**:
  - Seleccionar el tipo de trabajo (Revista, Libro, Folleto, etc.)
  - Configurar colores de impresión (4/0, 4/4, 1/0, 1/1, etc.)
  - Añadir colores pantone adicionales
  - Mostrar información de colores junto al tipo de trabajo

- **Props**:
  - `jobType`: Tipo de trabajo seleccionado
  - `onJobTypeChange`: Función a ejecutar cuando cambia el tipo de trabajo
  - `colorConfig`: Configuración de colores actual
  - `onColorConfigChange`: Función a ejecutar cuando cambia la configuración de colores

- **Estado**:
  - `colorModalOpen`: Estado del modal de selección de colores
  - `frontColors`: Número de colores en el frente
  - `backColors`: Número de colores en el dorso
  - `pantones`: Lista de colores pantone adicionales

- **Ejemplo de uso**:
  ```jsx
  <JobTypeSelector
    jobType="Revista"
    onJobTypeChange={(value) => setBudget({...budget, job_type: value})}
    colorConfig={colorConfig}
    onColorConfigChange={setColorConfig}
  />
  ```

### FinishingSelector

El componente `FinishingSelector` proporciona una interfaz para seleccionar y gestionar los procesos de acabado:

- **Funcionalidades**:
  - Mostrar procesos de acabado disponibles (corte, plegado, encuadernación, etc.)
  - Seleccionar procesos para el presupuesto
  - Configurar cantidades para cada proceso
  - Calcular costes de acabados
  - Mostrar un resumen de los procesos seleccionados

- **Props**:
  - `selectedProcesses`: Lista de procesos seleccionados
  - `setSelectedProcesses`: Función para actualizar los procesos seleccionados
  - `calculatedProcessCost`: Coste calculado de los acabados
  - `setCalculatedProcessCost`: Función para actualizar el coste calculado
  - `budget`: Datos del presupuesto (para acceder a copies)

- **Estado**:
  - `processes`: Lista de procesos disponibles
  - `expanded`: Estado del acordeon (abierto/cerrado)

- **Ejemplo de uso**:
  ```jsx
  <FinishingSelector
    selectedProcesses={selectedProcesses}
    setSelectedProcesses={setSelectedProcesses}
    calculatedProcessCost={calculatedProcessCost}
    setCalculatedProcessCost={setCalculatedProcessCost}
    budget={budget}
  />
  ```

### PdfUploader

El componente `PdfUploader` proporciona una interfaz para subir archivos PDF y mostrar su información:

- **Funcionalidades**:
  - Seleccionar archivos PDF
  - Extraer y mostrar información del PDF (tamaño, páginas, dimensiones)
  - Mostrar vista previa del PDF
  - Actualizar automáticamente el tamaño y número de páginas en el presupuesto

- **Props**:
  - `selectedPdf`: Archivo PDF seleccionado
  - `setSelectedPdf`: Función para actualizar el PDF seleccionado
  - `pdfInfo`: Información del PDF
  - `setPdfInfo`: Función para actualizar la información del PDF
  - `onPdfInfoExtracted`: Función a ejecutar cuando se extrae la información del PDF

- **Estado**:
  - `uploading`: Estado de subida
  - `progress`: Progreso de subida
  - `previewUrl`: URL para la vista previa del PDF

- **Ejemplo de uso**:
  ```jsx
  <PdfUploader
    selectedPdf={selectedPdf}
    setSelectedPdf={setSelectedPdf}
    pdfInfo={pdfInfo}
    setPdfInfo={setPdfInfo}
    onPdfInfoExtracted={(info) => {
      setBudget({
        ...budget,
        page_size: { width: info.width, height: info.height },
        page_count: info.numPages
      });
    }}
  />
  ```

### SheetCalculationModal

El componente `SheetCalculationModal` proporciona una interfaz para calcular pliegos y costes:

- **Funcionalidades**:
  - Calcular disposición de páginas en pliegos
  - Visualizar esquemas posibles
  - Calcular número total de pliegos y planchas
  - Calcular costes de papel y máquina
  - Mostrar resumen de cálculos

- **Props**:
  - `open`: Estado del modal (abierto/cerrado)
  - `onClose`: Función a ejecutar al cerrar el modal
  - `budget`: Datos del presupuesto
  - `onCalculationComplete`: Función a ejecutar cuando se completa el cálculo
  - `paper`: Datos del papel seleccionado
  - `machine`: Datos de la máquina seleccionada
  - `customMachinePrintTime`: Tiempo personalizado para la máquina
  - `setCustomMachinePrintTime`: Función para actualizar el tiempo personalizado

- **Estado**:
  - `formData`: Datos del formulario
  - `result`: Resultado del cálculo
  - `loading`: Estado de carga
  - `error`: Error del cálculo

- **Ejemplo de uso**:
  ```jsx
  <SheetCalculationModal
    open={isCalculating}
    onClose={() => setIsCalculating(false)}
    budget={budget}
    onCalculationComplete={handleCalculationComplete}
    paper={selectedPaper}
    machine={selectedMachine}
    customMachinePrintTime={customMachinePrintTime}
    setCustomMachinePrintTime={setCustomMachinePrintTime}
  />
  ```

### MachineInfoModal

El componente `MachineInfoModal` proporciona una interfaz para mostrar información detallada de la máquina y sus costes:

- **Funcionalidades**:
  - Mostrar características de la máquina
  - Mostrar coste horario y CFA
  - Calcular tiempo de impresión
  - Personalizar tiempo de impresión
  - Calcular coste total de la máquina

- **Props**:
  - `open`: Estado del modal (abierto/cerrado)
  - `onClose`: Función a ejecutar al cerrar el modal
  - `machine`: Datos de la máquina
  - `totalSheets`: Número total de pliegos
  - `customPrintTime`: Tiempo personalizado para la máquina
  - `setCustomPrintTime`: Función para actualizar el tiempo personalizado

- **Estado**:
  - `printTime`: Tiempo de impresión calculado
  - `isCustomTime`: Indica si se está usando un tiempo personalizado

- **Ejemplo de uso**:
  ```jsx
  <MachineInfoModal
    open={machineInfoOpen}
    onClose={() => setMachineInfoOpen(false)}
    machine={selectedMachine}
    totalSheets={sheetCalculation?.total_sheets || 0}
    customPrintTime={customMachinePrintTime}
    setCustomPrintTime={setCustomMachinePrintTime}
  />
  ```

## Componentes Comunes

### FileUploader

El componente `FileUploader` proporciona una interfaz para subir archivos:

- **Funcionalidades**:
  - Seleccionar archivos
  - Subir archivos
  - Mostrar progreso de subida

- **Props**:
  - `onFileSelected`: Función a ejecutar cuando se selecciona un archivo
  - `onUploadComplete`: Función a ejecutar cuando se completa la subida
  - `accept`: Tipos de archivo aceptados
  - `multiple`: Permitir selección múltiple

- **Estado**:
  - `selectedFile`: Archivo seleccionado
  - `uploading`: Estado de subida
  - `progress`: Progreso de subida

- **Ejemplo de uso**:
  ```jsx
  <FileUploader
    onFileSelected={(file) => console.log(`Archivo seleccionado: ${file.name}`)}
    onUploadComplete={(response) => console.log('Subida completada', response)}
    accept=".pdf"
    multiple={false}
  />
  ```

### ConfirmDialog

El componente `ConfirmDialog` proporciona un diálogo de confirmación:

- **Funcionalidades**:
  - Mostrar un mensaje de confirmación
  - Proporcionar botones de confirmación y cancelación

- **Props**:
  - `open`: Estado del diálogo
  - `title`: Título del diálogo
  - `message`: Mensaje del diálogo
  - `onConfirm`: Función a ejecutar al confirmar
  - `onCancel`: Función a ejecutar al cancelar
  - `confirmText`: Texto del botón de confirmación
  - `cancelText`: Texto del botón de cancelación

- **Estado**:
  - Ninguno

- **Ejemplo de uso**:
  ```jsx
  <ConfirmDialog
    open={true}
    title="Confirmar eliminación"
    message="¿Estás seguro de que deseas eliminar este elemento?"
    onConfirm={() => console.log('Confirmado')}
    onCancel={() => console.log('Cancelado')}
    confirmText="Eliminar"
    cancelText="Cancelar"
  />
  ```

### App

El componente `App` es el componente principal de la aplicación:

- **Funcionalidades**:
  - Gestionar la navegación entre pestañas
  - Mostrar el formulario de presupuesto o la lista de presupuestos
  - Escuchar eventos personalizados para la navegación automática
  - Redireccionar al listado de presupuestos después de actualizar un presupuesto

- **Estado**:
  - `currentTab`: Pestaña actual
  - `editBudgetId`: ID del presupuesto en edición

- **Eventos personalizados**:
  - `navigate-to-budget-list`: Evento que se dispara para navegar automáticamente al listado de presupuestos

### Planning

- **Descripción**: Componente para visualizar y gestionar la planificación de trabajos en las máquinas de impresión.

- **Funcionalidades**:
  - Visualización de calendario laboral por día o semana
  - Arrastrar y soltar eventos (drag and drop) para reasignar trabajos
  - Visualización de presupuestos por máquina y horario
  - Edición de eventos de trabajo
  - Navegación entre días y semanas
  - Visualización de trabajos por estado (Aprobado, Enviado, Completado, Actualizado)

- **Props**:
  - No recibe props

- **Estado**:
  - `currentDate`: Fecha actual seleccionada
  - `viewType`: Tipo de vista ('day' o 'week')
  - `dateRange`: Rango de fechas a mostrar
  - `machines`: Lista de máquinas disponibles
  - `budgets`: Lista de presupuestos
  - `events`: Lista de eventos generados a partir de los presupuestos
  - `selectedEvent`: Evento seleccionado para edición
  - `isEditModalOpen`: Estado del modal de edición
  - `loading`: Indicador de carga
  - `error`: Mensaje de error
