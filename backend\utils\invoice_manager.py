import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from utils.logger import log_info, log_error
from utils.data_manager import read_data, write_data
import uuid

# Directorio base para facturas
INVOICES_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "facturas")

def ensure_invoice_directories():
    """
    Asegura que exista la estructura de directorios para las facturas
    """
    try:
        # Crear el directorio base si no existe
        os.makedirs(INVOICES_DIR, exist_ok=True)
        log_info(f"Directorio base de facturas creado: {INVOICES_DIR}")

        # Crear directorios para el año actual
        current_year = datetime.now().year
        year_dir = os.path.join(INVOICES_DIR, str(current_year))
        os.makedirs(year_dir, exist_ok=True)

        # Crear directorios para cada mes del año actual
        for month in range(1, 13):
            month_dir = os.path.join(year_dir, f"{month:02d}")
            os.makedirs(month_dir, exist_ok=True)

        log_info(f"Estructura de directorios para facturas del año {current_year} creada correctamente")
        return True
    except Exception as e:
        log_error(f"Error al crear la estructura de directorios para facturas: {str(e)}")
        return False

def get_invoice_directory(date=None):
    """
    Obtiene el directorio para guardar una factura según la fecha

    Args:
        date: Fecha para la factura (por defecto, fecha actual)

    Returns:
        str: Ruta al directorio donde guardar la factura
    """
    if date is None:
        date = datetime.now()
    elif isinstance(date, str):
        try:
            date = datetime.fromisoformat(date)
        except ValueError:
            log_error(f"Formato de fecha inválido: {date}")
            date = datetime.now()

    year_dir = os.path.join(INVOICES_DIR, str(date.year))
    month_dir = os.path.join(year_dir, f"{date.month:02d}")

    # Asegurarse de que los directorios existan
    os.makedirs(month_dir, exist_ok=True)

    return month_dir

def get_next_invoice_number():
    """
    Genera el siguiente número de factura

    Returns:
        str: Número de factura en formato FACT-YYYYMM-XXXX
    """
    now = datetime.now()
    year_month = f"{now.year}{now.month:02d}"

    # Cargar el contador de facturas
    counter_file = os.path.join(INVOICES_DIR, "counter.json")
    counter = 1

    try:
        if os.path.exists(counter_file):
            with open(counter_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                if year_month in data:
                    counter = data[year_month] + 1
                    data[year_month] = counter
                else:
                    data[year_month] = counter
        else:
            data = {year_month: counter}

        # Guardar el contador actualizado
        with open(counter_file, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4)

        # Generar el número de factura
        invoice_number = f"FACT-{year_month}-{counter:04d}"
        log_info(f"Generado nuevo número de factura: {invoice_number}")
        return invoice_number

    except Exception as e:
        log_error(f"Error al generar número de factura: {str(e)}")
        # Generar un número aleatorio en caso de error
        random_id = uuid.uuid4().hex[:4].upper()
        return f"FACT-{year_month}-{random_id}"

def get_billable_budgets():
    """
    Obtiene los presupuestos que pueden ser facturados (Completados o Enviados y no facturados)

    Returns:
        List[Dict]: Lista de presupuestos facturables
    """
    try:
        budgets = read_data("budgets")
        billable_budgets = [
            budget for budget in budgets
            if budget.get("status") in ["Completado", "Enviado"]
            and not budget.get("facturado", False)
        ]

        log_info(f"Encontrados {len(billable_budgets)} presupuestos facturables")
        return billable_budgets
    except Exception as e:
        log_error(f"Error al obtener presupuestos facturables: {str(e)}")
        return []

def mark_budget_as_billed(budget_id: str, invoice_number: str):
    """
    Marca un presupuesto como facturado y cambia su estado a "Facturado"

    Args:
        budget_id: ID del presupuesto
        invoice_number: Número de factura

    Returns:
        bool: True si se actualizó correctamente, False en caso contrario
    """
    try:
        budgets = read_data("budgets")
        budget_found = False

        for i, budget in enumerate(budgets):
            if budget.get("budget_id") == budget_id:
                budgets[i]["facturado"] = True
                budgets[i]["fecha_factura"] = datetime.now().isoformat()
                budgets[i]["numero_factura"] = invoice_number
                # Cambiar el estado del presupuesto a "Facturado"
                budgets[i]["status"] = "Facturado"
                budgets[i]["updated_at"] = datetime.now().isoformat()
                log_info(f"Cambiando estado del presupuesto {budget_id} a 'Facturado'")
                budget_found = True
                break

        if not budget_found:
            log_error(f"No se encontró el presupuesto con ID {budget_id}")
            return False

        # Guardar los cambios
        result = write_data("budgets", budgets)
        if result:
            log_info(f"Presupuesto {budget_id} marcado como facturado con número {invoice_number}")
        return result

    except Exception as e:
        log_error(f"Error al marcar presupuesto como facturado: {str(e)}")
        return False

# Asegurarse de que la estructura de directorios exista al importar el módulo
ensure_invoice_directories()
