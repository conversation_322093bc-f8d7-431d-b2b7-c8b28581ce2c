import React from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Container,
  Paper
} from '@mui/material';
import BlockIcon from '@mui/icons-material/Block';

const AccessDeniedPage = () => {
  return (
    <Container component="main" maxWidth="md">
      <Paper
        elevation={3}
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: 4
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%'
          }}
        >
          <Box
            sx={{
              backgroundColor: 'error.main',
              borderRadius: '50%',
              padding: 2,
              marginBottom: 2
            }}
          >
            <BlockIcon sx={{ color: 'white', fontSize: 40 }} />
          </Box>
          
          <Typography component="h1" variant="h4" gutterBottom>
            Acceso Denegado
          </Typography>
          
          <Typography variant="body1" align="center" sx={{ mb: 4 }}>
            No tienes permisos suficientes para acceder a esta página.
            Por favor, contacta con el administrador si crees que deberías tener acceso.
          </Typography>
          
          <Button
            component={Link}
            to="/"
            variant="contained"
            color="primary"
            sx={{ mt: 2 }}
          >
            Volver a la página principal
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default AccessDeniedPage;
