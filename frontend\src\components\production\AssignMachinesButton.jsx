import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>nackbar, Alert, CircularProgress } from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';
import { buildApiUrl } from '../../config';
import { ApiInterceptor } from '../../services/simplifiedServices';

/**
 * Componente que muestra un botón para asignar máquinas a procesos de acabado
 * que no tienen máquina asignada.
 */
const AssignMachinesButton = ({ onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  const handleAssignMachines = async () => {
    try {
      setLoading(true);

      const response = await ApiInterceptor.fetch(buildApiUrl('/production/assign-machines'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al asignar máquinas a procesos');
      }

      const result = await response.json();

      setSnackbar({
        open: true,
        message: result.message || 'Máquinas asignadas correctamente',
        severity: 'success'
      });

      // Llamar a la función de éxito si se proporciona
      if (onSuccess && typeof onSuccess === 'function') {
        onSuccess();
      }
    } catch (error) {
      console.error('Error al asignar máquinas:', error);
      setSnackbar({
        open: true,
        message: error.message || 'Error al asignar máquinas a procesos',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <>
      <Button
        variant="outlined"
        color="primary"
        startIcon={loading ? <CircularProgress size={20} /> : <SettingsIcon />}
        onClick={handleAssignMachines}
        disabled={loading}
        sx={{ ml: 1 }}
      >
        {loading ? 'Asignando...' : 'Asignar Máquinas'}
      </Button>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default AssignMachinesButton;
