# Estructura actual de budgets.json

A continuación se muestra la estructura actual de un presupuesto en el fichero `budgets.json`, con una breve descripción de cada campo.

```plaintext
Presupuesto (budget)
├── budget_id                # Identificador único del presupuesto
├── ot_number                # Número de orden de trabajo (OT) asociado
├── description              # Descripción textual del trabajo o pedido
├── client_id                # Identificador único del cliente (referencia al catálogo)
├── client_data              # Objeto con los datos completos del cliente en el momento de crear/actualizar el presupuesto (snapshot histórico)
│   ├── client_id                # ID del cliente
│   ├── billing_code             # Código de facturación
│   ├── order_id                 # ID de pedido
│   ├── company                  # Datos de la empresa
│   │   ├── name                 # Nombre de la empresa
│   │   └── address              # Dirección completa
│   ├── contact                  # Persona de contacto
│   │   ├── position             # Cargo
│   │   ├── first_name           # Nombre
│   │   ├── last_name            # Apellidos
│   │   ├── phone                # Teléfono
│   │   ├── fax                  # Fax
│   │   └── email                # Correo electrónico
│   ├── active                   # Si el cliente está activo
│   ├── discount_percentage      # % de descuento aplicado
│   ├── created_at               # Fecha de alta
│   ├── updated_at               # Fecha de última actualización
│   └── notes                    # Notas adicionales
├── job_type                 # Tipo de trabajo (ej: Revista, Libro, etc.)
├── quantity                 # Cantidad de ejemplares solicitados
├── page_size                # Tamaño de página
│   ├── width                # Ancho de la página (mm)
│   └── height               # Alto de la página (mm)
├── pdf_filename             # Nombre del archivo PDF asociado (opcional)
├── status                   # Estado del presupuesto (ej: Pendiente, Aprobado)
├── created_at               # Fecha de creación
├── updated_at               # Fecha de última actualización
├── parts [ ]                # Lista de partes/componentes del presupuesto
│   └── Parte (part)
│       ├── part_id              # Identificador único de la parte
│       ├── name                 # Nombre de la parte/producto
│       ├── description          # Descripción de la parte
│       ├── assembly_order       # Tipo de encuadernado o ensamblaje
│       ├── page_size            # Tamaño de página de la parte
│       │   ├── width            # Ancho de la página (mm)
│       │   └── height           # Alto de la página (mm)
│       ├── page_count           # Número de páginas de la parte
│       ├── paper_id             # ID del papel usado
│       ├── machine_id           # ID de la máquina usada
│       ├── sheet_calculation    # Detalles de cálculo de pliegos/planchas
│       │   ├── copies           # Copias para esta parte
│       │   ├── mejor_combinacion    # Esquema óptimo de pliegos/planchas
│       │   │   ├── total_pliegos    # Total de pliegos usados
│       │   │   ├── total_planchas   # Total de planchas usadas
│       │   │   └── esquemas_utilizados [ ] # Lista de esquemas utilizados
│       │   ├── ... (otros campos técnicos y de cálculo)
│       ├── paper_cost            # Coste del papel para esta parte
│       ├── machine_cost          # Coste de máquina
│       ├── plate_cost            # Coste de planchas
│       ├── ink_cost              # Coste de tinta
│       ├── click_cost            # Coste de clicks digitales
│       ├── maculatura_cost       # Coste de maculatura
│       ├── total_cost            # Coste total de la parte
│       ├── custom_print_time     # Tiempo personalizado de impresión (opcional)
│       ├── color_config          # Configuración de colores
│       │   ├── frontColors       # Colores en anverso
│       │   └── backColors        # Colores en reverso
│       └── ... (otros campos técnicos y de cálculo)
├── process_costs [ ]         # Lista de procesos adicionales (ej: corte, encuadernación)
│   └── process_id, name, type, unit_cost, ...
├── process_total_cost        # Coste total de los procesos
├── total_paper_weight_kg     # Peso total estimado del papel (kg)
├── shipping_cost             # Coste de envío
├── facturado                 # Indica si ha sido facturado (true/false)
├── fecha_factura             # Fecha de facturación (opcional)
├── numero_factura            # Número de factura (opcional)
└── ... (otros campos adicionales)
```

---

## Nota sobre client_data

> El campo `client_data` se guarda junto con cada presupuesto como snapshot de los datos del cliente en el momento de la creación o actualización. Así, aunque los datos del cliente cambien en el catálogo, el presupuesto conserva la información original utilizada para su cálculo y facturación.

## Ejemplo real de un presupuesto (`budget`)

```json
{
  "budget_id": "PRES-629BC8",
  "ot_number": "OT-25057740",
  "description": "Revista (500)\nRevista\n40 páginas de tamaño A4\nPapel: Estucado Brillo 115g B1 115g\nPrensa: Prensa B1",
  "client_id": "Customer-2",
  "job_type": "Revista",
  "quantity": 500,
  "page_size": {
    "width": 210,
    "height": 297
  },
  "pdf_filename": null,
  "status": "Aprobado",
  "created_at": "2025-05-10T15:55:52.734946",
  "updated_at": "2025-05-10T16:48:47.212152",
  "parts": [
    {
      "part_id": "part-1746885327211-0",
      "name": "Revista",
      "description": "Revista con grapado a caballete",
      "assembly_order": "Collecting",
      "page_size": { "width": 210, "height": 297 },
      "page_count": 40,
      "paper_id": "Pap-003",
      "machine_id": "MAQ-003",
      "sheet_calculation": {
        "copies": 500,
        "mejor_combinacion": {
          "total_pliegos": 3,
          "total_planchas": 20,
          "esquemas_utilizados": [
            {
              "nombre": "F16-7",
              "numero_pliegos": 2,
              "paginas_por_pliego": 16,
              "disposicion": {
                "paginas_ancho": 4,
                "paginas_alto": 2,
                "orientacion": "vertical_normal"
              },
              "es_tira_retira": false,
              "sheet_type": "WorkAndBack",
              "plates_needed": 16,
              "needs_two_passes": true
            },
            {
              "nombre": "F8-7",
              "numero_pliegos": 1,
              "paginas_por_pliego": 8,
              "disposicion": {
                "paginas_ancho": 4,
                "paginas_alto": 2,
                "orientacion": "horizontal_tira_retira"
              },
              "es_tira_retira": true,
              "sheet_type": "WorkAndTurn",
              "plates_needed": 4,
              "needs_two_passes": false
            }
          ],
          "copies": "500",
          "total_passes": 0
        },
        "machine_id": "MAQ-003",
        "machine_name": "Prensa B1",
        "total_sheets": 3,
        "total_physical_sheets": 1250,
        "total_plates": 20
      },
      "paper_cost": 120.75,
      "machine_cost": 40,
      "plate_cost": 115,
      "ink_cost": 40.11,
      "click_cost": 0,
      "maculatura_cost": 43.47,
      "total_cost": 359.33,
      "color_config": {
        "frontColors": 4,
        "backColors": 4
      }
    }
  ],
  "process_costs": [
    {
      "process_id": "PROC-001",
      "name": "Corte (Guillotina)",
      "type": "Corte",
      "unit_cost": 15,
      "unit_type": "Hora",
      "quantity": 1,
      "total_cost": 15
    },
    {
      "process_id": "PROC-004",
      "name": "Encuadernación Grapada",
      "type": "Encuadernación",
      "unit_cost": 0.15,
      "unit_type": "Unidad",
      "quantity": 500,
      "total_cost": 75
    }
  ],
  "process_total_cost": 90.0,
  "total_paper_weight_kg": 100.62,
  "shipping_cost": 250.0,
  "facturado": false,
  "fecha_factura": null,
  "numero_factura": null,
  "client_data": {
    "client_id": "Customer-2",
    "billing_code": "BBB",
    "order_id": "OrderID-2",
    "company": {
      "name": "Beta Industries",
      "address": {
        "country": "Germany",
        "region": "Bavaria",
        "city": "Munich",
        "street": "Marienplatz",
        "postal_code": "80331"
      }
    },
    "contact": {
      "position": "Director",
      "first_name": "Anna",
      "last_name": "Müller",
      "phone": "************",
      "fax": "************",
      "email": "<EMAIL>"
    },
    "active": true,
    "discount_percentage": 10.0,
    "created_at": "2025-02-19T20:54:17+01:00",
    "updated_at": "2025-02-19T20:54:17+01:00",
    "notes": null
  }
}
```
