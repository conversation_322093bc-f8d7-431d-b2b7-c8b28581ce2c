// services/LogService.js
import { buildApiUrl, LOGGING_ENABLED, LOG_LEVEL, isProduction } from '../config';

/**
 * Servicio para gestionar el logging en el frontend
 */
class LogService {
  /**
   * Envía un registro de log al servidor
   * 
   * @param {string} action - Acción realizada
   * @param {object} details - Detalles adicionales (opcional)
   * @param {string} user - Usuario que realizó la acción (opcional)
   * @returns {Promise} - Promesa con la respuesta del servidor
   */
  static async log(action, details = null, user = null) {
    // Si el logging está desactivado, no hacer nada
    if (!LOGGING_ENABLED) {
      return true;
    }
    
    // Registrar en la consola local
    if (!isProduction) {
      console.log(`[LOG] ${action}`, details || {});
    }
    
    try {
      const response = await fetch(buildApiUrl('/logs/frontend'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          details,
          user,
          timestamp: new Date().toISOString(),
          level: LOG_LEVEL
        })
      });

      if (!response.ok) {
        console.warn('Error al enviar log al servidor:', response.statusText);
      }

      return response.ok;
    } catch (error) {
      console.warn('Error al enviar log al servidor:', error);
      return false;
    }
  }

  /**
   * Registra una acción de navegación
   * 
   * @param {string} route - Ruta a la que se navegó
   * @param {object} params - Parámetros de la ruta (opcional)
   */
  static async logNavigation(route, params = null) {
    return this.log('navigation', { route, params });
  }

  /**
   * Registra una acción del usuario
   * 
   * @param {string} action - Acción realizada
   * @param {object} data - Datos relacionados con la acción (opcional)
   */
  static async logUserAction(action, data = null) {
    return this.log('user_action', { action, data });
  }

  /**
   * Registra un error
   * 
   * @param {string} message - Mensaje de error
   * @param {object} errorData - Datos del error (opcional)
   */
  static async logError(message, errorData = null) {
    return this.log('error', { message, errorData });
  }

  /**
   * Registra una operación de API
   * 
   * @param {string} endpoint - Endpoint de la API
   * @param {string} method - Método HTTP
   * @param {object} data - Datos enviados (opcional)
   * @param {number} status - Código de estado HTTP (opcional)
   */
  static async logApiOperation(endpoint, method, data = null, status = null) {
    return this.log('api_operation', { endpoint, method, data, status });
  }
}

export default LogService;
