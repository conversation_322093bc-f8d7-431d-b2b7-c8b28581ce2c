import { useState, useEffect, useCallback } from 'react';
import { buildApiUrl } from '../../config';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Snackbar,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';

// Tipos de consumibles disponibles
const CONSUMABLE_TYPES = [
  'Todos',
  'Plancha',
  'Tinta',
  'Papel',
  'Embalaje',
  'Químico',
  'Otros'
];

// Tipos de unidades disponibles
const UNIT_TYPES = [
  'Unidad',
  'Kilogramo',
  '<PERSON><PERSON>',
  'Roll<PERSON>',
  'Paquete',
  'Metro',
  'Caja'
];

// Colores para los tipos de consumibles
const TYPE_COLORS = {
  'Plancha': 'primary',
  'Tinta': 'secondary',
  'Papel': 'success',
  'Embalaje': 'info',
  'Químico': 'warning',
  'Otros': 'default',
  'Todos': 'default'
};

/**
 * Componente para gestionar el catálogo de consumibles
 */
const ConsumablesCatalog = () => {
  const [consumables, setConsumables] = useState([]);
  const [filteredConsumables, setFilteredConsumables] = useState([]);
  const [selectedType, setSelectedType] = useState('Todos');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentConsumable, setCurrentConsumable] = useState({
    consumable_id: '',
    name: '',
    type: '',
    description: '',
    manufacturer: '',
    unit_cost: 0,
    unit_type: 'Unidad',
    stock: 0,
    min_stock: 0,
    properties: {}
  });
  const [isEditing, setIsEditing] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Cargar consumibles al montar el componente
  useEffect(() => {
    fetchConsumables();
  }, []);

  // Filtrar consumibles cuando cambia el tipo seleccionado
  useEffect(() => {
    filterConsumablesByType();
  }, [selectedType, consumables]);

  // Obtener consumibles de la API
  const fetchConsumables = async () => {
    try {
      const response = await fetch(buildApiUrl('/consumables'));
      if (!response.ok) throw new Error('Error al cargar los consumibles');

      const data = await response.json();
      setConsumables(data);
    } catch (err) {
      setSnackbar({ open: true, message: err.message, severity: 'error' });
    }
  };

  // Filtrar consumibles por tipo
  const filterConsumablesByType = () => {
    if (selectedType === 'Todos') {
      setFilteredConsumables(consumables);
    } else {
      setFilteredConsumables(consumables.filter(c => c.type === selectedType));
    }
  };

  // Manejar cambio de tipo
  const handleTypeChange = (event, newValue) => {
    setSelectedType(CONSUMABLE_TYPES[newValue]);
  };

  // Abrir diálogo para crear nuevo consumible
  const handleOpenCreateDialog = () => {
    setCurrentConsumable({
      consumable_id: '',
      name: '',
      type: CONSUMABLE_TYPES[1], // Por defecto el primer tipo real (no 'Todos')
      description: '',
      manufacturer: '',
      unit_cost: 0,
      unit_type: 'Unidad',
      stock: 0,
      min_stock: 0,
      properties: {}
    });
    setIsEditing(false);
    setDialogOpen(true);
  };

  // Abrir diálogo para editar consumible
  const handleOpenEditDialog = (consumable) => {
    setCurrentConsumable({
      ...consumable,
      properties: consumable.properties || {}
    });
    setIsEditing(true);
    setDialogOpen(true);
  };

  // Cerrar diálogo
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Manejar cambios en el formulario
  const handleFormChange = (e) => {
    const { name, value } = e.target;

    if (name === 'unit_cost' || name === 'stock' || name === 'min_stock') {
      setCurrentConsumable({
        ...currentConsumable,
        [name]: parseFloat(value) || 0
      });
    } else {
      setCurrentConsumable({
        ...currentConsumable,
        [name]: value
      });
    }
  };

  // Guardar consumible
  const handleSaveConsumable = async () => {
    try {
      const method = isEditing ? 'PUT' : 'POST';
      const url = isEditing
        ? buildApiUrl(`/consumables/${currentConsumable.consumable_id}`)
        : buildApiUrl('/consumables');

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(currentConsumable)
      });

      if (!response.ok) throw new Error(`Error al ${isEditing ? 'actualizar' : 'crear'} el consumible`);

      setSnackbar({
        open: true,
        message: `Consumible ${isEditing ? 'actualizado' : 'creado'} correctamente`,
        severity: 'success'
      });

      setDialogOpen(false);
      fetchConsumables();
    } catch (err) {
      setSnackbar({ open: true, message: err.message, severity: 'error' });
    }
  };

  // Eliminar consumible
  const handleDeleteConsumable = async (id) => {
    if (!window.confirm('¿Está seguro de que desea eliminar este consumible?')) return;

    try {
      const response = await fetch(buildApiUrl(`/consumables/${id}`), {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Error al eliminar el consumible');

      setSnackbar({ open: true, message: 'Consumible eliminado correctamente', severity: 'success' });
      fetchConsumables();
    } catch (err) {
      setSnackbar({ open: true, message: err.message, severity: 'error' });
    }
  };

  // Cerrar snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Catálogo de Consumibles
      </Typography>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Tabs
          value={CONSUMABLE_TYPES.indexOf(selectedType)}
          onChange={handleTypeChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          {CONSUMABLE_TYPES.map((type) => (
            <Tab key={type} label={type} />
          ))}
        </Tabs>

        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleOpenCreateDialog}
        >
          NUEVO CONSUMIBLE
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ backgroundColor: 'primary.main', color: 'white', fontWeight: 'bold' }}>Nombre</TableCell>
              <TableCell sx={{ backgroundColor: 'primary.main', color: 'white', fontWeight: 'bold' }}>Tipo</TableCell>
              <TableCell sx={{ backgroundColor: 'primary.main', color: 'white', fontWeight: 'bold' }}>Descripción</TableCell>
              <TableCell sx={{ backgroundColor: 'primary.main', color: 'white', fontWeight: 'bold' }}>Fabricante</TableCell>
              <TableCell sx={{ backgroundColor: 'primary.main', color: 'white', fontWeight: 'bold' }}>Costo por Unidad</TableCell>
              <TableCell sx={{ backgroundColor: 'primary.main', color: 'white', fontWeight: 'bold' }}>Tipo de Unidad</TableCell>
              <TableCell sx={{ backgroundColor: 'primary.main', color: 'white', fontWeight: 'bold' }}>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredConsumables.length > 0 ? (
              filteredConsumables.map((consumable) => (
                <TableRow key={consumable.consumable_id}>
                  <TableCell>{consumable.name}</TableCell>
                  <TableCell>
                    <Chip
                      label={consumable.type}
                      color={TYPE_COLORS[consumable.type] || 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{consumable.description}</TableCell>
                  <TableCell>{consumable.manufacturer}</TableCell>
                  <TableCell>€{consumable.unit_cost.toFixed(2)}</TableCell>
                  <TableCell>{consumable.unit_type}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleOpenEditDialog(consumable)}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteConsumable(consumable.consumable_id)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No hay consumibles disponibles en esta categoría
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Diálogo para crear/editar consumible */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {isEditing ? 'Editar Consumible' : 'Nuevo Consumible'}
          <IconButton
            aria-label="close"
            onClick={handleCloseDialog}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 2, p: 1 }}>
            <TextField
              name="name"
              label="Nombre"
              value={currentConsumable.name}
              onChange={handleFormChange}
              fullWidth
              required
              margin="normal"
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Tipo</InputLabel>
              <Select
                name="type"
                value={currentConsumable.type}
                onChange={handleFormChange}
                label="Tipo"
              >
                {CONSUMABLE_TYPES.slice(1).map((type) => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              name="description"
              label="Descripción"
              value={currentConsumable.description}
              onChange={handleFormChange}
              fullWidth
              multiline
              rows={2}
              margin="normal"
            />
            <TextField
              name="manufacturer"
              label="Fabricante"
              value={currentConsumable.manufacturer}
              onChange={handleFormChange}
              fullWidth
              margin="normal"
            />

            <TextField
              name="unit_cost"
              label="Costo por Unidad (€)"
              type="number"
              value={currentConsumable.unit_cost}
              onChange={handleFormChange}
              fullWidth
              margin="normal"
              inputProps={{ min: 0, step: 0.01 }}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Tipo de Unidad</InputLabel>
              <Select
                name="unit_type"
                value={currentConsumable.unit_type}
                onChange={handleFormChange}
                label="Tipo de Unidad"
              >
                {UNIT_TYPES.map((type) => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              name="stock"
              label="Stock Actual"
              type="number"
              value={currentConsumable.stock}
              onChange={handleFormChange}
              fullWidth
              margin="normal"
              inputProps={{ min: 0 }}
            />
            <TextField
              name="min_stock"
              label="Stock Mínimo"
              type="number"
              value={currentConsumable.min_stock}
              onChange={handleFormChange}
              fullWidth
              margin="normal"
              inputProps={{ min: 0 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="inherit">
            Cancelar
          </Button>
          <Button onClick={handleSaveConsumable} color="primary" variant="contained">
            Guardar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar para notificaciones */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default ConsumablesCatalog;
