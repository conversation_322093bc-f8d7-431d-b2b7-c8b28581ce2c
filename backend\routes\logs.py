"""
Rutas para gestionar los logs
"""
from fastapi import APIRouter, Body, HTTPException
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from utils.logger import log_frontend, LOG_DIR, LOG_FILE
import os
from pathlib import Path

router = APIRouter(
    prefix="/logs",
    tags=["logs"]
)

class LogEntry(BaseModel):
    """Modelo para una entrada de log del frontend"""
    action: str
    details: Optional[Dict[str, Any]] = None
    user: Optional[str] = None

@router.post("/frontend")
async def log_frontend_action(log_entry: LogEntry = Body(...)):
    """
    Registra una acción del frontend en el log

    Args:
        log_entry: Datos de la acción a registrar

    Returns:
        Confirmación de que la acción ha sido registrada
    """
    log_frontend(
        action=log_entry.action,
        details=log_entry.details,
        user=log_entry.user
    )

    return {"status": "success", "message": "Log entry recorded"}

@router.get("/files", response_model=List[str])
async def get_log_files():
    """
    Obtiene la lista de archivos de log disponibles

    Returns:
        Lista de nombres de archivos de log
    """
    try:
        # Verificar que el directorio de logs existe
        if not LOG_DIR.exists():
            return []

        # Obtener todos los archivos de log
        log_files = [f.name for f in LOG_DIR.iterdir() if f.is_file() and f.name.endswith('.log')]

        # Ordenar por fecha (más reciente primero)
        log_files.sort(reverse=True)

        return log_files
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error al obtener los archivos de log: {str(e)}"
        )

@router.get("/content/{filename}")
async def get_log_content(filename: str):
    """
    Obtiene el contenido de un archivo de log

    Args:
        filename: Nombre del archivo de log

    Returns:
        Contenido del archivo de log
    """
    try:
        # Verificar que el archivo existe
        file_path = LOG_DIR / filename
        if not file_path.exists():
            raise HTTPException(
                status_code=404,
                detail=f"Archivo de log {filename} no encontrado"
            )

        # Leer el contenido del archivo
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        return {"content": content}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error al leer el archivo de log: {str(e)}"
        )

@router.get("/current")
async def get_current_log():
    """
    Obtiene el contenido del archivo de log actual

    Returns:
        Contenido del archivo de log actual
    """
    try:
        # Verificar que el archivo existe
        if not LOG_FILE.exists():
            return {"content": "No hay archivo de log para hoy"}

        # Leer el contenido del archivo
        with open(LOG_FILE, "r", encoding="utf-8") as f:
            content = f.read()

        return {"content": content}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error al leer el archivo de log actual: {str(e)}"
        )
