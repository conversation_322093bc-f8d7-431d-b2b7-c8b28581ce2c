// Servicios simplificados para LogViewer
import { buildApiUrl } from '../config';

// Servicio de log simplificado
export const LogService = {
  logNavigation: (route, params = null) => {
    console.log(`[LOG] Navigation to ${route}`, params || {});
    return true;
  },
  
  logUserAction: (action, data = null) => {
    console.log(`[LOG] User action: ${action}`, data || {});
    return true;
  },
  
  logError: (message, errorData = null) => {
    console.error(`[LOG] Error: ${message}`, errorData || {});
    return true;
  },
  
  logApiOperation: (endpoint, method, data = null, status = null) => {
    console.log(`[LOG] API ${method} to ${endpoint}`, { data, status });
    return true;
  }
};

// Interceptor de API simplificado
export const ApiInterceptor = {
  fetch: async (url, options = {}) => {
    const method = options.method || 'GET';
    
    try {
      const response = await fetch(url, options);
      
      // Log simplificado
      console.log(`[API] ${method} ${url}`, { status: response.status });
      
      return response;
    } catch (error) {
      console.error(`[API] Error in ${method} ${url}`, error);
      throw error;
    }
  }
};
