import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Typography,
  TextField,
  Box,
  Chip,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Paper,
  Collapse
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CloseIcon from '@mui/icons-material/Close';
import CalculateIcon from '@mui/icons-material/Calculate';
import BuildIcon from '@mui/icons-material/Build';
import SummarizeIcon from '@mui/icons-material/Summarize';
import { buildApiUrl } from '../config';
import calculationService from '../services/calculationService';

const FinishingSelector = ({
  selectedProcesses,
  setSelectedProcesses,
  calculatedProcessCost,
  setCalculatedProcessCost,
  budget
}) => {
  const [processes, setProcesses] = useState([]);
  const [expanded, setExpanded] = useState(false);

  // Calcular el coste total de los procesos seleccionados usando la API
  // Esta función solo se llama explícitamente cuando es necesario, no automáticamente
  const calculateProcessCostAPI = useCallback(async () => {
    if (selectedProcesses.length === 0) {
      setCalculatedProcessCost(0);
      return 0;
    }

    // Calcular manualmente primero como respaldo
    const manualCalculation = selectedProcesses.reduce((total, process) => {
      // Determinar la cantidad según el tipo de unidad
      let quantity;
      if (process.unit_type && process.unit_type === 'Hora') {
        // Para procesos por hora, usar la cantidad específica (horas)
        quantity = process.quantity || 1; // Por defecto 1 hora si no se especifica
      } else {
        // Para procesos por unidad, ejemplar, etc., usar el número de ejemplares
        quantity = process.quantity || (budget && parseInt(budget.copies)) || 1;
      }

      const cost = process.unit_cost * quantity;
      return total + cost;
    }, 0);

    console.log('Calculando costo de procesos usando la API (solo cuando es explícitamente necesario)');

    try {
      // Preparar los procesos para el cálculo en el backend
      const processPromises = selectedProcesses.map(process => {
        // Determinar la cantidad según el tipo de unidad
        let quantity;
        if (process.unit_type && process.unit_type === 'Hora') {
          // Para procesos por hora, usar la cantidad específica (horas)
          quantity = process.quantity || 1; // Por defecto 1 hora si no se especifica
        } else {
          // Para procesos por unidad, ejemplar, etc., usar el número de ejemplares
          quantity = process.quantity || (budget && parseInt(budget.copies)) || 1;
        }

        // Llamar al servicio de cálculo para cada proceso
        return calculationService.calculateProcessCost(
          process.process_id,
          quantity,
          process.unit_type === 'Hora' ? 'hour' : 'unit'
        );
      });

      // Esperar a que se completen todos los cálculos
      const results = await Promise.all(processPromises);

      // Sumar los costes totales
      const processCost = results.reduce((total, result) => total + result.total_cost, 0);

      // Si el backend devuelve 0 pero tenemos procesos, usar el cálculo manual
      if (processCost === 0 && manualCalculation > 0) {
        setCalculatedProcessCost(manualCalculation);
        return manualCalculation;
      }

      // Actualizar el estado del coste de acabados
      setCalculatedProcessCost(processCost);
      return processCost;
    } catch (error) {
      console.error('Error al calcular costes de procesos en el backend:', error);
      setCalculatedProcessCost(manualCalculation);
      return manualCalculation;
    }
  }, [selectedProcesses, budget, budget?.copies, setCalculatedProcessCost]);

  // Cargar los procesos (acabados) disponibles
  useEffect(() => {
    const fetchProcesses = async () => {
      try {
        const apiUrl = buildApiUrl('/processes/');
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error('Error al obtener los procesos');
        const data = await response.json();

        // Filtrar solo procesos de acabado, corte, plegado, encuadernación, etc.
        const filteredProcesses = data.filter(process =>
          process.type && ['Corte', 'Plegado', 'Encuadernación', 'Acabado', 'Barnizado', 'Laminado', 'Troquelado'].includes(process.type)
        );

        setProcesses(filteredProcesses);
      } catch (error) {
        console.error('Error al cargar los procesos:', error);
      }
    };

    fetchProcesses();
  }, []);

  // Efecto para recalcular el coste cuando cambian los procesos seleccionados
  // Usamos un enfoque manual para evitar llamadas innecesarias a la API
  useEffect(() => {
    // Calcular el costo total manualmente
    const calculateManualCost = () => {
      return selectedProcesses.reduce((total, process) => {
        const quantity = process.quantity || 1;
        const unitCost = process.unit_cost || 0;
        return total + (unitCost * quantity);
      }, 0);
    };

    // Si no hay procesos seleccionados, establecer el costo a 0
    if (selectedProcesses.length === 0) {
      if (calculatedProcessCost !== 0) {
        console.log('No hay procesos seleccionados, estableciendo costo a 0');
        setCalculatedProcessCost(0);
      }
      return;
    }

    // Calcular el costo manualmente
    const manualTotal = calculateManualCost();

    // Actualizar el costo calculado si es diferente al actual
    if (Math.abs(manualTotal - calculatedProcessCost) > 0.01) { // Usar una pequeña tolerancia para evitar problemas de redondeo
      console.log(`Actualizando costo de procesos: ${calculatedProcessCost} -> ${manualTotal}`);
      setCalculatedProcessCost(manualTotal);
    }
  }, [selectedProcesses, calculatedProcessCost, setCalculatedProcessCost]);

  // Referencia para almacenar el valor anterior de budget.copies
  const [prevCopies, setPrevCopies] = useState(budget?.copies || '');

  // Efecto para actualizar las cantidades de los procesos cuando cambia el número de ejemplares
  // Solo se ejecuta cuando el usuario termina de editar el campo, no en cada cambio
  useEffect(() => {
    // Verificar si realmente hubo un cambio en el número de ejemplares
    if (budget?.copies !== prevCopies) {
      console.log(`Número de ejemplares cambiado de ${prevCopies} a ${budget?.copies}`);
      setPrevCopies(budget?.copies || '');

      // Solo actualizar si hay procesos seleccionados y un número válido de ejemplares
      if (selectedProcesses.length > 0 && budget && budget.copies) {
        // Actualizar las cantidades de los procesos seleccionados
        const updatedProcesses = selectedProcesses.map(process => {
          // Solo actualizar si la cantidad no ha sido modificada manualmente
          // y si el tipo de unidad no es 'Hora' (los procesos por hora no dependen del número de ejemplares)
          if (!process.quantityModified && (!process.unit_type || process.unit_type !== 'Hora')) {
            return { ...process, quantity: parseInt(budget.copies) || 1 };
          }
          return process;
        });

        // Verificar si realmente hay cambios antes de actualizar el estado
        const hasChanges = updatedProcesses.some((process, index) =>
          process.quantity !== selectedProcesses[index].quantity
        );

        if (hasChanges) {
          console.log('Actualizando cantidades de procesos debido al cambio en el número de ejemplares');
          setSelectedProcesses(updatedProcesses);

          // Calcular el costo manualmente sin llamar a la API
          const manualTotal = updatedProcesses.reduce((total, process) => {
            const quantity = process.quantity || 1;
            const unitCost = process.unit_cost || 0;
            return total + (unitCost * quantity);
          }, 0);

          // Actualizar el costo calculado directamente
          setCalculatedProcessCost(manualTotal);
        }
      }
    }
  }, [budget?.copies, selectedProcesses, setSelectedProcesses, setCalculatedProcessCost, prevCopies]);

  // Manejar la selección de un proceso (acabado)
  const handleProcessSelect = (process) => {
    // Verificar si el proceso ya está seleccionado
    if (selectedProcesses.some(p => p.process_id === process.process_id)) {
      return; // Si ya está seleccionado, no hacer nada
    }

    console.log('Seleccionando proceso:', process);

    // Determinar la cantidad predeterminada según el tipo de unidad
    let defaultQuantity;
    if (process.unit_type && process.unit_type === 'Hora') {
      defaultQuantity = 1; // Por defecto 1 hora para procesos medidos en horas
    } else {
      defaultQuantity = (budget && parseInt(budget.copies)) || 1; // Número de ejemplares para otros tipos
    }

    // Crear el proceso con la cantidad por defecto
    const newProcess = { ...process, quantity: defaultQuantity };

    // Añadir el proceso a los seleccionados
    const updatedProcesses = [...selectedProcesses, newProcess];
    setSelectedProcesses(updatedProcesses);

    // Calcular el costo manualmente sin llamar a la API
    const manualTotal = updatedProcesses.reduce((total, p) => {
      const qty = p.quantity || 1;
      const unitCost = p.unit_cost || 0;
      return total + (unitCost * qty);
    }, 0);

    // Actualizar el costo calculado directamente
    setCalculatedProcessCost(manualTotal);
  };

  // Manejar la eliminación de un proceso seleccionado
  const handleProcessRemove = (processId) => {
    console.log('Eliminando proceso:', processId);

    // Filtrar el proceso eliminado
    const updatedProcesses = selectedProcesses.filter(p => p.process_id !== processId);
    setSelectedProcesses(updatedProcesses);

    // Calcular el costo manualmente sin llamar a la API
    const manualTotal = updatedProcesses.reduce((total, process) => {
      const qty = process.quantity || 1;
      const unitCost = process.unit_cost || 0;
      return total + (unitCost * qty);
    }, 0);

    // Actualizar el costo calculado directamente
    setCalculatedProcessCost(manualTotal);
  };

  // Manejar el cambio de cantidad de un proceso
  const handleProcessQuantityChange = (processId, value) => {
    const quantity = parseInt(value) || 1;
    console.log(`Cambiando cantidad del proceso ${processId} a ${quantity}`);

    // Actualizar los procesos seleccionados
    const updatedProcesses = selectedProcesses.map(p => {
      if (p.process_id === processId) {
        return { ...p, quantity, quantityModified: true };
      }
      return p;
    });

    // Actualizar el estado
    setSelectedProcesses(updatedProcesses);

    // Calcular el costo manualmente sin llamar a la API
    const manualTotal = updatedProcesses.reduce((total, process) => {
      const qty = process.quantity || 1;
      const unitCost = process.unit_cost || 0;
      return total + (unitCost * qty);
    }, 0);

    // Actualizar el costo calculado directamente
    setCalculatedProcessCost(manualTotal);
  };

  // Agrupar procesos por tipo
  const processTypes = [...new Set(processes.map(p => p.type))];

  return (
    <Box>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          p: 1,
          cursor: 'pointer',
          borderBottom: expanded ? '1px solid #e0e0e0' : 'none'
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <IconButton
          size="small"
          sx={{ mr: 1 }}
          onClick={(e) => {
            e.stopPropagation();
            setExpanded(!expanded);
          }}
        >
          {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
        <Box sx={{ display: 'flex', flexDirection: 'column', ml: 1, flexGrow: 1 }}>
          <Typography variant="h6" component="div">
            Acabados ({selectedProcesses.length} seleccionados - {calculatedProcessCost.toFixed(2)} €)
          </Typography>
          {!expanded && selectedProcesses.length > 0 && (
            <Box sx={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: 1, 
              mt: 1.5,
              p: 1,
              bgcolor: '#f8f9fa',
              borderRadius: 1,
              border: '1px solid #e0e0e0'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                <CalculateIcon sx={{ mr: 0.5, fontSize: '1.2rem', color: '#555' }} />
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  Costos Calculados:
                </Typography>
              </Box>
              
              {selectedProcesses.map((process) => (
                <Box 
                  key={process.process_id}
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    px: 1,
                    borderLeft: '3px solid #9c27b0',
                    mr: 1,
                    borderRadius: '0 4px 4px 0'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BuildIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#9c27b0' }} />
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      {process.name}: 
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#9c27b0' }}>
                    {(process.unit_cost * (process.quantity || 1)).toFixed(2)} €
                  </Typography>
                </Box>
              ))}
              
              {/* Costo Total */}
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center',
                px: 1,
                ml: 'auto',
                borderLeft: '3px solid #2e7d32',
                bgcolor: '#f1f8e9',
                borderRadius: '0 4px 4px 0'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SummarizeIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#2e7d32' }} />
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    Total: 
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#2e7d32' }}>
                  {calculatedProcessCost.toFixed(2)} €
                </Typography>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
      <Collapse in={expanded}>
        <Box sx={{ p: 2 }}>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Selecciona los acabados necesarios para este trabajo
          </Typography>

        {/* Chips para seleccionar procesos */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 3 }}>
          {processes.map((process) => (
            <Chip
              key={process.process_id}
              label={`${process.name} (${process.unit_cost ? process.unit_cost.toFixed(2) : '0.00'} €/${process.unit_type || 'Unidad'})`}
              onClick={() => handleProcessSelect(process)}
              onDelete={selectedProcesses.some(p => p.process_id === process.process_id) ?
                () => handleProcessRemove(process.process_id) : undefined}
              color={selectedProcesses.some(p => p.process_id === process.process_id) ? "primary" : "default"}
              variant={selectedProcesses.some(p => p.process_id === process.process_id) ? "filled" : "outlined"}
              sx={{ m: 0.5 }}
            />
          ))}
        </Box>

        {/* Tabla de procesos seleccionados */}
        {selectedProcesses.length > 0 && (
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Acabado</TableCell>
                <TableCell>Tipo</TableCell>
                <TableCell align="right">Coste unitario</TableCell>
                <TableCell align="right">Cantidad</TableCell>
                <TableCell align="right">Coste total</TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {selectedProcesses.map((process) => (
                <TableRow key={process.process_id}>
                  <TableCell>{process.name}</TableCell>
                  <TableCell>{process.type}</TableCell>
                  <TableCell align="right">{process.unit_cost.toFixed(2)} €/{process.unit_type || 'Unidad'}</TableCell>
                  <TableCell align="right" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                      <TextField
                        type="number"
                        size="small"
                        value={process.quantity || ((process.unit_type && process.unit_type === 'Hora') ? 1 : (budget && budget.copies) || 1)}
                        onChange={(e) => handleProcessQuantityChange(process.process_id, e.target.value)}
                        inputProps={{ min: 1, step: 1 }}
                        sx={{ width: '80px' }}
                      />
                      <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                        {process.unit_type === 'Hora' ? 'horas' : process.unit_type === 'Unidad' ? 'unidades' : (process.unit_type ? process.unit_type.toLowerCase() : 'unidades')}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    {(process.unit_cost * (process.quantity || ((process.unit_type && process.unit_type === 'Hora') ? 1 : (budget && budget.copies) || 1))).toFixed(2)} €
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" onClick={() => handleProcessRemove(process.process_id)}>
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              <TableRow>
                <TableCell colSpan={4} align="right" sx={{ fontWeight: 'bold' }}>
                  Coste total de acabados:
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                  {calculatedProcessCost.toFixed(2)} €
                </TableCell>
                <TableCell></TableCell>
              </TableRow>
            </TableBody>
          </Table>
        )}
        </Box>
      </Collapse>
    </Box>
  );
};

FinishingSelector.propTypes = {
  selectedProcesses: PropTypes.array.isRequired,
  setSelectedProcesses: PropTypes.func.isRequired,
  calculatedProcessCost: PropTypes.number.isRequired,
  setCalculatedProcessCost: PropTypes.func.isRequired,
  budget: PropTypes.object.isRequired
};

export default FinishingSelector;
