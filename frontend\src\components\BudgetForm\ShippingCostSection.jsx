import React from 'react';
import PropTypes from 'prop-types';
import { Grid, Paper, Typography } from '@mui/material';

/**
 * Componente para mostrar el coste de envío y el peso total del papel
 */
const ShippingCostSection = ({ shippingCost, totalPaperWeightKg }) => {
  return (
    <Grid item xs={12}>
      <Paper elevation={2} sx={{ p: 2, mt: 2 }}>
        <Typography variant="h6" gutterBottom>
          Envío
        </Typography>
        <Typography variant="body1">
          <strong>Peso total del papel:</strong> {totalPaperWeightKg ? `${totalPaperWeightKg.toFixed(2)} kg` : 'No calculado'}
        </Typography>
        <Typography variant="body1">
          <strong>Coste de envío:</strong> {shippingCost !== undefined && shippingCost !== null ? `${shippingCost.toFixed(2)} €` : 'No calculado'}
        </Typography>
      </Paper>
    </Grid>
  );
};

ShippingCostSection.propTypes = {
  shippingCost: PropTypes.number,
  totalPaperWeightKg: PropTypes.number,
};

export default ShippingCostSection;
