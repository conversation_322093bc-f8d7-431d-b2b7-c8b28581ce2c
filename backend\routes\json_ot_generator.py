from fastapi import APIRouter, HTTPException, status
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
import json
import os
from datetime import datetime
from jinja2 import Environment, FileSystemLoader, select_autoescape
from routes.budgets import load_budgets
from routes.papers import get_paper_by_id
from routes.machines import get_machine_by_id
from routes.clients import get_client_by_id
from routes.products import get_product_by_id
from utils.logger import log_info, log_error

router = APIRouter(
    prefix="/json-ot",
    tags=["json_ot_generator"],
    responses={404: {"description": "Recurso no encontrado"}}
)

# Configuración de Jinja2
templates_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates")
jinja_env = Environment(
    loader=FileSystemLoader(templates_dir),
    autoescape=select_autoescape(['html', 'xml']),
    trim_blocks=True,
    lstrip_blocks=True
)

# Definir modelos Pydantic para la respuesta JSON_OT según la estructura requerida
class StripCellParams(BaseModel):
    spine: float = 0.0
    trim_face: float = 3.0
    trim_foot: float = 3.0
    trim_head: float = 3.0
    trim_size_width: float = 210.0
    trim_size_height: float = 297.0
    bleed_face: float = 3.0
    bleed_foot: float = 3.0
    bleed_head: float = 3.0
    bleed_spine: float = 3.0
    back_overfold: float = 0.0
    milling_depth: float = 0.0
    front_overfold: float = 0.0
    creep_x: float = 0.0
    creep_y: float = 0.0
    margin_top: float = 0.0
    margin_left: float = 0.0
    margin_right: float = 0.0
    margin_bottom: float = 0.0
    orientation: str = "Rotate0"
    relative_box_x1: float = 0.0
    relative_box_y1: float = 0.0
    relative_box_x2: float = 1.0
    relative_box_y2: float = 1.0

class SheetConfig(BaseModel):
    sheet_name: str
    bindery_signature_name: str
    paper_ref: str
    fold_catalog: str
    work_style: str
    strip_cell_params: Optional[StripCellParams] = None

class StrippingParams(BaseModel):
    signature_name: str
    sheets: List[SheetConfig]

class Signature(BaseModel):
    signature_ID: str
    job_part_id_name: str
    press_name: str
    assembly_order: Optional[str] = "Gathering"
    stripping_params: StrippingParams

class PaperConfig(BaseModel):
    weight: float
    dimension_width: float
    dimension_height: float
    media_type: str
    product_id: str
    thickness: float
    descriptive_name: str

class CustomerInfo(BaseModel):
    customer_id: Optional[str] = None
    billing_code: Optional[str] = None
    order_id: Optional[str] = None
    company_name: Optional[str] = None
    country: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    street: Optional[str] = None
    postal_code: Optional[str] = None
    job_title: Optional[str] = None
    first_name: Optional[str] = None
    family_name: Optional[str] = None
    phone: Optional[str] = None
    fax: Optional[str] = None
    email: Optional[str] = None

class RunList(BaseModel):
    runlist_id: str
    pages: int
    page_range: str
    signature_ref: str
    pdf_url: str

class StripCellParams(BaseModel):
    spine: Optional[float] = 0.0
    trim_face: Optional[float] = 3.0
    trim_foot: Optional[float] = 3.0
    trim_head: Optional[float] = 3.0
    trim_size_width: Optional[float] = 210.0
    trim_size_height: Optional[float] = 297.0
    bleed_face: Optional[float] = 3.0
    bleed_foot: Optional[float] = 3.0
    bleed_head: Optional[float] = 3.0
    bleed_spine: Optional[float] = 3.0
    back_overfold: Optional[float] = 0.0
    milling_depth: Optional[float] = 0.0
    front_overfold: Optional[float] = 0.0
    creep_x: Optional[float] = 0.0
    creep_y: Optional[float] = 0.0
    margin_top: Optional[float] = 0.0
    margin_left: Optional[float] = 0.0
    margin_right: Optional[float] = 0.0
    margin_bottom: Optional[float] = 0.0
    orientation: Optional[str] = "Rotate0"
    relative_box_x1: Optional[float] = 0.0
    relative_box_y1: Optional[float] = 0.0
    relative_box_x2: Optional[float] = 1.0
    relative_box_y2: Optional[float] = 1.0

class SheetConfig(BaseModel):
    sheet_name: str
    bindery_signature_name: str
    paper_ref: str
    fold_catalog: str
    work_style: str
    strip_cell_params: Optional[StripCellParams] = None

class StrippingParams(BaseModel):
    signature_name: str
    sheets: list[SheetConfig]

class ColorDetails(BaseModel):
    front: Optional[list[str]] = None
    back: Optional[list[str]] = None

class Signature(BaseModel):
    signature_ID: str
    job_part_id_name: str
    press_name: str
    assembly_order: Optional[str] = None
    color_config: str
    color_details: Optional[ColorDetails] = None
    stripping_params: StrippingParams

class RunList(BaseModel):
    runlist_id: str
    pages: int
    page_range: str
    signature_ref: str
    pdf_url: str

class PaperConfig(BaseModel):
    weight: float
    dimension_width: float
    dimension_height: float
    media_type: str
    product_id: str
    thickness: float
    descriptive_name: str

class CustomerInfo(BaseModel):
    customer_id: Optional[str] = None
    billing_code: Optional[str] = None
    order_id: Optional[str] = None
    company_name: Optional[str] = None
    country: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    street: Optional[str] = None
    postal_code: Optional[str] = None
    job_title: Optional[str] = None
    first_name: Optional[str] = None
    family_name: Optional[str] = None
    phone: Optional[str] = None
    fax: Optional[str] = None
    email: Optional[str] = None

class JSONOTModel(BaseModel):
    job_id: str
    descriptive_name: str
    author: Optional[str] = "JDFast"
    agent_name: Optional[str] = "JDFast"
    agent_version: Optional[str] = "3.1"
    comment: Optional[str] = None
    product_type: Optional[str] = None
    binding_side: Optional[str] = None
    signatures: List[Signature]
    runlists: Optional[List[RunList]] = None
    paper_configs: Optional[List[PaperConfig]] = None
    customer_info: Optional[CustomerInfo] = None

@router.get("/generate/{budget_id}", response_model=Dict[str, Any])
async def generate_json_ot(budget_id: str):
    """
    Genera un JSON_OT a partir de un presupuesto existente utilizando una plantilla Jinja2.
    Este JSON sigue el esquema definido para la generación de JDF.

    Args:
        budget_id: ID del presupuesto

    Returns:
        dict: JSON_OT con los datos de producción
    """
    try:
        # Cargar el presupuesto
        budgets = load_budgets()
        
        # Si no hay presupuestos, cargar directamente del archivo
        if not budgets:
            data_file = os.path.join(os.path.dirname(__file__), "..", "data", "budgets.json")
            if os.path.exists(data_file):
                with open(data_file, "r", encoding="utf-8") as f:
                    budgets = json.load(f)
        
        # Buscar el presupuesto por ID
        budget_data = None
        for budget in budgets:
            if budget["budget_id"] == budget_id:
                budget_data = budget
                break

        if not budget_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Presupuesto con ID {budget_id} no encontrado"
            )
            
        # Cargar la plantilla Jinja2
        template = jinja_env.get_template("json_ot_template.j2")
        
        # Preparar datos para la plantilla
        # Crear un conjunto para rastrear los paper_ids ya procesados
        previous_paper_ids = set()
        
        # Renderizar la plantilla con los datos del presupuesto
        json_ot_str = template.render(
            budget=budget_data,
            timestamp=datetime.now().isoformat(),
            previous_paper_ids=previous_paper_ids
        )
        
        # Convertir el string JSON a un diccionario Python
        json_ot = json.loads(json_ot_str)
        
        # Registrar la generación del JSON_OT en el log
        log_info(f"JSON_OT generado para el presupuesto {budget_id} utilizando plantilla Jinja2")
        
        return json_ot
        
    except Exception as e:
        log_info(f"Error al generar JSON_OT: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al generar JSON_OT: {str(e)}"
        )


    # Obtener datos del cliente
    client_id = budget_data.get("client_id")
    client_data = budget_data.get("client_data")

    # Intentar obtener datos del cliente, pero no fallar si no existe
    try:
        if not client_data and client_id:
            client_data = get_client_by_id(client_id)
    except Exception as e:
        print(f"Advertencia: No se pudo obtener información del cliente {client_id}: {e}")
        # Usar datos mínimos del cliente si no se encuentran
        client_data = {"client_id": client_id}

    # Preparar datos del cliente para el JSON_OT (opcional)
    customer_info = None
    if client_data:
        customer_info = {
            "customer_id": client_data.get("client_id", ""),
            "billing_code": client_data.get("billing_code", ""),
            "order_id": client_data.get("order_id", ""),
            "company_name": client_data.get("company", {}).get("name", ""),
            "country": client_data.get("company", {}).get("address", {}).get("country", ""),
            "region": client_data.get("company", {}).get("address", {}).get("region", ""),
            "city": client_data.get("company", {}).get("address", {}).get("city", ""),
            "street": client_data.get("company", {}).get("address", {}).get("street", ""),
            "postal_code": client_data.get("company", {}).get("address", {}).get("postal_code", ""),
            "job_title": client_data.get("contact", {}).get("position", ""),
            "first_name": client_data.get("contact", {}).get("first_name", ""),
            "family_name": client_data.get("contact", {}).get("last_name", ""),
            "phone": client_data.get("contact", {}).get("phone", ""),
            "fax": client_data.get("contact", {}).get("fax", ""),
            "email": client_data.get("contact", {}).get("email", "")
        }

    # Preparar configuraciones de papel (obligatorio para la estructura mínima)
    paper_configs = []
    paper_ids = set()

    # Recopilar todos los IDs de papel de las partes del presupuesto
    if budget_data.get("parts"):
        for part in budget_data["parts"]:
            if part.get("paper_id") and part.get("paper_id") not in paper_ids:
                paper_ids.add(part.get("paper_id"))

                # Obtener datos del papel
                paper_data = part.get("paper_data")
                
                # Intentar obtener datos del papel, pero no fallar si no existe
                try:
                    if not paper_data and part.get("paper_id"):
                        paper_data = get_paper_by_id(part.get("paper_id"))
                except Exception as e:
                    print(f"Advertencia: No se pudo obtener información del papel {part.get('paper_id')}: {e}")
                    # Crear datos mínimos del papel si no se encuentran
                    paper_data = {
                        "weight": 80.0,
                        "dimension_width": 1000.0,
                        "dimension_height": 700.0,
                        "media_type": "Paper",
                        "product_id": part.get("paper_id", ""),
                        "thickness": 100.0,
                        "descriptive_name": f"Papel {part.get('paper_id', 'Desconocido')}"
                    }
                
                # Siempre añadir configuración de papel, incluso con datos mínimos
                paper_configs.append({
                        "weight": paper_data.get("weight", 0.0),
                        "dimension_width": paper_data.get("dimension_width", 0.0),
                        "dimension_height": paper_data.get("dimension_height", 0.0),
                        "media_type": paper_data.get("media_type", "Paper"),
                        "product_id": paper_data.get("product_id", ""),
                        "thickness": paper_data.get("thickness", 0.0),
                        "descriptive_name": paper_data.get("descriptive_name", "")
                    })

    # Preparar signaturas (obligatorio)
    signatures = []

    if budget_data.get("parts"):
        for i, part in enumerate(budget_data["parts"]):
            # Obtener datos de la máquina
            machine_data = part.get("machine_data")
            
            # Intentar obtener datos de la máquina, pero no fallar si no existe
            try:
                if not machine_data and part.get("machine_id"):
                    machine_data = get_machine_by_id(part.get("machine_id"))
            except Exception as e:
                print(f"Advertencia: No se pudo obtener información de la máquina {part.get('machine_id')}: {e}")
                # Crear datos mínimos de la máquina si no se encuentran
                machine_data = {
                    "name": f"Máquina {part.get('machine_id', 'Desconocida')}",
                    "type": "Offset",
                    "manufacturer": "Desconocido",
                    "model": "Desconocido"
                }
            
            press_name = machine_data.get("name", "") if machine_data else f"Máquina {part.get('machine_id', 'Desconocida')}"

            # Obtener el tipo de encuadernación (assembly_order) del producto
            assembly_order = "Gathering"  # Valor por defecto
            product_type = budget_data.get("job_type", "")
            product_data = None
            
            # Intentar obtener datos del producto, pero no fallar si no existe
            try:
                if product_type:
                    product_data = get_product_by_id(product_type)
            except Exception as e:
                # Log del error pero continuar con valores por defecto
                print(f"Advertencia: No se pudo obtener información del producto {product_type}: {e}")
                product_data = None
            
            # Usar assembly_order del producto si existe
            if product_data and "assembly_order" in product_data:
                assembly_order = product_data["assembly_order"]
                log_info(f"Usando assembly_order '{assembly_order}' del producto {product_type}")

            # Esta sección se ha movido a la lógica de cada esquema individual

            # Crear configuración de hojas
            sheets = []

            # Forzar la creación de 2 pliegos para la revista con 32 páginas
            if "Revista" in budget_data.get("job_type", "") and part.get("page_count") == 32:
                # Limpiar la lista de hojas
                sheets = []

                # Obtener información de los esquemas utilizados
                esquemas_utilizados = []
                if part.get("sheet_calculation") and part["sheet_calculation"].get("mejor_combinacion"):
                    esquemas_utilizados = part["sheet_calculation"]["mejor_combinacion"].get("esquemas_utilizados", [])

                # Si no hay esquemas, crear uno por defecto
                if not esquemas_utilizados:
                    esquemas_utilizados = [{
                        "nombre": "F16-7",
                        "numero_pliegos": 2,
                        "paginas_por_pliego": 16,
                        "disposicion": {
                            "paginas_ancho": 4,
                            "paginas_alto": 2,
                            "orientacion": "vertical_normal"
                        },
                        "es_tira_retira": False
                    }]

                # Obtener la configuración de colores
                color_config = part.get("color_config", {})
                front_colors = color_config.get("frontColors", 0)
                back_colors = color_config.get("backColors", 0)

                # Crear una hoja por cada pliego en cada esquema
                sheet_index = 1
                for esquema in esquemas_utilizados:
                    fold_catalog = esquema.get("nombre", "F16-7")
                    num_pliegos = esquema.get("numero_pliegos", 1)
                    es_tira_retira = esquema.get("es_tira_retira", False)

                    # Determinar el estilo de trabajo
                    work_style = "Flat"
                    if back_colors > 0:
                        # Si tiene impresión en ambas caras (4/4, 4/1, etc.)
                        if es_tira_retira:
                            # Si es tira/retira
                            work_style = "WorkAndTurn"
                        else:
                            # Si no es tira/retira pero tiene dos caras (4/4, 4/1, 1/1, 5/5), usar "WorkAndBack"
                            work_style = "WorkAndBack"

                    # Crear una hoja por cada pliego
                    for pliego in range(1, num_pliegos + 1):
                        sheet_name = f"Sheet-{i+1}-{sheet_index}"
                        bindery_signature_name = f"BS-{i+1}-{sheet_index}"
                        sheet_index += 1

                        # Crear configuración de hoja
                        sheet_config = {
                            "sheet_name": sheet_name,
                            "bindery_signature_name": bindery_signature_name,
                            "paper_ref": part.get("paper_id", ""),
                            "fold_catalog": fold_catalog,
                            "work_style": work_style
                        }

                    # Añadir parámetros de StripCell (opcional)
                    strip_cell_params = {
                        "spine": 0.0,
                        "trim_face": 3.0,
                        "trim_foot": 3.0,
                        "trim_head": 3.0,
                        "trim_size_width": part.get("page_size", {}).get("width", 210.0),
                        "trim_size_height": part.get("page_size", {}).get("height", 297.0),
                        "bleed_face": 3.0,
                        "bleed_foot": 3.0,
                        "bleed_head": 3.0,
                        "bleed_spine": 3.0,
                        "back_overfold": 0.0,
                        "milling_depth": 0.0,
                        "front_overfold": 0.0,
                        "creep_x": 0.0,
                        "creep_y": 0.0,
                        "margin_top": 0.0,
                        "margin_left": 0.0,
                        "margin_right": 0.0,
                        "margin_bottom": 0.0,
                        "orientation": "Rotate0",
                        "relative_box_x1": 0.0,
                        "relative_box_y1": 0.0,
                        "relative_box_x2": 1.0,
                        "relative_box_y2": 1.0
                    }
                    sheet_config["strip_cell_params"] = strip_cell_params

                    sheets.append(sheet_config)

                log_info(f"Creados 2 pliegos F16-7 para la revista de 32 páginas")

                # Saltar el resto del procesamiento
                continue
            else:
                # Obtener información de los esquemas utilizados
                esquemas_utilizados = []
                if part.get("sheet_calculation") and part["sheet_calculation"].get("mejor_combinacion"):
                    esquemas_utilizados = part["sheet_calculation"]["mejor_combinacion"].get("esquemas_utilizados", [])

                # Si no hay esquemas, crear uno por defecto
                if not esquemas_utilizados:
                    esquemas_utilizados = [{
                        "nombre": "F16-1",
                        "numero_pliegos": 1,
                        "paginas_por_pliego": part.get("page_count", 0),
                        "disposicion": {
                            "paginas_ancho": 4,
                            "paginas_alto": 2,
                            "orientacion": "vertical_normal"
                        },
                        "es_tira_retira": False
                    }]

                # Crear una hoja por cada pliego en cada esquema
                sheet_index = 1
                for esquema in esquemas_utilizados:
                    fold_catalog = esquema.get("nombre", "F16-1")
                    num_pliegos = esquema.get("numero_pliegos", 1)
                    es_tira_retira = esquema.get("es_tira_retira", False)

                    # Determinar el estilo de trabajo basado en el esquema y la configuración de colores
                    esquema_work_style = "Flat"  # Valor por defecto

                    # Obtener la configuración de colores
                    color_config = part.get("color_config", {})
                    front_colors = color_config.get("frontColors", 0)
                    back_colors = color_config.get("backColors", 0)

                    # Determinar si es impresión a una o dos caras
                    is_one_sided = back_colors == 0

                    if is_one_sided:
                        # Si solo tiene una cara (4/0, 1/0), usar "Flat"
                        esquema_work_style = "Flat"
                    else:
                        # Si tiene dos caras
                        if es_tira_retira:
                            # Si es tira/retira
                            esquema_work_style = "WorkAndTurn"
                        else:
                            # Si no es tira/retira pero tiene dos caras (4/4, 4/1, 1/1, 5/5), usar "WorkAndBack"
                            esquema_work_style = "WorkAndBack"

                    # Crear una hoja por cada pliego
                    for pliego in range(1, num_pliegos + 1):
                        sheet_name = f"Sheet-{i+1}-{pliego}"
                        bindery_signature_name = f"BS-{i+1}-{pliego}"

                        # Crear configuración de hoja
                        sheet_config = {
                            "sheet_name": sheet_name,
                            "bindery_signature_name": bindery_signature_name,
                            "paper_ref": part.get("paper_id", ""),
                            "fold_catalog": fold_catalog,
                            "work_style": esquema_work_style
                        }

                        # Añadir parámetros de StripCell (opcional)
                        strip_cell_params = {
                            "spine": 0.0,
                            "trim_face": 3.0,
                            "trim_foot": 3.0,
                            "trim_head": 3.0,
                            "trim_size_width": part.get("page_size", {}).get("width", 210.0),
                            "trim_size_height": part.get("page_size", {}).get("height", 297.0),
                            "bleed_face": 3.0,
                            "bleed_foot": 3.0,
                            "bleed_head": 3.0,
                            "bleed_spine": 3.0,
                            "back_overfold": 0.0,
                            "milling_depth": 0.0,
                            "front_overfold": 0.0,
                            "creep_x": 0.0,
                            "creep_y": 0.0,
                            "margin_top": 0.0,
                            "margin_left": 0.0,
                            "margin_right": 0.0,
                            "margin_bottom": 0.0,
                            "orientation": "Rotate0",
                            "relative_box_x1": 0.0,
                            "relative_box_y1": 0.0,
                            "relative_box_x2": 1.0,
                            "relative_box_y2": 1.0
                        }
                        sheet_config["strip_cell_params"] = strip_cell_params

        # Obtener la configuración de colores
        color_config = part.get("color_config", {})
        front_colors = color_config.get("frontColors", 0)
        back_colors = color_config.get("backColors", 0)

        # Determinar si es impresión a una o dos caras
        is_one_sided = back_colors == 0

        if is_one_sided:
            # Si solo tiene una cara (4/0, 1/0), usar "Flat"
            esquema_work_style = "Flat"
        else:
            # Si tiene dos caras
            if es_tira_retira:
                # Si es tira/retira
                esquema_work_style = "WorkAndTurn"
            else:
                # Si no es tira/retira pero tiene dos caras (4/4, 4/1, 1/1, 5/5), usar "WorkAndBack"
                esquema_work_style = "WorkAndBack"

        # Crear una hoja por cada pliego
        for pliego in range(1, num_pliegos + 1):
            sheet_name = f"Sheet-{i+1}-{pliego}"
            bindery_signature_name = f"BS-{i+1}-{pliego}"

            # Crear configuración de hoja
            sheet_config = {
                "sheet_name": sheet_name,
                "bindery_signature_name": bindery_signature_name,
                "paper_ref": part.get("paper_id", ""),
                "fold_catalog": fold_catalog,
                "work_style": esquema_work_style
            }

            # Añadir parámetros de StripCell (opcional)
            strip_cell_params = {
                "spine": 0.0,
                "trim_face": 3.0,
                "trim_foot": 3.0,
                "trim_head": 3.0,
                "trim_size_width": part.get("page_size", {}).get("width", 210.0),
                "trim_size_height": part.get("page_size", {}).get("height", 297.0),
                "bleed_face": 3.0,
                "bleed_foot": 3.0,
                "bleed_head": 3.0,
                "bleed_spine": 3.0,
                "back_overfold": 0.0,
                "milling_depth": 0.0,
                "front_overfold": 0.0,
                "creep_x": 0.0,
                "creep_y": 0.0,
                "margin_top": 0.0,
                "margin_left": 0.0,
                "margin_right": 0.0,
                "margin_bottom": 0.0,
                "orientation": "Rotate0",
                "relative_box_x1": 0.0,
                "relative_box_y1": 0.0,
                "relative_box_x2": 1.0,
                "relative_box_y2": 1.0
            }
            sheet_config["strip_cell_params"] = strip_cell_params
    if budget_data.get("pdf_filename"):
        page_count = 0

        # Calcular el número total de páginas
        if budget_data.get("parts"):
            for part in budget_data["parts"]:
                page_count += part.get("page_count", 0)

        if page_count > 0:
            # Construir la URL del PDF
            pdf_url = f"http://localhost:3005/uploads/{budget_data['pdf_filename']}"

            runlists = [{
                "runlist_id": "RL-1d",
                "pages": page_count,
                "page_range": f"0 ~ {page_count-1}",
                "signature_ref": "Str-1",  # Referencia a la primera signatura
                "pdf_url": pdf_url
            }]

    # Crear el JSON_OT con la estructura mínima requerida
    json_ot = {
        "job_id": budget_data.get("ot_number", "").replace("OT-", ""),  # Eliminar prefijo OT-
        "descriptive_name": budget_data.get("description", ""),
        "signatures": signatures,
        "paper_configs": paper_configs
    }

    # Añadir campos opcionales
    json_ot["author"] = "JDFast"
    json_ot["agent_name"] = "JDFast"
    json_ot["agent_version"] = "3.1"
    json_ot["timestamp"] = datetime.now().isoformat()
    json_ot["comment"] = f"Generado a partir del presupuesto {budget_id}"
    json_ot["product_type"] = budget_data.get("job_type", "")
    json_ot["binding_side"] = "Left"  # Valor por defecto

    # Añadir información del cliente si existe
    if customer_info:
        json_ot["customer_info"] = customer_info
        
    # Inicializar runlists como lista vacía (no se usa en este contexto)
    runlists = []

    # Registrar la generación del JSON_OT en el log
    log_info(f"JSON_OT generado para el presupuesto {budget_id}")

    return json_ot

@router.post("/save/{budget_id}", response_model=Dict[str, Any])
async def save_json_ot_endpoint(budget_id: str):
    """Endpoint FastAPI para guardar el JSON_OT de un presupuesto."""
    # Llama a la función síncrona o asíncrona según corresponda
    from fastapi import Request
    import asyncio
    json_ot = generate_json_ot(budget_id)
    if hasattr(json_ot, '__await__'):
        json_ot = await json_ot
    data_dir = os.path.join(os.path.dirname(__file__), "..", "data")
    os.makedirs(data_dir, exist_ok=True)
    file_path = os.path.join(data_dir, f"json_ot_{budget_id}.json")
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(json_ot, f, ensure_ascii=False, indent=2)
    return {"status": "ok", "file": file_path}

import sys

def save_json_ot(budget_id: str):
    """
    Genera un JSON_OT a partir de un presupuesto y lo guarda en un archivo.
    Llama a generate_json_ot de manera síncrona o asíncrona según el contexto.
    """
    json_ot = generate_json_ot(budget_id)
    # Si es una corrutina (async), solo permitir await si hay loop activo
    if hasattr(json_ot, '__await__'):
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Si hay un loop corriendo (por ejemplo, FastAPI), el endpoint debe usar await
                raise RuntimeError("save_json_ot debe ser await en contexto async")
            else:
                json_ot = loop.run_until_complete(json_ot)
        except RuntimeError:
            # Si no hay loop, usar asyncio.run
            import asyncio
            json_ot = asyncio.run(generate_json_ot(budget_id))
    data_dir = os.path.join(os.path.dirname(__file__), "..", "data")
    os.makedirs(data_dir, exist_ok=True)
    file_path = os.path.join(data_dir, f"json_ot_{budget_id}.json")
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(json_ot, f, ensure_ascii=False, indent=2)
    return {"status": "ok", "file": file_path}

# Utilidad para generar todos los json_ot desde budgets.json
if __name__ == "__main__":
    import json
    budgets = json.load(open(os.path.join(os.path.dirname(__file__), "..", "data", "budgets.json"), encoding="utf-8"))
    for b in budgets:
        try:
            result = save_json_ot(b["budget_id"])
            print(f"OK: {b['budget_id']} -> {result['file']}")
        except Exception as e:
            print(f"ERROR: {b['budget_id']}: {e}", file=sys.stderr)

@router.get("/render/{budget_id}", response_model=Dict[str, Any])
async def render_jdf_template(budget_id: str):
    """
    Genera un archivo JDF a partir de un presupuesto utilizando la plantilla base.jdf.

    Args:
        budget_id: ID del presupuesto

    Returns:
        dict: Información sobre el archivo generado y el contenido JDF
    """
    json_ot = generate_json_ot(budget_id)
    # Si la función es async, obtener el resultado real
    if hasattr(json_ot, '__await__'):
        import asyncio
        json_ot = asyncio.get_event_loop().run_until_complete(json_ot)
    # Cargar plantilla JDF
    template_dir = os.path.join(os.path.dirname(__file__), "..", "templates")
    env = Environment(loader=FileSystemLoader(template_dir), autoescape=True)
    template = env.get_template("base.jdf")

    # Renderizar plantilla usando datos del JSON_OT
    jdf_content = template.render(**json_ot)

    # Guardar archivo JDF
    data_dir = os.path.join(os.path.dirname(__file__), "..", "data")
    os.makedirs(data_dir, exist_ok=True)
    jdf_path = os.path.join(data_dir, f"jdf_{budget_id}.jdf")
    with open(jdf_path, "w", encoding="utf-8") as f:
        f.write(jdf_content)
    return {"status": "ok", "file": jdf_path, "jdf": jdf_content}

    # Crear directorio para los archivos JDF si no existe
    os.makedirs("data/jdf", exist_ok=True)

    # Nombre del archivo basado en el ID del trabajo
    filename = f"JDF_{json_ot['job_id']}_{datetime.now().strftime('%Y%m%d%H%M%S')}.jdf"
    file_path = f"data/jdf/{filename}"

    # Guardar el JDF en un archivo
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(jdf_content)

    # Registrar la acción en el log
    log_info(f"JDF generado en {file_path} para el presupuesto {budget_id}")

    return {
        "success": True,
        "message": f"JDF generado correctamente como {filename}",
        "file_path": file_path,
        "filename": filename,
        "jdf_content": jdf_content,
        "json_ot": json_ot  # Incluir el JSON_OT en la respuesta para referencia
    }
