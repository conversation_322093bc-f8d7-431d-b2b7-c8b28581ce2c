import { useState, useEffect } from 'react';
import {
  <PERSON>,
  AppBar,
  <PERSON><PERSON>bar,
  <PERSON>po<PERSON>,
  ThemeProvider,
  createTheme,
  <PERSON>u,
  Menu<PERSON>tem,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>b,
  Divider,
  ListSubheader
} from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import HomeIcon from '@mui/icons-material/Home';
import UserMenu from './components/UserMenu';
import PaperCalculator from './components/PaperCalculator';
import PaperCatalog from './components/catalogs/PaperCatalog';
import MachineCatalog from './components/catalogs/MachineCatalog';
import ClientCatalog from './components/catalogs/ClientCatalog';
import ProcessCatalog from './components/catalogs/ProcessCatalog';
//import PaperSheetCalculator from './components/PaperSheetCalculator';
import PaperWeightCalculator from './components/PaperWeightCalculator';
import ShippingCostCalculator from './components/ShippingCostCalculator';
import BudgetForm from './components/BudgetForm';
import BudgetList from './pages/BudgetList';
import LogViewer from './components/LogViewer';
import ProductCatalog from './components/catalogs/ProductCatalog';
import ConsumablesCatalog from './components/catalogs/ConsumablesCatalog';
import ProductionList from './pages/ProductionList';
import ShippingHistory from './pages/ShippingHistory';
import InvoicingPage from './pages/InvoicingPage';
import DashboardPage from './pages/DashboardPage';
import HomePage from './pages/HomePage';
import OffsetPressPlanner from './components/planners/OffsetPressPlanner';
import FinishingPlanner from './components/planners/FinishingPlanner';
import MachinePlanner from './components/planners/MachinePlanner';
import AppConfig from './components/AppConfig';
import ActivityLogsPage from './pages/ActivityLogsPage';

import OffsetCalculatorPage from './pages/OffsetCalculatorPage';
import DigitalCalculatorPage from './pages/DigitalCalculatorPage';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App({ initialTabProp = 0 }) {
  // Iniciar en Presupuestos (ahora es el índice 0)
  // Usar la variable global definida en index.html si está disponible, o la prop si está disponible
  const initialTabValue = window.INITIAL_TAB !== undefined ? window.INITIAL_TAB : initialTabProp;
  const [currentTab, setCurrentTab] = useState(initialTabValue);



  const [editBudgetId, setEditBudgetId] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);



  // Efecto para establecer la pestaña inicial y manejar la navegación por hash
  useEffect(() => {
    // Verificar si hay un hash en la URL
    if (window.location.hash) {
      const hash = window.location.hash.substring(1); // Eliminar el # del inicio

      // Mapeo de hashes a índices de pestañas
      const hashMapping = {
        'budgets': 0,
        'production': 1,
        'planning': 23,
        'shipping': 3,
        'invoicing': 4,
        'dashboard': 5
      };

      // Si el hash está en el mapeo, establecer la pestaña correspondiente
      if (hash in hashMapping) {
        setCurrentTab(hashMapping[hash]);
      } else {
        // Si el hash no está en el mapeo, establecer la pestaña de inicio
        setCurrentTab(-1);
      }
    } else {
      // Si no hay hash, establecer la pestaña de inicio
      setCurrentTab(-1);
    }
  }, []);

  // Efecto para escuchar eventos personalizados de navegación
  useEffect(() => {
    const handleNavigateToBudgetList = () => {
      setCurrentTab(0); // Cambiar a la pestaña de presupuestos (ahora es el índice 0)
      setEditBudgetId(null); // Limpiar el ID de presupuesto en edición
    };

    const handleNavigateToShippingHistory = () => {
      setCurrentTab(3); // Cambiar a la pestaña de historial de envíos (ahora es el índice 3)
    };

    const handleNavigateToTab = (event) => {
      const { tabIndex, processId } = event.detail;
      setCurrentTab(tabIndex);

      // Si se proporciona un ID de proceso, almacenarlo en sessionStorage para que
      // ProductionList pueda expandir la OT correspondiente y mostrar el proceso
      if (processId) {
        sessionStorage.setItem('highlightProcessId', processId);
      }
    };

    // Agregar los event listeners
    window.addEventListener('navigate-to-budget-list', handleNavigateToBudgetList);
    window.addEventListener('navigate-to-shipping-history', handleNavigateToShippingHistory);
    window.addEventListener('navigate-to-tab', handleNavigateToTab);

    // Limpiar los event listeners cuando el componente se desmonte
    return () => {
      window.removeEventListener('navigate-to-budget-list', handleNavigateToBudgetList);
      window.removeEventListener('navigate-to-shipping-history', handleNavigateToShippingHistory);
      window.removeEventListener('navigate-to-tab', handleNavigateToTab);
    };
  }, []);

  // Función para manejar la creación o edición de un presupuesto
  const handleEditBudget = (budgetId) => {
    // Si budgetId es null, estamos creando un nuevo presupuesto
    // Si budgetId tiene un valor, estamos editando un presupuesto existente
    console.log(`${budgetId ? 'Editando' : 'Creando nuevo'} presupuesto${budgetId ? ` con ID: ${budgetId}` : ''}`);
    setEditBudgetId(budgetId);
    setCurrentTab(6); // Cambiar a la pestaña del formulario de presupuesto (ahora es el índice 6)
  };

  // Función para manejar el cambio de pestaña
  const handleTabChange = (_, newValue) => {
    // Mapeo directo de índices de pestañas visuales a índices de componentes
    const tabMapping = {
      0: 0, // PRESUPUESTOS -> BudgetList
      1: 1, // PRODUCCIÓN -> ProductionList
      2: 23, // PLANIFICACIÓN -> MachinePlanner (directo)
      3: 3, // ENVÍOS -> ShippingHistory
      4: 4, // FACTURACIÓN -> InvoicingPage
      5: 5  // DASHBOARD -> DashboardPage
    };

    // Usar el mapeo para establecer el índice correcto
    const mappedValue = tabMapping[newValue];
    setCurrentTab(mappedValue);
  };

  // Funciones para el menú de configuración
  const handleConfigClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleConfigClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (index) => {
    setCurrentTab(index);
    handleConfigClose();
  };

  // Las funciones para el menú de planificación han sido eliminadas

  // Renderizar el contenido según la pestaña seleccionada
  const renderContent = () => {
    switch (currentTab) {
      case -1: // Nueva página de inicio
        return <HomePage onNavigate={(tabIndex) => setCurrentTab(tabIndex)} />;
      case 0:
        return <BudgetList onEditBudget={handleEditBudget} />;
      case 1:
        return <ProductionList />;
      case 2:
        return <MachinePlanner />;
      case 3:
        return <ShippingHistory />;
      case 4:
        return <InvoicingPage />;
      case 5:
        return <DashboardPage />;
      case 6:
        return <BudgetForm budgetId={editBudgetId} />;
      case 7:
        return <PaperCatalog />;
      case 8:
        return <MachineCatalog />;
      case 9:
        return <ClientCatalog />;
      case 10:
        return <ProcessCatalog />;
      //case 11:
      //  return <PaperSheetCalculator />;
      case 12:
        return <LogViewer />;
      case 13:
        return <ProductCatalog />;
      case 14:
        return <ConsumablesCatalog />;
      case 15:
        return <OffsetPressPlanner />;
      case 16:
        return <FinishingPlanner />;
      case 23:
        return <MachinePlanner />;
      case 17:
        return <PaperCalculator />;
      case 18:
        return <PaperWeightCalculator />;
      case 19:
        return <ShippingCostCalculator />;
      case 20:
        return <AppConfig />;
      case 21:
        return <ActivityLogsPage />;

      case 24:
        return <OffsetCalculatorPage />;
      case 25:
        return <DigitalCalculatorPage />;
      default:
        return <HomePage onNavigate={(tabIndex) => setCurrentTab(tabIndex)} />; // Por defecto, mostrar la página de inicio
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh', width: '100%' }}>
        <AppBar position="static">
          <Toolbar>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="h6" component="div" sx={{ flexGrow: 1, mr: 2 }}>
                Gestión de Imprenta
              </Typography>
              <Button
                color="inherit"
                onClick={() => setCurrentTab(-1)}
                sx={{ mr: 2, textTransform: 'uppercase' }}
                startIcon={<HomeIcon />}
              >
                INICIO
              </Button>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
              <Tabs
                value={(() => {
                  // Mapeo inverso de índices de componentes a índices de pestañas visuales
                  const inverseMapping = {
                    0: 0, // BudgetList -> PRESUPUESTOS
                    1: 1, // ProductionList -> PRODUCCIÓN
                    23: 2, // MachinePlanner -> PLANIFICACIÓN (ahora es el componente predeterminado)
                    3: 3, // ShippingHistory -> ENVÍOS
                    4: 4, // InvoicingPage -> FACTURACIÓN
                    5: 5  // DashboardPage -> DASHBOARD
                  };

                  // Si el currentTab está en el mapeo, devolver el índice visual
                  return currentTab in inverseMapping ? inverseMapping[currentTab] : false;
                })()}
                onChange={handleTabChange}
                textColor="inherit"
                indicatorColor="secondary"
              >
                <Tab label="PRESUPUESTOS" />
                <Tab label="PRODUCCIÓN" />
                {/* Tab de Planificación que ahora es un enlace directo */}
                <Tab
                  label="PLANIFICACIÓN"
                  sx={{
                    '&:hover': {
                      opacity: 1,
                    }
                  }}
                />
                <Tab label="ENVÍOS" />
                <Tab label="FACTURACIÓN" />
                <Tab label="DASHBOARD" />
              </Tabs>
              <Button
                color="inherit"
                endIcon={<ArrowDropDownIcon />}
                onClick={handleConfigClick}
                aria-controls={open ? 'config-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                sx={{ ml: 2, textTransform: 'uppercase' }}
              >
                CONFIGURACIÓN
              </Button>
            </Box>
            <UserMenu />
            {/* El menú desplegable de Planificación ha sido eliminado */}

            {/* Menú desplegable de Configuración */}
            <Menu
              id="config-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleConfigClose}
              slotProps={{
                paper: {
                  'aria-labelledby': 'config-button',
                }
              }}
            >
              {/* Catálogos agrupados */}
              <ListSubheader sx={{ bgcolor: 'background.paper', fontWeight: 'bold', color: 'primary.main' }}>Catálogos</ListSubheader>
              <MenuItem onClick={() => handleMenuItemClick(7)}>Catálogo de Papel</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(8)}>Catálogo de Máquinas</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(9)}>Catálogo de Clientes</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(10)}>Catálogo de Procesos</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(13)}>Catálogo de Productos</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(14)}>Catálogo de Consumibles</MenuItem>

              <Divider sx={{ my: 1 }} />

              {/* Calculadoras */}
              <ListSubheader sx={{ bgcolor: 'background.paper', fontWeight: 'bold', color: 'primary.main' }}>Calculadoras</ListSubheader>
              <MenuItem onClick={() => handleMenuItemClick(17)}>Calculadora de Pliegos</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(24)}>Calculadora de Offset</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(25)}>Calculadora de Digital</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(18)}>Calculadora de Peso</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(19)}>Calculadora de Envío</MenuItem>

              <Divider sx={{ my: 1 }} />

              {/* Sistema */}
              <ListSubheader sx={{ bgcolor: 'background.paper', fontWeight: 'bold', color: 'primary.main' }}>Sistema</ListSubheader>
              <MenuItem onClick={() => handleMenuItemClick(20)}>Configuración del Sistema</MenuItem>

              <MenuItem onClick={() => handleMenuItemClick(12)}>Ver Logs</MenuItem>
              <MenuItem onClick={() => handleMenuItemClick(21)}>Registros de Actividad</MenuItem>
            </Menu>
          </Toolbar>
        </AppBar>

        <Box sx={{ display: 'flex', width: '100%', flexGrow: 1 }}>
          <Box sx={{ width: '100%', padding: '10px' }}>
            {renderContent()}
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default App;
