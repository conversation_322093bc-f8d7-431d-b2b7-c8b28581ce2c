// Configuración de la aplicación

// Detectar si estamos en un entorno de producción o desarrollo local
// En producción, la variable de entorno VITE_PRODUCTION será establecida
const isProduction = import.meta.env.VITE_PRODUCTION === 'true' || window.location.hostname === 'imprenta.triky.app';

// Configurar la URL del API según el entorno
const API_URL = isProduction
  ? 'https://api.imprenta.triky.app' // URL para el entorno de producción
  : 'http://localhost:3005';        // URL para desarrollo local

// Registrar el entorno detectado
console.log('Entorno detectado:', isProduction ? 'Producción' : 'Desarrollo local');
console.log('API_URL configurada como:', API_URL);

// Función para construir URLs de API correctamente
const buildApiUrl = (path) => {
  // Asegurarse de que la ruta comience con '/' y eliminar la barra final si existe
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `${API_URL}${normalizedPath}`;
};

// Configuración de logging
const LOGGING_ENABLED = false;
const LOG_LEVEL = 'info'; // 'debug', 'info', 'warn', 'error'

// Ya registramos el entorno detectado arriba
console.log('API_URL configurada como:', API_URL);

export {
  API_URL,
  buildApiUrl,
  LOGGING_ENABLED,
  LOG_LEVEL,
  isProduction
};
