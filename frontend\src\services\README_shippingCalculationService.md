# Shipping Calculation Service

## Descripción

El `shippingCalculationService` es un servicio especializado que extrae la lógica compleja de cálculo de costos de envío del componente `BudgetForm`. Este servicio maneja tanto el cálculo a través de la API como el cálculo local de respaldo, proporcionando una interfaz unificada para todas las operaciones relacionadas con el envío.

## Motivación

La función `recalculateShippingCost` original en `BudgetForm` tenía más de 100 líneas de código con múltiples responsabilidades:
- Cálculo del peso total del papel
- Preparación de datos para la API
- Llamadas HTTP al endpoint de envío
- Manejo de errores y fallback local
- Actualización del estado del presupuesto
- Cálculo local simplificado como respaldo

Al extraer esta lógica a un servicio especializado, conseguimos:
- **Mejor modularización**: Separación clara de responsabilidades
- **Reutilización**: Funciones disponibles para otros componentes
- **Testabilidad**: Funciones más pequeñas y fáciles de probar
- **Mantenibilidad**: Código más organizado y legible
- **Robustez**: Mejor manejo de errores y fallbacks

## Estructura del Servicio

### Funciones Principales

#### `recalculateShippingCost(params)`
Función principal que coordina todo el proceso de cálculo de envío.

**Parámetros:**
```javascript
{
  budgetParts: Array,              // Partes del presupuesto
  budget: Object,                  // Presupuesto actual
  calculateTotalPaperWeight: Function, // Función para calcular peso total
  buildApiUrl: Function,           // Función para construir URLs
  setBudget: Function,             // Setter para actualizar presupuesto
  showSnackbar: Function           // Función para mostrar mensajes
}
```

**Retorna:**
```javascript
{
  weight: number,                  // Peso total en kg
  cost: number,                    // Costo de envío
  country: string,                 // País de destino
  distance_factor: number,         // Factor de distancia
  calculated_locally?: boolean,    // Si fue calculado localmente
  error?: boolean                  // Si hubo error
}
```

### Funciones de Preparación y Validación

#### `prepareShippingRequestData(totalPaperWeight, client)`
Prepara los datos de solicitud para la API de envío.

#### `validateShippingWeight(totalPaperWeight)`
Valida que el peso sea válido para el cálculo (> 0).

### Funciones de Cálculo

#### `callShippingCalculationAPI(requestData, buildApiUrl)`
Realiza la llamada HTTP a la API de cálculo de envío.

#### `calculateLocalShippingCost(totalPaperWeight)`
Calcula el costo de envío usando un algoritmo local simplificado.

**Tarifas locales:**
- ≤ 5kg: €10
- ≤ 10kg: €15
- ≤ 20kg: €20
- > 20kg: €25 + (peso - 20) × €0.5

### Funciones de Actualización

#### `updateBudgetWithShippingData(shippingData, totalPaperWeight, setBudget)`
Actualiza el estado del presupuesto con los datos de envío calculados.

### Funciones Utilitarias

#### `getCurrentShippingCost(budget)`
Obtiene el costo de envío actual del presupuesto.

#### `getCurrentPaperWeight(budget)`
Obtiene el peso total actual del presupuesto.

#### `isShippingCalculatedLocally(budget)`
Verifica si el envío fue calculado localmente.

## Uso en BudgetForm

### Antes (Código Original)
```javascript
const recalculateShippingCost = async () => {
  try {
    // 100+ líneas de lógica compleja
    const totalPaperWeight = calculateTotalPaperWeight(budgetParts, budget.copies);
    
    if (totalPaperWeight <= 0) {
      return { weight: 0, cost: 0 };
    }

    const requestData = {
      weight_kg: totalPaperWeight,
      country: "España"
    };

    if (budget.client && budget.client.client_id) {
      requestData.client_id = budget.client.client_id;
    }

    const response = await fetch(buildApiUrl('/calculations/shipping-cost'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
      },
      body: JSON.stringify(requestData)
    });

    // ... resto de la lógica compleja
  } catch (error) {
    // ... manejo de errores y fallback local
  }
};
```

### Después (Con Servicio)
```javascript
const recalculateShippingCost = async () => {
  try {
    return await shippingCalculationService.recalculateShippingCost({
      budgetParts,
      budget,
      calculateTotalPaperWeight,
      buildApiUrl,
      setBudget,
      showSnackbar
    });
  } catch (error) {
    console.error('Error en recalculateShippingCost:', error);
    return { weight: 0, cost: 0, error: true };
  }
};
```

## Beneficios Obtenidos

### 1. **Reducción Dramática de Complejidad**
- BudgetForm: De ~100 líneas a ~15 líneas
- Función más legible y mantenible

### 2. **Modularización Granular**
- Cada responsabilidad en su propia función
- Fácil identificación de problemas
- Reutilización de componentes

### 3. **Mejor Manejo de Errores**
- Fallback automático a cálculo local
- Mensajes de error específicos
- Logging detallado

### 4. **Testabilidad Mejorada**
- 15+ tests unitarios
- Cobertura de casos edge
- Fácil mockeo de dependencias

## Flujo de Trabajo del Servicio

```
recalculateShippingCost()
├── calculateTotalPaperWeight()    // Calcular peso total
├── validateShippingWeight()       // Validar peso > 0
├── prepareShippingRequestData()   // Preparar datos API
├── callShippingCalculationAPI()   // Llamada HTTP
├── updateBudgetWithShippingData() // Actualizar estado
└── [Error] calculateLocalShippingCost() // Fallback local
```

## Ejemplo de Uso Independiente

```javascript
import shippingCalculationService from '../services/shippingCalculationService';

// Calcular costo local
const localCost = shippingCalculationService.calculateLocalShippingCost(15);
// { weight: 15, cost: 20, country: "España", calculated_locally: true }

// Validar peso
const isValid = shippingCalculationService.validateShippingWeight(10.5);
// true

// Obtener costo actual
const currentCost = shippingCalculationService.getCurrentShippingCost(budget);
// 25.50

// Verificar si fue calculado localmente
const isLocal = shippingCalculationService.isShippingCalculatedLocally(budget);
// false
```

## Integración con Otros Servicios

### calculateTotalPaperWeight
- Función del `silentSheetCalculationService`
- Calcula el peso total basado en partes y copias

### buildApiUrl
- Función de configuración para construir URLs
- Usado para llamadas a la API

### setBudget
- Setter del estado del presupuesto en BudgetForm
- Actualiza el estado con datos de envío

## Testing

El servicio incluye tests comprehensivos:

```javascript
describe('shippingCalculationService', () => {
  test('should prepare shipping request data', () => { ... });
  test('should validate shipping weight', () => { ... });
  test('should calculate local shipping cost', () => { ... });
  test('should handle API calls successfully', () => { ... });
  test('should fallback to local calculation on error', () => { ... });
  // ... 15+ tests más
});
```

## Consideraciones de Implementación

### Autenticación
- Incluye automáticamente el token de autenticación
- Maneja casos donde el token no está disponible

### Manejo de Errores
- Try-catch en operaciones asíncronas
- Fallback automático a cálculo local
- Mensajes descriptivos para el usuario

### Performance
- Validación temprana para evitar llamadas innecesarias
- Cálculo local optimizado
- Actualización inmutable del estado

### Robustez
- Manejo de respuestas malformadas de la API
- Validación de datos de entrada
- Valores por defecto seguros

## Configuración de Tarifas

Las tarifas locales están configuradas en `calculateLocalShippingCost`:

```javascript
if (weight <= 5) {
  shippingCost = 10;
} else if (weight <= 10) {
  shippingCost = 15;
} else if (weight <= 20) {
  shippingCost = 20;
} else {
  shippingCost = 25 + (weight - 20) * 0.5;
}
```

Estas pueden ser fácilmente modificadas o extraídas a un archivo de configuración.

## Próximos Pasos

1. **Añadir configuración externa** para tarifas de envío
2. **Implementar cache** para evitar recálculos innecesarios
3. **Añadir más países** y tarifas internacionales
4. **Crear hooks personalizados** para uso en React
5. **Añadir métricas** de uso y performance

La refactorización ha sido exitosa, mejorando significativamente la modularización del código mientras mantiene toda la funcionalidad original y añadiendo robustez con tests comprehensivos y mejor manejo de errores.
