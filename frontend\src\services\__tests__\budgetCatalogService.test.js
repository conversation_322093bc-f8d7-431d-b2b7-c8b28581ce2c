/**
 * Tests para budgetCatalogService
 */
import budgetCatalogService, {
  fetchCatalogData,
  catalogFilters,
  catalogConfig,
  fetchClients,
  fetchPapers,
  fetchMachines,
  fetchProcesses,
  loadAllCatalogs,
  createCatalogManager,
  getCatalogStats
} from '../budgetCatalogService';

// Mock de fetch global
global.fetch = jest.fn();

// Mock de localStorage
const mockLocalStorage = {
  getItem: jest.fn(() => 'mock-token'),
  setItem: jest.fn(),
  removeItem: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('budgetCatalogService', () => {
  beforeEach(() => {
    fetch.mockClear();
    mockLocalStorage.getItem.mockClear();
  });

  describe('catalogFilters', () => {
    test('should filter active clients', () => {
      const clients = [
        { id: 1, name: 'Client 1', active: true },
        { id: 2, name: 'Client 2', active: false },
        { id: 3, name: 'Client 3', active: true }
      ];

      const filtered = clients.filter(catalogFilters.clients);
      expect(filtered).toHaveLength(2);
      expect(filtered.every(c => c.active)).toBe(true);
    });

    test('should filter papers in stock', () => {
      const papers = [
        { id: 1, name: 'Paper 1', inStock: true },
        { id: 2, name: 'Paper 2', inStock: false },
        { id: 3, name: 'Paper 3', inStock: true }
      ];

      const filtered = papers.filter(catalogFilters.papers);
      expect(filtered).toHaveLength(2);
      expect(filtered.every(p => p.inStock)).toBe(true);
    });

    test('should filter active offset and digital machines', () => {
      const machines = [
        { id: 1, name: 'Machine 1', status: 'Activa', type: 'Offset' },
        { id: 2, name: 'Machine 2', status: 'Inactiva', type: 'Offset' },
        { id: 3, name: 'Machine 3', status: 'Activa', type: 'Digital' },
        { id: 4, name: 'Machine 4', status: 'Activa', type: 'Guillotina' }
      ];

      const filtered = machines.filter(catalogFilters.machines);
      expect(filtered).toHaveLength(2);
      expect(filtered.every(m => m.status === 'Activa')).toBe(true);
      expect(filtered.every(m => ['Offset', 'Digital'].includes(m.type))).toBe(true);
    });

    test('should filter finishing processes', () => {
      const processes = [
        { id: 1, name: 'Process 1', type: 'Corte' },
        { id: 2, name: 'Process 2', type: 'Impresión' },
        { id: 3, name: 'Process 3', type: 'Plegado' },
        { id: 4, name: 'Process 4', type: 'Acabado' }
      ];

      const filtered = processes.filter(catalogFilters.processes);
      expect(filtered).toHaveLength(3);
      expect(filtered.every(p => ['Corte', 'Plegado', 'Acabado'].includes(p.type))).toBe(true);
    });
  });

  describe('fetchCatalogData', () => {
    test('should fetch and filter catalog data successfully', async () => {
      const mockData = [
        { id: 1, name: 'Item 1', active: true },
        { id: 2, name: 'Item 2', active: false },
        { id: 3, name: 'Item 3', active: true }
      ];

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockData
      });

      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);
      const filterFn = item => item.active;

      const result = await fetchCatalogData('/test/', filterFn, 'Test error', mockBuildApiUrl);

      expect(fetch).toHaveBeenCalledWith(
        'http://api.test/test/',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token',
            'Content-Type': 'application/json'
          })
        })
      );

      expect(result).toHaveLength(2);
      expect(result.every(item => item.active)).toBe(true);
    });

    test('should throw error on failed fetch', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Not Found'
      });

      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);

      await expect(fetchCatalogData('/test/', null, 'Test error', mockBuildApiUrl))
        .rejects.toThrow('Test error');
    });
  });

  describe('individual fetch functions', () => {
    test('fetchClients should fetch active clients', async () => {
      const mockClients = [
        { id: 1, name: 'Client 1', active: true },
        { id: 2, name: 'Client 2', active: false }
      ];

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockClients
      });

      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);
      const result = await fetchClients(mockBuildApiUrl);

      expect(mockBuildApiUrl).toHaveBeenCalledWith('/clients/');
      expect(result).toHaveLength(1);
      expect(result[0].active).toBe(true);
    });

    test('fetchPapers should fetch papers in stock', async () => {
      const mockPapers = [
        { id: 1, name: 'Paper 1', inStock: true },
        { id: 2, name: 'Paper 2', inStock: false }
      ];

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockPapers
      });

      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);
      const result = await fetchPapers(mockBuildApiUrl);

      expect(mockBuildApiUrl).toHaveBeenCalledWith('/papers/');
      expect(result).toHaveLength(1);
      expect(result[0].inStock).toBe(true);
    });

    test('fetchMachines should fetch active offset/digital machines', async () => {
      const mockMachines = [
        { id: 1, name: 'Machine 1', status: 'Activa', type: 'Offset' },
        { id: 2, name: 'Machine 2', status: 'Inactiva', type: 'Offset' },
        { id: 3, name: 'Machine 3', status: 'Activa', type: 'Guillotina' }
      ];

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockMachines
      });

      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);
      const result = await fetchMachines(mockBuildApiUrl);

      expect(mockBuildApiUrl).toHaveBeenCalledWith('/machines/');
      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('Activa');
      expect(result[0].type).toBe('Offset');
    });

    test('fetchProcesses should fetch finishing processes', async () => {
      const mockProcesses = [
        { id: 1, name: 'Process 1', type: 'Corte' },
        { id: 2, name: 'Process 2', type: 'Impresión' }
      ];

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProcesses
      });

      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);
      const result = await fetchProcesses(mockBuildApiUrl);

      expect(mockBuildApiUrl).toHaveBeenCalledWith('/processes/');
      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('Corte');
    });
  });

  describe('loadAllCatalogs', () => {
    test('should load all catalogs successfully', async () => {
      const mockClients = [{ id: 1, active: true }];
      const mockPapers = [{ id: 1, inStock: true }];
      const mockMachines = [{ id: 1, status: 'Activa', type: 'Offset' }];
      const mockProcesses = [{ id: 1, type: 'Corte' }];

      fetch
        .mockResolvedValueOnce({ ok: true, json: async () => mockClients })
        .mockResolvedValueOnce({ ok: true, json: async () => mockPapers })
        .mockResolvedValueOnce({ ok: true, json: async () => mockMachines })
        .mockResolvedValueOnce({ ok: true, json: async () => mockProcesses });

      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);
      const mockShowSnackbar = jest.fn();

      const result = await loadAllCatalogs(mockBuildApiUrl, mockShowSnackbar);

      expect(result).toEqual({
        clients: mockClients,
        papers: mockPapers,
        machines: mockMachines,
        processes: mockProcesses
      });
      expect(mockShowSnackbar).not.toHaveBeenCalled();
    });

    test('should handle individual catalog errors gracefully', async () => {
      fetch
        .mockRejectedValueOnce(new Error('Clients error'))
        .mockResolvedValueOnce({ ok: true, json: async () => [{ id: 1, inStock: true }] })
        .mockResolvedValueOnce({ ok: true, json: async () => [{ id: 1, status: 'Activa', type: 'Offset' }] })
        .mockResolvedValueOnce({ ok: true, json: async () => [{ id: 1, type: 'Corte' }] });

      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);
      const mockShowSnackbar = jest.fn();

      const result = await loadAllCatalogs(mockBuildApiUrl, mockShowSnackbar);

      expect(result.clients).toEqual([]);
      expect(result.papers).toHaveLength(1);
      expect(mockShowSnackbar).toHaveBeenCalledWith('Clients error', 'error');
    });
  });

  describe('createCatalogManager', () => {
    test('should create catalog manager with correct functions', () => {
      const mockParams = {
        buildApiUrl: jest.fn(),
        showSnackbar: jest.fn(),
        setClients: jest.fn(),
        setPapers: jest.fn(),
        setMachines: jest.fn(),
        setProcesses: jest.fn(),
        fetchedClientsRef: { current: false },
        fetchedPapersRef: { current: false },
        fetchedMachinesRef: { current: false },
        fetchedProcessesRef: { current: false }
      };

      const manager = createCatalogManager(mockParams);

      expect(manager).toHaveProperty('loadInitialData');
      expect(manager).toHaveProperty('loadCatalogIfNeeded');
      expect(manager).toHaveProperty('cleanup');
      expect(manager).toHaveProperty('reloadCatalog');
      expect(typeof manager.loadInitialData).toBe('function');
    });

    test('should load catalog if needed', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => [{ id: 1, active: true }]
      });

      const mockParams = {
        buildApiUrl: jest.fn(path => `http://api.test${path}`),
        showSnackbar: jest.fn(),
        setClients: jest.fn(),
        setPapers: jest.fn(),
        setMachines: jest.fn(),
        setProcesses: jest.fn(),
        fetchedClientsRef: { current: false },
        fetchedPapersRef: { current: false },
        fetchedMachinesRef: { current: false },
        fetchedProcessesRef: { current: false }
      };

      const manager = createCatalogManager(mockParams);
      await manager.loadCatalogIfNeeded('clients');

      expect(mockParams.setClients).toHaveBeenCalled();
      expect(mockParams.fetchedClientsRef.current).toBe(true);
    });
  });

  describe('getCatalogStats', () => {
    test('should calculate catalog statistics correctly', () => {
      const catalogs = {
        clients: [
          { id: 1, active: true },
          { id: 2, active: false },
          { id: 3, active: true }
        ],
        papers: [
          { id: 1, inStock: true },
          { id: 2, inStock: false }
        ],
        machines: [
          { id: 1, status: 'Activa', type: 'Offset' },
          { id: 2, status: 'Activa', type: 'Digital' },
          { id: 3, status: 'Inactiva', type: 'Offset' }
        ],
        processes: [
          { id: 1, type: 'Corte' },
          { id: 2, type: 'Impresión' },
          { id: 3, type: 'Plegado' }
        ]
      };

      const stats = getCatalogStats(catalogs);

      expect(stats).toEqual({
        clients: { total: 3, active: 2 },
        papers: { total: 2, inStock: 1 },
        machines: { total: 3, active: 2, offset: 1, digital: 1 },
        processes: { total: 3, finishing: 2 }
      });
    });

    test('should handle empty catalogs', () => {
      const stats = getCatalogStats({});

      expect(stats).toEqual({
        clients: { total: 0, active: 0 },
        papers: { total: 0, inStock: 0 },
        machines: { total: 0, active: 0, offset: 0, digital: 0 },
        processes: { total: 0, finishing: 0 }
      });
    });
  });
});
