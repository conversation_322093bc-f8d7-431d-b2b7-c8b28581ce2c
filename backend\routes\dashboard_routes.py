from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any
from datetime import datetime, timedelta
from dependencies.auth import get_current_user
from utils.data_manager import read_data
from utils.logger import log_info

# Crear el router
router = APIRouter()

@router.get("/stats")
async def get_dashboard_stats(current_user: dict = Depends(get_current_user)):
    """
    Obtiene estadísticas generales para el dashboard
    """
    try:
        # Registrar la acción
        log_info(f"Usuario {current_user['username']} solicitó estadísticas del dashboard")

        # Obtener datos
        budgets = read_data("budgets")
        production = read_data("production")

        # Calcular estadísticas de presupuestos
        budget_stats = calculate_budget_stats(budgets)

        # Calcular estadísticas de producción
        production_stats = calculate_production_stats(production)

        return {
            "budget_stats": budget_stats,
            "production_stats": production_stats
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener estadísticas: {str(e)}")

def calculate_budget_stats(budgets: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calcula estadísticas de presupuestos
    """
    # Total de presupuestos
    total = len(budgets)

    # Agrupar por estado
    by_status = {}
    for budget in budgets:
        status = budget.get("status", "Desconocido")
        if status not in by_status:
            by_status[status] = 0
        by_status[status] += 1

    status_list = [{"name": status, "value": count} for status, count in by_status.items()]

    # Agrupar por mes (últimos 6 meses)
    by_month = {}
    today = datetime.now()

    for i in range(6):
        # Calcular el primer día del mes
        month_date = (today - timedelta(days=30*i))
        month_key = f"{month_date.year}-{month_date.month:02d}"
        month_name = f"{get_month_name(month_date.month)} {month_date.year}"

        by_month[month_key] = {
            "name": month_name,
            "total": 0,
            "aprobados": 0,
            "completados": 0,
            "facturados": 0
        }

    # Contar presupuestos por mes
    for budget in budgets:
        if "created_at" not in budget:
            continue

        try:
            created_date = datetime.fromisoformat(budget["created_at"].replace("Z", "+00:00"))
            month_key = f"{created_date.year}-{created_date.month:02d}"

            # Solo contar si está en los últimos 6 meses
            if month_key in by_month:
                by_month[month_key]["total"] += 1

                status = budget.get("status", "")
                if status == "Aprobado":
                    by_month[month_key]["aprobados"] += 1
                elif status == "Completado":
                    by_month[month_key]["completados"] += 1
                elif status == "Facturado":
                    by_month[month_key]["facturados"] += 1
        except (ValueError, TypeError):
            continue

    # Convertir a lista ordenada por fecha
    month_list = []
    for month_key in sorted(by_month.keys(), reverse=True):
        month_list.append(by_month[month_key])

    # Obtener presupuestos recientes (últimos 5)
    recent_budgets = sorted(
        budgets,
        key=lambda x: x.get("created_at", ""),
        reverse=True
    )[:5]

    return {
        "total": total,
        "by_status": status_list,
        "by_month": month_list,
        "recent_budgets": recent_budgets
    }

def calculate_production_stats(production: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calcula estadísticas de producción
    """
    # Total de trabajos
    total = len(production)

    # Contar trabajos activos y completados
    active_jobs = 0
    completed_jobs = 0
    shipped_jobs = 0

    for job in production:
        if job.get("shipped", False):
            shipped_jobs += 1
            continue

        all_completed = all(
            process.get("status") == "Completado"
            for process in job.get("processes", [])
        )

        if all_completed:
            completed_jobs += 1
        else:
            active_jobs += 1

    # Agrupar por estado
    by_status = [
        {"name": "En Proceso", "value": active_jobs},
        {"name": "Completado", "value": completed_jobs},
        {"name": "Enviado", "value": shipped_jobs}
    ]

    # Agrupar por máquina
    machine_stats = {}

    for job in production:
        for process in job.get("processes", []):
            machine = process.get("machine", "Desconocida")
            if machine not in machine_stats:
                machine_stats[machine] = {
                    "total": 0,
                    "completed": 0
                }

            machine_stats[machine]["total"] += 1
            if process.get("status") == "Completado":
                machine_stats[machine]["completed"] += 1

    by_machine = [
        {
            "name": machine,
            "total": stats["total"],
            "completed": stats["completed"]
        }
        for machine, stats in machine_stats.items()
    ]

    # Obtener trabajos recientes (últimos 5)
    recent_jobs = sorted(
        production,
        key=lambda x: x.get("created_at", ""),
        reverse=True
    )[:5]

    return {
        "total": total,
        "active_jobs": active_jobs,
        "completed_jobs": completed_jobs,
        "shipped_jobs": shipped_jobs,
        "by_status": by_status,
        "by_machine": by_machine,
        "recent_jobs": recent_jobs
    }

def get_month_name(month: int) -> str:
    """
    Obtiene el nombre del mes en español
    """
    months = [
        "Ene", "Feb", "Mar", "Abr", "May", "Jun",
        "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"
    ]
    return months[month - 1]
