from fastapi import APIRouter, HTTPException
from typing import List
from models.models import Process, ProcessCreate, ProcessUpdate
from utils.catalog_manager import load_process_catalog, save_process_catalog

router = APIRouter(
    prefix="/processes",
    tags=["processes"],
    responses={404: {"description": "Proceso no encontrado"}}
)

# Variable global para el catálogo
process_catalog = load_process_catalog()

@router.get("/", response_model=List[Process])
def get_all_processes():
    """
    Obtiene todos los procesos del catálogo
    """
    global process_catalog
    process_catalog = load_process_catalog()
    return process_catalog

@router.get("/{process_id}", response_model=Process)
def get_process_by_id(process_id: str):
    """
    Obtiene un proceso específico por su ID
    """
    process = next((p for p in process_catalog if p["process_id"] == process_id), None)
    if not process:
        raise HTTPException(status_code=404, detail="Proceso no encontrado")
    return process

@router.post("/", response_model=Process)
def create_process(process: ProcessCreate):
    """
    Crea un nuevo proceso en el catálogo
    """
    if any(p["process_id"] == process.process_id for p in process_catalog):
        raise HTTPException(status_code=400, detail="Ya existe un proceso con ese ID")
    
    process_dict = process.model_dump()
    process_catalog.append(process_dict)
    save_process_catalog(process_catalog)
    return process_dict

@router.put("/{process_id}", response_model=Process)
def update_process(process_id: str, process_update: ProcessUpdate):
    """
    Actualiza un proceso existente en el catálogo
    """
    process_idx = next((i for i, p in enumerate(process_catalog) if p["process_id"] == process_id), None)
    if process_idx is None:
        raise HTTPException(status_code=404, detail="Proceso no encontrado")
    
    current_process = process_catalog[process_idx]
    update_data = process_update.model_dump(exclude_unset=True)
    
    updated_process = {**current_process, **update_data}
    process_catalog[process_idx] = updated_process
    save_process_catalog(process_catalog)
    return updated_process

@router.delete("/{process_id}")
def delete_process(process_id: str):
    """
    Elimina un proceso del catálogo
    """
    process_idx = next((i for i, p in enumerate(process_catalog) if p["process_id"] == process_id), None)
    if process_idx is None:
        raise HTTPException(status_code=404, detail="Proceso no encontrado")
    
    process_catalog.pop(process_idx)
    save_process_catalog(process_catalog)
    return {"message": "Proceso eliminado correctamente"}
