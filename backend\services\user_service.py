import json
import os
import hashlib
import secrets
import uuid
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any

from models.auth import UserCreate, UserUpdate, UserInDB, User, UserRole


class UserService:
    def __init__(self):
        self.users_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "users.json")
        self._ensure_users_file_exists()
        self.users = self._load_users()

    def _ensure_users_file_exists(self):
        """Asegura que el archivo de usuarios exista, creándolo si es necesario."""
        os.makedirs(os.path.dirname(self.users_file), exist_ok=True)
        if not os.path.exists(self.users_file):
            with open(self.users_file, "w") as f:
                json.dump([], f)

    def _load_users(self) -> List[Dict[str, Any]]:
        """Carga los usuarios desde el archivo JSON."""
        try:
            with open(self.users_file, "r") as f:
                return json.load(f)
        except json.JSONDecodeError:
            # Si el archivo está vacío o mal formateado, devolver una lista vacía
            return []

    def _save_users(self):
        """Guarda los usuarios en el archivo JSON."""
        with open(self.users_file, "w") as f:
            json.dump(self.users, f, indent=2, default=str)

    def _generate_salt(self) -> str:
        """Genera un salt aleatorio para el hash de contraseñas."""
        return secrets.token_hex(16)

    def _hash_password(self, password: str, salt: str) -> str:
        """Genera un hash seguro para la contraseña usando PBKDF2."""
        key = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        )
        return key.hex()

    def verify_password(self, stored_hash: str, stored_salt: str, provided_password: str) -> bool:
        """Verifica si una contraseña coincide con el hash almacenado."""
        calculated_hash = self._hash_password(provided_password, stored_salt)
        return calculated_hash == stored_hash

    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Obtiene un usuario por su nombre de usuario."""
        for user in self.users:
            if user.get("username") == username:
                return user
        return None

    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Obtiene un usuario por su correo electrónico."""
        for user in self.users:
            if user.get("email") == email:
                return user
        return None

    def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene un usuario por su ID."""
        for user in self.users:
            if user.get("user_id") == user_id:
                return user
        return None

    def create_user(self, user_data: UserCreate) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Crea un nuevo usuario.
        
        Returns:
            Tuple[bool, str, Optional[Dict[str, Any]]]: (éxito, mensaje, datos del usuario)
        """
        # Verificar si el usuario ya existe
        if self.get_user_by_username(user_data.username):
            return False, "El nombre de usuario ya está en uso", None
        
        if self.get_user_by_email(user_data.email):
            return False, "El correo electrónico ya está en uso", None
        
        # Generar salt y hash de la contraseña
        salt = self._generate_salt()
        password_hash = self._hash_password(user_data.password, salt)
        
        # Crear el nuevo usuario
        new_user = {
            "user_id": str(uuid.uuid4()),
            "username": user_data.username,
            "email": user_data.email,
            "role": user_data.role,
            "active": user_data.active,
            "password_hash": password_hash,
            "salt": salt,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Añadir el usuario a la lista y guardar
        self.users.append(new_user)
        self._save_users()
        
        # Devolver una copia sin el hash de la contraseña
        user_copy = new_user.copy()
        del user_copy["password_hash"]
        del user_copy["salt"]
        
        return True, "Usuario creado correctamente", user_copy

    def update_user(self, user_id: str, user_data: UserUpdate) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Actualiza un usuario existente.
        
        Returns:
            Tuple[bool, str, Optional[Dict[str, Any]]]: (éxito, mensaje, datos del usuario)
        """
        # Buscar el usuario por ID
        user_index = None
        for i, user in enumerate(self.users):
            if user.get("user_id") == user_id:
                user_index = i
                break
        
        if user_index is None:
            return False, "Usuario no encontrado", None
        
        # Obtener el usuario actual
        current_user = self.users[user_index]
        
        # Verificar si el nuevo nombre de usuario ya está en uso
        if user_data.username and user_data.username != current_user["username"]:
            existing_user = self.get_user_by_username(user_data.username)
            if existing_user and existing_user["user_id"] != user_id:
                return False, "El nombre de usuario ya está en uso", None
        
        # Verificar si el nuevo correo electrónico ya está en uso
        if user_data.email and user_data.email != current_user["email"]:
            existing_user = self.get_user_by_email(user_data.email)
            if existing_user and existing_user["user_id"] != user_id:
                return False, "El correo electrónico ya está en uso", None
        
        # Actualizar los campos del usuario
        if user_data.username:
            current_user["username"] = user_data.username
        
        if user_data.email:
            current_user["email"] = user_data.email
        
        if user_data.role:
            current_user["role"] = user_data.role
        
        if user_data.active is not None:
            current_user["active"] = user_data.active
        
        # Actualizar la contraseña si se proporciona
        if user_data.password:
            salt = self._generate_salt()
            password_hash = self._hash_password(user_data.password, salt)
            current_user["password_hash"] = password_hash
            current_user["salt"] = salt
        
        # Actualizar la fecha de modificación
        current_user["updated_at"] = datetime.now().isoformat()
        
        # Guardar los cambios
        self.users[user_index] = current_user
        self._save_users()
        
        # Devolver una copia sin el hash de la contraseña
        user_copy = current_user.copy()
        del user_copy["password_hash"]
        del user_copy["salt"]
        
        return True, "Usuario actualizado correctamente", user_copy

    def delete_user(self, user_id: str) -> Tuple[bool, str]:
        """
        Elimina un usuario.
        
        Returns:
            Tuple[bool, str]: (éxito, mensaje)
        """
        # Buscar el usuario por ID
        user_index = None
        for i, user in enumerate(self.users):
            if user.get("user_id") == user_id:
                user_index = i
                break
        
        if user_index is None:
            return False, "Usuario no encontrado"
        
        # Eliminar el usuario
        self.users.pop(user_index)
        self._save_users()
        
        return True, "Usuario eliminado correctamente"

    def get_all_users(self) -> List[Dict[str, Any]]:
        """
        Obtiene todos los usuarios.
        
        Returns:
            List[Dict[str, Any]]: Lista de usuarios sin información sensible
        """
        # Devolver una copia de los usuarios sin información sensible
        users_copy = []
        for user in self.users:
            user_copy = user.copy()
            del user_copy["password_hash"]
            del user_copy["salt"]
            users_copy.append(user_copy)
        
        return users_copy

    def create_admin_if_not_exists(self):
        """Crea un usuario administrador si no existe ninguno."""
        # Verificar si ya existe un administrador
        admin_exists = False
        for user in self.users:
            if user.get("role") == UserRole.ADMIN:
                admin_exists = True
                break
        
        if not admin_exists:
            # Crear un usuario administrador por defecto
            admin_user = UserCreate(
                username="admin",
                email="<EMAIL>",
                role=UserRole.ADMIN,
                password="admin123"  # Esta contraseña debe cambiarse después
            )
            self.create_user(admin_user)
            print("Usuario administrador creado con credenciales por defecto. Por favor, cambie la contraseña.")
