import jwt
from datetime import datetime, timed<PERSON>ta
from typing import Dict, <PERSON><PERSON>, Optional, Any

from services.user_service import UserService
from models.auth import UserRole


class AuthService:
    def __init__(self, secret_key: str = "your-secret-key", algorithm: str = "HS256"):
        self.user_service = UserService()
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.token_expire_minutes = 1440  # 24 horas

    def authenticate_user(self, username: str, password: str) -> <PERSON><PERSON>[bool, str, Optional[Dict[str, Any]]]:
        """
        Autentica a un usuario verificando sus credenciales.
        
        Args:
            username: Nombre de usuario
            password: Contraseña
            
        Returns:
            Tuple[bool, str, Optional[Dict[str, Any]]]: (éxito, mensaje, datos del usuario y token)
        """
        user = self.user_service.get_user_by_username(username)
        if not user:
            return False, "Usuario no encontrado", None
        
        if not user.get("active", False):
            return False, "Usuario inactivo", None
        
        stored_hash = user.get("password_hash")
        stored_salt = user.get("salt")
        
        if not self.user_service.verify_password(stored_hash, stored_salt, password):
            return False, "Contraseña incorrecta", None
        
        # Generar token JWT
        token = self.create_access_token(username, user.get("role", "user"))
        
        # Preparar respuesta sin información sensible
        user_data = {
            "user_id": user.get("user_id"),
            "username": user.get("username"),
            "email": user.get("email"),
            "role": user.get("role"),
            "active": user.get("active"),
            "token": token
        }
        
        return True, "Autenticación exitosa", user_data

    def create_access_token(self, username: str, role: str) -> str:
        """
        Crea un token JWT para el usuario.
        
        Args:
            username: Nombre de usuario
            role: Rol del usuario
            
        Returns:
            str: Token JWT
        """
        expire = datetime.utcnow() + timedelta(minutes=self.token_expire_minutes)
        
        payload = {
            "sub": username,
            "role": role,
            "exp": expire
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Verifica un token JWT.
        
        Args:
            token: Token JWT
            
        Returns:
            Tuple[bool, str, Optional[Dict[str, Any]]]: (éxito, mensaje, payload del token)
        """
        try:
            # Decodificar el token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Verificar expiración
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
                return False, "Token expirado", None
            
            # Verificar que el usuario existe y está activo
            username = payload.get("sub")
            user = self.user_service.get_user_by_username(username)
            if not user or not user.get("active", False):
                return False, "Usuario no encontrado o inactivo", None
            
            return True, "Token válido", payload
        except jwt.PyJWTError as e:
            return False, f"Error al verificar token: {str(e)}", None

    def get_user_from_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Obtiene los datos del usuario a partir de un token.
        
        Args:
            token: Token JWT
            
        Returns:
            Optional[Dict[str, Any]]: Datos del usuario o None si el token es inválido
        """
        valid, _, payload = self.verify_token(token)
        if not valid or not payload:
            return None
        
        username = payload.get("sub")
        user = self.user_service.get_user_by_username(username)
        if not user:
            return None
        
        # Devolver datos sin información sensible
        return {
            "user_id": user.get("user_id"),
            "username": user.get("username"),
            "email": user.get("email"),
            "role": user.get("role"),
            "active": user.get("active")
        }

    def get_user_role(self, token: str) -> Optional[str]:
        """
        Obtiene el rol del usuario a partir de un token.
        
        Args:
            token: Token JWT
            
        Returns:
            Optional[str]: Rol del usuario o None si el token es inválido
        """
        valid, _, payload = self.verify_token(token)
        if not valid or not payload:
            return None
        
        return payload.get("role")
