import React, { useState } from 'react';
import { Box, Paper, Typography, useTheme, ToggleButton, ToggleButtonGroup } from '@mui/material';

/**
 * Componente para visualizar gráficamente un pliego con sus páginas, marcas y áreas de desperdicio
 */
const SheetPreview = ({ 
  anchoPliego, 
  altoPliego, 
  anchoPagina, 
  altoPagina, 
  paginasAncho, 
  paginasAlto, 
  sangrado = 3, 
  pinzas = 10, 
  margenLateral = 5, 
  margenSuperior = 5, 
  margenInferior = 5, 
  marcasRegistro = true, 
  tirasControl = true,
  esTiraRetira = false,
  sheetType = 'Flat',
  pageLayout = null,
  esquemaNombre = '',
  medianil = null,  // Si es null, se calculará como margenLateral * 2
  orientacion = 'Rotate0', // Nuevo parámetro para la orientación simplificada
}) => {
  const theme = useTheme();
  
  // Si medianil es null, lo calculamos como el doble del margen lateral
  const medianilReal = medianil !== null ? medianil : margenLateral * 2;
  
  // Estado para controlar si se muestra la cara o el dorso en esquemas tira-retira
  const [lado, setLado] = useState('cara');
  
  // Aseguramos que si es tira-retira, el tipo de pliego sea siempre WorkAndTurn
  const sheetTypeActual = esTiraRetira ? 'WorkAndTurn' : sheetType;
  
  // Interpretamos la orientación simplificada
  const esRotado = orientacion === 'Rotate90';
  
  // Para esquemas tipo F (fold), usamos directamente 2x2 para F8-7
  let paginasAnchoAjustado, paginasAltoAjustado;
  
  if (esTiraRetira || sheetTypeActual === 'WorkAndTurn') {
    // Para el esquema F8-7 (8 páginas), la disposición es exactamente 2x2 por cara
    if (paginasAncho === 4 && paginasAlto === 2) {
      // Este es el caso del F8-7
      paginasAnchoAjustado = 2;
      paginasAltoAjustado = 2;
    } else {
      // Para otros esquemas tira-retira, dividimos las páginas entre cara y dorso
      paginasAnchoAjustado = paginasAncho;
      paginasAltoAjustado = paginasAlto;
      
      if (paginasAncho > paginasAlto) {
        paginasAnchoAjustado = Math.floor(paginasAncho / 2);
      } else {
        paginasAltoAjustado = Math.floor(paginasAlto / 2);
      }
    }
  } else {
    // Para esquemas normales (no tira-retira), usamos los valores originales
    paginasAnchoAjustado = paginasAncho;
    paginasAltoAjustado = paginasAlto;
  }
  
  // Manejar cambio entre cara y dorso
  const handleLadoChange = (event, nuevoLado) => {
    if (nuevoLado !== null) {
      setLado(nuevoLado);
    }
  };
  
  // Calculamos la escala para que el pliego se ajuste al contenedor
  const maxWidth = 500;
  const maxHeight = 400;
  const escalaAncho = maxWidth / anchoPliego;
  const escalaAlto = maxHeight / altoPliego;
  const escala = Math.min(escalaAncho, escalaAlto, 1); // Limitamos la escala a 1:1 como máximo
  
  // Dimensiones escaladas
  const anchoEscalado = anchoPliego * escala;
  const altoEscalado = altoPliego * escala;
  
  // Ajustamos las dimensiones de la página según la orientación
  const anchoPaginaEscalado = esRotado ? altoPagina * escala : anchoPagina * escala;
  const altoPaginaEscalado = esRotado ? anchoPagina * escala : altoPagina * escala;
  const sangradoEscalado = sangrado * escala;
  const pinzasEscalado = pinzas * escala;
  const margenLateralEscalado = margenLateral * escala;
  const margenSuperiorEscalado = margenSuperior * escala;
  const margenInferiorEscalado = margenInferior * escala;
  const medianilEscalado = medianilReal * escala;
  
  // Determinamos si es un esquema tipo F (fold) basado en el nombre
  const esEsquemaTipoF = esquemaNombre && esquemaNombre.startsWith('F');
  
  // Determinamos si es un esquema F8-7 (8 páginas en total)
  const esF8 = esquemaNombre && esquemaNombre.includes('F8');
  
  // Calculamos el espacio disponible dentro del pliego (descontando márgenes)
  const anchoDisponible = anchoEscalado - (margenLateralEscalado * 2);
  const altoDisponible = altoEscalado - margenSuperiorEscalado - margenInferiorEscalado - pinzasEscalado;
  
  // Variables para centrado y dimensiones totales (inicializadas con valores por defecto)
  let espacioLateralCentrado = margenLateralEscalado;
  let espacioSuperiorCentrado = margenSuperiorEscalado;
  let anchoTotalConjunto = anchoEscalado - (margenLateralEscalado * 2);
  let altoTotalConjunto = altoEscalado - margenSuperiorEscalado - margenInferiorEscalado - pinzasEscalado;
  let separacionFilas = 0;
  
  // Calculamos el espacio entre páginas
  let espacioHorizontal, espacioVertical;
  
  if (esEsquemaTipoF) {
    // Para esquemas tipo F, calculamos el espacio disponible teniendo en cuenta:
    // - Hay un margen lateral para cada página
    // - El medianil (calle) entre dípticos está formado por los márgenes laterales de las páginas adyacentes
    
    // Calculamos cuántos dípticos hay horizontalmente (cada díptico tiene 2 páginas)
    const dipticosHorizontales = Math.ceil(paginasAnchoAjustado / 2);
    // Espacio total ocupado por páginas
    const espacioPaginasHorizontal = anchoPaginaEscalado * paginasAnchoAjustado;
    // Espacio total ocupado por medianiles entre dípticos
    const espacioMedianilesHorizontal = medianilEscalado * (dipticosHorizontales > 1 ? dipticosHorizontales - 1 : 0);
    // Espacio restante para los bordes
    const espacioBordesHorizontal = anchoDisponible - espacioPaginasHorizontal - espacioMedianilesHorizontal;
    // Dividimos el espacio de los bordes entre los dos lados
    espacioHorizontal = Math.max(0, espacioBordesHorizontal / 2);
    
    // Hacemos lo mismo para el eje vertical
    const dipticosVerticales = Math.ceil(paginasAltoAjustado / 2);
    const espacioPaginasVertical = altoPaginaEscalado * paginasAltoAjustado;
    const espacioMedianilesVertical = medianilEscalado * (dipticosVerticales > 1 ? dipticosVerticales - 1 : 0);
    const espacioBordesVertical = altoDisponible - espacioPaginasVertical - espacioMedianilesVertical;
    espacioVertical = Math.max(0, espacioBordesVertical / 2);
  } else {
    // Para otros tipos de esquemas, distribuimos el espacio uniformemente
    espacioHorizontal = (anchoDisponible - (anchoPaginaEscalado * paginasAnchoAjustado)) / (paginasAnchoAjustado + 1);
    espacioVertical = (altoDisponible - (altoPaginaEscalado * paginasAltoAjustado)) / (paginasAltoAjustado + 1);
  }
  
  // Generamos las páginas
  const paginas = [];
  
  // Generamos los medianiles entre páginas específicas (1-2 y 3-4) y entre front y back para tira-retira
  // Usamos objetos de datos que luego convertiremos en componentes JSX
  const datosMedianiles = [];
  
  if (esEsquemaTipoF) {
    // Para los esquemas tipo F (F16-7 o F8-7), necesitamos posicionar el medianil exactamente en el centro
    // y centrar todo el conjunto de páginas en el pliego
    
    // Calculamos la posición central del pliego
    const centroX = anchoEscalado / 2;
    const centroY = altoEscalado / 2;
    
    // El medianil debe tener un ancho fijo de 10mm
    const anchoMedianil = 10 * escala; // Medianil fijo de 10mm
    
    // Añadimos la separación entre filas igual al margen superior + inferior (5mm + 5mm = 10mm)
    const separacionFilas = (margenSuperiorEscalado + margenInferiorEscalado);
    
    // Calculamos el ancho y alto total del conjunto de páginas incluyendo separaciones
    // Para esquemas tira-retira, duplicamos el ancho para mostrar tanto front como back
    const anchoTotalConjunto = esTiraRetira || sheetType === 'WorkAndTurn' 
      ? (anchoPaginaEscalado * 4 * 2) + anchoMedianil * 3  // Front + medianil + Back
      : (anchoPaginaEscalado * 4) + anchoMedianil;
    const altoTotalConjunto = (altoPaginaEscalado * 2) + separacionFilas;
    
    // Calculamos el espacio para centrar todo el conjunto (considerando las pinzas abajo)
    const espacioLateralCentrado = (anchoEscalado - anchoTotalConjunto) / 2;
    const espacioSuperiorCentrado = ((altoEscalado - pinzasEscalado) - altoTotalConjunto) / 2;
    
    // Posición X del medianil (exactamente en el centro del pliego)
    const xMedianil = centroX - (anchoMedianil / 2);
    
    // Posiciones X de las columnas de páginas, centradas en el pliego
    const x1 = espacioLateralCentrado;                       // Columna 1 (páginas 16, 14)
    const x2 = x1 + anchoPaginaEscalado;                     // Columna 2 (páginas 1, 3)
    const x3 = xMedianil + anchoMedianil;                    // Columna 3 (páginas 2, 4) - justo después del medianil
    const x4 = x3 + anchoPaginaEscalado;                     // Columna 4 (páginas 15, 13)
    
    // Posiciones Y de las filas de páginas, centradas verticalmente (considerando pinzas abajo)
    const yBase = espacioSuperiorCentrado + margenSuperiorEscalado;  // Base + margen superior
    const y1 = yBase;                                        // Fila 1 (páginas 16, 1, 2, 15)
    const y2 = y1 + altoPaginaEscalado + separacionFilas;   // Fila 2 (páginas 14, 3, 4, 13)
    
    // Definimos las posiciones de las páginas según el esquema
    let posicionesPaginas = [];
    
    // Usamos el pageLayout proporcionado si está disponible
    if (pageLayout) {
      // Para esquemas tira-retira, mostramos tanto front como back en el mismo pliego
      if (esTiraRetira || sheetType === 'WorkAndTurn') {
        // Calculamos el ancho total del conjunto (front + medianil + back)
        const anchoFront = anchoPaginaEscalado * (pageLayout.front ? pageLayout.front[0].length : 0);
        const anchoBack = anchoPaginaEscalado * (pageLayout.back ? pageLayout.back[0].length : 0);
        const anchoTotalReal = anchoFront + medianilEscalado + anchoBack;
        
        // Recalculamos el espacio lateral para centrar todo el conjunto
        const espacioLateralReal = (anchoEscalado - anchoTotalReal) / 2;
        
        // Añadimos un medianil vertical entre front y back
        if (pageLayout.front && pageLayout.back) {
          const xMedianil = espacioLateralReal + anchoFront;
          const yMedianil = espacioSuperiorCentrado;
          const altoMedianil = altoPaginaEscalado * 2 + separacionFilas;
          
          datosMedianiles.push({
            x: xMedianil,
            y: yMedianil,
            width: medianilEscalado,
            height: altoMedianil,
            label: `${medianilReal} mm`
          });
        }
        
        // Mostramos front a la izquierda
        if (pageLayout.front) {
          pageLayout.front.forEach((fila, filaIndex) => {
            fila.forEach((numPagina, colIndex) => {
              const posX = espacioLateralReal + (colIndex * anchoPaginaEscalado);
              const posY = espacioSuperiorCentrado + (filaIndex * (altoPaginaEscalado + separacionFilas));
              
              posicionesPaginas.push({
                x: posX,
                y: posY,
                num: numPagina
              });
            });
          });
        }
        
        // Mostramos back a la derecha
        if (pageLayout.back) {
          // Calculamos el desplazamiento para el back (a la derecha del front)
          const offsetX = anchoFront + medianilEscalado;
          
          pageLayout.back.forEach((fila, filaIndex) => {
            fila.forEach((numPagina, colIndex) => {
              const posX = espacioLateralReal + offsetX + (colIndex * anchoPaginaEscalado);
              const posY = espacioSuperiorCentrado + (filaIndex * (altoPaginaEscalado + separacionFilas));
              
              posicionesPaginas.push({
                x: posX,
                y: posY,
                num: numPagina
              });
            });
          });
        }
      } else {
        // Para esquemas normales, mostramos solo front o back según el lado seleccionado
        const layoutCara = lado === 'cara' ? pageLayout.front : pageLayout.back;
        
        if (layoutCara) {
          // Convertimos el layout en posiciones de páginas
          layoutCara.forEach((fila, filaIndex) => {
            fila.forEach((numPagina, colIndex) => {
              const posX = espacioLateralCentrado + (colIndex * anchoPaginaEscalado) + 
                        (colIndex >= paginasAnchoAjustado / 2 ? medianilEscalado : 0);
              const posY = espacioSuperiorCentrado + (filaIndex * (altoPaginaEscalado + separacionFilas));
              
              posicionesPaginas.push({
                x: posX,
                y: posY,
                num: numPagina
              });
            });
          });
        }
      }
    } else if (esF8) {
      // Fallback para F8-7 si no hay pageLayout
      if (lado === 'cara') {
        posicionesPaginas = [
          { x: x1, y: y1, num: 8 },
          { x: x2, y: y1, num: 1 },
          { x: x1, y: y2, num: 7 },
          { x: x2, y: y2, num: 2 }
        ];
      } else {
        posicionesPaginas = [
          { x: x1, y: y1, num: 6 },
          { x: x2, y: y1, num: 3 },
          { x: x1, y: y2, num: 5 },
          { x: x2, y: y2, num: 4 }
        ];
      }
    } else {
      // Para el esquema F16-7 (16 páginas en total, 8 por cara)
      // [16][1]  [2][15]
      // [14][3]  [4][13]
      posicionesPaginas = [
        // Fila 1
        { x: x1, y: y1, num: 16 },
        { x: x2, y: y1, num: 1 },
        { x: x3, y: y1, num: 2 },
        { x: x4, y: y1, num: 15 },
        // Fila 2
        { x: x1, y: y2, num: 14 },
        { x: x2, y: y2, num: 3 },
        { x: x3, y: y2, num: 4 },
        { x: x4, y: y2, num: 13 }
      ];
    }
    
    // Generamos las páginas según las posiciones definidas
    posicionesPaginas.forEach((pagina) => {
      paginas.push(
        <Box
          key={`pagina-${pagina.num}`}
          sx={{
            position: 'absolute',
            left: pagina.x,
            top: pagina.y,
            width: anchoPaginaEscalado,
            height: altoPaginaEscalado,
            bgcolor: theme.palette.grey[200],
            border: `${Math.max(1, sangradoEscalado)}px solid ${theme.palette.warning.light}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 0,
          }}
        >
          <Typography variant="body1" color={pagina.num <= 4 ? 'green' : 'brown'}>
            {pagina.num}
          </Typography>
        </Box>
      );
    });
    
    // Medianil vertical entre páginas 1-2 y 3-4 (en el centro exacto del pliego)
    datosMedianiles.push({
      x: xMedianil,
      y: yBase,
      width: anchoMedianil,
      height: (altoPaginaEscalado * 2) + separacionFilas,  // Cubre ambas filas y la separación entre ellas
      label: `${medianilReal} mm`
    });
    
    // Ya no necesitamos añadir la etiqueta de texto por separado, ya que se incluye en el medianil
    
    // Añadimos etiquetas para mostrar que no hay separación en el lomo
    const etiquetasLomo = [
      { x: x1 + anchoPaginaEscalado - 20, y: y1 + (altoPaginaEscalado / 2), texto: "Lomo (0mm)" },
      { x: x1 + anchoPaginaEscalado - 20, y: y2 + (altoPaginaEscalado / 2), texto: "Lomo (0mm)" },
      { x: x3 + anchoPaginaEscalado - 20, y: y1 + (altoPaginaEscalado / 2), texto: "Lomo (0mm)" },
      { x: x3 + anchoPaginaEscalado - 20, y: y2 + (altoPaginaEscalado / 2), texto: "Lomo (0mm)" },
    ];
    
    // Añadimos las etiquetas de lomo como datos para renderizar después
    etiquetasLomo.forEach((etiqueta, index) => {
      datosMedianiles.push({
        tipo: 'etiqueta',
        x: etiqueta.x,
        y: etiqueta.y,
        texto: etiqueta.texto,
        key: `lomo-${index}`
      });
    });
    
    // Añadimos visualización de los márgenes superior e inferior para cada página
    // Recorremos todas las posiciones de páginas para añadir sus márgenes
    posicionesPaginas.forEach((pagina) => {
      // Margen superior de la página
      datosMedianiles.push({
        tipo: 'margen',
        x: pagina.x,
        y: pagina.y - margenSuperiorEscalado,
        width: anchoPaginaEscalado,
        height: margenSuperiorEscalado,
        key: `margen-superior-${pagina.num}`
      });
      
      // Margen inferior de la página
      datosMedianiles.push({
        tipo: 'margen',
        x: pagina.x,
        y: pagina.y + altoPaginaEscalado,
        width: anchoPaginaEscalado,
        height: margenInferiorEscalado,
        key: `margen-inferior-${pagina.num}`
      });
    });
    
    // Añadimos etiquetas explicativas para los márgenes
    datosMedianiles.push({
      tipo: 'etiqueta',
      x: x1,
      y: y1 - margenSuperiorEscalado / 2,
      texto: `Margen Superior (${margenSuperior} mm)`,
      key: 'texto-margen-superior'
    });
    
    datosMedianiles.push({
      tipo: 'etiqueta',
      x: x1,
      y: y2 + altoPaginaEscalado + margenInferiorEscalado / 2,
      texto: `Margen Inferior (${margenInferior} mm)`,
      key: 'texto-margen-inferior'
    });
  } else {
    // Para otros tipos de esquemas, usamos el espaciado uniforme
    for (let fila = 0; fila < paginasAltoAjustado; fila++) {
      for (let col = 0; col < paginasAnchoAjustado; col++) {
        let x = margenLateralEscalado + espacioHorizontal + col * (anchoPaginaEscalado + espacioHorizontal);
        let y = margenSuperiorEscalado + pinzasEscalado + espacioVertical + fila * (altoPaginaEscalado + espacioVertical);
        
        // Calcular el número de página
        let numeroPagina;
        
        // Si tenemos la información de page_layout del esquema, la usamos
        if (pageLayout && lado) {
          try {
            // Obtener la matriz de páginas para el lado actual ("front" o "back")
            const ladoKey = lado === 'cara' ? 'front' : 'back';
            if (pageLayout[ladoKey] && 
                pageLayout[ladoKey][fila] && 
                typeof pageLayout[ladoKey][fila][col] !== 'undefined') {
              numeroPagina = pageLayout[ladoKey][fila][col];
            } else {
              // Si no encontramos la página en el layout, usamos el cálculo por defecto
              const paginasPorCara = paginasAnchoAjustado * paginasAltoAjustado;
              if (lado === 'cara') {
                numeroPagina = fila * paginasAnchoAjustado + col + 1;
              } else {
                numeroPagina = paginasPorCara + (paginasAltoAjustado - fila - 1) * paginasAnchoAjustado + (paginasAnchoAjustado - col - 1) + 1;
              }
            }
          } catch (error) {
            console.error('Error al obtener numeración de página desde page_layout:', error);
            numeroPagina = fila * paginasAnchoAjustado + col + 1;
          }
        } else {
          // Si no tenemos page_layout, usamos el cálculo por defecto
          const paginasPorCara = paginasAnchoAjustado * paginasAltoAjustado;
          
          if (esTiraRetira || sheetType === 'WorkAndTurn') {
            if (lado === 'cara') {
              numeroPagina = fila * paginasAnchoAjustado + col + 1;
            } else {
              // En el dorso, las páginas se numeran de forma inversa para tira-retira
              numeroPagina = paginasPorCara + (paginasAltoAjustado - fila - 1) * paginasAnchoAjustado + (paginasAnchoAjustado - col - 1) + 1;
            }
          } else {
            numeroPagina = fila * paginasAnchoAjustado + col + 1;
          }
        }
        
        paginas.push(
          <Box
            key={`pagina-${fila}-${col}`}
            sx={{
              position: 'absolute',
              left: x - sangradoEscalado,
              top: y - sangradoEscalado,
              width: anchoPaginaEscalado + (sangradoEscalado * 2),
              height: altoPaginaEscalado + (sangradoEscalado * 2),
              bgcolor: theme.palette.grey[200],
              border: `${sangradoEscalado}px solid ${theme.palette.warning.light}`,
              boxSizing: 'content-box',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography variant="caption" color="text.secondary">
              {numeroPagina}
            </Typography>
          </Box>
        );
    }
  }
  
  // Área de pinzas (abajo)
    // Para el esquema F16-7, solo queremos mostrar medianil entre las páginas (1-2) y (3-4)
    // Las páginas (16-1), (14-3), (2-15), (4-13) están unidas por el lomo sin separación
    
    // Ajustamos la posición base considerando que las pinzas están abajo
    let xBase = margenLateralEscalado + espacioHorizontal;
    let yBase = margenSuperiorEscalado + espacioVertical;
    
    // Calculamos el ancho y alto de cada página
    const anchoPagina = anchoPaginaEscalado;
    const altoPagina = altoPaginaEscalado;
    
    // Para el esquema F16-7, las páginas están numeradas así:
    // [16][1]  [2][15]
    // [14][3]  [4][13]
    
    // Para el esquema F16-7, necesitamos posicionar el medianil exactamente en el centro
    // y ajustar las páginas para que la separación sea exactamente el ancho del medianil
    
    // El medianil debe tener un ancho fijo de 10mm
    const anchoMedianil = 10 * escala; // Medianil fijo de 10mm
    
    // Calculamos el ancho total disponible para las páginas
    const anchoDisponible = anchoEscalado - anchoMedianil;
    
    // Calculamos el ancho de cada página (4 páginas en total)
    const anchoPaginaAjustado = anchoPagina;
    
    // Calculamos el espacio lateral para centrar todo el conjunto
    const espacioLateral = (anchoDisponible - (anchoPaginaAjustado * 4)) / 2;
    
    // Calculamos la posición central del pliego
    const centroX = anchoEscalado / 2;
    
    // Posiciones X de las columnas de páginas
    const x1 = espacioLateral;                       // Columna 1 (páginas 16, 14)
    const x2 = x1 + anchoPaginaAjustado;             // Columna 2 (páginas 1, 3)
    const xMedianil = centroX - (anchoMedianil / 2); // Posición del medianil en el centro exacto
    const x3 = xMedianil + anchoMedianil;             // Columna 3 (páginas 2, 4) - justo después del medianil
    const x4 = x3 + anchoPaginaAjustado;             // Columna 4 (páginas 15, 13)
    
    // Posiciones Y de las filas de páginas (considerando pinzas abajo)
    const y1 = yBase;                                // Fila 1 (páginas 16, 1, 2, 15)
    const y2 = y1 + altoPagina;                      // Fila 2 (páginas 14, 3, 4, 13)
    
    // Medianil vertical entre páginas 1-2 y 3-4 (en el centro exacto del pliego)
    datosMedianiles.push({
      x: xMedianil,
      y: yBase,
      width: anchoMedianil,
      height: altoPagina * 2,  // Cubre ambas filas
      label: `${medianilReal} mm`
    });
    
    // Añadimos etiquetas para mostrar que no hay separación en el lomo
    const etiquetasLomo = [
      { x: x1 + anchoPagina - 20, y: y1 + (altoPagina / 2), texto: "Lomo (0mm)" },
      { x: x1 + anchoPagina - 20, y: y2 + (altoPagina / 2), texto: "Lomo (0mm)" },
      { x: x3 + anchoPagina - 20, y: y1 + (altoPagina / 2), texto: "Lomo (0mm)" },
      { x: x3 + anchoPagina - 20, y: y2 + (altoPagina / 2), texto: "Lomo (0mm)" },
    ];
    
    // Añadimos las etiquetas de lomo como datos para renderizar después
    etiquetasLomo.forEach((etiqueta, index) => {
      datosMedianiles.push({
        tipo: 'etiqueta',
        x: etiqueta.x,
        y: etiqueta.y,
        texto: etiqueta.texto,
        key: `lomo-${index}`
      });
    });
  }
  
  // Área de pinzas (abajo)
  const areaPinzas = pinzas > 0 ? (
    <Box
      key="pinzas"
      sx={{
        position: 'absolute',
        left: 0,
        bottom: 0,
        width: anchoEscalado,
        height: pinzasEscalado,
        bgcolor: 'rgba(0, 128, 255, 0.2)',
        border: '1px dashed rgba(0, 128, 255, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1, // Para que aparezca por encima de las páginas
      }}
    >
      <Typography variant="caption" color="primary">
        Pinzas ({pinzas} mm)
      </Typography>
    </Box>
  ) : null;
  
  // Marcas de registro
  const marcas = [];
  if (marcasRegistro) {
    const tamanoMarca = Math.min(20, Math.max(10, Math.min(anchoEscalado, altoEscalado) * 0.05));
    
    // Esquina superior izquierda
    marcas.push(
      <Box
        key="marca-sup-izq"
        sx={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: tamanoMarca,
          height: tamanoMarca,
          bgcolor: theme.palette.grey[300],
          border: `1px solid ${theme.palette.grey[500]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2, // Para que aparezca por encima de todo
        }}
      >
        <Box sx={{ width: '50%', height: '50%', border: '1px solid black', borderRadius: '50%' }} />
      </Box>
    );
    
    // Esquina superior derecha
    marcas.push(
      <Box
        key="marca-sup-der"
        sx={{
          position: 'absolute',
          right: 0,
          top: 0,
          width: tamanoMarca,
          height: tamanoMarca,
          bgcolor: theme.palette.grey[300],
          border: `1px solid ${theme.palette.grey[500]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2,
        }}
      >
        <Box sx={{ width: '50%', height: '50%', border: '1px solid black', borderRadius: '50%' }} />
      </Box>
    );
    
    // Esquina inferior izquierda
    marcas.push(
      <Box
        key="marca-inf-izq"
        sx={{
          position: 'absolute',
          left: 0,
          bottom: 0,
          width: tamanoMarca,
          height: tamanoMarca,
          bgcolor: theme.palette.grey[300],
          border: `1px solid ${theme.palette.grey[500]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2,
        }}
      >
        <Box sx={{ width: '50%', height: '50%', border: '1px solid black', borderRadius: '50%' }} />
      </Box>
    );
    
    // Esquina inferior derecha
    marcas.push(
      <Box
        key="marca-inf-der"
        sx={{
          position: 'absolute',
          right: 0,
          bottom: 0,
          width: tamanoMarca,
          height: tamanoMarca,
          bgcolor: theme.palette.grey[300],
          border: `1px solid ${theme.palette.grey[500]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 2,
        }}
      >
        <Box sx={{ width: '50%', height: '50%', border: '1px solid black', borderRadius: '50%' }} />
      </Box>
    );
  }
  
  // Renderizamos los medianiles a partir de los datos
  const medianilsRenderizados = datosMedianiles.map((medianil, index) => {
    if (medianil.tipo === 'etiqueta') {
      // Renderizamos una etiqueta de texto
      return (
        <Typography
          key={`etiqueta-${medianil.key || index}`}
          variant="caption"
          color="text.secondary"
          sx={{
            position: 'absolute',
            left: medianil.x,
            top: medianil.y,
            whiteSpace: 'nowrap',
            zIndex: 2,
            fontSize: '0.6rem',
            opacity: 0.7,
          }}
        >
          {medianil.texto}
        </Typography>
      );
    } else {
      // Renderizamos un medianil (caja vertical u horizontal)
      return (
        <React.Fragment key={`medianil-${index}`}>
          <Box
            sx={{
              position: 'absolute',
              left: medianil.x,
              top: medianil.y,
              width: medianil.width,
              height: medianil.height,
              bgcolor: 'rgba(255, 165, 0, 0.2)',  // Color naranja semi-transparente
              border: '1px dashed rgba(255, 165, 0, 0.5)',
              zIndex: 1,
            }}
          />
          {medianil.label && (
            <Typography
              variant="caption"
              color="orange"
              sx={{
                position: 'absolute',
                left: medianil.x + (medianil.width / 2) - 5,
                top: medianil.y + (medianil.height / 2) - 10,
                whiteSpace: 'nowrap',
                zIndex: 2,
                fontSize: '0.7rem',
                transform: medianil.height > medianil.width ? 'rotate(90deg)' : 'none',
                transformOrigin: 'left center',
              }}
            >
              {medianil.label}
            </Typography>
          )}
        </React.Fragment>
      );
    }
  });
  
  // Tira de control (justo encima del margen superior de las páginas)
  const tiraControl = tirasControl ? (
    <Box
      key="tira-control"
      sx={{
        position: 'absolute',
        left: espacioLateralCentrado,
        top: espacioSuperiorCentrado - margenSuperiorEscalado,
        width: anchoTotalConjunto,
        height: margenSuperiorEscalado,
        bgcolor: theme.palette.grey[300],
        border: `1px solid ${theme.palette.grey[500]}`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1,
      }}
    >
      <Typography variant="caption" color="text.secondary">
        Tira de control
      </Typography>
    </Box>
  ) : null;
  
  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: 2, 
        mb: 3,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}
    >
      <Typography variant="h6" gutterBottom>
        Visualización del Pliego {esquemaNombre && `- Esquema ${esquemaNombre}`}
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Dimensiones del pliego: {anchoPliego} × {altoPliego} mm
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Dimensiones de página: {anchoPagina} × {altoPagina} mm
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Disposición: {paginasAnchoAjustado} × {paginasAltoAjustado} páginas {esTiraRetira || sheetTypeActual === 'WorkAndTurn' ? 'por cara' : ''}
        </Typography>
        {/* Botones para cambiar entre cara y dorso (solo si no es tira-retira) */}
        {!esTiraRetira && sheetTypeActual !== 'WorkAndTurn' && (
          <Box sx={{ position: 'absolute', top: 5, right: 5, zIndex: 10 }}>
            <ToggleButtonGroup
              value={lado}
              exclusive
              onChange={handleLadoChange}
              size="small"
              aria-label="lado del pliego"
            >
              <ToggleButton value="cara" aria-label="cara">
                Cara (Anverso)
              </ToggleButton>
              <ToggleButton value="dorso" aria-label="dorso">
                Dorso (Reverso)
              </ToggleButton>
            </ToggleButtonGroup>
          </Box>
        )}
        
        {/* Mostramos mensaje informativo para esquemas tira-retira */}
        {(esTiraRetira || sheetTypeActual === 'WorkAndTurn') && (
          <Box sx={{ position: 'absolute', top: 5, left: 5, zIndex: 10 }}>
            <Typography variant="caption" sx={{ bgcolor: 'rgba(255,255,255,0.7)', px: 1, py: 0.5, borderRadius: 1 }}>
              {esTiraRetira ? 'TIRA-RETIRA' : sheetTypeActual} {esRotado ? '(Rotado 90°)' : ''}
            </Typography>
          </Box>
        )}
      </Box>
      
      <Box
        sx={{
          position: 'relative',
          width: anchoEscalado,
          height: altoEscalado,
          bgcolor: theme.palette.grey[100],
          border: `1px solid ${theme.palette.grey[400]}`,
          overflow: 'hidden',
        }}
      >
        {/* Área de pinzas */}
        {areaPinzas}
        
        {/* Medianiles entre páginas específicas */}
        {medianilsRenderizados}
        
        {/* Tira de control */}
        {tiraControl}
        
        {/* Marcas de registro */}
        {marcas}
        
        {/* Páginas */}
        {paginas}
      </Box>
      
      <Box sx={{ mt: 2, display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.grey[100], border: `1px solid ${theme.palette.grey[400]}`, mr: 1 }} />
          <Typography variant="caption">Pliego</Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.grey[200], border: `${Math.max(1, sangradoEscalado)}px solid ${theme.palette.warning.light}`, mr: 1 }} />
          <Typography variant="caption">Página con sangrado</Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, bgcolor: 'rgba(0, 128, 255, 0.2)', border: '1px dashed rgba(0, 128, 255, 0.5)', mr: 1 }} />
          <Typography variant="caption">Pinzas</Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.grey[300], border: `1px solid ${theme.palette.grey[500]}`, mr: 1 }} />
          <Typography variant="caption">Marcas/Tiras</Typography>
        </Box>
        
        {esEsquemaTipoF && (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ width: 16, height: 16, bgcolor: 'rgba(255, 165, 0, 0.2)', border: '1px dashed rgba(255, 165, 0, 0.5)', mr: 1 }} />
              <Typography variant="caption">Medianil (10 mm)</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ width: 16, height: 16, bgcolor: 'transparent', border: '1px solid rgba(0, 0, 0, 0.3)', mr: 1 }} />
              <Typography variant="caption">Lomo (0 mm)</Typography>
            </Box>
          </>
        )}
      </Box>
    </Paper>
  );
};

export default SheetPreview;
