import React from 'react';
import PropTypes from 'prop-types';
import {
  Grid,
  Paper,
  Box,
  Typography,
  Divider
} from '@mui/material';
import FinishingSelector from '../FinishingSelector';

/**
 * Componente para manejar la sección de acabados del presupuesto
 */
const FinishingSection = ({
  selectedProcesses,
  setSelectedProcesses,
  calculatedProcessCost,
  setCalculatedProcessCost,
  budget
}) => {
  return (
    <Grid item xs={12}>
      <Paper elevation={1} sx={{ mb: 2, overflow: 'hidden' }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 1,
            cursor: 'pointer',
            borderBottom: '1px solid #e0e0e0'
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
            Acabados
          </Typography>
        </Box>
        <Divider />
        <FinishingSelector
          selectedProcesses={selectedProcesses}
          setSelectedProcesses={setSelectedProcesses}
          calculatedProcessCost={calculatedProcessCost}
          setCalculatedProcessCost={setCalculatedProcessCost}
          budget={budget}
        />
      </Paper>
    </Grid>
  );
};

FinishingSection.propTypes = {
  selectedProcesses: PropTypes.array.isRequired,
  setSelectedProcesses: PropTypes.func.isRequired,
  calculatedProcessCost: PropTypes.number.isRequired,
  setCalculatedProcessCost: PropTypes.func.isRequired,
  budget: PropTypes.object.isRequired
};

export default FinishingSection;
