// No necesitamos importar React explícitamente en React 17+
// ya que JSX se transforma automáticamente
import PropTypes from 'prop-types';
import {
  Grid,
  Paper,
  Box,
  Typography,
  Button,
  Divider
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import BudgetPartForm from './BudgetPartForm';

/**
 * Componente para manejar la sección de partes del presupuesto
 */
const BudgetParts = ({
  budgetParts,
  setBudgetParts,
  papers,
  machines,
  processes,
  handleCalculateSheetsV2Part,
  handleCalculateSheetsV2Silent,
  handleMachineInfoClickPart,
  showSnackbar,
  calculatingSheets,
  createEmptyPart,
  isEditMode,
  budgetCopies
}) => {
  return (
    <Grid item xs={12}>
      <Paper elevation={1} sx={{ mb: 2, overflow: 'hidden' }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 1,
            cursor: 'pointer',
            borderBottom: '1px solid #e0e0e0'
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
            Impresión {budgetParts.length > 0 && `(${budgetParts.length} ${budgetParts.length === 1 ? 'parte' : 'partes'})`}
          </Typography>
          <Button
            variant="outlined"
            color="primary"
            size="small"
            startIcon={<AddIcon />}
            onClick={() => {
              const newPart = createEmptyPart(budgetParts.length, `Parte ${budgetParts.length + 1}`);
              setBudgetParts([...budgetParts, newPart]);
            }}
          >
            Añadir Parte
          </Button>
        </Box>
        <Divider />

        {/* Lista de partes */}
        {budgetParts.map((part, index) => (
          <BudgetPartForm
            key={part.part_id}
            part={{...part, budget_copies: budgetCopies}}
            index={index}
            papers={papers}
            machines={machines}
            processes={processes}
            onPartChange={(index, updatedPart) => {
              const newParts = [...budgetParts];
              newParts[index] = updatedPart;
              setBudgetParts(newParts);
            }}
            onDeletePart={Object.assign(
              (index) => {
                // No permitir eliminar la única parte
                if (budgetParts.length <= 1) {
                  showSnackbar('Debe haber al menos una parte en el presupuesto', 'warning');
                  return;
                }
                const newParts = budgetParts.filter((_, i) => i !== index);
                setBudgetParts(newParts);
              },
              { showSnackbar } // Añadir la función showSnackbar como propiedad
            )}
            onCalculateSheets={(index) => {
              // ELIMINADO: V1 - Usar V2 en su lugar
              handleCalculateSheetsV2Part(index);
            }}
            onCalculateSheetsV2={(index) => {
              // Implementar cálculo de pliegos V2 para esta parte
              handleCalculateSheetsV2Part(index);
            }}
            onCalculateSheetsV2Silent={(index) => {
              // Implementar cálculo de pliegos V2 sin mostrar modal
              handleCalculateSheetsV2Silent(index);
            }}
            onMachineInfoClick={(machine, index) => {
              // Mostrar información de la máquina para esta parte
              handleMachineInfoClickPart(machine, index);
            }}
            calculatingSheets={calculatingSheets}
            isEditMode={isEditMode}
          />
        ))}
      </Paper>
    </Grid>
  );
};

BudgetParts.propTypes = {
  budgetParts: PropTypes.array.isRequired,
  setBudgetParts: PropTypes.func.isRequired,
  papers: PropTypes.array,
  machines: PropTypes.array,
  processes: PropTypes.array,
  handleCalculateSheetsV2Part: PropTypes.func.isRequired,
  handleCalculateSheetsV2Silent: PropTypes.func.isRequired,
  handleMachineInfoClickPart: PropTypes.func.isRequired,
  showSnackbar: PropTypes.func.isRequired,
  calculatingSheets: PropTypes.bool,
  createEmptyPart: PropTypes.func.isRequired,
  isEditMode: PropTypes.bool,
  budgetCopies: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
};

export default BudgetParts;
