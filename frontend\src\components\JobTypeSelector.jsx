import { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography
} from '@mui/material';
import ProductService from '../services/ProductService';

/**
 * Componente para seleccionar el tipo de trabajo
 */
const JobTypeSelector = ({
  jobType,
  jobTypes: propJobTypes, // Renombrado para evitar conflictos
  onChange,
  disabled = false,
  useProductCatalog = true // Nueva prop para decidir si usar el catálogo de productos
}) => {
  const [loading, setLoading] = useState(false);
  const [catalogProducts, setCatalogProducts] = useState([]);

  // Cargar productos desde el catálogo
  useEffect(() => {
    if (useProductCatalog) {
      const fetchProducts = async () => {
        setLoading(true);
        try {
          // Obtener todos los productos activos, sin filtrar por tipo
          const products = await ProductService.getProductsForSelector(true, false);
          setCatalogProducts(products);
        } catch (error) {
          console.error('JobTypeSelector: Error al cargar productos del catálogo:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchProducts();
    }
  }, [useProductCatalog]);

  // Función para obtener el producto por tipo
  const getProductByType = useCallback(async (type) => {
    if (!useProductCatalog || !type) return null;

    // Primero buscar en los productos ya cargados
    const localProduct = catalogProducts.find(p => p.type === type);
    if (localProduct) {
      return localProduct;
    }

    // Si no hay productos cargados localmente, esperar a que se carguen
    if (catalogProducts.length === 0 && loading) {
      return null;
    }

    // Si no se encuentra, intentar obtenerlo de la API
    try {
      const apiProduct = await ProductService.getProductByType(type);
      return apiProduct;
    } catch (error) {
      // Error silencioso
      return null;
    }
  }, [useProductCatalog, catalogProducts, loading]);

  // Efecto para cargar información del producto cuando cambia el tipo de trabajo
  useEffect(() => {
    if (!jobType) return;

    const loadProductInfo = async () => {
      try {
        // Obtener el producto seleccionado
        await getProductByType(jobType);

        // Aquí podríamos hacer otras operaciones con el producto si fuera necesario
      } catch (error) {
        // Error silencioso
      }
    };

    loadProductInfo();
  }, [jobType, getProductByType]);

  // Convertir productos a opciones para el selector
  const jobOptions = useProductCatalog && catalogProducts.length > 0
    ? catalogProducts.map(product => ({
        value: product.type, // Usar type como valor para mantener compatibilidad con presupuestos existentes
        label: `${product.name} (${product.type})`,
        product: product,
        id: product.product_id // Añadir ID único para usar como clave
      }))
    : propJobTypes ? propJobTypes.map((type, index) => ({
        value: type,
        label: type,
        id: `type-${index}` // Añadir ID único para tipos de trabajo sin producto
      })) : [];

  // Mostrar indicador de carga si es necesario
  if (useProductCatalog && loading && jobOptions.length === 0) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '56px' }}>
        <Typography>Cargando tipos de trabajo...</Typography>
      </Box>
    );
  }

  // Encontrar la opción seleccionada para mostrar su etiqueta
  const selectedOption = jobOptions.find(option => option.value === jobType);
  const displayLabel = selectedOption ? selectedOption.label : jobType;

  return (
    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
      <FormControl fullWidth required>
        <InputLabel id="job-type-label">Tipo de trabajo</InputLabel>
        <Select
          labelId="job-type-label"
          name="jobType"
          value={jobType || ''}
          onChange={onChange}
          label="Tipo de trabajo"
          disabled={disabled}
          renderValue={() => (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography>
                {displayLabel}
              </Typography>
            </Box>
          )}
        >
          {jobOptions.map((option) => (
            <MenuItem key={option.id} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

// Validación de props
JobTypeSelector.propTypes = {
  jobType: PropTypes.string,
  jobTypes: PropTypes.arrayOf(PropTypes.string),
  onChange: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  useProductCatalog: PropTypes.bool
};

// Props por defecto
JobTypeSelector.defaultProps = {
  jobType: '',
  jobTypes: [],
  disabled: false,
  useProductCatalog: true
};

export default JobTypeSelector;
