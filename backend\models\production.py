from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
import uuid
from datetime import datetime, timedelta

# Enumeración para tipos de procesos de producción
class ProductionProcessType(str, Enum):
    PRINTING = "Impresión"
    FINISHING = "Acabado"
    HANDLING = "Manipulado"
    SHIPPING = "Envío"

# Enumeración para estados de procesos de producción
class ProductionStatus(str, Enum):
    PENDING = "Pendiente"
    IN_PROGRESS = "En Proceso"
    COMPLETED = "Completado"
    DELAYED = "Retrasado"
    CANCELLED = "Cancelado"

    # Alias para compatibilidad con código existente
    COMPLETADO = "Completado"

# Enumeración para estados de OT
class OTStatus(str, Enum):
    PENDING = "Pendiente"
    IN_PROGRESS = "En Proceso"
    COMPLETED = "Completado"
    SHIPPED = "Enviado"
    CANCELLED = "Cancelado"

# Modelo para un proceso de producción (nueva versión)
class ProductionProcessV2(BaseModel):
    process_id: str = Field(default_factory=lambda: f"PROD-{uuid.uuid4().hex[:6].upper()}")
    process_type: ProductionProcessType  # Tipo de proceso
    name: str  # Nombre del proceso
    description: str  # Descripción detallada
    quantity: int  # Cantidad a producir
    status: ProductionStatus = ProductionStatus.PENDING  # Estado actual
    start_date: str  # Fecha de inicio planificada
    end_date: str  # Fecha de finalización planificada
    actual_start_date: Optional[str] = None  # Fecha real de inicio
    actual_end_date: Optional[str] = None  # Fecha real de finalización
    estimated_hours: float  # Horas estimadas para completar el proceso
    machine_id: Optional[str] = None  # ID de la máquina asignada (si aplica)
    operator_id: Optional[str] = None  # ID del operador asignado (si aplica)
    notes: Optional[str] = None  # Notas adicionales
    dependencies: Optional[List[str]] = []  # IDs de procesos que deben completarse antes
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())

# Modelo para una Orden de Trabajo (OT)
class ProductionOrder(BaseModel):
    ot_id: str  # Número de OT (ej: OT-25042158)
    budget_id: str  # ID del presupuesto asociado
    client_id: str  # ID del cliente
    approval_date: str  # Fecha de aprobación del presupuesto (cuando se crea la OT)
    status: OTStatus = OTStatus.PENDING  # Estado general de la OT
    processes: List[ProductionProcessV2] = []  # Lista de procesos asociados a esta OT
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())

# Modelo para un proceso de producción (versión original para compatibilidad)
class ProductionProcess(BaseModel):
    process_id: str = Field(default_factory=lambda: f"PROD-{uuid.uuid4().hex[:6].upper()}")
    budget_id: str  # ID del presupuesto asociado
    ot_number: str  # Número de OT
    client_id: str  # ID del cliente
    process_type: ProductionProcessType  # Tipo de proceso
    name: str  # Nombre del proceso
    description: str  # Descripción detallada
    quantity: int  # Cantidad a producir
    status: ProductionStatus = ProductionStatus.PENDING  # Estado actual
    start_date: str  # Fecha de inicio planificada
    end_date: str  # Fecha de finalización planificada
    actual_start_date: Optional[str] = None  # Fecha real de inicio
    actual_end_date: Optional[str] = None  # Fecha real de finalización
    estimated_hours: float  # Horas estimadas para completar el proceso
    machine_id: Optional[str] = None  # ID de la máquina asignada (si aplica)
    operator_id: Optional[str] = None  # ID del operador asignado (si aplica)
    notes: Optional[str] = None  # Notas adicionales
    dependencies: Optional[List[str]] = []  # IDs de procesos que deben completarse antes
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())

# Modelo para crear un nuevo proceso de producción (versión original para compatibilidad)
class ProductionProcessCreate(BaseModel):
    budget_id: str
    ot_number: str
    client_id: str
    process_type: ProductionProcessType
    name: str
    description: str
    quantity: int
    estimated_hours: float
    machine_id: Optional[str] = None
    dependencies: Optional[List[str]] = []
    notes: Optional[str] = None

# Modelo para actualizar un proceso de producción (versión original para compatibilidad)
class ProductionProcessUpdate(BaseModel):
    status: Optional[ProductionStatus] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    actual_start_date: Optional[str] = None
    actual_end_date: Optional[str] = None
    estimated_hours: Optional[float] = None
    machine_id: Optional[str] = None
    operator_id: Optional[str] = None
    notes: Optional[str] = None
    dependencies: Optional[List[str]] = None

# Modelo para crear un nuevo proceso de producción (nueva versión)
class ProductionProcessV2Create(BaseModel):
    process_type: ProductionProcessType
    name: str
    description: str
    quantity: int
    estimated_hours: float
    machine_id: Optional[str] = None
    dependencies: Optional[List[str]] = []
    notes: Optional[str] = None

# Modelo para actualizar un proceso de producción (nueva versión)
class ProductionProcessV2Update(BaseModel):
    status: Optional[ProductionStatus] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    actual_start_date: Optional[str] = None
    actual_end_date: Optional[str] = None
    estimated_hours: Optional[float] = None
    machine_id: Optional[str] = None
    operator_id: Optional[str] = None
    notes: Optional[str] = None
    dependencies: Optional[List[str]] = None

# Modelo para crear una nueva Orden de Trabajo (OT)
class ProductionOrderCreate(BaseModel):
    ot_id: str
    budget_id: str
    client_id: str
    approval_date: str
    processes: List[ProductionProcessV2Create] = []

# Modelo para actualizar una Orden de Trabajo (OT)
class ProductionOrderUpdate(BaseModel):
    status: Optional[OTStatus] = None
    processes: Optional[List[ProductionProcessV2]] = None
