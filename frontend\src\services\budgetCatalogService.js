/**
 * Servicio especializado para la gestión de catálogos en BudgetForm
 * Extrae las funciones de fetch de clientes, papeles, máquinas y procesos del BudgetForm
 */

/**
 * Función genérica para realizar fetch de catálogos
 * @param {string} endpoint - Endpoint de la API
 * @param {Function} filterFn - Función de filtrado
 * @param {string} errorMessage - Mensaje de error personalizado
 * @param {Function} buildApiUrl - Función para construir URLs
 * @returns {Promise<Array>} - Array de elementos filtrados
 */
export const fetchCatalogData = async (endpoint, filterFn, errorMessage, buildApiUrl) => {
  const apiUrl = buildApiUrl(endpoint);
  const response = await fetch(apiUrl, {
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(errorMessage);
  }
  
  const data = await response.json();
  return filterFn ? data.filter(filterFn) : data;
};

/**
 * Filtros predefinidos para cada tipo de catálogo
 */
export const catalogFilters = {
  clients: (client) => client.active,
  papers: (paper) => paper.inStock,
  machines: (machine) => 
    machine.status === 'Activa' && 
    (machine.type === 'Offset' || machine.type === 'Digital'),
  processes: (process) => 
    process.type && 
    ['Corte', 'Plegado', 'Encuadernación', 'Acabado', 'Barnizado', 'Laminado', 'Troquelado'].includes(process.type)
};

/**
 * Configuración de endpoints y mensajes de error
 */
export const catalogConfig = {
  clients: {
    endpoint: '/clients/',
    filter: catalogFilters.clients,
    errorMessage: 'Error al obtener los clientes'
  },
  papers: {
    endpoint: '/papers/',
    filter: catalogFilters.papers,
    errorMessage: 'Error al obtener los papeles'
  },
  machines: {
    endpoint: '/machines/',
    filter: catalogFilters.machines,
    errorMessage: 'Error al obtener las máquinas'
  },
  processes: {
    endpoint: '/processes/',
    filter: catalogFilters.processes,
    errorMessage: 'Error al obtener los procesos'
  }
};

/**
 * Obtiene clientes activos del catálogo
 * @param {Function} buildApiUrl - Función para construir URLs
 * @returns {Promise<Array>} - Array de clientes activos
 */
export const fetchClients = async (buildApiUrl) => {
  const config = catalogConfig.clients;
  return await fetchCatalogData(config.endpoint, config.filter, config.errorMessage, buildApiUrl);
};

/**
 * Obtiene papeles en stock del catálogo
 * @param {Function} buildApiUrl - Función para construir URLs
 * @returns {Promise<Array>} - Array de papeles en stock
 */
export const fetchPapers = async (buildApiUrl) => {
  const config = catalogConfig.papers;
  return await fetchCatalogData(config.endpoint, config.filter, config.errorMessage, buildApiUrl);
};

/**
 * Obtiene máquinas activas (Offset y Digital) del catálogo
 * @param {Function} buildApiUrl - Función para construir URLs
 * @returns {Promise<Array>} - Array de máquinas activas
 */
export const fetchMachines = async (buildApiUrl) => {
  const config = catalogConfig.machines;
  return await fetchCatalogData(config.endpoint, config.filter, config.errorMessage, buildApiUrl);
};

/**
 * Obtiene procesos de acabado del catálogo
 * @param {Function} buildApiUrl - Función para construir URLs
 * @returns {Promise<Array>} - Array de procesos de acabado
 */
export const fetchProcesses = async (buildApiUrl) => {
  const config = catalogConfig.processes;
  return await fetchCatalogData(config.endpoint, config.filter, config.errorMessage, buildApiUrl);
};

/**
 * Carga todos los catálogos en paralelo
 * @param {Function} buildApiUrl - Función para construir URLs
 * @param {Function} showSnackbar - Función para mostrar mensajes
 * @returns {Promise<Object>} - Objeto con todos los catálogos cargados
 */
export const loadAllCatalogs = async (buildApiUrl, showSnackbar) => {
  try {
    const [clients, papers, machines, processes] = await Promise.all([
      fetchClients(buildApiUrl).catch(err => {
        console.error('Error al obtener clientes:', err);
        showSnackbar(err.message, 'error');
        return [];
      }),
      fetchPapers(buildApiUrl).catch(err => {
        console.error('Error al obtener papeles:', err);
        showSnackbar(err.message, 'error');
        return [];
      }),
      fetchMachines(buildApiUrl).catch(err => {
        console.error('Error al obtener máquinas:', err);
        showSnackbar(err.message, 'error');
        return [];
      }),
      fetchProcesses(buildApiUrl).catch(err => {
        console.error('Error al obtener procesos:', err);
        showSnackbar(err.message, 'error');
        return [];
      })
    ]);

    return {
      clients,
      papers,
      machines,
      processes
    };
  } catch (error) {
    console.error('Error al cargar catálogos:', error);
    showSnackbar('Error al cargar los catálogos', 'error');
    return {
      clients: [],
      papers: [],
      machines: [],
      processes: []
    };
  }
};

/**
 * Hook personalizado para gestionar catálogos con referencias de carga
 * @param {Object} params - Parámetros para la gestión de catálogos
 * @returns {Object} - Funciones y estados para gestionar catálogos
 */
export const createCatalogManager = (params) => {
  const {
    buildApiUrl,
    showSnackbar,
    setClients,
    setPapers,
    setMachines,
    setProcesses,
    fetchedClientsRef,
    fetchedPapersRef,
    fetchedMachinesRef,
    fetchedProcessesRef
  } = params;

  /**
   * Carga un catálogo específico si no ha sido cargado previamente
   * @param {string} catalogType - Tipo de catálogo (clients, papers, machines, processes)
   * @returns {Promise<void>}
   */
  const loadCatalogIfNeeded = async (catalogType) => {
    const catalogMap = {
      clients: {
        ref: fetchedClientsRef,
        setter: setClients,
        fetcher: fetchClients
      },
      papers: {
        ref: fetchedPapersRef,
        setter: setPapers,
        fetcher: fetchPapers
      },
      machines: {
        ref: fetchedMachinesRef,
        setter: setMachines,
        fetcher: fetchMachines
      },
      processes: {
        ref: fetchedProcessesRef,
        setter: setProcesses,
        fetcher: fetchProcesses
      }
    };

    const catalog = catalogMap[catalogType];
    if (!catalog) {
      throw new Error(`Tipo de catálogo desconocido: ${catalogType}`);
    }

    if (!catalog.ref.current) {
      try {
        const data = await catalog.fetcher(buildApiUrl);
        catalog.setter(data);
        catalog.ref.current = true;
      } catch (error) {
        console.error(`Error al cargar ${catalogType}:`, error);
        showSnackbar(error.message, 'error');
      }
    }
  };

  /**
   * Carga todos los catálogos necesarios en paralelo
   * @returns {Promise<void>}
   */
  const loadInitialData = async () => {
    const loadPromises = [];

    if (!fetchedClientsRef.current) {
      loadPromises.push(loadCatalogIfNeeded('clients'));
    }

    if (!fetchedPapersRef.current) {
      loadPromises.push(loadCatalogIfNeeded('papers'));
    }

    if (!fetchedMachinesRef.current) {
      loadPromises.push(loadCatalogIfNeeded('machines'));
    }

    if (!fetchedProcessesRef.current) {
      loadPromises.push(loadCatalogIfNeeded('processes'));
    }

    if (loadPromises.length > 0) {
      await Promise.all(loadPromises);
    }
  };

  /**
   * Limpia las referencias de carga
   */
  const cleanup = () => {
    fetchedClientsRef.current = false;
    fetchedPapersRef.current = false;
    fetchedMachinesRef.current = false;
    fetchedProcessesRef.current = false;
  };

  /**
   * Recarga un catálogo específico forzando la actualización
   * @param {string} catalogType - Tipo de catálogo a recargar
   * @returns {Promise<void>}
   */
  const reloadCatalog = async (catalogType) => {
    const catalogMap = {
      clients: { ref: fetchedClientsRef },
      papers: { ref: fetchedPapersRef },
      machines: { ref: fetchedMachinesRef },
      processes: { ref: fetchedProcessesRef }
    };

    const catalog = catalogMap[catalogType];
    if (catalog) {
      catalog.ref.current = false;
      await loadCatalogIfNeeded(catalogType);
    }
  };

  return {
    loadInitialData,
    loadCatalogIfNeeded,
    cleanup,
    reloadCatalog
  };
};

/**
 * Obtiene estadísticas de los catálogos cargados
 * @param {Object} catalogs - Objeto con los catálogos
 * @returns {Object} - Estadísticas de los catálogos
 */
export const getCatalogStats = (catalogs) => {
  const { clients = [], papers = [], machines = [], processes = [] } = catalogs;
  
  return {
    clients: {
      total: clients.length,
      active: clients.filter(catalogFilters.clients).length
    },
    papers: {
      total: papers.length,
      inStock: papers.filter(catalogFilters.papers).length
    },
    machines: {
      total: machines.length,
      active: machines.filter(catalogFilters.machines).length,
      offset: machines.filter(m => m.type === 'Offset' && m.status === 'Activa').length,
      digital: machines.filter(m => m.type === 'Digital' && m.status === 'Activa').length
    },
    processes: {
      total: processes.length,
      finishing: processes.filter(catalogFilters.processes).length
    }
  };
};

export default {
  fetchClients,
  fetchPapers,
  fetchMachines,
  fetchProcesses,
  loadAllCatalogs,
  createCatalogManager,
  getCatalogStats,
  fetchCatalogData,
  catalogFilters,
  catalogConfig
};
