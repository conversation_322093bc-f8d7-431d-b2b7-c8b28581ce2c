import { useState, useEffect, useMemo } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  CircularProgress,
  Alert,
  AlertTitle,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Tooltip,
  TextField
} from '@mui/material';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/es';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import withDragAndDrop from 'react-big-calendar/lib/addons/dragAndDrop';
import 'react-big-calendar/lib/addons/dragAndDrop/styles.css';
import PropTypes from 'prop-types';
import { reorganizeProcesses } from '../../utils/offsetUtils';
import { buildApiUrl } from '../../config';
import { ApiInterceptor } from '../../services/simplifiedServices';
import { format } from 'date-fns';

// Configurar el localizador para el calendario
moment.locale('es');
const localizer = momentLocalizer(moment);
const DragAndDropCalendar = withDragAndDrop(Calendar);

// No usamos un ID de máquina fijo, sino que mostramos todos los procesos de acabado

// Colores para los diferentes estados
const STATUS_COLORS = {
  'Pendiente': '#ff9800',  // Naranja
  'En Proceso': '#2196f3', // Azul
  'Completado': '#4caf50', // Verde
  'Cancelado': '#f44336'   // Rojo
};

/**
 * Componente para la planificación de trabajos de acabados
 */
const FinishingPlanner = () => {
  // Estados básicos
  const [processes, setProcesses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedProcess, setSelectedProcess] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [successMessage, setSuccessMessage] = useState(null);

  // Estados para varios filtros y vistas
  const [view, setView] = useState('week'); // 'day', 'week', 'work_week'
  const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'Pendiente', 'En Proceso', etc.

  // Cargar procesos de producción
  useEffect(() => {
    fetchEvents();
  }, []);

  // Función para obtener los procesos desde la API
  const fetchEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      const url = buildApiUrl('/production/');

      console.log('Obteniendo procesos desde:', url);

      const response = await ApiInterceptor.fetch(url);
      if (!response.ok) {
        throw new Error(`Error al obtener procesos: ${response.statusText}`);
      }

      const data = await response.json();

      // Filtrar procesos de acabados
      const finishingProcesses = data.filter(process => {
        // Verificar todos los posibles tipos de acabado
        const isFinishing =
          process.process_type === 'Acabado' ||
          process.process_type === 'Finishing' ||
          process.process_type === 'Corte' ||
          process.process_type === 'Plegado' ||
          process.process_type === 'Encuadernación' ||
          process.process_type === 'Barnizado' ||
          process.process_type === 'Laminado' ||
          process.process_type === 'Troquelado' ||
          (process.name && process.name.toLowerCase().includes('acaba')) ||
          (process.description && process.description.toLowerCase().includes('acaba'));

        return isFinishing;
      });

      console.log(`Encontrados ${finishingProcesses.length} procesos de acabados:`, finishingProcesses);

      setProcesses(finishingProcesses);
      setSuccessMessage(`Se han cargado ${finishingProcesses.length} trabajos de acabados`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      console.error('Error al cargar procesos:', err);
      setError(`Error al cargar los procesos: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Convertir procesos a eventos para el calendario
  const calendarEvents = useMemo(() => {
    if (!processes.length) return [];

    console.log('Convirtiendo procesos a eventos de calendario');

    return processes.map(process => {
      // Determinar color según estado
      const backgroundColor = STATUS_COLORS[process.status] || '#757575';

      return {
        id: process.process_id,
        title: `${process.ot_number} - ${process.name}`,
        start: new Date(process.start_date),
        end: new Date(process.end_date),
        allDay: false,
        resource: process,
        backgroundColor
      };
    });
  }, [processes]);

  // Filtrar eventos según el estado seleccionado
  const filteredEvents = useMemo(() => {
    if (filterStatus === 'all') {
      return calendarEvents;
    }

    return calendarEvents.filter(event =>
      event.resource.status === filterStatus
    );
  }, [calendarEvents, filterStatus]);

  // Función para verificar si un evento se solapa con otros
  const checkForOverlap = (eventId, start, end) => {
    // Obtener la máquina del evento actual
    const currentEvent = filteredEvents.find(event => event.id === eventId);
    if (!currentEvent || !currentEvent.resource.machine_id) {
      return false; // Si no tiene máquina asignada, no puede haber solapamiento
    }

    const currentMachineId = currentEvent.resource.machine_id;
    console.log('Verificando solapamientos para máquina:', currentMachineId);
    console.log('Eventos antes de filtrar por máquina:', filteredEvents);

    // Filtrar eventos de la misma máquina, excluyendo el evento actual
    const machineEvents = filteredEvents.filter(event => {
      console.log('Evento:', event.id, 'Machine ID:', event.resource.machine_id);
      return event.id !== eventId && event.resource.machine_id === currentMachineId;
    });

    // Verificar solapamientos
    for (const event of machineEvents) {
      // Si el inicio del nuevo evento está entre el inicio y fin de un evento existente
      if ((start >= event.start && start < event.end) ||
          // O si el fin del nuevo evento está entre el inicio y fin de un evento existente
          (end > event.start && end <= event.end) ||
          // O si el nuevo evento engloba completamente a un evento existente
          (start <= event.start && end >= event.end)) {
        return true; // Hay solapamiento
      }
    }

    return false; // No hay solapamiento
  };

  // Función para manejar el cambio de fecha/vista en el calendario
  const handleNavigate = (date) => {
    console.log('Navegando a fecha:', date);
    setSelectedDate(date);
  };

  // Función para manejar el cambio de vista en el calendario
  const handleViewChange = (newView) => {
    console.log('Cambiando a vista:', newView);
    setView(newView);
  };

  // Función para manejar el clic en un evento (abrir detalles)
  const handleSelectEvent = (event) => {
    console.log('Evento seleccionado:', event);
    setSelectedProcess(event.resource);
    setIsEditModalOpen(true);
  };

  // Función para guardar cambios en un proceso
  const handleSaveProcess = async () => {
    try {
      setLoading(true);

      // Preparar datos para actualizar
      const updateData = {
        status: selectedProcess.status,
        start_date: selectedProcess.start_date,
        end_date: selectedProcess.end_date,
        estimated_hours: selectedProcess.estimated_hours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${selectedProcess.process_id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === selectedProcess.process_id ? { ...p, ...updateData } : p
        )
      );

      setSuccessMessage(`Proceso ${selectedProcess.name} actualizado correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);

      // Cerrar modal
      setIsEditModalOpen(false);
      setSelectedProcess(null);
    } catch (err) {
      console.error('Error al guardar proceso:', err);
      setError(`Error al guardar los cambios: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para cambiar el estado de un proceso
  const handleStatusChange = (e) => {
    setSelectedProcess({
      ...selectedProcess,
      status: e.target.value
    });
  };

  // Función para cambiar duración estimada
  const handleDurationChange = (e) => {
    const hours = parseFloat(e.target.value);
    if (isNaN(hours) || hours <= 0) return;

    // Actualizar horas estimadas
    setSelectedProcess({
      ...selectedProcess,
      estimated_hours: hours,

      // También actualizar fecha de fin basada en nueva duración
      end_date: (() => {
        const start = new Date(selectedProcess.start_date);
        const end = new Date(start);
        end.setTime(start.getTime() + (hours * 60 * 60 * 1000));
        return end.toISOString();
      })()
    });
  };

  // Función para manejar el movimiento de un evento
  const handleEventDrop = async ({ event, start, end }) => {
    try {
      console.log('Evento movido:', event);
      console.log('Nueva fecha de inicio:', start);
      console.log('Nueva fecha de fin:', end);

      // Verificar si el proceso está completado
      if (event.resource.status === 'Completado') {
        setError('No se pueden mover procesos completados');
        return;
      }

      // Verificar que los procesos "En Proceso" no se coloquen antes de la fecha actual
      if (event.resource.status === 'En Proceso') {
        const now = new Date();
        if (start < now) {
          setError('No se pueden programar procesos "En Proceso" antes de la fecha y hora actual');
          return;
        }
      }

      // Verificar solapamientos
      if (checkForOverlap(event.id, start, end)) {
        setError('No se puede mover el evento a esta posición porque se solapa con otro proceso');
        return;
      }

      setLoading(true);

      // Calcular la duración en horas
      const durationMs = end - start;
      const durationHours = durationMs / (1000 * 60 * 60);

      // Preparar datos para actualizar
      const updateData = {
        start_date: start.toISOString(),
        end_date: end.toISOString(),
        estimated_hours: durationHours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${event.id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === event.id ? {
            ...p,
            start_date: start.toISOString(),
            end_date: end.toISOString(),
            estimated_hours: durationHours
          } : p
        )
      );

      setSuccessMessage(`Proceso ${event.title} actualizado correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      console.error('Error al mover evento:', err);
      setError(`Error al actualizar el proceso: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para manejar el redimensionamiento de un evento
  const handleEventResize = async ({ event, start, end }) => {
    try {
      console.log('Evento redimensionado:', event);
      console.log('Nueva fecha de inicio:', start);
      console.log('Nueva fecha de fin:', end);

      // Verificar si el proceso está completado
      if (event.resource.status === 'Completado') {
        setError('No se pueden modificar procesos completados');
        return;
      }

      // Verificar que los procesos "En Proceso" no se redimensionen antes de la fecha actual
      if (event.resource.status === 'En Proceso') {
        const now = new Date();
        if (start < now || end < now) {
          setError('No se pueden redimensionar procesos "En Proceso" antes de la fecha y hora actual');
          return;
        }
      }

      // Verificar solapamientos
      if (checkForOverlap(event.id, start, end)) {
        setError('No se puede redimensionar el evento a esta posición porque se solapa con otro proceso');
        return;
      }

      setLoading(true);

      // Calcular la duración en horas
      const durationMs = end - start;
      const durationHours = durationMs / (1000 * 60 * 60);

      // Preparar datos para actualizar
      const updateData = {
        start_date: start.toISOString(),
        end_date: end.toISOString(),
        estimated_hours: durationHours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${event.id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === event.id ? {
            ...p,
            start_date: start.toISOString(),
            end_date: end.toISOString(),
            estimated_hours: durationHours
          } : p
        )
      );

      setSuccessMessage(`Duración de ${event.title} actualizada correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      console.error('Error al redimensionar evento:', err);
      setError(`Error al actualizar la duración: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para manejar la reorganización automática de procesos
  const handleReorganizeProcesses = async () => {
    try {
      setLoading(true);

      // Limpiar mensajes anteriores
      setError(null);
      setSuccessMessage(null);

      // Obtener la hora actual
      const now = new Date();

      // Reorganizar procesos (pasamos null como machineId para que reorganice todos los procesos de acabado)
      const reorganized = await reorganizeProcesses(processes, null, now);

      if (reorganized.length === 0) {
        setSuccessMessage('No hay procesos pendientes o en proceso para mover');
        setLoading(false);

        // Limpiar mensaje después de 5 segundos
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);

        return;
      }

      console.log(`Reorganizando ${reorganized.length} procesos desde ${format(now, 'yyyy-MM-dd HH:mm')}`);

      // Actualizar cada proceso en el backend
      const updatePromises = reorganized.map(async (process) => {
        // Solo actualizar si las fechas han cambiado
        const originalProcess = processes.find(p => p.process_id === process.process_id);
        if (!originalProcess) {
          console.warn(`No se encontró el proceso original con ID ${process.process_id}`);
          return process; // Devolver el proceso sin cambios
        }

        // Verificar si las fechas han cambiado
        if (originalProcess.start_date === process.start_date &&
            originalProcess.end_date === process.end_date) {
          console.log(`El proceso ${process.process_id} no ha cambiado, no se actualiza`);
          return process; // No hay cambios, devolver el proceso original
        }

        // Preparar solo los datos que necesitamos actualizar
        const updateData = {
          start_date: process.start_date,
          end_date: process.end_date,
          estimated_hours: process.estimated_hours
        };

        // Asegurarse de que el ID sea correcto
        const processId = process.process_id;
        console.log(`Actualizando proceso con ID: ${processId}`);

        const url = buildApiUrl(`/production/${processId}`);

        try {
          const response = await ApiInterceptor.fetch(url, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
          });

          if (!response.ok) {
            throw new Error(`Error al actualizar proceso ${processId}: ${response.statusText}`);
          }

          return await response.json();
        } catch (error) {
          console.error(`Error al actualizar proceso ${processId}:`, error);
          throw error;
        }
      });

      try {
        // Esperar a que todas las actualizaciones se completen
        const updatedProcesses = await Promise.all(updatePromises);

        // Actualizar la lista local de procesos
        setProcesses(prevProcesses => {
          const newProcesses = [...prevProcesses];

          // Actualizar cada proceso modificado
          updatedProcesses.forEach(updatedProcess => {
            if (!updatedProcess) return; // Ignorar procesos nulos

            const index = newProcesses.findIndex(p => p.process_id === updatedProcess.process_id);
            if (index !== -1) {
              newProcesses[index] = updatedProcess;
            }
          });

          return newProcesses;
        });

        setSuccessMessage(`Se han movido ${reorganized.length} procesos al siguiente día laborable`);

        // Limpiar mensaje después de 5 segundos
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);
      } catch (error) {
        console.error('Error al procesar las actualizaciones:', error);
        setError(`Error al reorganizar procesos: ${error.message}`);

        // Limpiar mensaje de error después de 10 segundos
        setTimeout(() => {
          setError(null);
        }, 10000);
      }
    } catch (err) {
      console.error('Error al reorganizar procesos:', err);
      setError(`Error al reorganizar procesos: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Personalizar la apariencia de los eventos en el calendario
  const eventPropGetter = (event) => {
    return {
      style: {
        backgroundColor: STATUS_COLORS[event.resource.status] || '#757575',
        borderRadius: '4px',
        opacity: 0.9,
        color: '#fff',
        border: '0px',
        display: 'block'
      }
    };
  };

  // Personalizar el formato de la hora en el calendario
  const formats = {
    timeGutterFormat: 'HH:mm',
    eventTimeRangeFormat: ({ start, end }) => {
      return `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`;
    }
  };

  // Componente personalizado para mostrar eventos en el calendario
  const CustomEvent = ({ event }) => {
    return (
      <Box sx={{ height: '100%', overflow: 'hidden', p: 0.5 }}>
        <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
          {event.resource && event.resource.ot_number ? `OT: ${event.resource.ot_number}` : 'Sin OT'}
        </Typography>
        <Typography variant="caption" sx={{ display: 'block' }}>
          {event.resource && event.resource.name ? event.resource.name : 'Sin nombre'}
        </Typography>
      </Box>
    );
  };

  // Validación de propiedades para CustomEvent
  CustomEvent.propTypes = {
    event: PropTypes.shape({
      resource: PropTypes.shape({
        ot_number: PropTypes.string,
        name: PropTypes.string
      })
    }).isRequired
  };

  return (
    <Container maxWidth="xl">
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Planificación de Acabados
          </Typography>

          <Box>
            <FormControl size="small" sx={{ minWidth: 150, mr: 2 }}>
              <InputLabel>Estado</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Estado"
              >
                <MenuItem value="all">Todos</MenuItem>
                <MenuItem value="Pendiente">Pendiente</MenuItem>
                <MenuItem value="En Proceso">En Proceso</MenuItem>
                <MenuItem value="Completado">Completado</MenuItem>
                <MenuItem value="Cancelado">Cancelado</MenuItem>
              </Select>
            </FormControl>

            <Tooltip title="Mueve TODOS los trabajos pendientes y en proceso al siguiente día laborable, incluso los que ya han comenzado">
              <span>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleReorganizeProcesses}
                  disabled={loading}
                  sx={{ mr: 1 }}
                >
                  Reorganizar
                </Button>
              </span>
            </Tooltip>
          </Box>
        </Box>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
            <CircularProgress />
          </Box>
        )}

        <Box sx={{ height: 'calc(100vh - 250px)', minHeight: '500px' }}>
          <DragAndDropCalendar
            localizer={localizer}
            events={filteredEvents}
            startAccessor="start"
            endAccessor="end"
            view={view}
            views={['day', 'week', 'work_week']}
            step={30}
            timeslots={2}
            date={selectedDate}
            onNavigate={handleNavigate}
            onView={handleViewChange}
            onSelectEvent={handleSelectEvent}
            onEventDrop={handleEventDrop}
            onEventResize={handleEventResize}
            resizable
            selectable
            eventPropGetter={eventPropGetter}
            formats={formats}
            components={{
              event: CustomEvent
            }}
            min={new Date(0, 0, 0, 8, 0)} // 8:00 AM
            max={new Date(0, 0, 0, 20, 0)} // 8:00 PM
          />
        </Box>

        <Box sx={{ position: 'fixed', bottom: 20, left: 0, right: 0, zIndex: 1000, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2, width: '80%', maxWidth: '800px' }} onClose={() => setError(null)}>
              <AlertTitle>Error</AlertTitle>
              {error}
            </Alert>
          )}

          {successMessage && (
            <Alert severity="success" sx={{ mb: 2, width: '80%', maxWidth: '800px' }} onClose={() => setSuccessMessage(null)}>
              <AlertTitle>Éxito</AlertTitle>
              {successMessage}
            </Alert>
          )}
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography variant="h6" component="h2" sx={{ mb: 1 }}>
            Leyenda
          </Typography>
          <Stack direction="row" spacing={2}>
            {Object.entries(STATUS_COLORS).map(([status, color]) => (
              <Box key={status} sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{ width: 16, height: 16, backgroundColor: color, mr: 1, borderRadius: '2px' }} />
                <Typography variant="body2">{status}</Typography>
              </Box>
            ))}
          </Stack>
        </Box>
      </Paper>

      {/* Modal de edición de proceso */}
      <Dialog open={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} maxWidth="sm" fullWidth>
        {selectedProcess && (
          <>
            <DialogTitle>
              Editar Proceso de Acabado
            </DialogTitle>
            <DialogContent>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1" component="div" sx={{ fontWeight: 'bold' }}>
                  OT-{selectedProcess.ot_number}
                </Typography>
                <Typography variant="h6" component="div">
                  {selectedProcess.name}
                </Typography>
              </Box>

              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Estado</InputLabel>
                <Select
                  value={selectedProcess.status}
                  onChange={handleStatusChange}
                  label="Estado"
                >
                  <MenuItem value="Pendiente">Pendiente</MenuItem>
                  <MenuItem value="En Proceso">En Proceso</MenuItem>
                  <MenuItem value="Completado">Completado</MenuItem>
                  <MenuItem value="Cancelado">Cancelado</MenuItem>
                </Select>
              </FormControl>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2">Fechas:</Typography>
                <Typography variant="body2">
                  Inicio: {moment(new Date(selectedProcess.start_date)).format('DD/MM/YYYY HH:mm')}
                </Typography>
                <Typography variant="body2">
                  Fin: {moment(new Date(selectedProcess.end_date)).format('DD/MM/YYYY HH:mm')}
                </Typography>
              </Box>

              <TextField
                label="Duración estimada (horas)"
                type="number"
                value={selectedProcess.estimated_hours}
                onChange={handleDurationChange}
                fullWidth
                sx={{ mb: 2 }}
                InputProps={{
                  inputProps: { min: 0.25, step: 0.25 }
                }}
              />

              {selectedProcess.description && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">Descripción:</Typography>
                  <Typography variant="body2">
                    {selectedProcess.description}
                  </Typography>
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setIsEditModalOpen(false)}>Cancelar</Button>
              <Button onClick={handleSaveProcess} variant="contained" color="primary">
                Guardar
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
}

export default FinishingPlanner;
