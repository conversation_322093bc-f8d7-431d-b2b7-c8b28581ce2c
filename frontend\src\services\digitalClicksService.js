import { buildApiUrl } from '../config';

/**
 * <PERSON>cula el número de clicks y el coste para una máquina digital
 * @param {Object} params - Parámetros para el cálculo
 * @param {string} params.machineId - ID de la máquina digital
 * @param {number} params.sheets - Número de pliegos
 * @param {number} params.copies - Número de ejemplares
 * @param {boolean} params.isColor - Si es impresión a color (true) o blanco y negro (false)
 * @param {boolean} params.isDuplex - Si es impresión a doble cara (true) o una cara (false)
 * @param {string} [params.paperId] - ID del papel en el catálogo
 * @param {string} [params.foldingScheme] - Esquema de plegado (ej: "F4-1")
 * @param {number} [params.pagesPerSheet] - Páginas por pliego según el esquema
 * @param {number} [params.pageWidthMm] - <PERSON><PERSON> de la página en mm
 * @param {number} [params.pageHeightMm] - Alto de la página en mm
 * @param {number} [params.sheetWidthMm] - An<PERSON> del pliego en mm
 * @param {number} [params.sheetHeightMm] - Alto del pliego en mm
 * @returns {Promise<Object>} - Resultado del cálculo
 */
export const calculateDigitalClicks = async ({
  machineId,
  sheets,
  copies,
  isColor = true,
  isDuplex = true,
  paperId = null,
  foldingScheme = null,
  pagesPerSheet = null,
  pageWidthMm = null,
  pageHeightMm = null,
  sheetWidthMm = null,
  sheetHeightMm = null
}) => {
  try {
    // Validar parámetros
    if (!machineId) {
      throw new Error('Se requiere el ID de la máquina');
    }

    if (!sheets || sheets <= 0) {
      throw new Error('El número de pliegos debe ser mayor que 0');
    }

    if (!copies || copies <= 0) {
      throw new Error('El número de ejemplares debe ser mayor que 0');
    }

    // Preparar datos para la solicitud
    const requestData = {
      machine_id: machineId,
      sheets: parseInt(sheets),
      copies: parseInt(copies),
      is_color: isColor,
      is_duplex: isDuplex
    };

    // Añadir parámetros opcionales si están presentes
    if (paperId) requestData.paper_id = paperId;
    if (foldingScheme) requestData.folding_scheme = foldingScheme;
    if (pagesPerSheet) requestData.pages_per_sheet = parseInt(pagesPerSheet);
    if (pageWidthMm) requestData.page_width_mm = parseFloat(pageWidthMm);
    if (pageHeightMm) requestData.page_height_mm = parseFloat(pageHeightMm);
    if (sheetWidthMm) requestData.sheet_width_mm = parseFloat(sheetWidthMm);
    if (sheetHeightMm) requestData.sheet_height_mm = parseFloat(sheetHeightMm);

    console.log('Enviando solicitud de cálculo de clicks:', requestData);

    // Llamar a la API
    const response = await fetch(buildApiUrl('/calculations/digital-clicks'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error en la respuesta de la API:', errorText);
      throw new Error(`Error al calcular clicks: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Resultado del cálculo de clicks:', data);

    return data;
  } catch (error) {
    console.error('Error al calcular clicks:', error);
    throw error;
  }
};

export default {
  calculateDigitalClicks
};
