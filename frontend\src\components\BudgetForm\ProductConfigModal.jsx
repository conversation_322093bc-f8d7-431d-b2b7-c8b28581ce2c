import React from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Grid,
  Box,
  Divider,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  List,
  ListItem,
  IconButton,
  Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PaletteIcon from '@mui/icons-material/Palette';
import SettingsIcon from '@mui/icons-material/Settings';
import ExtensionIcon from '@mui/icons-material/Extension';

/**
 * Componente que muestra la configuración del producto
 */
const ProductConfigModal = ({ 
  open, 
  onClose, 
  colorConfig, 
  setColorConfig, 
  productConfig, 
  setProductConfig, 
  budget,
  showSnackbar 
}) => {
  
  const handleApply = () => {
    // Mostrar mensaje de éxito
    showSnackbar(`Configuración del producto actualizada`, 'success');
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        Configuración del Producto
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <Tabs
          value={0}
          sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
        >
          <Tab label="Configuración del Producto" />
        </Tabs>

        {/* Sección de Colores */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <PaletteIcon sx={{ mr: 1 }} /> Configuración de Colores
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Colores Anverso (Cara)</Typography>
                <RadioGroup
                  row
                  value={colorConfig.frontColors}
                  onChange={(e) => setColorConfig({ ...colorConfig, frontColors: e.target.value })}
                >
                  <FormControlLabel value="4" control={<Radio />} label="CMYK (4 colores)" />
                  <FormControlLabel value="1" control={<Radio />} label="1 color" />
                </RadioGroup>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Colores Reverso</Typography>
                <RadioGroup
                  row
                  value={colorConfig.backColors}
                  onChange={(e) => setColorConfig({ ...colorConfig, backColors: e.target.value })}
                >
                  <FormControlLabel value="0" control={<Radio />} label="Sin impresión" />
                  <FormControlLabel value="4" control={<Radio />} label="CMYK (4 colores)" />
                  <FormControlLabel value="1" control={<Radio />} label="1 color" />
                </RadioGroup>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Pantones Adicionales</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body1" sx={{ mr: 2 }}>Número de pantones:</Typography>
                  <TextField
                    type="number"
                    value={colorConfig.pantones}
                    onChange={(e) => setColorConfig({ ...colorConfig, pantones: parseInt(e.target.value) || 0 })}
                    inputProps={{ min: 0, max: 5 }}
                    size="small"
                    sx={{ width: '80px' }}
                  />
                </Box>
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>Configuración de Colores Actual</Typography>
            <Typography variant="body1">
              {colorConfig.frontColors === '4' ? 'CMYK' : '1 color'}
              {colorConfig.backColors === '0' ? '' : colorConfig.backColors === '4' ? '/ CMYK' : '/ 1 color'}
              {colorConfig.pantones > 0 ? ` + ${colorConfig.pantones} pantone${colorConfig.pantones > 1 ? 's' : ''}` : ''}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Notación: {colorConfig.frontColors}/{colorConfig.backColors}
              {colorConfig.pantones > 0 ? ` + ${colorConfig.pantones}P` : ''}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Sección de Estilo de Trabajo */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <SettingsIcon sx={{ mr: 1 }} /> Estilo de Trabajo
          </Typography>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Estilo de Trabajo</InputLabel>
            <Select
              value={productConfig.workStyle}
              onChange={(e) => setProductConfig({ ...productConfig, workStyle: e.target.value })}
              label="Estilo de Trabajo"
            >
              <MenuItem value="Flat">Tira (Flat)</MenuItem>
              <MenuItem value="WorkAndBack">Tira y Retira (Work and Back)</MenuItem>
              <MenuItem value="WorkAndTurn">Volteo (Work and Turn)</MenuItem>
              <MenuItem value="Perfecting">Perfecting</MenuItem>
            </Select>
            <FormHelperText>
              El estilo de trabajo afecta cómo se imprimen las hojas y el cálculo de pliegos
            </FormHelperText>
          </FormControl>

          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>Descripción del Estilo de Trabajo</Typography>
            <Typography variant="body2">
              {productConfig.workStyle === 'Flat' && 'Impresión a una cara (solo tira)'}
              {productConfig.workStyle === 'WorkAndBack' && 'Impresión a doble cara (tira y retira)'}
              {productConfig.workStyle === 'WorkAndTurn' && 'Impresión con volteo para optimizar el uso del papel'}
              {productConfig.workStyle === 'Perfecting' && 'Impresión a doble cara en una sola pasada'}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        {/* Sección de Partes del Producto */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <ExtensionIcon sx={{ mr: 1 }} /> Partes del Producto
          </Typography>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Seleccione las partes que componen este producto (por ejemplo, cubierta e interior para una revista)
          </Typography>

          {budget.jobType && (
            <Box>
              {/* Cargar las partes disponibles para el tipo de producto seleccionado */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Partes disponibles para {budget.jobType}:
                </Typography>

                {/* Lista de partes disponibles */}
                <Box sx={{ maxHeight: '200px', overflowY: 'auto', border: '1px solid #e0e0e0', borderRadius: 1, p: 1 }}>
                  {productConfig.selectedParts && productConfig.selectedParts.length > 0 ? (
                    <List dense>
                      {productConfig.selectedParts.map((part, index) => (
                        <ListItem key={part.part_id || index} sx={{ py: 0.5 }}>
                          <Box>
                            <Typography variant="body2">
                              <strong>{part.name}</strong> - {part.description || 'Sin descripción'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Colores: {part.default_colors || '4/4'}
                            </Typography>
                          </Box>
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 1 }}>
                      No hay partes definidas para este producto. Se usará una parte general.
                    </Typography>
                  )}
                </Box>
              </Box>

              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                Las partes se cargan automáticamente al seleccionar el tipo de producto.
              </Typography>
            </Box>
          )}

          {!budget.jobType && (
            <Alert severity="info" sx={{ mb: 2 }}>
              Seleccione un tipo de trabajo para ver las partes disponibles.
            </Alert>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancelar</Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleApply}
        >
          Aplicar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

ProductConfigModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  colorConfig: PropTypes.object.isRequired,
  setColorConfig: PropTypes.func.isRequired,
  productConfig: PropTypes.object.isRequired,
  setProductConfig: PropTypes.func.isRequired,
  budget: PropTypes.object.isRequired,
  showSnackbar: PropTypes.func.isRequired
};

export default ProductConfigModal;
