import React from 'react';
import { Box, Paper, Typography, Button, Grid } from '@mui/material';
import ReceiptIcon from '@mui/icons-material/Receipt';
import ConstructionIcon from '@mui/icons-material/Construction';

/**
 * Componente placeholder para la página de Facturación
 * Este componente será reemplazado por la implementación real en el futuro
 */
const BillingPage = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Paper elevation={3} sx={{ p: 4, textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
        <ConstructionIcon sx={{ fontSize: 80, color: 'warning.main', mb: 2 }} />
        
        <Typography variant="h4" gutterBottom>
          Módulo de Facturación
        </Typography>
        
        <Typography variant="h6" color="text.secondary" gutterBottom>
          En desarrollo
        </Typography>
        
        <Typography variant="body1" paragraph sx={{ mb: 4 }}>
          El módulo de facturación está actualmente en desarrollo. Pronto podrás gestionar facturas, 
          albaranes, presupuestos aprobados y más desde esta sección.
        </Typography>
        
        <Grid container spacing={3} justifyContent="center" sx={{ mt: 2 }}>
          <Grid item>
            <Paper 
              elevation={2} 
              sx={{ 
                p: 3, 
                textAlign: 'center',
                minWidth: 200,
                bgcolor: 'primary.light',
                color: 'primary.contrastText'
              }}
            >
              <ReceiptIcon sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Funcionalidades previstas
              </Typography>
              <Box component="ul" sx={{ textAlign: 'left', pl: 2 }}>
                <li>Generación de facturas</li>
                <li>Seguimiento de pagos</li>
                <li>Informes financieros</li>
                <li>Integración con presupuestos</li>
                <li>Exportación a sistemas contables</li>
              </Box>
            </Paper>
          </Grid>
        </Grid>
        
        <Button 
          variant="outlined" 
          color="primary" 
          sx={{ mt: 4 }}
          onClick={() => window.dispatchEvent(new CustomEvent('navigate-to-budget-list'))}
        >
          Volver a Presupuestos
        </Button>
      </Paper>
    </Box>
  );
};

export default BillingPage;
