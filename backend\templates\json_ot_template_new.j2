{
  "job_id": "{{ budget.ot_number }}",
  "descriptive_name": "{{ budget.description.split('\n')[0] if budget.description else '' }}",
  "author": "JDFast",
  "agent_name": "JDFast",
  "agent_version": "3.1",
  "timestamp": "{{ timestamp }}",
  "comment": "Generado a partir del presupuesto {{ budget.budget_id }}",
  "product_type": "{{ budget.job_type }}",
  "binding_side": "Left",
  "signatures": [
    {% for part in budget.parts %}
    {% set part_index = loop.index %}
    {
      "signature_ID": "Sig-{{ part_index }}",
      "job_part_id_name": "{{ part.name }}",
      "press_name": "{{ part.machine_data.name if part.machine_data else 'Máquina ' ~ part.machine_id }}",
      "assembly_order": "{{ part.assembly_order or 'Gathering' }}",
      "color_config": "{{ part.color_config.frontColors if part.color_config else 4 }}/{{ part.color_config.backColors if part.color_config else 4 }}",
      "color_details": {
        "front": [
          {% if part.color_config and part.color_config.frontColors == 1 %}
          "Black"
          {% elif part.color_config and part.color_config.frontColors == 2 %}
          "Black",
          "Spot1"
          {% else %}
          "Cyan",
          "Magenta",
          "Yellow",
          "Black"
          {%- for i in range(5, (part.color_config.frontColors if part.color_config else 4) + 1) %}
          ,
          "Spot{{ i-4 }}"
          {%- endfor %}
          {% endif %}
        ],
        "back": [
          {% if part.color_config and part.color_config.backColors == 1 %}
          "Black"
          {% elif part.color_config and part.color_config.backColors == 2 %}
          "Black",
          "Spot1"
          {% else %}
          "Cyan",
          "Magenta",
          "Yellow",
          "Black"
          {%- for i in range(5, (part.color_config.backColors if part.color_config else 4) + 1) %}
          ,
          "Spot{{ i-4 }}"
          {%- endfor %}
          {% endif %}
        ]
      },
      "stripping_params": {
        "signature_name": "Sig-{{ part_index }}",
        "sheets": [
          {% if part.sheet_calculation and part.sheet_calculation.mejor_combinacion and part.sheet_calculation.mejor_combinacion.esquemas_utilizados %}
          {% for esquema in part.sheet_calculation.mejor_combinacion.esquemas_utilizados %}
          {% set esquema_index = loop.index %}
          {% set outer_loop = loop %}
          {% for pliego in range(1, esquema.numero_pliegos + 1) %}
          {
            "sheet_name": "Sheet-{{ part_index }}-{{ esquema_index }}-{{ pliego }}",
            "bindery_signature_name": "Sig-{{ part_index }}-{{ esquema_index }}-{{ pliego }}",
            "paper_ref": "{{ part.paper_id }}",
            "fold_catalog": "{{ esquema.nombre }}",
            "work_style": "{% if esquema.es_tira_retira %}WorkAndTurn{% elif part.color_config and part.color_config.backColors == 0 %}Flat{% else %}WorkAndBack{% endif %}",
            "strip_cell_params": {
              "trim_size_width": {{ part.page_size.width }},
              "trim_size_height": {{ part.page_size.height }},
              "bleed_face": 3.0,
              "bleed_foot": 3.0,
              "bleed_head": 3.0,
              "bleed_spine": 3.0,
              "trim_face": 3.0,
              "trim_foot": 3.0,
              "trim_head": 3.0,
              "spine": 0.0,
              "orientation": "Rotate0"
            }
          }{% if not loop.last or not outer_loop.last %},{% endif %}
          {% endfor %}
          {% endfor %}
          {% else %}
          {
            "sheet_name": "Sheet-{{ part_index }}",
            "bindery_signature_name": "Sig-{{ part_index }}",
            "paper_ref": "{{ part.paper_id }}",
            "fold_catalog": "F16",
            "work_style": "WorkAndTurn",
            "strip_cell_params": {
              "trim_size_width": {{ part.page_size.width }},
              "trim_size_height": {{ part.page_size.height }},
              "bleed_face": 3.0,
              "bleed_foot": 3.0,
              "bleed_head": 3.0,
              "bleed_spine": 3.0,
              "trim_face": 3.0,
              "trim_foot": 3.0,
              "trim_head": 3.0,
              "spine": 0.0,
              "orientation": "Rotate0"
            }
          }
          {% endif %}
        ]
      }
    }{% if not loop.last %},{% endif %}
    {% endfor %}
  ],
  "paper_configs": [
    {% set paper_ids = [] %}
    {% for part in budget.parts %}
    {% if part.paper_id and part.paper_id not in paper_ids %}
    {{ paper_ids.append(part.paper_id) or "" }}
    {
      "weight": {{ part.paper_data.weight if part.paper_data else 80 }},
      "dimension_width": {{ part.paper_data.dimension_width if part.paper_data else 1000 }},
      "dimension_height": {{ part.paper_data.dimension_height if part.paper_data else 700 }},
      "media_type": "{{ part.paper_data.media_type if part.paper_data else 'Paper' }}",
      "product_id": "{{ part.paper_id }}",
      "thickness": {{ part.paper_data.thickness if part.paper_data else 100 }},
      "descriptive_name": "{{ part.paper_data.descriptive_name if part.paper_data else 'Papel ' ~ part.paper_id }}"
    }{% if not loop.last %},{% endif %}
    {% endif %}
    {% endfor %}
  ]
  {% if budget.pdf_filename or budget.parts and budget.parts[0].pdf_filename %}
  ,
  "runlists": [
    {
      "runlist_id": "RL-{{ budget.budget_id }}",
      "pages": {{ budget.parts[0].page_count if budget.parts and budget.parts[0].page_count else 1 }},
      "page_range": "0 ~ {{ (budget.parts[0].page_count if budget.parts and budget.parts[0].page_count else 1) - 1 }}",
      "signature_ref": "Sig-1",
      "pdf_url": "{{ budget.pdf_filename or (budget.parts[0].pdf_filename if budget.parts else '') }}"
    }
  ]
  {% endif %}
  {% if budget.client_data %}
  ,
  "customer_info": {
    "customer_id": "{{ budget.client_data.client_id }}",
    "billing_code": "{{ budget.client_data.billing_code }}",
    "order_id": "{{ budget.client_data.order_id }}",
    "company_name": "{{ budget.client_data.company.name if budget.client_data.company else '' }}",
    "country": "{{ budget.client_data.company.address.country if budget.client_data.company and budget.client_data.company.address else '' }}",
    "region": "{{ budget.client_data.company.address.region if budget.client_data.company and budget.client_data.company.address else '' }}",
    "city": "{{ budget.client_data.company.address.city if budget.client_data.company and budget.client_data.company.address else '' }}",
    "street": "{{ budget.client_data.company.address.street if budget.client_data.company and budget.client_data.company.address else '' }}",
    "postal_code": "{{ budget.client_data.company.address.postal_code if budget.client_data.company and budget.client_data.company.address else '' }}",
    "job_title": "{{ budget.client_data.contact.position if budget.client_data.contact else '' }}",
    "first_name": "{{ budget.client_data.contact.first_name if budget.client_data.contact else '' }}",
    "family_name": "{{ budget.client_data.contact.last_name if budget.client_data.contact else '' }}",
    "phone": "{{ budget.client_data.contact.phone if budget.client_data.contact else '' }}",
    "fax": "{{ budget.client_data.contact.fax if budget.client_data.contact else '' }}",
    "email": "{{ budget.client_data.contact.email if budget.client_data.contact else '' }}"
  }
  {% endif %}
}
