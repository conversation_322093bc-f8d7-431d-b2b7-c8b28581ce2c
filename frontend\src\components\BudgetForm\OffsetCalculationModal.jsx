import { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Grid,
  Paper,
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

/**
 * Modal especializado para mostrar resultados de cálculos de pliegos en máquinas offset
 */
const OffsetCalculationModal = ({
  open,
  onClose,
  sheetCalculation,
  budget,
  selectedProcesses = [],
  calculatedProcessCost = 0
}) => {


  // Efecto para mostrar la respuesta completa del endpoint solo cuando el modal está abierto
  useEffect(() => {
    if (open && sheetCalculation) {
      // Mostrar la respuesta completa del endpoint
      console.log('Respuesta del endpoint /v2/calculate-offset en el modal:', sheetCalculation);

      // Mostrar la respuesta en formato JSON para facilitar la copia
      console.log('Respuesta en formato JSON:', JSON.stringify(sheetCalculation, null, 2));

      // Mostrar información específica sobre esquemas utilizados
      if (sheetCalculation.esquemas_utilizados && sheetCalculation.esquemas_utilizados.length > 0) {
        console.log('Esquemas utilizados:', sheetCalculation.esquemas_utilizados);
      }
    }
  }, [open, sheetCalculation]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        Resultado del cálculo de pliegos (Offset)
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        {sheetCalculation && (
          <>
            {/* Sección de Resumen */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Resumen
              </Typography>
              <Typography variant="body1">
                <strong>Producto:</strong> {budget.page_count || budget.pageCount} páginas de tamaño {budget.page_size || budget.pageSize} {(budget.page_size || budget.pageSize) === 'Personalizado' ? `(${budget.custom_page_size || budget.customPageSize})` : ''}
              </Typography>
              <Typography variant="body1">
                <strong>Tipo de impresión:</strong> Offset (planchas)
              </Typography>
              <Typography variant="body1">
                <strong>Número de ejemplares:</strong> {
                  (() => {
                    // Intentar obtener el valor de diferentes fuentes
                    const copiesValue = budget.copies ||
                                      sheetCalculation?.copies ||
                                      budget.part?.copies ||
                                      sheetCalculation?.mejor_combinacion?.copies;

                    // Convertir a número si es string, o usar 0 como valor por defecto
                    return copiesValue ? parseInt(copiesValue, 10) : 0;
                  })()
                }
              </Typography>
              <Typography variant="body1">
                <strong>Papel seleccionado:</strong>
                {budget.paper_data ? (
                  <span>
                    {budget.paper_data.descriptive_name || budget.paper_data.name || 'Sin nombre'} ({budget.paper_data.dimension_width} x {budget.paper_data.dimension_height} mm)
                  </span>
                ) : budget.paper ? (
                  <span>
                    {budget.paper.descriptive_name || budget.paper.name || 'Sin nombre'} ({budget.paper.dimension_width} x {budget.paper.dimension_height} mm)
                  </span>
                ) : (
                  'No seleccionado'
                )}
              </Typography>
              {/* Eliminada la configuración de colores */}
              <Typography variant="body1">
                <strong>Tipo de encuadernado:</strong> {budget.binding_type ?
                  (budget.binding_type === 'gathering' ? 'Alzado' :
                   budget.binding_type === 'collection' ? 'Grapado' :
                   budget.binding_type === 'none' ? 'Sin encuadernado' : budget.binding_type) : 'Alzado (por defecto)'}
              </Typography>
            </Box>

            {/* Sección de Esquema utilizado */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Esquema utilizado
              </Typography>
              <Paper elevation={2} sx={{ p: 2, bgcolor: '#f5f5f5' }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    Total: {sheetCalculation && sheetCalculation.mejor_combinacion ? sheetCalculation.mejor_combinacion.total_pliegos :
                           (sheetCalculation && sheetCalculation.total_pliegos ? sheetCalculation.total_pliegos : 0)} pliegos /
                    {sheetCalculation && sheetCalculation.mejor_combinacion ? sheetCalculation.mejor_combinacion.total_planchas :
                      (sheetCalculation && sheetCalculation.total_planchas ? sheetCalculation.total_planchas : 0)} planchas
                  </Typography>

                  {/* Mostrar total de pasadas si está disponible */}

                </Box>

                <Table size="small" sx={{ mt: 2 }}>
                  <TableHead>
                    <TableRow sx={{ bgcolor: '#e0e0e0' }}>
                      <TableCell sx={{ fontWeight: 'bold' }}>Esquema</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Pliegos</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Págs/Pliego</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Disposición</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Tipo Pliego</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Planchas</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {sheetCalculation &&
                     ((sheetCalculation.mejor_combinacion && sheetCalculation.mejor_combinacion.esquemas_utilizados) ||
                      sheetCalculation.esquemas_utilizados) ?
                      (sheetCalculation.esquemas_utilizados || sheetCalculation.mejor_combinacion.esquemas_utilizados || []).map((esquema, index) => {
                        // Obtener el color de fondo según el tipo de pliego
                        const getSheetTypeColor = (sheetType) => {
                          switch (sheetType) {
                            case 'Flat':
                              return '#e3f2fd'; // Azul claro
                            case 'WorkAndTurn':
                              return '#e8f5e9'; // Verde claro
                            case 'WorkAndBack':
                              return '#fff3e0'; // Naranja claro
                            case 'Perfecting':
                              return '#f3e5f5'; // Púrpura claro
                            default:
                              return 'transparent';
                          }
                        };

                        // Obtener la descripción del tipo de pliego
                        const getSheetTypeDescription = (sheetType) => {
                          switch (sheetType) {
                            case 'Flat':
                              return 'Una cara';
                            case 'WorkAndTurn':
                              return 'Tira-retira (mismas planchas)';
                            case 'WorkAndBack':
                              return 'Diferentes planchas';
                            case 'Perfecting':
                              return 'Ambas caras a la vez';
                            default:
                              return esquema.es_tira_retira ? 'Tira y retira' : 'Solo tira';
                          }
                        };

                        return (
                          <TableRow
                            key={index}
                            sx={{ backgroundColor: esquema.sheet_type ? getSheetTypeColor(esquema.sheet_type) : 'transparent' }}
                          >
                            <TableCell>{esquema.nombre}</TableCell>
                            <TableCell>{esquema.numero_pliegos}</TableCell>
                            <TableCell>{esquema.paginas_por_pliego}</TableCell>
                            <TableCell>
                              {esquema.disposicion ?
                                `${esquema.disposicion.paginas_ancho} × ${esquema.disposicion.paginas_alto}` :
                                (esquema.paginas_ancho && esquema.paginas_alto ?
                                  `${esquema.paginas_ancho} × ${esquema.paginas_alto}` : 'N/A')}
                            </TableCell>
                            <TableCell>
                              {esquema.sheet_type ? (
                                <>
                                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                    {esquema.sheet_type}
                                  </Typography>
                                  <Typography variant="caption" display="block">
                                    {getSheetTypeDescription(esquema.sheet_type)}
                                  </Typography>
                                  {esquema.needs_two_passes && (
                                    <Typography variant="caption" display="block" color="warning.main">
                                      Requiere 2 pasadas
                                    </Typography>
                                  )}
                                </>
                              ) : (
                                esquema.es_tira_retira ? 'Tira y retira' : 'Solo tira'
                              )}
                            </TableCell>
                            <TableCell>
                              {esquema.plates_needed !== undefined ? esquema.plates_needed : '-'}
                            </TableCell>
                          </TableRow>
                        );
                      })
                    : <TableRow><TableCell colSpan={6}>No hay esquemas disponibles</TableCell></TableRow>}
                  </TableBody>
                </Table>
              </Paper>
            </Box>

            {/* Sección de Cálculo de costes */}
            <Box>
              <Typography variant="h6" gutterBottom>
                Cálculo de costes
              </Typography>
              <Paper elevation={2} sx={{ p: 2, bgcolor: '#f5f5f5' }}>
                {budget && (
                  <>
                    {/* No incluimos las secciones de datos del papel y datos de la máquina */}

                    {/* Sección de COSTE TOTAL DEL TRABAJO con formato similar a la imagen */}
                    <Box sx={{ bgcolor: '#e3f2fd', p: 2, borderRadius: 1 }}>
                      <Typography variant="h6" color="primary" gutterBottom>
                        COSTE TOTAL DEL TRABAJO
                      </Typography>

                      <Grid container spacing={2}>
                        {/* Columna izquierda: Datos de producción */}
                        <Grid item xs={12} md={7}>
                          {(() => {
                            // Obtener datos de diferentes fuentes posibles
                            const copies = parseInt(budget.copies || sheetCalculation?.copies || 0, 10);
                            const totalSheets = sheetCalculation?.total_sheets || sheetCalculation?.mejor_combinacion?.total_pliegos || 0;
                            const totalPlates = sheetCalculation?.total_plates || 0;
                            const timeMinutes = sheetCalculation?.machine_data?.total_time_minutes || 0;
                            const timeHours = sheetCalculation?.machine_data?.total_time_hours || 0;
                            const setupTime = sheetCalculation?.machine_data?.setup_time || 0;
                            const plateChangeTime = sheetCalculation?.machine_data?.plate_change_time_minutes || 0;
                            const printingTime = sheetCalculation?.machine_data?.printing_time_minutes || 0;

                            return (
                              <>
                                <Typography variant="body1">
                                  <strong>Ejemplares:</strong> {copies}
                                </Typography>
                                <Typography variant="body1">
                                  <strong>Pliegos por ejemplar:</strong> {totalSheets}
                                </Typography>
                                {sheetCalculation?.total_physical_sheets !== undefined && (
                                  <Typography variant="body1">
                                    <strong>Pliegos de producción:</strong> {sheetCalculation.total_physical_sheets}
                                  </Typography>
                                )}
                                {sheetCalculation?.total_maculatura !== undefined && (
                                  <Typography variant="body1">
                                    <strong>Pliegos de maculatura:</strong> {sheetCalculation.total_maculatura}
                                  </Typography>
                                )}
                                {sheetCalculation?.total_paper !== undefined && (
                                  <Typography variant="body1">
                                    <strong>Papel total:</strong> {sheetCalculation.total_paper} pliegos
                                  </Typography>
                                )}
                                <Typography variant="body1">
                                  <strong>Planchas necesarias:</strong> {totalPlates}
                                </Typography>
                                <Typography variant="body1">
                                  <strong>Tiempo calculado:</strong> {timeMinutes.toFixed(0)} minutos ({timeHours.toFixed(2)} horas)
                                </Typography>
                                <Typography variant="caption" display="block" sx={{ ml: 2, mt: 0.5 }}>
                                  Arranque: {setupTime.toFixed(0)} min + Cambios: {plateChangeTime.toFixed(0)} min + Impresión: {printingTime.toFixed(0)} min
                                </Typography>
                              </>
                            );
                          })()}

                          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mt: 2 }}>
                            Desglose de costos:
                          </Typography>
                          {(() => {
                            // Obtener costos de diferentes fuentes posibles
                            // Calcular el costo del papel usando exactamente el mismo método que en BudgetPartForm.jsx
                            let paperCost = 0;
                            let paperDetails = "";

                            if (sheetCalculation) {
                              // Verificar si tenemos el costo del papel directamente del backend
                              if (sheetCalculation.paper_cost) {
                                paperCost = sheetCalculation.paper_cost;
                              }

                              // Obtener el número de pliegos totales
                              let pliegosTotales = 0;
                              let pliegosProduccion = 0;
                              let pliegosMaculatura = 0;

                              if (sheetCalculation.total_physical_sheets) {
                                // Si tenemos el número total de pliegos físicos del backend, usarlo
                                pliegosProduccion = sheetCalculation.total_physical_sheets;

                                // Usar los valores de maculatura del backend si están disponibles
                                if (sheetCalculation.total_maculatura !== undefined) {
                                  pliegosMaculatura = sheetCalculation.total_maculatura;
                                  pliegosTotales = sheetCalculation.total_paper;
                                } else {
                                  // Cálculo anterior como fallback
                                  const totalPliegos = sheetCalculation.mejor_combinacion?.total_pliegos || sheetCalculation.total_pliegos || 0;
                                  const isDigital = budget.machine && budget.machine.type === 'Digital';
                                  const maculaturaPorPliego = isDigital ? 0 : 150;

                                  pliegosMaculatura = totalPliegos * maculaturaPorPliego;
                                  pliegosTotales = pliegosProduccion + pliegosMaculatura;
                                }

                                // Verificar que la suma de producción + maculatura coincida con el total
                                // Si no coincide, ajustar para que la suma sea correcta
                                if (pliegosProduccion + pliegosMaculatura !== pliegosTotales) {
                                  pliegosTotales = pliegosProduccion + pliegosMaculatura;
                                }

                                // Si no tenemos el costo del papel, calcularlo
                                if (!sheetCalculation.paper_cost) {
                                  const precioPorMillar = sheetCalculation.paper_cost_per_1000 ||
                                                         (budget.paper_data && budget.paper_data.price_per_1000) || 96.60;
                                  paperCost = (pliegosTotales * precioPorMillar) / 1000;
                                }
                              } else if (sheetCalculation.mejor_combinacion) {
                                // Si no tenemos el número total de pliegos físicos, calcularlo
                                const totalPliegos = sheetCalculation.mejor_combinacion.total_pliegos || 0;
                                const isDigital = budget.machine && budget.machine.type === 'Digital';
                                const maculaturaPorPliego = isDigital ? 0 : 150;

                                pliegosProduccion = totalPliegos * (budget.copies || 500);
                                pliegosMaculatura = totalPliegos * maculaturaPorPliego;
                                pliegosTotales = pliegosProduccion + pliegosMaculatura;

                                // Si no tenemos el costo del papel, calcularlo
                                if (!sheetCalculation.paper_cost) {
                                  const precioPorMillar = sheetCalculation.paper_cost_per_1000 ||
                                                         (budget.paper_data && budget.paper_data.price_per_1000) || 96.60;
                                  paperCost = (pliegosTotales * precioPorMillar) / 1000;
                                }
                              }

                              // No mostrar detalles de pliegos
                              paperDetails = "";
                            } else {
                              paperCost = 0;
                              paperDetails = "(Sin datos de pliegos)";
                            }

                            const plateCost = sheetCalculation?.plate_cost || 0;
                            const inkCost = sheetCalculation?.ink_cost || 0;
                            const hourlyRate = sheetCalculation?.machine_data?.hourly_cost || 0;
                            const timeHours = sheetCalculation?.machine_data?.total_time_hours || 0;
                            const calculatedMachineCost = timeHours * hourlyRate;
                            const plateUnitCost = sheetCalculation?.plate_unit_cost || 5.75;
                            const totalPlates = sheetCalculation?.total_plates || 0;

                            // Calcular el consumo total de tinta en kg
                            const inkConsumptionPerM2 = sheetCalculation?.ink_consumption || 0;
                            const inkPricePerKg = sheetCalculation?.ink_price_per_kg || 0;

                            // Calcular el peso total de tinta en kg
                            // Método 1: Si tenemos el precio por kg y el coste total, podemos calcular los kg
                            let totalInkKg = 0;
                            if (inkPricePerKg > 0 && inkCost > 0) {
                              totalInkKg = inkCost / inkPricePerKg;
                            } else {
                              // Método 2: Si tenemos el consumo por m² y el área total
                              const totalArea = sheetCalculation?.total_printed_area || 0; // área total impresa en m²
                              if (inkConsumptionPerM2 > 0 && totalArea > 0) {
                                totalInkKg = (inkConsumptionPerM2 * totalArea) / 1000; // convertir de gramos a kg
                              }
                            }

                            return (
                              <>
                                <Typography variant="body2">
                                  <strong>Máquina:</strong> {calculatedMachineCost.toFixed(2)} € {hourlyRate > 0 && `(${timeHours.toFixed(2)} horas, ${hourlyRate.toFixed(2)} €/h)`}
                                </Typography>
                                <Typography variant="body2">
                                  <strong>Papel (producción):</strong> {paperCost.toFixed(2)} € {paperDetails}
                                </Typography>
                                {sheetCalculation?.total_maculatura > 0 && (
                                  <Typography variant="body2">
                                    <strong>Papel (maculatura):</strong> {(() => {
                                      // Calcular el costo de la maculatura
                                      const precioPorMillar = sheetCalculation.paper_cost_per_1000 ||
                                                           (budget.paper_data && budget.paper_data.price_per_1000) || 96.60;
                                      const maculaturaCost = (sheetCalculation.total_maculatura * precioPorMillar) / 1000;
                                      return maculaturaCost.toFixed(2);
                                    })()} € ({sheetCalculation.total_maculatura} pliegos)
                                  </Typography>
                                )}
                                <Typography variant="body2">
                                  <strong>Planchas:</strong> {plateCost.toFixed(2)} € {totalPlates > 0 && `(${totalPlates} planchas x ${plateUnitCost.toFixed(2)} € por plancha)`}
                                </Typography>
                                {inkCost > 0 && (
                                  <Typography variant="body2">
                                    <strong>Tinta:</strong> {inkCost.toFixed(2)} € {totalInkKg > 0 && `(${totalInkKg.toFixed(2)} kg)`}
                                  </Typography>
                                )}
                              </>
                            );
                          })()}
                        </Grid>

                        {/* Columna derecha: Resumen de costes */}
                        <Grid item xs={12} md={5} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', justifyContent: 'space-between' }}>
                          {(() => {
                            // Calcular los costos de máquina para esta sección
                            const hourlyRate = sheetCalculation?.machine_data?.hourly_cost || 0;
                            const timeHours = sheetCalculation?.machine_data?.total_time_hours || 0;
                            const calculatedMachineCost = timeHours * hourlyRate;

                            // Calcular el costo del papel para esta sección
                            let paperCostSummary = 0;
                            let paperDetailsSummary = "";
                            let plateCostSummary = 0;
                            let inkCostSummary = 0;
                            let maculaturaCost = 0; // Nuevo costo de maculatura

                            // Usar los valores del backend si están disponibles
                            if (sheetCalculation) {
                              if (sheetCalculation.paper_cost) {
                                paperCostSummary = sheetCalculation.paper_cost;
                              }

                              plateCostSummary = sheetCalculation.plate_cost || 0;
                              inkCostSummary = sheetCalculation.ink_cost || 0;

                              // Preparar los detalles del papel y calcular el costo de maculatura
                              if (sheetCalculation.total_physical_sheets) {
                                // No mostrar detalles de pliegos
                                paperDetailsSummary = "";

                                // Calcular el costo de maculatura usando los valores del backend si están disponibles
                                let pliegosMaculaturaCalc;

                                if (sheetCalculation.total_maculatura !== undefined) {
                                  pliegosMaculaturaCalc = sheetCalculation.total_maculatura;
                                } else {
                                  // Cálculo anterior como fallback
                                  const totalPliegos = sheetCalculation.mejor_combinacion?.total_pliegos || sheetCalculation.total_pliegos || 0;
                                  const isDigital = budget.machine && budget.machine.type === 'Digital';
                                  const maculaturaPorPliego = isDigital ? 0 : 150;
                                  pliegosMaculaturaCalc = totalPliegos * maculaturaPorPliego;
                                }

                                // Calcular el costo de la maculatura usando el mismo precio por millar que el papel
                                const precioPorMillar = sheetCalculation.paper_cost_per_1000 ||
                                                      (budget.paper_data && budget.paper_data.price_per_1000) || 96.60;
                                maculaturaCost = (pliegosMaculaturaCalc * precioPorMillar) / 1000;
                              } else {
                                paperDetailsSummary = "(Sin datos de pliegos)";
                              }
                            }

                            // Calcular el coste total sumando siempre los componentes individuales
                            // en lugar de usar el valor total_cost del backend que podría ser incorrecto
                            const totalCost = calculatedMachineCost + paperCostSummary + plateCostSummary + inkCostSummary + maculaturaCost;

                            return (
                              <>
                                <Box>
                                  <Typography variant="body1" align="right">
                                    Máquina: {calculatedMachineCost.toFixed(2)} €
                                  </Typography>
                                  <Typography variant="body1" align="right">
                                    Papel (producción): {paperCostSummary.toFixed(2)} €
                                    {paperDetailsSummary && (
                                      <Typography variant="caption" display="block" sx={{ fontSize: '0.7rem', color: 'text.secondary', textAlign: 'right' }}>
                                        {paperDetailsSummary}
                                      </Typography>
                                    )}
                                  </Typography>
                                  {sheetCalculation?.total_maculatura > 0 ? (
                                    <Typography variant="body1" align="right">
                                      Papel (maculatura): {(() => {
                                        // Calcular el costo de la maculatura directamente del backend
                                        const precioPorMillar = sheetCalculation.paper_cost_per_1000 ||
                                                             (budget.paper_data && budget.paper_data.price_per_1000) || 96.60;
                                        return ((sheetCalculation.total_maculatura * precioPorMillar) / 1000).toFixed(2);
                                      })()} €
                                    </Typography>
                                  ) : maculaturaCost > 0 && (
                                    <Typography variant="body1" align="right">
                                      Papel (maculatura): {maculaturaCost.toFixed(2)} €
                                    </Typography>
                                  )}
                                  <Typography variant="body1" align="right">
                                    Planchas: {plateCostSummary.toFixed(2)} €
                                  </Typography>
                                  {inkCostSummary > 0 && (
                                    <Typography variant="body1" align="right">
                                      Tinta: {inkCostSummary.toFixed(2)} €
                                    </Typography>
                                  )}
                                </Box>

                                <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                                  <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                                    {totalCost.toFixed(2)} €
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    Coste total de impresión
                                  </Typography>
                                </Box>
                              </>
                            );
                          })()}
                        </Grid>
                      </Grid>
                    </Box>

                    {/* No incluimos la sección de procesos adicionales ya que el modal es solo para la parte de impresión */}

                    {/* No incluimos la sección de coste total del presupuesto ya que el modal es solo para la parte de impresión */}
                  </>
                )}
              </Paper>
            </Box>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

OffsetCalculationModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  sheetCalculation: PropTypes.object,
  budget: PropTypes.object,
  selectedProcesses: PropTypes.array,
  calculatedProcessCost: PropTypes.number
};

export default OffsetCalculationModal;
