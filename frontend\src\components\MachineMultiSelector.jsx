import { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  Checkbox,
  ListItemText,
  OutlinedInput
} from '@mui/material';
import { buildApiUrl } from '../config';
import { ApiInterceptor } from '../services/simplifiedServices';

// Colores para los diferentes tipos de máquinas
const TYPE_COLORS = {
  'Offset': 'primary',
  'Digital': 'secondary',
  'Plotter': 'info',
  'CTP': 'warning',
  'Encuadernadora': 'success',
  'Guillotina': 'error',
  'Plegadora': 'default'
};

// Estilo para el menú de selección múltiple
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

/**
 * Componente para seleccionar múltiples máquinas
 * @param {Array} selectedMachines - Array de IDs de máquinas seleccionadas
 * @param {Function} onMachinesChange - Función a llamar cuando cambia la selección
 * @param {Boolean} showAllOption - Mostrar opción para seleccionar todas las máquinas
 */
const MachineMultiSelector = ({ selectedMachines = [], onMachinesChange, showAllOption = true }) => {
  const [machines, setMachines] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [machinesByType, setMachinesByType] = useState({});

  // Cargar máquinas activas
  useEffect(() => {
    const fetchMachines = async () => {
      try {
        setLoading(true);
        setError(null);

        const url = buildApiUrl('/machines/');
        const response = await ApiInterceptor.fetch(url);

        if (!response.ok) {
          throw new Error(`Error al obtener máquinas: ${response.statusText}`);
        }

        const data = await response.json();

        // Filtrar solo máquinas activas
        const activeMachines = data.filter(machine => machine.status === 'Activa');

        console.log(`Encontradas ${activeMachines.length} máquinas activas:`, activeMachines);
        setMachines(activeMachines);

        // Agrupar máquinas por tipo
        const groupedMachines = {};
        activeMachines.forEach(machine => {
          if (!groupedMachines[machine.type]) {
            groupedMachines[machine.type] = [];
          }
          groupedMachines[machine.type].push(machine);
        });
        setMachinesByType(groupedMachines);

        // Si no hay máquinas seleccionadas y hay máquinas disponibles, seleccionar la primera
        if (selectedMachines.length === 0 && activeMachines.length > 0 && onMachinesChange) {
          onMachinesChange([activeMachines[0].machine_id]);
        }
      } catch (err) {
        console.error('Error al cargar máquinas:', err);
        setError(`Error al cargar las máquinas: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchMachines();
  }, [selectedMachines, onMachinesChange, showAllOption]);

  const handleChange = (event) => {
    const value = event.target.value;

    // Manejo normal de selección múltiple
    onMachinesChange(value);
  };

  if (loading) {
    return <Typography variant="body2">Cargando máquinas...</Typography>;
  }

  if (error) {
    return <Typography variant="body2" color="error">{error}</Typography>;
  }

  if (machines.length === 0) {
    return <Typography variant="body2">No hay máquinas activas disponibles</Typography>;
  }

  return (
    <Box sx={{ minWidth: 250 }}>
      <FormControl fullWidth size="small">
        <InputLabel id="machine-multi-selector-label">Máquinas</InputLabel>
        <Select
          labelId="machine-multi-selector-label"
          id="machine-multi-selector"
          multiple
          value={selectedMachines}
          onChange={handleChange}
          input={<OutlinedInput label="Máquinas" />}
          renderValue={(selected) => (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {selected.map((machineId) => {
                const machine = machines.find(m => m.machine_id === machineId);
                if (!machine) return null;

                return (
                  <Chip
                    key={machineId}
                    label={machine.name}
                    size="small"
                    color={TYPE_COLORS[machine.type] || 'default'}
                    sx={{ height: 20, fontSize: '0.7rem' }}
                  />
                );
              })}
            </Box>
          )}
          MenuProps={MenuProps}
        >

          {/* Agrupar máquinas por tipo */}
          {Object.entries(machinesByType).map(([type, typeMachines]) => [
            // Encabezado del tipo
            <MenuItem
              key={`type-${type}`}
              disabled
              sx={{
                backgroundColor: 'action.hover',
                fontWeight: 'bold',
                color: TYPE_COLORS[type] ? `${TYPE_COLORS[type]}.main` : 'text.primary'
              }}
            >
              {type}
            </MenuItem>,
            // Máquinas de este tipo
            ...typeMachines.map((machine) => (
              <MenuItem key={machine.machine_id} value={machine.machine_id} sx={{ pl: 4 }}>
                <Checkbox checked={selectedMachines.includes(machine.machine_id)} />
                <ListItemText
                  primary={machine.name}
                  secondary={`${machine.manufacturer} ${machine.model}`}
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </MenuItem>
            ))
          ]).flat()}
        </Select>
      </FormControl>
    </Box>
  );
};

export default MachineMultiSelector;
