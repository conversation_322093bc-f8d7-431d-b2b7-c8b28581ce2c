# Print Time Update Service

## Descripción

El `printTimeUpdateService` es un servicio especializado que extrae la lógica compleja de actualización del tiempo de impresión del componente `BudgetForm`. Este servicio maneja tanto la actualización del tiempo para partes específicas como para el tiempo general de impresión.

## Motivación

La función `handleUpdatePrintTime` original en `BudgetForm` tenía más de 90 líneas de código con lógica compleja que incluía:
- Validación de datos
- Preparación de diferentes tipos de solicitudes (offset vs digital)
- Llamadas a API
- Actualización de estados
- Manejo de errores

Al extraer esta lógica a un servicio especializado, conseguimos:
- **Mejor modularización**: Separación de responsabilidades
- **Reutilización**: El servicio puede usarse en otros componentes
- **Testabilidad**: Funciones más pequeñas y fáciles de probar
- **Mantenibilidad**: Código más organizado y legible

## Estructura del Servicio

### Funciones Principales

#### `handlePrintTimeUpdate(params)`
Función principal que decide si actualizar una parte específica o el tiempo general.

**Parámetros:**
```javascript
{
  time: number,                    // Tiempo en horas
  selectedPartIndex: number|null,  // Índice de la parte seleccionada
  budgetParts: Array,             // Array de partes del presupuesto
  budget: Object,                 // Presupuesto actual
  buildApiUrl: Function,          // Función para construir URLs
  setBudgetParts: Function,       // Setter para las partes
  setCurrentCalculatedPart: Function, // Setter para parte actual
  setCustomMachinePrintTime: Function, // Setter para tiempo general
  setMachineInfoDialog: Function, // Setter para modal
  showSnackbar: Function          // Función para mostrar mensajes
}
```

#### `updatePartPrintTime(params)`
Actualiza el tiempo de impresión para una parte específica del presupuesto.

#### `updateGeneralPrintTime(params)`
Actualiza el tiempo personalizado general (sin parte específica).

#### `handleCustomMachineTimeReset(params)`
Maneja el reseteo del tiempo personalizado de la máquina, restaurando el tiempo por defecto calculado por el backend.

### Funciones Auxiliares

#### `prepareDigitalRequestData(part, budget, time)`
Prepara los datos de solicitud específicos para máquinas digitales.

#### `prepareOffsetRequestData(part, budget, time)`
Prepara los datos de solicitud específicos para máquinas offset.

#### `validatePartForRecalculation(part)`
Valida que una parte tenga todos los datos necesarios para el recálculo.

#### `updatePartWithCalculation(part, calculationData, time)`
Actualiza una parte con los datos calculados por el backend.

#### `callRecalculationAPI(endpoint, requestData, buildApiUrl)`
Realiza la llamada a la API para recalcular con el nuevo tiempo.

#### `getDefaultTimeFromPart(currentCalculatedPart)`
Obtiene el tiempo por defecto de una parte calculada, extrayendo el valor de `estimated_time_hours` del backend.

## Uso en BudgetForm

### Antes (Código Original)
```javascript
const handleUpdatePrintTime = async (time) => {
  // 90+ líneas de lógica compleja
  if (selectedPartIndex !== null) {
    // Validación
    // Preparación de datos
    // Llamada a API
    // Actualización de estados
    // Manejo de errores
  } else {
    // Lógica para tiempo general
  }
};
```

### Después (Con Servicio)
```javascript
const handleUpdatePrintTime = async (time) => {
  try {
    await printTimeUpdateService.handlePrintTimeUpdate({
      time,
      selectedPartIndex,
      budgetParts,
      budget,
      buildApiUrl,
      setBudgetParts,
      setCurrentCalculatedPart,
      setCustomMachinePrintTime,
      setMachineInfoDialog,
      showSnackbar
    });
  } catch (error) {
    console.error('Error al actualizar tiempo de impresión:', error);
  }
};

const handleCustomMachineTimeChange = async (value) => {
  if (value === null) {
    try {
      await printTimeUpdateService.handleCustomMachineTimeReset({
        currentCalculatedPart,
        selectedPartIndex,
        setCustomMachinePrintTime,
        handleUpdatePrintTime,
        showSnackbar
      });
    } catch (error) {
      console.error('Error al resetear tiempo personalizado:', error);
    }
  }
};
```

## Beneficios Obtenidos

### 1. **Reducción de Complejidad**
- BudgetForm: De ~90 líneas a ~15 líneas
- Función más legible y mantenible

### 2. **Mejor Separación de Responsabilidades**
- BudgetForm: Se enfoca en la UI y coordinación
- Servicio: Maneja la lógica de negocio

### 3. **Reutilización**
- El servicio puede usarse en otros componentes
- Funciones auxiliares disponibles independientemente

### 4. **Testabilidad Mejorada**
- Funciones más pequeñas y específicas
- Fácil mockeo de dependencias
- Tests unitarios más precisos

### 5. **Manejo de Errores Centralizado**
- Validaciones consistentes
- Mensajes de error estandarizados
- Logging centralizado

## Ejemplo de Uso Independiente

```javascript
import printTimeUpdateService from '../services/printTimeUpdateService';

// Usar solo la validación
const validation = printTimeUpdateService.validatePartForRecalculation(part);
if (!validation.isValid) {
  console.error(validation.error);
}

// Preparar datos para API
const requestData = printTimeUpdateService.prepareDigitalRequestData(part, budget, time);

// Actualizar parte con cálculos
const updatedPart = printTimeUpdateService.updatePartWithCalculation(part, calculationData, time);
```

## Consideraciones de Implementación

### Autenticación
El servicio incluye automáticamente el token de autenticación en las llamadas a la API:
```javascript
headers: {
  'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
}
```

### Manejo de Errores
- Validaciones previas antes de llamadas a API
- Try-catch en todas las operaciones asíncronas
- Mensajes de error descriptivos para el usuario
- Logging para debugging

### Compatibilidad
- Mantiene la misma interfaz que la función original
- Compatible con máquinas offset y digitales
- Preserva todos los comportamientos existentes

## Testing

El servicio está diseñado para ser fácilmente testeable:

```javascript
// Ejemplo de test
describe('printTimeUpdateService', () => {
  test('should validate part correctly', () => {
    const part = { machine: {}, paper: {}, pageCount: 10 };
    const result = printTimeUpdateService.validatePartForRecalculation(part);
    expect(result.isValid).toBe(true);
  });

  test('should prepare digital request data', () => {
    const requestData = printTimeUpdateService.prepareDigitalRequestData(part, budget, 2);
    expect(requestData.custom_print_time).toBe(2);
  });
});
```

## Próximos Pasos

1. **Añadir tests unitarios** para todas las funciones
2. **Implementar retry logic** para llamadas fallidas
3. **Añadir cache** para evitar recálculos innecesarios
4. **Crear hooks personalizados** para uso en React
5. **Documentar tipos TypeScript** para mejor desarrollo
