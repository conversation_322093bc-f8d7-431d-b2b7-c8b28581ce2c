# Imprenta - Sistema de Gestión para Imprentas

Imprenta es una aplicación web para la gestión de presupuestos, clientes, trabajos y recursos en imprentas. Permite calcular costes, gestionar acabados, y generar documentación para los trabajos de impresión.

## Puertos
- Backend: 3005
- Frontend: 4005

## Instalación con Docker

```bash
# Construir los contenedores
docker-compose build --no-cache

# Iniciar los contenedores
docker-compose up -d
```

## Documentación

### Frontend
- [Documentación General](frontend/docs/README.md)
- [Componentes](frontend/docs/components.md)

### Backend
- [Documentación General](backend/docs/README.md)
- [API](backend/docs/api.md)
- [Modelos](backend/docs/models.md)

### Funcionalidades
- [Estados de Presupuesto](docs/ESTADOS_PRESUPUESTO.md)
- [Resumen de Actualizaciones](docs/RESUMEN_ACTUALIZACIONES.md)

## Características Principales

- Gestión de presupuestos
- Cálculo automático de costes
- Gestión de clientes
- Gestión de papeles y máquinas
- Cálculo de pliegos y optimización
- Gestión de acabados
- Extracción de información de PDFs
- Generación de documentos JDF
- Sistema de logging

## Licencia

Este proyecto es propiedad de su autor y no está disponible para uso público sin autorización expresa.