2025-06-02 18:02:20 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-02 18:02:20 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-02 18:02:22 - INFO - Iniciando la aplicación FastAPI
2025-06-02 18:04:11 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-02 18:04:11 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-02 18:04:11 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-02 18:04:11 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes - Status: 307
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes - Status: 307
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes/
2025-06-02 18:04:32 - INFO - Obteniendo todos los esquemas de plegado
2025-06-02 18:04:32 - INFO - Se encontraron 6 esquemas de plegado
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes/ - Status: 200
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes/
2025-06-02 18:04:32 - INFO - Obteniendo todos los esquemas de plegado
2025-06-02 18:04:32 - INFO - Se encontraron 6 esquemas de plegado
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes/ - Status: 200
2025-06-02 18:09:47 - INFO - Request: 127.0.0.1 - GET /docs
2025-06-02 18:09:47 - INFO - Request: 127.0.0.1 - GET /docs - Status: 200
2025-06-02 18:09:47 - INFO - Request: 127.0.0.1 - GET /openapi.json
2025-06-02 18:09:48 - INFO - Request: 127.0.0.1 - GET /openapi.json - Status: 200
2025-06-02 18:15:00 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 18:15:00 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 18:15:00 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 18:15:00 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 18:15:03 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-02 18:15:03 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines - Status: 307
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines - Status: 307
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 18:15:46 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-001
2025-06-02 18:15:46 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-001 - Status: 200
2025-06-02 18:15:49 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset
2025-06-02 18:15:49 - INFO - Solicitud de cálculo offset v2 recibida: machine_id='MAQ-001' copies=500 num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 colors_front=4 colors_back=4 paper_id='Pap-002' ancho_pliego=None alto_pliego=None custom_setup_time=None custom_sheets_per_hour=None custom_maculatura=None
2025-06-02 18:15:49 - INFO - Dimensiones del pliego obtenidas del papel: 1000.0x700.0mm
2025-06-02 18:15:49 - INFO - Usando máquina MAQ-001 con 8 cuerpos de impresión
2025-06-02 18:15:49 - INFO - Cálculo de pliegos completado: mejor_combinacion=CombinacionResponse(esquemas_utilizados=[EsquemaResponse(nombre='F16-7', numero_pliegos=2, paginas_por_pliego=16, disposicion=DisposicionResponse(paginas_ancho=4, paginas_alto=2, orientacion='Rotate0'), es_tira_retira=False, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=16, needs_two_passes=False, page_layout=None), EsquemaResponse(nombre='F8-7', numero_pliegos=1, paginas_por_pliego=8, disposicion=DisposicionResponse(paginas_ancho=2, paginas_alto=2, orientacion='Rotate90'), es_tira_retira=True, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=8, needs_two_passes=False, page_layout=None)], total_pliegos=3, total_planchas=24, total_passes=2, total_clicks=0, a4_per_sheet=None, print_speed=None, setup_time=None, sheets_per_hour=None, estimated_time_minutes=90.0, estimated_time_hours=1.5, area_pliego_mm2=None, area_utilizada_mm2=None, area_desperdiciada_mm2=None, porcentaje_desperdicio=None, porcentaje_aprovechamiento=None, recomendaciones_desperdicio=None)
2025-06-02 18:15:49 - INFO - Cálculo offset v2 completado. total_sheets=3, copies=500, total_physical_sheets=1250
2025-06-02 18:15:49 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset - Status: 200
2025-06-02 18:55:27 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-02 18:55:27 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-02 18:55:28 - INFO - Iniciando la aplicación FastAPI
