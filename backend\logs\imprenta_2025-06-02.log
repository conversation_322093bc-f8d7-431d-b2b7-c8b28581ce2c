2025-06-02 18:02:20 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-02 18:02:20 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-02 18:02:22 - INFO - Iniciando la aplicación FastAPI
2025-06-02 18:04:11 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-02 18:04:11 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-02 18:04:11 - INFO - Request: 127.0.0.1 - GET /auth/me
2025-06-02 18:04:11 - INFO - Request: 127.0.0.1 - GET /auth/me - Status: 200
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 18:04:31 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes - Status: 307
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes - Status: 307
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes/
2025-06-02 18:04:32 - INFO - Obteniendo todos los esquemas de plegado
2025-06-02 18:04:32 - INFO - Se encontraron 6 esquemas de plegado
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes/ - Status: 200
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes/
2025-06-02 18:04:32 - INFO - Obteniendo todos los esquemas de plegado
2025-06-02 18:04:32 - INFO - Se encontraron 6 esquemas de plegado
2025-06-02 18:04:32 - INFO - Request: 127.0.0.1 - GET /folding-schemes/ - Status: 200
2025-06-02 18:09:47 - INFO - Request: 127.0.0.1 - GET /docs
2025-06-02 18:09:47 - INFO - Request: 127.0.0.1 - GET /docs - Status: 200
2025-06-02 18:09:47 - INFO - Request: 127.0.0.1 - GET /openapi.json
2025-06-02 18:09:48 - INFO - Request: 127.0.0.1 - GET /openapi.json - Status: 200
2025-06-02 18:15:00 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 18:15:00 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 18:15:00 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 18:15:00 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 18:15:03 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos
2025-06-02 18:15:03 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos - Status: 200
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines - Status: 307
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines - Status: 307
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-02 18:15:39 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 18:15:46 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-001
2025-06-02 18:15:46 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-001 - Status: 200
2025-06-02 18:15:49 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset
2025-06-02 18:15:49 - INFO - Solicitud de cálculo offset v2 recibida: machine_id='MAQ-001' copies=500 num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 colors_front=4 colors_back=4 paper_id='Pap-002' ancho_pliego=None alto_pliego=None custom_setup_time=None custom_sheets_per_hour=None custom_maculatura=None
2025-06-02 18:15:49 - INFO - Dimensiones del pliego obtenidas del papel: 1000.0x700.0mm
2025-06-02 18:15:49 - INFO - Usando máquina MAQ-001 con 8 cuerpos de impresión
2025-06-02 18:15:49 - INFO - Cálculo de pliegos completado: mejor_combinacion=CombinacionResponse(esquemas_utilizados=[EsquemaResponse(nombre='F16-7', numero_pliegos=2, paginas_por_pliego=16, disposicion=DisposicionResponse(paginas_ancho=4, paginas_alto=2, orientacion='Rotate0'), es_tira_retira=False, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=16, needs_two_passes=False, page_layout=None), EsquemaResponse(nombre='F8-7', numero_pliegos=1, paginas_por_pliego=8, disposicion=DisposicionResponse(paginas_ancho=2, paginas_alto=2, orientacion='Rotate90'), es_tira_retira=True, sheet_type=<SheetType.PERFECTING: 'Perfecting'>, plates_needed=8, needs_two_passes=False, page_layout=None)], total_pliegos=3, total_planchas=24, total_passes=2, total_clicks=0, a4_per_sheet=None, print_speed=None, setup_time=None, sheets_per_hour=None, estimated_time_minutes=90.0, estimated_time_hours=1.5, area_pliego_mm2=None, area_utilizada_mm2=None, area_desperdiciada_mm2=None, porcentaje_desperdicio=None, porcentaje_aprovechamiento=None, recomendaciones_desperdicio=None)
2025-06-02 18:15:49 - INFO - Cálculo offset v2 completado. total_sheets=3, copies=500, total_physical_sheets=1250
2025-06-02 18:15:49 - INFO - Request: 127.0.0.1 - POST /v2/calculate-offset - Status: 200
2025-06-02 18:55:27 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-02 18:55:27 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-02 18:55:28 - INFO - Iniciando la aplicación FastAPI
2025-06-02 18:56:07 - INFO - Request: 127.0.0.1 - GET /docs
2025-06-02 18:56:07 - INFO - Request: 127.0.0.1 - GET /docs - Status: 200
2025-06-02 18:56:07 - INFO - Request: 127.0.0.1 - GET /openapi.json
2025-06-02 18:56:08 - INFO - Request: 127.0.0.1 - GET /openapi.json - Status: 200
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /machines
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /machines - Status: 307
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /machines
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /machines - Status: 307
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 19:00:34 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-02 19:00:35 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 19:00:39 - INFO - Request: 127.0.0.1 - GET /papers
2025-06-02 19:00:39 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-016
2025-06-02 19:00:39 - INFO - Request: 127.0.0.1 - GET /papers - Status: 307
2025-06-02 19:00:39 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-016 - Status: 200
2025-06-02 19:00:39 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 19:00:39 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 19:00:39 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-016
2025-06-02 19:00:39 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-016 - Status: 200
2025-06-02 19:00:42 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos-digital
2025-06-02 19:00:42 - INFO - Calculando pliegos digital para: num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 ancho_pliego=480.0 alto_pliego=330.0 front_colors=4 back_colors=4 copies=500 binding_type='gathering'
2025-06-02 19:00:42 - INFO - Calculando pliegos digital para 40 páginas, dimensiones: 210.0x297.0mm, pliego: 480.0x330.0mm
2025-06-02 19:00:42 - INFO - Colores: 4/4, Duplex: True, Color: True
2025-06-02 19:00:42 - INFO - Caben 2 A4 por cara en el pliego
2025-06-02 19:00:42 - INFO - Total de A4 por pliego: 4 (2 por cara × 2 caras)
2025-06-02 19:00:42 - INFO - Resultado del cálculo de pliegos digital: 10 pliegos, 10000 clicks
2025-06-02 19:00:42 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos-digital - Status: 200
2025-06-02 19:02:45 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-02 19:02:45 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-02 19:02:45 - INFO - Iniciando la aplicación FastAPI
2025-06-02 19:02:47 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos-digital
2025-06-02 19:02:47 - INFO - Calculando pliegos digital para: num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 ancho_pliego=480.0 alto_pliego=330.0 front_colors=4 back_colors=4 copies=500 binding_type='gathering'
2025-06-02 19:02:47 - INFO - Calculando pliegos digital para 40 páginas, dimensiones: 210.0x297.0mm, pliego: 480.0x330.0mm
2025-06-02 19:02:47 - INFO - Colores: 4/4, Duplex: True, Color: True
2025-06-02 19:02:47 - INFO - Caben 2 A4 por cara en el pliego
2025-06-02 19:02:47 - INFO - Total de A4 por pliego: 4 (2 por cara × 2 caras)
2025-06-02 19:02:47 - INFO - Resultado del cálculo de pliegos digital: 10 pliegos, 10000 clicks
2025-06-02 19:02:47 - INFO - Request: 127.0.0.1 - POST /calcular-pliegos-digital - Status: 200
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /budgets
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /budgets - Status: 307
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /clients
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /clients - Status: 307
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /budgets/
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /budgets/ - Status: 200
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B - Status: 200
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-02 19:02:52 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /products_t=1748883775894
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /products_t=1748883775894 - Status: 307
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /products_t=1748883775901
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /products_t=1748883775901 - Status: 307
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /papers/
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /machines/
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-02 19:02:55 - INFO - Request: 127.0.0.1 - GET /papers/ - Status: 200
2025-06-02 19:02:56 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-02 19:02:56 - INFO - Request: 127.0.0.1 - GET /products/_t=1748883775894
2025-06-02 19:02:56 - INFO - Request: 127.0.0.1 - GET /machines/ - Status: 200
2025-06-02 19:02:56 - INFO - Request: 127.0.0.1 - GET /products/_t=1748883775901
2025-06-02 19:02:56 - INFO - Request: 127.0.0.1 - GET /products/_t=1748883775901 - Status: 200
2025-06-02 19:02:56 - INFO - Request: 127.0.0.1 - GET /products/_t=1748883775894 - Status: 200
2025-06-02 19:03:00 - INFO - Request: 127.0.0.1 - GET /products_t=1748883780022
2025-06-02 19:03:00 - INFO - Request: 127.0.0.1 - GET /products_t=1748883780022 - Status: 307
2025-06-02 19:03:00 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-02 19:03:00 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-02 19:03:00 - INFO - Request: 127.0.0.1 - GET /processes/
2025-06-02 19:03:00 - INFO - Request: 127.0.0.1 - GET /processes/ - Status: 200
2025-06-02 19:03:00 - INFO - Request: 127.0.0.1 - GET /products/_t=1748883780022
2025-06-02 19:03:00 - INFO - Request: 127.0.0.1 - GET /products/_t=1748883780022 - Status: 200
2025-06-02 19:03:13 - INFO - Request: 127.0.0.1 - POST /v2/calculate-digital
2025-06-02 19:03:13 - INFO - Solicitud de cálculo digital recibida: machine_id='MAQ-016' copies=100 paper_id='Pap-temp-1748289660322' is_duplex=True is_color=True num_paginas=40 ancho_pagina=210.0 alto_pagina=297.0 colors_front=None colors_back=None custom_print_time=None custom_setup_time=None
2025-06-02 19:03:13 - INFO - Cálculo digital completado: machine_id='MAQ-016' machine_name='Revoria' machine_type='Digital' copies=100 total_pages=40 total_sheets=1000 clicks_per_sheet=2 total_clicks=2000 is_duplex=True is_color=True click_unit_cost=0.023 click_cost=46.0 paper_cost=58.21 printing_time_minutes=8.33 total_time_minutes=8.33 total_time_hours=0.14 paper_id='Pap-temp-1748289660322' paper_name='Novatech 300gr' sheet_width_mm=420.0 sheet_height_mm=330.0 a4_per_sheet=2 print_speed=120 total_cost=112.54 machine_cost=8.33 mejor_combinacion={'total_pliegos': 10, 'total_clicks': 2000, 'a4_per_sheet': 2, 'is_duplex': True, 'is_color': True, 'print_speed': 120, 'printing_time_minutes': 8.333333333333334, 'estimated_time_minutes': 8.333333333333334, 'estimated_time_hours': 0.1388888888888889} esquemas_utilizados=[{'nombre': 'F4-1', 'numero_pliegos': 10, 'paginas_por_pliego': 4, 'disposicion': {'paginas_ancho': 2, 'paginas_alto': 1, 'orientacion': 'vertical_normal'}, 'es_tira_retira': False, 'is_duplex': True, 'is_color': True, 'clicks_per_sheet': 2}] paper_data={'paper_name': 'Novatech 300gr', 'name': 'Novatech 300gr', 'descriptive_name': 'Novatech 300gr', 'paper_cost': 58.21, 'sheet_width_mm': 420.0, 'sheet_height_mm': 330.0, 'dimension_width': 420.0, 'dimension_height': 330.0} machine_data={'setup_time': 0, 'printing_time_minutes': 8.33, 'total_time_minutes': 8.33, 'total_time_hours': 0.14, 'hourly_cost': 60.0, 'click_unit_cost': 0.023, 'click_color_cost': 0.023, 'speed': 120, 'name': 'Revoria', 'type': 'Digital'} clicks_data={'clicks_per_sheet': 2, 'total_clicks': 2000, 'click_unit_cost': 0.023, 'click_cost': 46.0}
2025-06-02 19:03:13 - INFO - Request: 127.0.0.1 - POST /v2/calculate-digital - Status: 200
2025-06-02 19:03:32 - INFO - Request: 127.0.0.1 - POST /budgets
2025-06-02 19:03:32 - INFO - Request: 127.0.0.1 - POST /budgets - Status: 307
2025-06-02 19:03:32 - INFO - Request: 127.0.0.1 - POST /budgets/
2025-06-02 19:03:34 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:34 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:34 - INFO - Request: 127.0.0.1 - POST /budgets/ - Status: 201
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /clients
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets - Status: 307
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /clients - Status: 307
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets/
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /clients/
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /clients/ - Status: 200
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets/ - Status: 200
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-1BB91B - Status: 200
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-96240E - Status: 200
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-9A418B
2025-06-02 19:03:36 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-9A418B - Status: 200
2025-06-02 19:03:38 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-9A418B
2025-06-02 19:03:38 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-9A418B - Status: 200
2025-06-02 19:03:38 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:38 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:38 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:38 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:38 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:38 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:41 - INFO - Request: 127.0.0.1 - PUT /budgets/PRES-9A418B
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:43 - INFO - Usando tiempo de cálculo de pliegos para Revista: 0.14 horas
2025-06-02 19:03:43 - INFO - Asignando máquina MAQ-008 al proceso de acabado Corte (Guillotina)
2025-06-02 19:03:43 - INFO - Asignando máquina MAQ-012 al proceso de acabado Encuadernación Grapada
2025-06-02 19:03:43 - INFO - Presupuesto PRES-9A418B añadido a producción con OT OT-25063394 y 3 procesos
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - PUT /budgets/PRES-9A418B - Status: 200
2025-06-02 19:03:43 - INFO - Request: 127.0.0.1 - PUT /budgets/PRES-9A418B
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:44 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - PUT /budgets/PRES-9A418B - Status: 200
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:45 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:47 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:47 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:47 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:47 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:47 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:47 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-9A418B
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-9A418B - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-9A418B
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /budgets/PRES-9A418B - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /papers/Pap-temp-1748289660322
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /papers/Pap-temp-1748289660322 - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /papers/Pap-temp-1748289660322
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /papers/Pap-temp-1748289660322 - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-016
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-016 - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-016
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /machines/MAQ-016 - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:48 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:03:59 - INFO - Request: 127.0.0.1 - GET /config
2025-06-02 19:03:59 - INFO - Request: 127.0.0.1 - GET /config - Status: 307
2025-06-02 19:03:59 - INFO - Request: 127.0.0.1 - GET /config/
2025-06-02 19:03:59 - INFO - Request: 127.0.0.1 - GET /config/ - Status: 200
2025-06-02 19:03:59 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2
2025-06-02 19:03:59 - INFO - Request: 127.0.0.1 - GET /clients/Customer-2 - Status: 200
2025-06-02 19:04:10 - INFO - Request: 127.0.0.1 - GET /docs
2025-06-02 19:04:10 - INFO - Request: 127.0.0.1 - GET /docs - Status: 200
2025-06-02 19:04:10 - INFO - Request: 127.0.0.1 - GET /openapi.json
2025-06-02 19:04:11 - INFO - Request: 127.0.0.1 - GET /openapi.json - Status: 200
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/files
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/current
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/files - Status: 200
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/current - Status: 200
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/files
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/current
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/files - Status: 200
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/current - Status: 200
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/content/imprenta_2025-06-02.log
2025-06-02 19:07:35 - INFO - Request: 127.0.0.1 - GET /logs/content/imprenta_2025-06-02.log - Status: 200
2025-06-02 19:07:36 - INFO - Request: 127.0.0.1 - GET /logs/content/imprenta_2025-06-02.log
2025-06-02 19:07:36 - INFO - Request: 127.0.0.1 - GET /logs/content/imprenta_2025-06-02.log - Status: 200
2025-06-02 19:11:20 - INFO - Directorio base de facturas creado: C:\Users\<USER>\Desktop\Imprenta\backend\data\facturas
2025-06-02 19:11:20 - INFO - Estructura de directorios para facturas del año 2025 creada correctamente
2025-06-02 19:11:20 - INFO - Iniciando la aplicación FastAPI
