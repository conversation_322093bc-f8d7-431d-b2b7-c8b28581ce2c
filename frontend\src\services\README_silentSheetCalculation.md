# Silent Sheet Calculation Service

Servicio modular para cálculo silencioso de pliegos extraído del componente `BudgetForm.jsx`.

## 🎯 Propósito

Este servicio centraliza la funcionalidad de cálculo de pliegos sin mostrar modales, mejorando la modularización y reutilización del código.

## 📁 Estructura

```
silentSheetCalculationService.js
├── Funciones auxiliares
│   ├── validatePartForCalculation()
│   ├── updatePartWithResults()
│   ├── calculatePartPaperWeight()
│   ├── calculateTotalPaperWeight()
│   └── recalculateShippingCost()
├── Función principal
│   └── calculateSheetsV2Silent()
└── Hook personalizado
    └── useSilentSheetCalculation()
```

## 🚀 Uso Básico

### 1. Hook Personalizado (Recomendado)

```javascript
import { useSilentSheetCalculation } from '../services/silentSheetCalculationService';

const MyComponent = () => {
  // Configurar dependencias
  const silentCalculation = useSilentSheetCalculation({
    budgetParts,
    budget,
    budgetId,
    buildApiUrl,
    showSnackbar,
    setBudgetParts,
    setBudget,
    setCalculatingSheets,
    setCurrentCalculatedPart,
    setCalculationResults
  });

  // Usar el cálculo silencioso
  const handleCalculate = async (partIndex) => {
    await silentCalculation(partIndex, {
      showModal: false,
      showSuccessMessage: true,
      recalculateShipping: true
    });
  };
};
```

### 2. Función Directa

```javascript
import { calculateSheetsV2Silent } from '../services/silentSheetCalculationService';

const result = await calculateSheetsV2Silent({
  budgetParts,
  partIndex: 0,
  copies: budget.copies,
  budget,
  budgetId,
  buildApiUrl,
  showSnackbar,
  setBudgetParts,
  setBudget,
  setCalculatingSheets,
  setCurrentCalculatedPart,
  setCalculationResults,
  config: {
    showModal: false,
    showSuccessMessage: true,
    recalculateShipping: true
  }
});
```

### 3. Componente Reutilizable

```javascript
import SilentCalculationButton from './BudgetForm/SilentCalculationButton';

<SilentCalculationButton
  partIndex={0}
  budgetParts={budgetParts}
  budget={budget}
  budgetId={budgetId}
  buildApiUrl={buildApiUrl}
  showSnackbar={showSnackbar}
  setBudgetParts={setBudgetParts}
  setBudget={setBudget}
  setCurrentCalculatedPart={setCurrentCalculatedPart}
  setCalculationResults={setCalculationResults}
  config={{
    showModal: false,
    showSuccessMessage: true,
    recalculateShipping: true
  }}
/>
```

## ⚙️ Configuración

### Opciones de Configuración

```javascript
const config = {
  showModal: false,           // No mostrar modal de resultados
  showSuccessMessage: true,   // Mostrar mensaje de éxito
  recalculateShipping: true   // Recalcular costo de envío automáticamente
};
```

### Validaciones Automáticas

El servicio valida automáticamente:
- ✅ Existencia de la parte
- ✅ Máquina seleccionada
- ✅ Papel seleccionado
- ✅ Número de páginas válido
- ✅ Cantidad de copias válida

## 🔧 Funciones Auxiliares

### `calculatePartPaperWeight(part, copies)`
Calcula el peso del papel para una parte específica.

### `calculateTotalPaperWeight(parts, copies)`
Calcula el peso total del papel de todas las partes.

### `recalculateShippingCost(totalPaperWeight, budget, buildApiUrl, setBudget)`
Recalcula el costo de envío basado en el peso del papel.

### `validatePartForCalculation(part, copies)`
Valida que una parte tenga los datos necesarios para el cálculo.

### `updatePartWithResults(part, results)`
Actualiza una parte con los resultados del cálculo.

## 🎯 Beneficios

### ✅ Modularización
- Código reutilizable y mantenible
- Separación de responsabilidades
- Fácil testing unitario

### ✅ Flexibilidad
- Configuración personalizable
- Hook personalizado para diferentes contextos
- Componente reutilizable incluido

### ✅ Robustez
- Validaciones automáticas
- Manejo de errores centralizado
- Fallbacks para cálculos locales

### ✅ Performance
- Eliminación de código duplicado
- Cálculos optimizados
- Estados de carga centralizados

## 🧪 Testing

```javascript
// Ejemplo de test unitario
import { validatePartForCalculation, calculatePartPaperWeight } from '../services/silentSheetCalculationService';

describe('Silent Sheet Calculation Service', () => {
  test('should validate part correctly', () => {
    const part = { machine: {}, paper: {}, pageCount: 4 };
    const result = validatePartForCalculation(part, 500);
    expect(result.isValid).toBe(true);
  });

  test('should calculate paper weight correctly', () => {
    const part = {
      paper: { weight: 80, dimension_width: 700, dimension_height: 1000 },
      sheetCalculation: { total_physical_sheets: 100 }
    };
    const weight = calculatePartPaperWeight(part, 500);
    expect(weight).toBeGreaterThan(0);
  });
});
```

## 🔄 Migración desde BudgetForm

### Antes (BudgetForm.jsx)
```javascript
// 60+ líneas de código duplicado
const handleCalculateSheetsV2Silent = async (partIndex) => {
  // ... lógica compleja duplicada
};
```

### Después (Con servicio externo)
```javascript
// 5 líneas usando el servicio
const handleCalculateSheetsV2Silent = async (partIndex) => {
  return await silentCalculation(partIndex, {
    showModal: false,
    showSuccessMessage: true,
    recalculateShipping: true
  });
};
```

## 📈 Métricas de Mejora

- **Reducción de código**: ~60 líneas eliminadas del BudgetForm
- **Reutilización**: Servicio disponible para otros componentes
- **Mantenibilidad**: Lógica centralizada en un solo lugar
- **Testing**: Funciones auxiliares fácilmente testeable
