/**
 * Servicio para el cálculo de pliegos y costes relacionados
 * Este archivo contiene funciones para calcular pliegos, costes de papel, máquina y planchas
 */
import digitalClicksService from './digitalClicksService';
import { generatePartJobSummary } from './jobSummaryService';

/**
 * Calcula el total de pliegos físicos a partir de los esquemas utilizados
 * @param {Array} esquemas - Esquemas utilizados en el cálculo
 * @param {number} copies - Número de copias a imprimir
 * @returns {number} - Total de pliegos físicos
 */
export const calculateTotalPhysicalSheets = (esquemas, copies) => {
  if (!esquemas || esquemas.length === 0 || !copies) return 0;

  let totalPliegosFisicos = 0;

  // Para cada esquema utilizado
  esquemas.forEach(esquema => {
    // Si es Tira y Retira, cada pliego físico produce 2 unidades
    const factor = esquema.es_tira_retira ? 0.5 : 1;
    totalPliegosFisicos += esquema.numero_pliegos * factor * parseInt(copies);
  });

  return Math.ceil(totalPliegosFisicos);
};

/**
 * Normaliza la estructura de datos del cálculo de pliegos
 * @param {Object} data - Datos recibidos de la API
 * @returns {Object} - Datos normalizados
 */
export const normalizeSheetCalculationData = (data) => {
  // Si no hay datos, devolver un objeto vacío
  if (!data) return {};

  // Crear una copia para no modificar el original
  const normalizedData = { ...data };

  // Si los datos ya tienen la estructura correcta, devolverlos tal cual
  if (normalizedData.mejor_combinacion && normalizedData.mejor_combinacion.esquemas_utilizados) {
    // Asegurarse de que total_clicks existe (para compatibilidad con el frontend)
    if (!normalizedData.mejor_combinacion.total_clicks) {
      normalizedData.mejor_combinacion.total_clicks = 0;
    }

    // Asegurarse de que total_passes existe (para compatibilidad con el frontend)
    if (!normalizedData.mejor_combinacion.total_passes) {
      normalizedData.mejor_combinacion.total_passes = normalizedData.mejor_combinacion.total_pliegos;
    }

    return normalizedData;
  }

  // Si los esquemas están en la raíz, moverlos a mejor_combinacion
  if (normalizedData.esquemas_utilizados) {
    normalizedData.mejor_combinacion = {
      esquemas_utilizados: normalizedData.esquemas_utilizados,
      total_pliegos: normalizedData.total_pliegos || 0,
      total_planchas: normalizedData.total_planchas || 0,
      total_clicks: 0, // Para compatibilidad con el frontend
      total_passes: normalizedData.total_passes || normalizedData.total_pliegos || 0 // Para compatibilidad con el frontend
    };
  }

  return normalizedData;
};



/**
 * Calcula los pliegos para una parte específica del presupuesto
 * @param {Object} params - Parámetros para el cálculo
 * @param {Object} params.part - Parte del presupuesto a calcular
 * @param {number} params.partIndex - Índice de la parte en el array de partes
 * @param {number} params.copies - Número de copias a imprimir
 * @param {Function} params.buildApiUrl - Función para construir URLs de API
 * @param {Function} params.showSnackbar - Función para mostrar notificaciones
 * @param {Function} params.setCalculatingSheets - Función para actualizar el estado de cálculo
 * @param {Function} params.setBudgetParts - Función para actualizar las partes del presupuesto
 * @param {Function} params.setBudget - Función para actualizar el presupuesto
 * @param {Function} params.setCurrentCalculatedPart - Función para establecer la parte calculada actual
 * @param {Function} params.setSheetCalculationModal - Función para mostrar/ocultar el modal de cálculo
 * @param {Function} params.setSheetCalculation - Función para establecer los datos del cálculo de pliegos
 * @param {string} params.budgetId - ID del presupuesto (opcional, para cálculo de envío)
 * @returns {Promise<Object>} - Resultado del cálculo
 */
export const calculateSheetsPart = async ({
  part,
  partIndex,
  copies,
  buildApiUrl,
  showSnackbar,
  setCalculatingSheets,
  setBudgetParts,
  setBudget,
  setCurrentCalculatedPart,
  setSheetCalculationModal,
  setSheetCalculation,
  budgetParts,
  budget,
  budgetId
}) => {
  if (!part || !part.paper || !part.machine || !part.pageCount) {
    showSnackbar('Selecciona papel, máquina y número de páginas para esta parte', 'warning');
    return null;
  }

  setCalculatingSheets(true);

  try {
    // Determinar el tamaño de página
    let pageSize;

    if (part.pageSize === 'Personalizado') {
      // Si es personalizado, usar el valor del campo customPageSize
      const dimensions = part.customPageSize.split('x').map(dim => parseFloat(dim.trim()));
      if (dimensions.length !== 2 || isNaN(dimensions[0]) || isNaN(dimensions[1])) {
        showSnackbar('Formato de tamaño personalizado incorrecto. Usa el formato "ancho x alto"', 'error');
        setCalculatingSheets(false);
        return null;
      }
      pageSize = {
        width: dimensions[0],
        height: dimensions[1]
      };
    } else {
      // Si es un tamaño estándar, usar las dimensiones predefinidas
      switch (part.pageSize) {
        case 'A4':
          pageSize = { width: 210, height: 297 };
          break;
        case 'A5':
          pageSize = { width: 148, height: 210 };
          break;
        case 'A3':
          pageSize = { width: 297, height: 420 };
          break;
        case 'Carta':
          pageSize = { width: 216, height: 279 };
          break;
        case 'Legal':
          pageSize = { width: 216, height: 356 };
          break;
        default:
          showSnackbar('Tamaño de página no reconocido', 'error');
          setCalculatingSheets(false);
          return null;
      }
    }

    // Obtener dimensiones del papel seleccionado (en mm)
    // Asegurarse de que las dimensiones estén en mm (algunas veces pueden estar en cm)
    let paperWidth = part.paper.dimension_width;
    let paperHeight = part.paper.dimension_height;

    // Si las dimensiones son muy pequeñas, podrían estar en cm, convertir a mm
    if (paperWidth < 100 || paperHeight < 100) {
      paperWidth = paperWidth * 10;
      paperHeight = paperHeight * 10;
      console.log('Dimensiones convertidas de cm a mm:', { paperWidth, paperHeight });
    }

    // Validar que todos los valores necesarios estén presentes
    if (!paperWidth || !paperHeight) {
      showSnackbar('El papel seleccionado no tiene dimensiones válidas', 'error');
      setCalculatingSheets(false);
      return null;
    }

    // Asegurarse de que todos los valores sean números
    const numPaginas = parseInt(part.pageCount);
    const anchoPagina = parseFloat(pageSize.width);
    const altoPagina = parseFloat(pageSize.height);
    const anchoPliego = parseFloat(paperWidth);
    const altoPliego = parseFloat(paperHeight);

    // Validar que todos los valores sean números válidos
    if (isNaN(numPaginas) || isNaN(anchoPagina) || isNaN(altoPagina) || isNaN(anchoPliego) || isNaN(altoPliego)) {
      showSnackbar('Algunos valores no son números válidos', 'error');
      setCalculatingSheets(false);
      return null;
    }

    // Validar que el número de páginas sea par (requerido por el backend)
    if (numPaginas % 2 !== 0) {
      showSnackbar('El número de páginas debe ser par', 'warning');
      setCalculatingSheets(false);
      return null;
    }

    // Determinar si la máquina es digital
    const isDigital = part.machine && part.machine.type === 'Digital';
    const isOffset = part.machine && part.machine.type === 'Offset';

    // Preparar datos para la API incluyendo el número de copias
    const requestData = {
      num_paginas: numPaginas,
      ancho_pagina: anchoPagina,
      alto_pagina: altoPagina,
      ancho_pliego: anchoPliego,
      alto_pliego: altoPliego,
      front_colors: part.colorConfig?.frontColors || 4,
      back_colors: part.colorConfig?.backColors || 0,
      machine_id: part.machine?.machine_id || part.machine?.product_id,
      machine_type: isDigital ? 'Digital' : 'Offset',
      copies: parseInt(copies) || 1,
      binding_type: part.binding_type || 'gathering' // Tipo de encuadernado: "gathering" (alzado), "collection" (grapado) o "none" (sin encuadernado)
    };

    // Añadir el número de cuerpos de impresión para máquinas offset
    if (isOffset && part.machine?.print_units) {
      requestData.print_units = part.machine.print_units;
      console.log(`Máquina offset con ${part.machine.print_units} cuerpos de impresión`);
    }

    console.log('Enviando datos para cálculo de pliegos:', requestData);
    console.log('Valores detallados:', {
      numPaginas,
      anchoPagina,
      altoPagina,
      anchoPliego,
      altoPliego,
      paperWidth,
      paperHeight,
      pageSize
    });

    // Determinar qué endpoint usar según el tipo de máquina
    let endpoint = '';
    let requestBody = {};

    if (isOffset) {
      // Para máquinas offset, usar el nuevo endpoint v2/calculate-offset
      endpoint = '/v2/calculate-offset';

      // Preparar los datos para el endpoint v2/calculate-offset
      requestBody = {
        machine_id: part.machine?.machine_id || part.machine?.product_id,
        copies: parseInt(copies) || 1,
        colors_front: part.colorConfig?.frontColors || 4,
        colors_back: part.colorConfig?.backColors || 0,
        paper_id: part.paper?.paper_id || part.paper?.product_id,
        custom_setup_time: part.customSetupTime,
        custom_sheets_per_hour: part.customSheetsPerHour,
        custom_maculatura: part.customMaculatura,

        // Parámetros para modo automático
        num_paginas: numPaginas,
        ancho_pagina: anchoPagina,
        alto_pagina: altoPagina,
        ancho_pliego: anchoPliego,
        alto_pliego: altoPliego
      };

      console.log(`Usando nuevo endpoint ${endpoint} para máquina offset`);
    } else if (isDigital) {
      // Para máquinas digitales, usar el nuevo endpoint v2/calculate-digital
      endpoint = '/v2/calculate-digital';

      // Preparar los datos para el endpoint v2/calculate-digital
      requestBody = {
        machine_id: part.machine?.machine_id || part.machine?.product_id,
        copies: parseInt(copies) || 1,
        is_duplex: part.colorConfig?.backColors > 0, // Si hay colores en el reverso, es duplex
        is_color: part.colorConfig?.frontColors > 0 || part.colorConfig?.backColors > 0, // Si hay colores en anverso o reverso, es color
        paper_id: part.paper?.paper_id || part.paper?.product_id,
        custom_print_time: part.customPrintTime,

        // Parámetros para el cálculo
        num_paginas: numPaginas,
        ancho_pagina: anchoPagina,
        alto_pagina: altoPagina
      };

      console.log(`Usando nuevo endpoint ${endpoint} para máquina digital`);
    } else {
      // Para otros tipos de máquinas, seguir usando el endpoint original
      endpoint = '/calcular-pliegos';
      requestBody = requestData;
      console.log(`Usando endpoint ${endpoint} para máquina de tipo desconocido`);
    }

    // Llamar a la API para calcular pliegos
    const response = await fetch(buildApiUrl(endpoint), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error en la respuesta de la API:', errorText);
      throw new Error(`Error al calcular pliegos: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Resultado del cálculo de pliegos para la parte:', data);

    // Actualizar la parte con los resultados del cálculo
    const updatedParts = [...budgetParts];

    // Inicializar variables para costos
    let paperCost = 0;
    let machineCost = 0;
    let plateCost = 0;
    let clickCost = 0;

    // Normalizar la estructura de datos para manejar diferentes formatos de respuesta
    let normalizedData = {};

    // Procesar la respuesta según el tipo de máquina
    if (isOffset && endpoint === '/v2/calculate-offset') {
      // Para el endpoint v2/calculate-offset, la estructura es diferente
      console.log('Procesando respuesta del endpoint v2/calculate-offset');

      // Extraer costes directamente de la respuesta
      // El endpoint v2/calculate-offset ya incluye todos los costes necesarios
      paperCost = data.paper_cost || 0;
      machineCost = data.printing_cost + (data.cfa_cost || 0);
      plateCost = data.plates_cost || 0;

      console.log('Costes obtenidos directamente del endpoint v2/calculate-offset:', {
        paperCost,
        machineCost,
        plateCost,
        inkCost: data.ink_cost || 0
      });

      // Normalizar los datos para que sean compatibles con la estructura esperada
      normalizedData = {
        mejor_combinacion: {
          total_pliegos: data.total_sheets || 0,
          total_planchas: data.total_plates || 0,
          esquemas_utilizados: data.esquemas_utilizados || []
        },
        machine_data: {
          setup_time: data.setup_time_minutes || 0,
          printing_time_minutes: data.printing_time_minutes || 0,
          total_time_minutes: data.total_time_minutes || 0,
          total_time_hours: data.total_time_hours || 0,
          hourly_cost: data.hourly_cost || 0,
          cfa_percentage: data.cfa_percentage || 0
        },
        paper_data: {
          paper_name: data.paper_name || '',
          paper_cost_per_1000: data.paper_cost_per_1000 || 0
        },
        total_cost: data.total_cost || 0
      };

      console.log('Datos normalizados para el modal (offset):', normalizedData);
    } else if (isDigital && endpoint === '/v2/calculate-digital') {
      // Para el endpoint v2/calculate-digital, la estructura es diferente
      console.log('Procesando respuesta del endpoint v2/calculate-digital');

      // Extraer costes directamente de la respuesta
      // El endpoint v2/calculate-digital ya incluye todos los costes necesarios
      paperCost = data.paper_cost || 0;
      machineCost = data.machine_cost || 0;
      clickCost = data.click_cost || 0;

      console.log('Costes obtenidos directamente del endpoint v2/calculate-digital:', {
        paperCost,
        machineCost,
        clickCost
      });

      // Normalizar los datos para que sean compatibles con la estructura esperada
      normalizedData = {
        mejor_combinacion: {
          total_pliegos: data.total_sheets || 0,
          total_clicks: data.total_clicks || 0,
          a4_per_sheet: data.a4_per_sheet || 1,
          is_duplex: data.is_duplex || false,
          is_color: data.is_color || false,
          print_speed: data.print_speed || 100,
          estimated_time_minutes: data.total_time_minutes || 0,
          estimated_time_hours: data.total_time_hours || 0
        },
        machine_data: {
          type: 'Digital',
          machine_name: data.machine_name || '',
          printing_time_minutes: data.printing_time_minutes || 0,
          total_time_minutes: data.total_time_minutes || 0,
          total_time_hours: data.total_time_hours || 0,
          hourly_cost: data.machine_cost ? (data.machine_cost / data.total_time_hours) : 0,
          click_unit_cost: data.click_unit_cost || 0
        },
        paper_data: {
          paper_name: data.paper_name || '',
          paper_cost: data.paper_cost || 0,
          sheet_width_mm: data.sheet_width_mm || 0,
          sheet_height_mm: data.sheet_height_mm || 0
        },
        total_cost: data.total_cost || 0,
        clicks_data: {
          clicks_per_sheet: data.clicks_per_sheet || 0,
          total_clicks: data.total_clicks || 0,
          click_unit_cost: data.click_unit_cost || 0,
          click_cost: data.click_cost || 0
        }
      };

      console.log('Datos normalizados para el modal (digital):', normalizedData);
    } else {
      // Para los endpoints originales, usar la normalización existente
      normalizedData = normalizeSheetCalculationData(data);
    }

    // Generar el resumen del trabajo y añadirlo a la descripción
    const jobSummary = generatePartJobSummary(part, data, budget, copies);
    const currentDesc = budget.description || '';

    // Actualizar la descripción con el resumen del trabajo
    setBudget({
      ...budget,
      description: currentDesc + (currentDesc ? '\n\n' : '') + jobSummary
    });

    // Calcular el total de pliegos físicos considerando Tira y Retira
    let totalPliegosFisicos = 0;

    if (data && part.paper) {
      // Verificar la estructura del objeto data
      // Puede tener esquemas_utilizados directamente o estar dentro de mejor_combinacion
      const esquemas = (normalizedData.mejor_combinacion && normalizedData.mejor_combinacion.esquemas_utilizados) ||
                      normalizedData.esquemas_utilizados ||
                      [];

      console.log('Esquemas utilizados:', esquemas);
      console.log('Estructura completa de data:', data);

      // Para máquinas digitales, podemos no tener esquemas pero sí tener total_pliegos
      const isDigital = part.machine && part.machine.type === 'Digital';

      // Para cada esquema utilizado
      if (esquemas.length > 0) {
        esquemas.forEach(esquema => {
          // Si es Tira y Retira, cada pliego físico produce 2 unidades
          const factor = esquema.es_tira_retira ? 0.5 : 1;
          totalPliegosFisicos += esquema.numero_pliegos * factor * parseInt(copies);
        });
      } else if (isDigital && normalizedData.mejor_combinacion && normalizedData.mejor_combinacion.total_pliegos) {
        // Para máquinas digitales, usar el total_pliegos directamente si no hay esquemas
        console.log('Usando total_pliegos directamente para máquina digital:', normalizedData.mejor_combinacion.total_pliegos);
        totalPliegosFisicos = normalizedData.mejor_combinacion.total_pliegos * parseInt(copies);
      } else {
        console.warn('No se encontraron esquemas utilizados en los datos recibidos');
        showSnackbar('No se encontraron esquemas de imposición válidos', 'warning');
        setCalculatingSheets(false);
        return null;
      }

      console.log('Total de pliegos físicos calculados:', totalPliegosFisicos);

      // Si estamos usando los endpoints v2, ya tenemos todos los costes
      // No necesitamos hacer llamadas adicionales a /calculations/paper-cost y /calculations/machine-cost
      if (!(isOffset && endpoint === '/v2/calculate-offset') && !(isDigital && endpoint === '/v2/calculate-digital')) {
        // Solo calculamos los costes con los endpoints tradicionales si NO estamos usando endpoints v2
        try {
          // Verificar que part.paper existe y tiene paper_id o product_id
          if (!part.paper || (!part.paper.paper_id && !part.paper.product_id)) {
            console.error('Error: No hay papel seleccionado o el papel no tiene ID');
            throw new Error('No hay papel seleccionado o el papel no tiene ID');
          }

          // Calcular coste de papel
          const paperResponse = await fetch(buildApiUrl('/calculations/paper-cost'), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              paper_id: part.paper.paper_id || part.paper.product_id,
              total_sheets: Math.ceil(totalPliegosFisicos)
            })
          });

          if (!paperResponse.ok) {
            throw new Error('Error al calcular el coste del papel');
          }

          const paperData = await paperResponse.json();
          paperCost = paperData.cost;
          console.log('Coste de papel calculado en el backend:', paperCost);

          // Calcular coste de máquina
          if (part.machine) {
            // Para máquinas digitales, calcular caras en color y B/N
            let colorSides = null;
            let bwSides = null;

            if (part.machine.type === 'Digital') {
              // Calcular el número total de caras impresas
              const totalSides = Math.ceil(totalPliegosFisicos) * 2; // Asumimos impresión a doble cara

              // Determinar cuántas caras son a color y cuántas en B/N basado en la configuración de color
              // Si hay colores en el anverso o reverso, asumimos que es a color
              const hasColor = (part.colorConfig?.frontColors > 0 || part.colorConfig?.backColors > 0);

              if (hasColor) {
                colorSides = totalSides;
                bwSides = 0;
              } else {
                colorSides = 0;
                bwSides = totalSides;
              }
            }

            // NOTA: El endpoint /calculations/machine-cost ha sido eliminado
            // Se debe usar /v2/calculate-offset o /v2/calculate-digital según el tipo de máquina
            // Por ahora, usar cálculo local de respaldo
            if (part.machine && part.machine.hourly_cost) {
              // Estimar tiempo de impresión: 1 hora por cada 1000 pliegos
              const estimatedTime = part.customPrintTime || Math.max(1, Math.ceil(totalPliegosFisicos / 1000));
              const cfaPercentage = part.machine.cfa_percentage || 0;
              const cfaCost = (part.machine.hourly_cost * cfaPercentage / 100);
              const usageCost = part.machine.hourly_cost * estimatedTime;
              machineCost = cfaCost + usageCost;
              console.log('Coste de máquina calculado localmente (legacy):', machineCost);
            } else {
              machineCost = 0;
            }
          } else {
            console.warn('No hay máquina seleccionada para calcular el coste');
            machineCost = 0;
          }
        } catch (costError) {
          console.error('Error al calcular costes:', costError);
          showSnackbar(`Error al calcular costes: ${costError.message}`, 'error');

          // Calcular costes localmente como respaldo
          if (part.paper && part.paper.price_per_1000) {
            paperCost = (part.paper.price_per_1000 / 1000) * Math.ceil(totalPliegosFisicos);
            console.log('Coste de papel calculado localmente:', paperCost);
          }

          if (part.machine && part.machine.hourly_cost) {
            // Estimar tiempo de impresión: 1 hora por cada 1000 pliegos
            const estimatedTime = part.customPrintTime || Math.max(1, Math.ceil(totalPliegosFisicos / 1000));
            const cfaPercentage = part.machine.cfa_percentage || 0;
            const cfaCost = (part.machine.hourly_cost * cfaPercentage / 100);
            const usageCost = part.machine.hourly_cost * estimatedTime;
            machineCost = cfaCost + usageCost;
            console.log('Coste de máquina calculado localmente:', machineCost);
          }
        }
      } else if (isDigital && endpoint === '/v2/calculate-digital') {
        console.log('Usando costes ya calculados por el endpoint v2/calculate-digital');
      } else {
        console.log('Usando costes ya calculados por el endpoint v2/calculate-offset');
      }
    }

    // Calcular el coste de las planchas (para máquinas offset) o clicks (para máquinas digitales)
    if ((isOffset && endpoint === '/v2/calculate-offset') || (isDigital && endpoint === '/v2/calculate-digital')) {
      // Ya se han calculado los costes en el procesamiento de la respuesta
      if (isOffset) {
        console.log('Costes ya calculados por el endpoint v2/calculate-offset:', { paperCost, machineCost, plateCost });
      } else {
        console.log('Costes ya calculados por el endpoint v2/calculate-digital:', { paperCost, machineCost, clickCost });
      }

      // No necesitamos hacer más cálculos, todos los costes ya están incluidos en la respuesta
    } else {
      // Solo para endpoints tradicionales o máquinas digitales
      // Reiniciar para calcular con los endpoints tradicionales
      plateCost = 0;
      clickCost = 0;

      // Usar la variable isDigital ya definida anteriormente
      if (part.machine && part.machine.type === 'Digital') {
        try {
          // Para máquinas digitales, usamos el nuevo servicio de cálculo de clicks
          const totalSheets = Math.ceil(totalPliegosFisicos);
          const isColorPrint = part.colorConfig?.frontColors > 1 || part.colorConfig?.backColors > 1;
          const isDuplex = part.colorConfig?.backColors > 0; // Si hay colores en el reverso, es duplex

          // Llamar al servicio de cálculo de clicks
          // No enviamos copies porque totalSheets ya incluye el número de ejemplares
          const clicksResult = await digitalClicksService.calculateDigitalClicks({
            machineId: part.machine.machine_id || part.machine.product_id,
            sheets: totalSheets,
            copies: 1, // Usamos 1 porque totalSheets ya incluye el número de ejemplares
            isColor: isColorPrint,
            isDuplex: isDuplex
          });

          console.log('Resultado del cálculo de clicks:', clicksResult);

          // Actualizar el coste de clicks
          clickCost = clicksResult.total_cost;

          // Guardar los datos de tiempo estimado
          updatedParts[partIndex].clicksData = {
            print_speed: clicksResult.print_speed,
            a4_per_sheet: clicksResult.a4_per_sheet,
            estimated_time_minutes: clicksResult.estimated_time_minutes,
            estimated_time_hours: clicksResult.estimated_time_hours
          };

          // Actualizar el número de clicks en los datos normalizados
          if (normalizedData.mejor_combinacion) {
            normalizedData.mejor_combinacion.total_clicks = clicksResult.total_clicks;
          } else {
            normalizedData.total_clicks = clicksResult.total_clicks;
          }
        } catch (clickError) {
          console.error('Error al calcular los clicks:', clickError);

          // Cálculo de respaldo si falla el servicio
          const totalClicks = (normalizedData.mejor_combinacion && normalizedData.mejor_combinacion.total_clicks) ||
                            normalizedData.total_clicks ||
                            0;
          console.log('Total de clicks encontrado (respaldo):', totalClicks);

          // Obtener los costes de click de la máquina
          const clickColorCost = part.machine.click_color_cost || 0.15; // Coste por defecto si no está definido
          const clickBWCost = part.machine.click_bw_cost || 0.05; // Coste por defecto si no está definido

          // Determinar si los clicks son en color o en B/N
          const isColorPrint = part.colorConfig?.frontColors > 1 || part.colorConfig?.backColors > 1;
          const clickUnitCost = isColorPrint ? clickColorCost : clickBWCost;

          // Calcular el coste total de clicks
          clickCost = totalClicks * clickUnitCost;
          console.log('Coste de clicks calculado (respaldo):', clickCost);
        }
      } else if (!isOffset) { // Solo para máquinas offset que NO usan el endpoint v2
        // Para máquinas offset con endpoint tradicional, calculamos el coste de las planchas
        const totalPlanchas = (normalizedData.mejor_combinacion && normalizedData.mejor_combinacion.total_planchas) ||
                            normalizedData.total_planchas ||
                            0;
        console.log('Total de planchas encontrado:', totalPlanchas);

        // Calcular coste de planchas
        try {
          const plateResponse = await fetch(buildApiUrl('/calculations/plate-cost'), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              total_plates: totalPlanchas
            })
          });

          if (plateResponse.ok) {
            const plateData = await plateResponse.json();
            plateCost = plateData.cost;
            console.log('Coste de planchas calculado en el backend:', plateCost);
          } else {
            // Calcular localmente como respaldo
            // Precio por plancha (valor por defecto)
            const platePriceDefault = 15;
            plateCost = totalPlanchas * platePriceDefault;
            console.log('Coste de planchas calculado localmente:', plateCost);
          }
        } catch (plateError) {
          console.error('Error al calcular el coste de las planchas:', plateError);
          // Calcular localmente como respaldo
          const platePriceDefault = 15;
          plateCost = totalPlanchas * platePriceDefault;
          console.log('Coste de planchas calculado localmente (respaldo):', plateCost);
        }
      }
    }

    // Actualizar la parte con los resultados
    const isDigitalMachine = part.machine && part.machine.type === 'Digital';

    // Si es una máquina digital y no tenemos datos de clicks pero tenemos datos de tiempo estimado en el cálculo de pliegos
    if (isDigitalMachine &&
        normalizedData && normalizedData.mejor_combinacion &&
        normalizedData.mejor_combinacion.print_speed &&
        normalizedData.mejor_combinacion.estimated_time_minutes) {

      // Guardar los datos de tiempo estimado del cálculo de pliegos
      if (!updatedParts[partIndex].clicksData) {
        updatedParts[partIndex].clicksData = {};
      }

      // Solo sobrescribir si no hay datos previos de clicks
      if (!updatedParts[partIndex].clicksData.print_speed) {
        updatedParts[partIndex].clicksData = {
          print_speed: normalizedData.mejor_combinacion.print_speed,
          a4_per_sheet: normalizedData.mejor_combinacion.a4_per_sheet,
          estimated_time_minutes: normalizedData.mejor_combinacion.estimated_time_minutes,
          estimated_time_hours: normalizedData.mejor_combinacion.estimated_time_hours
        };
      }
    }

    updatedParts[partIndex] = {
      ...part,
      sheetCalculation: normalizedData,
      paperCost: paperCost,
      machineCost: machineCost,
      plateCost: isDigitalMachine ? 0 : plateCost,
      clickCost: isDigitalMachine ? clickCost : 0,
      // No incluir processCosts en el cálculo del coste total de la parte
      totalCost: paperCost + machineCost + (isDigitalMachine ? clickCost : plateCost)
    };

    console.log('Parte actualizada con costes:', updatedParts[partIndex]);

    setBudgetParts(updatedParts);

    // Calcular el coste de envío después de calcular los pliegos
    try {
      // Calcular el peso total del papel sumando los pesos de todas las partes
      let totalPaperWeight = 0;
      let clientCountry = "España"; // País por defecto

      // Obtener el peso del papel de los datos recibidos
      if (data && data.paper_weight_kg) {
        totalPaperWeight = data.paper_weight_kg;
        console.log('Peso del papel obtenido de la respuesta:', totalPaperWeight, 'kg');
      } else {
        // Si no hay peso en la respuesta, intentar calcularlo manualmente
        // Obtener las dimensiones y gramaje del papel
        const paperWidth = part.paper.dimension_width / 1000; // convertir a metros
        const paperHeight = part.paper.dimension_height / 1000; // convertir a metros
        const paperGsm = part.paper.weight; // gramaje en g/m²

        // Calcular el área del papel en m²
        const paperArea = paperWidth * paperHeight;

        // Calcular el peso de un pliego en kg
        const sheetWeightKg = (paperArea * paperGsm) / 1000;

        // Calcular el número total de pliegos
        let totalSheets = 0;
        if (data && data.mejor_combinacion && data.mejor_combinacion.total_pliegos) {
          totalSheets = data.mejor_combinacion.total_pliegos * copies;
        }

        // Calcular el peso total
        totalPaperWeight = sheetWeightKg * totalSheets;
        console.log('Peso del papel calculado manualmente:', totalPaperWeight, 'kg');
      }

      // Obtener el país del cliente si está disponible
      if (budget && budget.client_data && budget.client_data.company &&
          budget.client_data.company.address && budget.client_data.company.address.country) {
        clientCountry = budget.client_data.company.address.country;
      }

      // Si tenemos un presupuesto con ID, usar el endpoint del backend
      if (budgetId) {
        console.log('Calculando coste de envío para el presupuesto:', budgetId);
        const shippingResponse = await fetch(buildApiUrl('/calculator/calculate-shipping'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ budget_id: budgetId }),
        });

        if (shippingResponse.ok) {
          const shippingData = await shippingResponse.json();
          console.log('Datos de envío calculados:', shippingData);

          // Actualizar el presupuesto con los datos de envío
          setBudget(prev => ({
            ...prev,
            shipping_cost: shippingData.shipping_cost,
            total_paper_weight_kg: shippingData.total_paper_weight_kg
          }));

          showSnackbar(`Coste de envío calculado: ${shippingData.shipping_cost.toFixed(2)}€ (${shippingData.total_paper_weight_kg.toFixed(2)} kg)`, 'info');
        }
      } else {
        // Si no tenemos un presupuesto con ID, calcular el coste de envío usando el endpoint general
        console.log('Calculando coste de envío para presupuesto nuevo con peso:', totalPaperWeight, 'kg y país:', clientCountry);

        // Preparar los datos para la solicitud de cálculo de peso
        // El endpoint espera: paper_id, sheets, width_mm, height_mm, weight_gsm

        // Determinar el número total de pliegos según la estructura de datos
        let totalPliegos = 0;

        // Para el endpoint v2/calculate-offset
        if (data.total_sheets !== undefined) {
          console.log('Usando total_sheets del endpoint v2:', data.total_sheets);
          totalPliegos = data.total_sheets;
        }
        // Para el endpoint tradicional
        else if (data.mejor_combinacion && data.mejor_combinacion.total_pliegos !== undefined) {
          console.log('Usando total_pliegos de mejor_combinacion:', data.mejor_combinacion.total_pliegos);
          totalPliegos = data.mejor_combinacion.total_pliegos;
        }
        // Si no hay datos, usar un valor por defecto
        else {
          console.warn('No se encontró información de pliegos en la respuesta, usando valor por defecto');
          totalPliegos = 0;
        }

        const paperWeightRequest = {
          paper_id: part.paper.product_id, // Usar product_id en lugar de paper_id
          sheets: totalPliegos * copies
        };

        console.log('Enviando solicitud de cálculo de peso:', paperWeightRequest);

        const shippingResponse = await fetch(buildApiUrl('/calcular-peso-papel'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(paperWeightRequest),
        });

        if (shippingResponse.ok) {
          const weightData = await shippingResponse.json();
          console.log('Datos de peso calculados:', weightData);

          // Usar el endpoint /calculations/shipping-cost para calcular el costo de envío
          console.log(`Calculando costo de envío para ${weightData.total_weight_kg} kg a ${clientCountry}`);

          const shippingCostResponse = await fetch(buildApiUrl('/calculations/shipping-cost'), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              weight_kg: weightData.total_weight_kg,
              country: clientCountry
            }),
          });

          if (shippingCostResponse.ok) {
            const shippingData = await shippingCostResponse.json();
            console.log('Datos de envío calculados por el endpoint:', shippingData);

            // Actualizar el presupuesto con los datos de envío
            setBudget(prev => ({
              ...prev,
              shipping_cost: shippingData.shipping_cost,
              total_paper_weight_kg: weightData.total_weight_kg
            }));

            showSnackbar(`Coste de envío calculado: ${shippingData.shipping_cost.toFixed(2)}€ (${weightData.total_weight_kg.toFixed(2)} kg)`, 'info');
          } else {
            console.error('Error al calcular el coste de envío');
            // Actualizar solo el peso total del papel
            setBudget(prev => ({
              ...prev,
              total_paper_weight_kg: weightData.total_weight_kg
            }));
          }
        }
      }
    } catch (shippingError) {
      console.error('Error al calcular el coste de envío:', shippingError);
      // No mostrar error al usuario para no interrumpir el flujo principal
    }

    // Preparar datos adicionales para el modal
    const partWithExtraInfo = {
      ...updatedParts[partIndex],
      paper_data: part.paper,
      machine_data: part.machine,
      copies: copies
    };

    // Mostrar el modal con los resultados
    // Usar la estructura normalizada para el modal
    setSheetCalculation(normalizedData);
    setCurrentCalculatedPart(partWithExtraInfo);
    console.log('Parte calculada que se pasa al modal:', partWithExtraInfo);
    setSheetCalculationModal(true);

    showSnackbar(`Cálculo de pliegos completado para la parte ${part.name}`, 'success');

    return normalizedData;
  } catch (err) {
    console.error('Error al calcular pliegos para la parte:', err);
    showSnackbar(err.message, 'error');
    return null;
  } finally {
    setCalculatingSheets(false);
  }
};
