from pydantic import BaseModel
from typing import List, Optional, Dict, Any

class PaperWasteParams(BaseModel):
    """
    Modelo para los parámetros de cálculo de desperdicio de papel.
    """
    sangrado: float = 3.0
    pinzas: float = 10.0
    margen_lateral: float = 5.0
    margen_superior: float = 5.0
    margen_inferior: float = 5.0
    marcas_registro: bool = True
    tiras_control: bool = True

class PaperWasteInfo(BaseModel):
    """
    Modelo para información de desperdicio de papel.
    """
    area_pliego_mm2: float
    area_utilizada_mm2: float
    area_desperdiciada_mm2: float
    porcentaje_desperdicio: float
    porcentaje_aprovechamiento: float
    ancho_minimo_mm: float
    alto_minimo_mm: float
    area_minima_mm2: float
    pliego_optimo: bool
    area_paginas_mm2: float
    area_sangrado_mm2: float = 0.0
    area_pinzas_mm2: float = 0.0
    area_margenes_mm2: float = 0.0
    area_marcas_mm2: float = 0.0
    recomendaciones: List[str] = []
