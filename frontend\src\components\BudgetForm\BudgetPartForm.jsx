import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  TextField,
  Grid,
  Autocomplete,
  MenuItem,
  IconButton,
  Paper,
  Divider,
  Button,
  Tooltip,
  Collapse,
  Radio,
  RadioGroup,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import CalculateIcon from '@mui/icons-material/Calculate';
import InfoIcon from '@mui/icons-material/Info';
import PaletteIcon from '@mui/icons-material/Palette';
import PrintIcon from '@mui/icons-material/Print';
import DescriptionIcon from '@mui/icons-material/Description';
import ContentCutIcon from '@mui/icons-material/ContentCut';
import LayersIcon from '@mui/icons-material/Layers';
import InvertColorsIcon from '@mui/icons-material/InvertColors';
import CameraIcon from '@mui/icons-material/Camera';
import SummarizeIcon from '@mui/icons-material/Summarize';
import PaperInfoModal from './PaperInfoModal';

/**
 * Componente para manejar una parte individual del presupuesto
 */
const BudgetPartForm = ({
  part,
  index,
  papers,
  machines,
  // processes no se utiliza directamente en este componente pero se pasa a través de las props
  // para mantener la compatibilidad con la interfaz existente
  onPartChange,
  onDeletePart,
  onCalculateSheetsV2,
  onCalculateSheetsV2Silent,
  onMachineInfoClick,
  disabled = false,
  calculatingSheets = false,
  isEditMode = false
}) => {
  // Si es un nuevo presupuesto, el desplegable estará abierto por defecto
  // Si es edición, estará cerrado por defecto
  // Forzamos que esté cerrado en modo edición
  const [expanded, setExpanded] = useState(isEditMode ? false : true);

  // Actualizar el estado expanded cuando cambia isEditMode
  useEffect(() => {
    setExpanded(!isEditMode);
  }, [isEditMode]);
  // Estado local para procesos (no utilizado actualmente, pero mantenido para compatibilidad futura)
  const [, setSelectedProcesses] = useState([]);
  const [colorConfig, setColorConfig] = useState(part.colorConfig || {
    frontColors: 4,
    backColors: 4,
    pantones: 0
  });
  const [colorDialogOpen, setColorDialogOpen] = useState(false);
  const [paperDialogOpen, setPaperDialogOpen] = useState(false);

  // Actualizar la configuración de colores cuando cambia la parte
  useEffect(() => {
    if (part.colorConfig) {
      setColorConfig(part.colorConfig);
    }
  }, [part.colorConfig]);

  // Actualizar los procesos seleccionados cuando cambian en el prop
  useEffect(() => {
    if (part.processCosts) {
      setSelectedProcesses(part.processCosts);
    }
  }, [part.processCosts]);

  // Efecto para actualizar cuando cambian los datos de tinta
  useEffect(() => {
    // Monitorear cambios en los datos de tinta
  }, [part.sheetCalculation?.ink_weight_kg, part.sheetCalculation?.ink_cost]);

  // Comparar el total_cost del endpoint con el calculado localmente
  useEffect(() => {
    if (part.sheetCalculation?.total_cost !== undefined) {
      // Calcular los componentes del costo total
      let paperCost = part.sheetCalculation.paper_cost || 0;
      let machineCost = part.sheetCalculation.machine_cost || 0;
      let plateCost = part.sheetCalculation.plates_cost || 0;
      let inkCost = part.sheetCalculation.ink_cost || 0;

      // Calcular el costo de maculatura
      let calculatedMaculaturaCost = 0;
      if (part.sheetCalculation?.total_maculatura > 0) {
        const precioPorMillar = part.sheetCalculation.paper_cost_per_1000 ||
                              (part.paper && part.paper.price_per_1000) || 96.60;
        calculatedMaculaturaCost = (part.sheetCalculation.total_maculatura * precioPorMillar) / 1000;
      }

      // Calcular el total
      const calculatedTotal = paperCost + machineCost + plateCost + inkCost + calculatedMaculaturaCost;

      // Verificar diferencia entre costo calculado y costo del endpoint
    }
  }, [part.sheetCalculation?.total_cost, part.name, part.paper, part.sheetCalculation]);

  // Manejar cambios en los campos de texto
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Lista de campos que afectan al cálculo de pliegos
    const fieldsAffectingCalculation = ['pageCount', 'pageSize', 'customPageSize', 'copies'];

    // Si el campo modificado afecta al cálculo y la parte ya tiene cálculos previos, marcarla como desactualizada
    if (fieldsAffectingCalculation.includes(name) && part.sheetCalculation) {
      onPartChange(index, {
        ...part,
        [name]: value,
        sheetCalculation: {
          ...part.sheetCalculation,
          outdated: true // Marcar como desactualizada
        }
      });
    } else {
      // Cambios en otros campos que no afectan al cálculo
      onPartChange(index, { ...part, [name]: value });
    }

    // No recalcular pliegos automáticamente - el usuario debe pulsar el botón de calcular pliegos
  };

  // Manejar cambios en la configuración de colores
  const handleColorConfigChange = (newColorConfig) => {
    setColorConfig(newColorConfig);
    onPartChange(index, { ...part, colorConfig: newColorConfig });
  };

  // Manejar cambios en selecciones de autocomplete
  const handleAutocompleteChange = (name, value) => {
    // Lista de campos que afectan al cálculo de pliegos
    const fieldsAffectingCalculation = ['machine', 'paper'];

    // Si estamos cambiando la máquina, verificar si el papel actual es compatible
    if (name === 'machine' && value && part.paper) {
      const paperIsCompatible = isPaperCompatibleWithMachine(part.paper, value);

      // Si el papel no es compatible con la nueva máquina, resetear el papel
      if (!paperIsCompatible) {
        // Mostrar mensaje de advertencia
        if (typeof onDeletePart === 'function' && typeof onDeletePart.showSnackbar === 'function') {
          onDeletePart.showSnackbar(
            `El papel "${part.paper.descriptive_name}" no es compatible con la máquina "${value.name}". Se ha reseteado la selección de papel.`,
            'warning'
          );
        }

        onPartChange(index, {
          ...part,
          [name]: value,
          paper: null, // Resetear el papel si no es compatible
          // Si ya tenía cálculos previos, marcarlos como desactualizados
          sheetCalculation: part.sheetCalculation ? {
            ...part.sheetCalculation,
            outdated: true
          } : null
        });
        return;
      }
    }

    // Si el campo modificado afecta al cálculo y la parte ya tiene cálculos previos, marcarla como desactualizada
    if (fieldsAffectingCalculation.includes(name) && part.sheetCalculation) {
      onPartChange(index, {
        ...part,
        [name]: value,
        sheetCalculation: {
          ...part.sheetCalculation,
          outdated: true // Marcar como desactualizada
        }
      });
    } else {
      // Cambios en otros campos que no afectan al cálculo
      onPartChange(index, { ...part, [name]: value });
    }

    // No recalcular pliegos automáticamente - el usuario debe pulsar el botón de calcular pliegos
  };

  // Función para verificar si un papel es compatible con una máquina
  const isPaperCompatibleWithMachine = (paper, machine) => {
    if (!paper || !machine) return false;

    // Obtener dimensiones del papel
    const paperWidth = parseFloat(paper.dimension_width);
    const paperHeight = parseFloat(paper.dimension_height);

    // Verificar compatibilidad basada en el tipo de máquina
    if (machine.type === 'Digital') {
      // Para máquinas digitales, usar las dimensiones máximas reales de la máquina
      const machineMaxWidth = parseFloat(machine.max_width || 330); // Por defecto SRA3 ancho
      const machineMaxHeight = parseFloat(machine.max_height || 488); // Por defecto SRA3 alto

      // Verificar si el papel cabe en la máquina (en cualquier orientación)
      const fitsNormal = paperWidth <= machineMaxWidth && paperHeight <= machineMaxHeight;
      const fitsRotated = paperHeight <= machineMaxWidth && paperWidth <= machineMaxHeight;

      // Debug log para máquinas digitales
      console.log(`[DEBUG] Papel "${paper.descriptive_name}" (${paperWidth}x${paperHeight}mm) vs Máquina "${machine.name}" (${machineMaxWidth}x${machineMaxHeight}mm)`);
      console.log(`[DEBUG] - Cabe normal: ${fitsNormal}, Cabe rotado: ${fitsRotated}, Compatible: ${fitsNormal || fitsRotated}`);

      return fitsNormal || fitsRotated;
    } else if (machine.type === 'Offset') {
      // Para máquinas offset, verificar dimensiones exactas
      // Obtener dimensiones mínimas y máximas de la máquina
      const machineMinWidth = parseFloat(machine.min_width || 0);
      const machineMinHeight = parseFloat(machine.min_height || 0);
      const machineMaxWidth = parseFloat(machine.max_width || 2000); // Valor por defecto alto
      const machineMaxHeight = parseFloat(machine.max_height || 2000); // Valor por defecto alto

      // Si alguna dimensión no es un número válido, asumir que es compatible
      if (isNaN(paperWidth) || isNaN(paperHeight) ||
          isNaN(machineMinWidth) || isNaN(machineMinHeight) ||
          isNaN(machineMaxWidth) || isNaN(machineMaxHeight)) {
        return true;
      }

      // Verificar orientación normal (ancho x alto)
      const normalOrientation =
        paperWidth >= machineMinWidth && paperWidth <= machineMaxWidth &&
        paperHeight >= machineMinHeight && paperHeight <= machineMaxHeight;

      // Verificar orientación rotada (alto x ancho)
      const rotatedOrientation =
        paperHeight >= machineMinWidth && paperHeight <= machineMaxWidth &&
        paperWidth >= machineMinHeight && paperWidth <= machineMaxHeight;

      // El papel es compatible si funciona en al menos una orientación
      return normalOrientation || rotatedOrientation;
    }

    // Si no podemos determinar la compatibilidad, asumir que es compatible
    return true;
  };

  // Función para manejar la creación de un nuevo papel
  const handlePaperCreated = (newPaper) => {
    // Verificar si el nuevo papel es compatible con la máquina seleccionada
    if (part.machine && !isPaperCompatibleWithMachine(newPaper, part.machine)) {
      // Mostrar mensaje de advertencia si no es compatible
      if (typeof onDeletePart === 'function' && typeof onDeletePart.showSnackbar === 'function') {
        let mensaje = `El papel "${newPaper.descriptive_name}" no es compatible con la máquina "${part.machine.name}".`;

        if (part.machine.type === 'Digital') {
          const maxW = part.machine.max_width || 330;
          const maxH = part.machine.max_height || 488;
          mensaje += ` Esta máquina digital solo acepta papeles de hasta ${maxW}x${maxH}mm.`;
        } else if (part.machine.type === 'Offset') {
          mensaje += ` Verifique que las dimensiones del papel (${newPaper.dimension_width}x${newPaper.dimension_height}mm) sean compatibles con la máquina.`;
        }

        onDeletePart.showSnackbar(mensaje, 'warning');
      }
      return;
    }

    // Seleccionar automáticamente el nuevo papel
    handleAutocompleteChange('paper', newPaper);

    // Mostrar mensaje de éxito
    if (typeof onDeletePart === 'function' && typeof onDeletePart.showSnackbar === 'function') {
      onDeletePart.showSnackbar(
        `Papel "${newPaper.descriptive_name}" creado y seleccionado correctamente.`,
        'success'
      );
    }
  };

  // Filtrar los papeles según la máquina seleccionada
  const getFilteredPapers = () => {
    if (!part.machine) return papers || [];

    const allPapers = papers || [];
    const filteredPapers = allPapers.filter(paper => isPaperCompatibleWithMachine(paper, part.machine));

    // Debug log para ver el resultado del filtrado
    console.log(`[DEBUG] Máquina seleccionada: ${part.machine.name} (${part.machine.type})`);
    console.log(`[DEBUG] Total papeles disponibles: ${allPapers.length}`);
    console.log(`[DEBUG] Papeles compatibles: ${filteredPapers.length}`);
    console.log(`[DEBUG] Papeles compatibles:`, filteredPapers.map(p => `${p.descriptive_name} (${p.dimension_width}x${p.dimension_height}mm, inStock: ${p.inStock})`));

    return filteredPapers;
  };

  // Declarar variables para los costos y detalles
  let machineCost = 0;
  let paperCost = 0;
  let plateCost = 0;
  let inkCost = 0;
  let clickCost = 0; // Costo de clicks para máquinas digitales
  let cfaCost = 0; // Costo de ajuste de color (Color Fine Adjustment)
  let paperWeight = 0; // Peso del papel en kg
  let paperDetails = "";
  let plateDetails = "";
  let inkDetails = "";
  let clickDetails = ""; // Detalles de clicks para máquinas digitales
  let paperWeightDetails = "";
  let totalCost = 0;

  // Determinar si es una máquina digital
  const isDigital = part.machine && part.machine.type === 'Digital';

  // Solo usar los valores calculados por el backend si están disponibles
  if (part.sheetCalculation) {

    // Usar directamente los valores calculados por el backend

    // Costo del papel
    if (part.sheetCalculation.paper_cost !== undefined) {
      paperCost = part.sheetCalculation.paper_cost;

      // Mostrar el número de pliegos impresos si está disponible
      if (part.sheetCalculation.total_physical_sheets !== undefined) {
        paperDetails = `(${part.sheetCalculation.total_physical_sheets} pliegos impresos)`;
      } else if (part.sheetCalculation.total_sheets !== undefined) {
        paperDetails = `(${part.sheetCalculation.total_sheets} pliegos impresos)`;
      } else if (part.sheetCalculation.calculo_pliegos_info?.total_pliegos !== undefined) {
        paperDetails = `(${part.sheetCalculation.calculo_pliegos_info.total_pliegos} pliegos impresos)`;
      } else {
        paperDetails = "(Calculado por el backend)";
      }
    }

    // Costo de la máquina basado en el tiempo y tarifa horaria


    if (part.sheetCalculation.hourly_cost && part.sheetCalculation.total_time_hours) {
      // Calcular el costo de máquina basado en el tiempo total y la tarifa horaria
      machineCost = part.sheetCalculation.total_time_hours * part.sheetCalculation.hourly_cost;

    } else if (part.sheetCalculation.machine_cost !== undefined) {
      // Si no podemos calcular basado en tiempo, usar el valor proporcionado por el backend
      machineCost = part.sheetCalculation.machine_cost;

    } else if (part.sheetCalculation.printing_cost !== undefined) {
      // Como última opción, usar el costo de impresión
      machineCost = part.sheetCalculation.printing_cost;
      // Añadir el CFA si está disponible
      if (part.sheetCalculation.cfa_cost !== undefined) {
        cfaCost = part.sheetCalculation.cfa_cost;
        machineCost += cfaCost;
      }
    }

    // Para máquinas digitales, manejar clicks en lugar de planchas y tinta
    if (isDigital) {
      // Usar directamente los valores del endpoint para máquinas digitales
      clickCost = part.sheetCalculation.click_cost || 0;

      // Detalles de clicks
      if (part.sheetCalculation.total_clicks !== undefined && part.sheetCalculation.click_unit_cost !== undefined) {
        clickDetails = `(${part.sheetCalculation.total_clicks} clicks x ${part.sheetCalculation.click_unit_cost.toFixed(4)} € por click)`;
      } else {
        clickDetails = "(Calculado por el backend)";
      }
    } else {
      // Para máquinas offset, manejar planchas y tinta
      // Costo de planchas
      if (part.sheetCalculation.plates_cost !== undefined) {
        plateCost = part.sheetCalculation.plates_cost;
        plateDetails = "(Calculado por el backend)";
      }

      // Costo de tinta
      if (part.sheetCalculation.ink_cost !== undefined) {
        inkCost = part.sheetCalculation.ink_cost;

        if (part.sheetCalculation.ink_weight_kg !== undefined) {
          // Usar el valor directamente del endpoint
          inkDetails = `(${part.sheetCalculation.ink_weight_kg.toFixed(2)} kg)`;
        } else if (part.sheetCalculation.machine_data?.ink_weight_kg !== undefined) {
          // Alternativa: usar el valor de machine_data si está disponible
          inkDetails = `(${part.sheetCalculation.machine_data.ink_weight_kg.toFixed(2)} kg)`;
        } else {
          inkDetails = "(Peso no disponible)";
        }
      }
    }

    // Peso del papel
    if (part.sheetCalculation.paper_weight_kg !== undefined) {
      paperWeight = part.sheetCalculation.paper_weight_kg;
      paperWeightDetails = `(${paperWeight.toFixed(2)} kg)`;
    }


    // Añadir más detalles a los costos si están disponibles
    if (part.sheetCalculation.mejor_combinacion) {
      const totalPlanchas = part.sheetCalculation.mejor_combinacion.total_planchas || 0;

      // Añadir detalles de planchas al costo de planchas
      if (totalPlanchas > 0) {
        plateDetails = `(${totalPlanchas} planchas)`;
      }

      // No sobrescribimos paperDetails aquí, ya que queremos mostrar los pliegos físicos impresos
    }

    // Recalcular el costo total basado en los valores actualizados
    // Especialmente importante porque hemos cambiado el cálculo del costo de máquina
    // Calcular el costo de maculatura si está disponible
    let calculatedMaculaturaCost = 0;
    if (part.sheetCalculation?.total_maculatura > 0) {
      const precioPorMillar = part.sheetCalculation.paper_cost_per_1000 ||
                            (part.paper && part.paper.price_per_1000) || 96.60;
      calculatedMaculaturaCost = (part.sheetCalculation.total_maculatura * precioPorMillar) / 1000;
    }

    // Calcular el costo total
    if (isDigital) {
      // Para máquinas digitales, incluir el costo de clicks en lugar de planchas y tinta
      // Las máquinas digitales no utilizan maculatura
      totalCost = paperCost + machineCost + clickCost;
    } else {
      // Para máquinas offset, incluir el costo de planchas, tinta y maculatura
      totalCost = paperCost + machineCost + plateCost + inkCost + calculatedMaculaturaCost;
    }
  } else {
    // Si no tenemos los datos del cálculo, no hacemos nada
  }

  return (
    <>
      <Dialog
        open={colorDialogOpen}
        onClose={() => setColorDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PaletteIcon sx={{ mr: 1 }} /> Configuración de Colores
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Colores Anverso (Cara)</Typography>
                <RadioGroup
                  name="frontColors"
                  value={colorConfig.frontColors.toString()}
                  onChange={(e) => handleColorConfigChange({ ...colorConfig, frontColors: parseInt(e.target.value) })}
                >
                  <FormControlLabel value="4" control={<Radio />} label="4 colores (CMYK)" />
                  <FormControlLabel value="2" control={<Radio />} label="2 colores" />
                  <FormControlLabel value="1" control={<Radio />} label="1 color" />
                  <FormControlLabel value="0" control={<Radio />} label="Sin color" />
                </RadioGroup>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Colores Reverso</Typography>
                <RadioGroup
                  name="backColors"
                  value={colorConfig.backColors.toString()}
                  onChange={(e) => handleColorConfigChange({ ...colorConfig, backColors: parseInt(e.target.value) })}
                >
                  <FormControlLabel value="4" control={<Radio />} label="4 colores (CMYK)" />
                  <FormControlLabel value="2" control={<Radio />} label="2 colores" />
                  <FormControlLabel value="1" control={<Radio />} label="1 color" />
                  <FormControlLabel value="0" control={<Radio />} label="Sin color" />
                </RadioGroup>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>Pantones Adicionales</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body2" sx={{ mr: 2 }}>Número de pantones:</Typography>
                  <TextField
                    type="number"
                    value={colorConfig.pantones}
                    onChange={(e) => handleColorConfigChange({ ...colorConfig, pantones: parseInt(e.target.value) || 0 })}
                    InputProps={{ inputProps: { min: 0, max: 5 } }}
                    size="small"
                    sx={{ width: '80px' }}
                  />
                </Box>
              </Box>
            </Grid>
          </Grid>

          <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>Configuración de Colores Actual</Typography>
            <Typography variant="body1">
              {`${colorConfig.frontColors}/${colorConfig.backColors}${colorConfig.pantones > 0 ? ` + ${colorConfig.pantones} Pantones` : ''}`}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setColorDialogOpen(false)}>Cancelar</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              setColorDialogOpen(false);
            }}
          >
            Aceptar
          </Button>
        </DialogActions>
      </Dialog>

      <Paper elevation={2} sx={{ p: 2, mb: 3, position: 'relative' }}>
        {/* Cabecera de la parte */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 1,
            cursor: 'pointer',
            borderBottom: expanded ? '1px solid #e0e0e0' : 'none'
          }}
          onClick={() => setExpanded(!expanded)}
        >
          {/* Grupo de controles a la izquierda: expandir + título */}
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            {/* Botón expandir/colapsar */}
            <IconButton
              onClick={(e) => {
                e.stopPropagation();
                setExpanded(!expanded);
              }}
              size="small"
              sx={{ mr: 1 }}
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>

            {/* Botón de calcular pliegos */}
            <Tooltip title="Calcular pliegos">
              <span>
                <IconButton
                  color="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    onCalculateSheetsV2Silent(index); // Usar la versión silenciosa que no muestra el modal
                  }}
                  size="small"
                  disabled={!part.paper || !part.machine || !part.pageCount || disabled || calculatingSheets}
                  sx={{ mr: 1 }}
                >
                  <CalculateIcon />
                </IconButton>
              </span>
            </Tooltip>

            {/* Título de la parte */}
            <Typography variant="h6" component="div">
              Parte {index + 1}: {part.name} {totalCost > 0 && `(${totalCost.toFixed(2)} €)`}
            </Typography>
          </Box>

          {/* Botón de eliminar parte (a la derecha) */}
          <Tooltip title="Eliminar parte">
            <span>
              <IconButton
                color="error"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeletePart(index);
                }}
                size="small"
                disabled={disabled}
              >
                <DeleteIcon />
              </IconButton>
            </span>
          </Tooltip>
        </Box>

        {!expanded && (
          <Box>
            {/* Resumen de costos cuando está minimizado */}
            {part.sheetCalculation && (
              <Box sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 1,
                mt: 1.5,
                p: 1,
                bgcolor: '#f8f9fa',
                borderRadius: 1,
                border: '1px solid #e0e0e0'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                  <CalculateIcon sx={{ mr: 0.5, fontSize: '1.2rem', color: '#555' }} />
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                    Costos Calculados:
                  </Typography>
                </Box>

                {/* Máquina */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  px: 1,
                  borderLeft: '3px solid #1976d2',
                  mr: 1
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PrintIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#1976d2' }} />
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      Máquina:
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#1976d2' }}>
                    {(part.sheetCalculation.machine_cost || 0).toFixed(2)} €
                  </Typography>
                </Box>

                {/* Papel */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  px: 1,
                  borderLeft: '3px solid #4caf50',
                  mr: 1
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <DescriptionIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#4caf50' }} />
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                      Papel:
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#4caf50' }}>
                    {(part.sheetCalculation.paper_cost || 0).toFixed(2)} €
                  </Typography>
                </Box>

                {/* Maculatura - Solo si hay datos */}
                {part.sheetCalculation.total_maculatura > 0 && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 1,
                    borderLeft: '3px solid #ff9800',
                    mr: 1
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <DescriptionIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#ff9800' }} />
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        Maculatura:
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#ff9800' }}>
                      {(() => {
                        const precioPorMillar = part.sheetCalculation.paper_cost_per_1000 ||
                                            (part.paper && part.paper.price_per_1000) || 96.60;
                        const maculaturaCost = (part.sheetCalculation.total_maculatura * precioPorMillar) / 1000;
                        return maculaturaCost.toFixed(2);
                      })()} €
                    </Typography>
                  </Box>
                )}

                {/* Clicks o Planchas y Tinta */}
                {part.machine && part.machine.type === 'Digital' ? (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 1,
                    borderLeft: '3px solid #9c27b0',
                    mr: 1
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CameraIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#9c27b0' }} />
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        Clicks:
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#9c27b0' }}>
                      {(part.sheetCalculation.click_cost || 0).toFixed(2)} €
                    </Typography>
                  </Box>
                ) : (
                  <>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      px: 1,
                      borderLeft: '3px solid #f44336',
                      mr: 1
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <LayersIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#f44336' }} />
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          Planchas:
                        </Typography>
                      </Box>
                      <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#f44336' }}>
                        {(part.sheetCalculation.plates_cost || 0).toFixed(2)} €
                      </Typography>
                    </Box>

                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      px: 1,
                      borderLeft: '3px solid #673ab7',
                      mr: 1
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <InvertColorsIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#673ab7' }} />
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          Tinta:
                        </Typography>
                      </Box>
                      <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#673ab7' }}>
                        {(part.sheetCalculation.ink_cost || 0).toFixed(2)} €
                      </Typography>
                    </Box>
                  </>
                )}

                {/* Costo Total */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  px: 1,
                  ml: 'auto',
                  borderLeft: '3px solid #2e7d32',
                  bgcolor: '#f1f8e9',
                  borderRadius: '0 4px 4px 0'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SummarizeIcon sx={{ mr: 0.5, fontSize: '1rem', color: '#2e7d32' }} />
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      Total:
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 'bold', color: '#2e7d32' }}>
                    {totalCost.toFixed(2)} €
                  </Typography>
                </Box>
              </Box>
            )}
          </Box>
        )}

      <Collapse in={expanded}>
        <Grid container spacing={2}>
          {/* Nombre de la parte */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Nombre de la parte"
              name="name"
              value={part.name}
              onChange={handleChange}
              variant="outlined"
              required
              disabled={disabled}
            />
          </Grid>

          {/* Descripción de la parte */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Descripción"
              name="description"
              value={part.description}
              onChange={handleChange}
              variant="outlined"
              disabled={disabled}
            />
          </Grid>

          {/* Tipo de encuadernado */}
          <Grid item xs={12} md={6}>
            <TextField
              select
              label="Tipo de encuadernado"
              name="assembly_order"
              value={part.assembly_order || 'None'}
              onChange={handleChange}
              fullWidth
              disabled={disabled}
            >
              <MenuItem value="None">Sin encuadernado</MenuItem>
              <MenuItem value="Gathering">Alzado (Gathering)</MenuItem>
              <MenuItem value="Collecting">Grapado (Collecting)</MenuItem>
            </TextField>
          </Grid>

          {/* Tamaño de página */}
          <Grid item xs={12} md={6}>
            <TextField
              select
              label="Tamaño de página"
              name="pageSize"
              value={part.pageSize}
              onChange={handleChange}
              fullWidth
              required
              disabled={disabled}
            >
              <MenuItem value="A4">A4 (210 x 297 mm)</MenuItem>
              <MenuItem value="A5">A5 (148 x 210 mm)</MenuItem>
              <MenuItem value="A3">A3 (297 x 420 mm)</MenuItem>
              <MenuItem value="Carta">Carta (216 x 279 mm)</MenuItem>
              <MenuItem value="Personalizado">Personalizado</MenuItem>
            </TextField>
          </Grid>

          {/* Tamaño personalizado */}
          {part.pageSize === 'Personalizado' && (
            <Grid item xs={12} md={6}>
              <TextField
                label="Tamaño personalizado (ancho x alto mm)"
                name="customPageSize"
                value={part.customPageSize}
                onChange={handleChange}
                fullWidth
                placeholder="Ej: 210 x 297"
                required={part.pageSize === 'Personalizado'}
                disabled={disabled}
              />
            </Grid>
          )}

          {/* Configuración de colores */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <TextField
                label="Configuración de colores"
                value={`${colorConfig.frontColors}/${colorConfig.backColors}${colorConfig.pantones > 0 ? ` + ${colorConfig.pantones}P` : ''}`}
                fullWidth
                InputProps={{
                  readOnly: true,
                  startAdornment: (
                    <PaletteIcon color="primary" sx={{ mr: 1 }} />
                  )
                }}
                helperText="Colores anverso/reverso + pantones"
                disabled={disabled}
              />
              <Tooltip title="Configurar colores">
                <span>
                  <IconButton
                    color="primary"
                    onClick={() => setColorDialogOpen(true)}
                    size="small"
                    sx={{ ml: 1, mt: 1 }}
                    disabled={disabled}
                  >
                    <InfoIcon />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          </Grid>

          {/* Número de páginas */}
          <Grid item xs={12} md={6}>
            <TextField
              label="Número de páginas"
              type="number"
              name="pageCount"
              value={part.pageCount}
              onChange={handleChange}
              fullWidth
              required
              InputProps={{
                inputProps: { min: 1 }
              }}
              helperText="Número de páginas de esta parte"
              disabled={disabled}
            />
          </Grid>

          {/* Máquina */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <Autocomplete
                options={machines || []}
                getOptionLabel={(option) => {
                  if (!option) return '';
                  return `${option.name || ''} - ${option.type || ''}`;
                }}
                value={part.machine}
                onChange={(_, newValue) => {
                  handleAutocompleteChange('machine', newValue);
                }}
                renderOption={(props, option) => (
                  <li {...props} key={option.machine_id || `machine-${Math.random()}`}>
                    {`${option.name || ''} - ${option.type || ''}`}
                  </li>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Máquina de imprimir"
                    variant="outlined"
                    required
                    disabled={disabled}
                  />
                )}
                sx={{ flexGrow: 1 }}
                disabled={disabled}
              />
              <Tooltip title="Ver información detallada y costes de la máquina">
                <span>
                  <IconButton
                    color="primary"
                    onClick={() => onMachineInfoClick(part.machine, index)}
                    disabled={!part.machine || disabled}
                    sx={{ mt: 1, ml: 1 }}
                  >
                    <InfoIcon />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          </Grid>

          {/* Papel */}
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <Autocomplete
                options={getFilteredPapers()}
                getOptionLabel={(option) => {
                  if (!option) return '';
                  return `${option.descriptive_name || ''} - ${option.weight || ''}g - ${option.dimension_width || ''}x${option.dimension_height || ''}mm`;
                }}
                value={part.paper}
                onChange={(_, newValue) => {
                  handleAutocompleteChange('paper', newValue);
                }}
                renderOption={(props, option) => (
                  <li {...props} key={option.product_id || `paper-${Math.random()}`}>
                    {`${option.descriptive_name || ''} - ${option.weight || ''}g - ${option.dimension_width || ''}x${option.dimension_height || ''}mm`}
                  </li>
                )}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Papel"
                    variant="outlined"
                    required
                    disabled={disabled}
                    helperText={part.machine ?
                      `Mostrando ${getFilteredPapers().length} papeles compatibles con ${part.machine.name}` :
                      "Selecciona primero una máquina para filtrar los papeles compatibles"}
                  />
                )}
                disabled={disabled || !part.machine}
                noOptionsText="No hay papeles compatibles con esta máquina"
                sx={{ flexGrow: 1 }}
              />
              <Tooltip title="Crear nuevo papel">
                <span>
                  <IconButton
                    color="primary"
                    onClick={() => setPaperDialogOpen(true)}
                    disabled={disabled}
                    sx={{ mt: 1, ml: 1 }}
                  >
                    <InfoIcon />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
          </Grid>

          {/* Botones para calcular pliegos */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
              <Button
  variant="contained"
  color="primary"
  startIcon={calculatingSheets ? null : <CalculateIcon />}
  onClick={() => onCalculateSheetsV2(index)}
  disabled={!part.paper || !part.machine || !part.pageCount || disabled || calculatingSheets}
>
  {calculatingSheets ? 'Calculando...' : 'Calcular Pliegos'}
</Button>
            </Box>
          </Grid>

          {/* Información de costos */}
          {part.sheetCalculation && (
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom sx={{
                  fontWeight: 600,
                  color: '#1976d2',
                  display: 'flex',
                  alignItems: 'center',
                  '&:after': {
                    content: '""',
                    display: 'block',
                    height: '2px',
                    background: 'linear-gradient(90deg, #1976d2 0%, rgba(25, 118, 210, 0.1) 100%)',
                    flexGrow: 1,
                    ml: 2
                  }
                }}>
                  Costos Calculados
                </Typography>

                <Box sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 2,
                  mt: 3
                }}>
                  {/* Máquina */}
                  <Paper elevation={2} sx={{
                    p: 2,
                    minWidth: '150px',
                    flex: '1 1 150px',
                    borderTop: '4px solid #1976d2',
                    borderRadius: '4px',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-3px)',
                      boxShadow: 3
                    }
                  }}>
                    <Typography variant="subtitle2" sx={{ color: 'text.secondary', mb: 1 }}>
                      Máquina
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                      {machineCost.toFixed(2)} €
                    </Typography>
                    {part.sheetCalculation?.total_time_minutes && (
                      <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                        Tiempo: {part.sheetCalculation.total_time_minutes} min
                      </Typography>
                    )}
                  </Paper>

                  {/* Papel */}
                  <Paper elevation={2} sx={{
                    p: 2,
                    minWidth: '150px',
                    flex: '1 1 150px',
                    borderTop: '4px solid #4caf50',
                    borderRadius: '4px',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-3px)',
                      boxShadow: 3
                    }
                  }}>
                    <Typography variant="subtitle2" sx={{ color: 'text.secondary', mb: 1 }}>
                      Papel
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                      {paperCost.toFixed(2)} €
                    </Typography>
                    <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                      {paperDetails}
                    </Typography>
                    {paperWeight > 0 && (
                      <Typography variant="caption" display="block" sx={{ color: 'text.secondary' }}>
                        Peso: {paperWeightDetails}
                      </Typography>
                    )}
                  </Paper>

                  {/* Maculatura - Solo visible si hay datos de maculatura */}
                  {part.sheetCalculation?.total_maculatura > 0 && (
                    <Paper elevation={2} sx={{
                      p: 2,
                      minWidth: '150px',
                      flex: '1 1 150px',
                      borderTop: '4px solid #ff9800',
                      borderRadius: '4px',
                      transition: 'transform 0.2s',
                      '&:hover': {
                        transform: 'translateY(-3px)',
                        boxShadow: 3
                      }
                    }}>
                      <Typography variant="subtitle2" sx={{ color: 'text.secondary', mb: 1 }}>
                        Maculatura
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                        {(() => {
                          // Calcular el costo de la maculatura directamente del backend
                          const precioPorMillar = part.sheetCalculation.paper_cost_per_1000 ||
                                                (part.paper && part.paper.price_per_1000) || 96.60;
                          const maculaturaCost = (part.sheetCalculation.total_maculatura * precioPorMillar) / 1000;
                          return maculaturaCost.toFixed(2);
                        })()} €
                      </Typography>
                      <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                        {part.sheetCalculation.total_maculatura} pliegos
                      </Typography>
                    </Paper>
                  )}

                  {/* Clicks o Planchas y Tinta */}
                  {isDigital ? (
                    <Paper elevation={2} sx={{
                      p: 2,
                      minWidth: '150px',
                      flex: '1 1 150px',
                      borderTop: '4px solid #9c27b0',
                      borderRadius: '4px',
                      transition: 'transform 0.2s',
                      '&:hover': {
                        transform: 'translateY(-3px)',
                        boxShadow: 3
                      }
                    }}>
                      <Typography variant="subtitle2" sx={{ color: 'text.secondary', mb: 1 }}>
                        Clicks
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
                        {clickCost.toFixed(2)} €
                      </Typography>
                      <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                        {clickDetails}
                      </Typography>
                    </Paper>
                  ) : (
                    <>
                      <Paper elevation={2} sx={{
                        p: 2,
                        minWidth: '150px',
                        flex: '1 1 150px',
                        borderTop: '4px solid #f44336',
                        borderRadius: '4px',
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'translateY(-3px)',
                          boxShadow: 3
                        }
                      }}>
                        <Typography variant="subtitle2" sx={{ color: 'text.secondary', mb: 1 }}>
                          Planchas
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#f44336' }}>
                          {plateCost.toFixed(2)} €
                        </Typography>
                        <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                          {part.sheetCalculation?.mejor_combinacion?.total_planchas ?
                            `${part.sheetCalculation.mejor_combinacion.total_planchas} planchas` :
                            plateDetails}
                        </Typography>
                      </Paper>

                      <Paper elevation={2} sx={{
                        p: 2,
                        minWidth: '150px',
                        flex: '1 1 150px',
                        borderTop: '4px solid #673ab7',
                        borderRadius: '4px',
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'translateY(-3px)',
                          boxShadow: 3
                        }
                      }}>
                        <Typography variant="subtitle2" sx={{ color: 'text.secondary', mb: 1 }}>
                          Tinta
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#673ab7' }}>
                          {inkCost.toFixed(2)} €
                        </Typography>
                        <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                          {inkDetails}
                        </Typography>
                      </Paper>
                    </>
                  )}
                </Box>

                {/* Costo Total */}
                <Paper elevation={3} sx={{
                  p: 2,
                  mt: 3,
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  bgcolor: '#f5f5f5',
                  borderLeft: '4px solid #2e7d32'
                }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                    Costo Total de la Parte:
                  </Typography>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                    {totalCost.toFixed(2)} €
                  </Typography>
                </Paper>
              </Box>
            </Grid>
          )}
        </Grid>
      </Collapse>
    </Paper>

    {/* Modal para crear nuevo papel */}
    <PaperInfoModal
      open={paperDialogOpen}
      onClose={() => setPaperDialogOpen(false)}
      onPaperCreated={handlePaperCreated}
    />
    </>
  );
};

BudgetPartForm.propTypes = {
  part: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  papers: PropTypes.array,
  machines: PropTypes.array,
  processes: PropTypes.array,
  onPartChange: PropTypes.func.isRequired,
  onDeletePart: PropTypes.func.isRequired,
  onCalculateSheetsV2: PropTypes.func.isRequired,
  onCalculateSheetsV2Silent: PropTypes.func.isRequired,
  onMachineInfoClick: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  calculatingSheets: PropTypes.bool,
  isEditMode: PropTypes.bool
};

export default BudgetPartForm;
