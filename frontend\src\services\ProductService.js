// services/ProductService.js
import { buildApiUrl } from '../config';
import { ApiInterceptor, LogService } from './simplifiedServices';

/**
 * Servicio para gestionar los productos del catálogo
 */
class ProductService {
  /**
   * Obtiene todos los productos del catálogo
   *
   * @param {boolean} onlyActive - Si es true, solo devuelve productos activos
   * @returns {Promise<Array>} - Promesa con los productos
   */
  static async getProducts(onlyActive = true) {
    try {
      // Obtener productos desde la API con un timestamp para evitar caché
      const timestamp = new Date().getTime();
      const url = buildApiUrl(`/products?_t=${timestamp}`);
      const response = await ApiInterceptor.fetch(url, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error(`Error al obtener productos: ${response.statusText}`);
      }

      const data = await response.json();

      const productsArray = Array.isArray(data) ? data : [];

      // Filtrar productos activos si es necesario
      const filteredProducts = onlyActive
        ? productsArray.filter(product => product.active)
        : productsArray;

      LogService.logUserAction('fetch_products_api', {
        count: productsArray.length
      });

      return filteredProducts;
    } catch (error) {
      console.error('Error al obtener productos:', error);
      LogService.logError('Error al obtener productos', { error: error.message });

      // Si hay un error, devolver un array vacío
      return [];
    }
  }

  /**
   * Obtiene los productos disponibles para el selector de tipos de trabajo
   *
   * @param {boolean} onlyActive - Si es true, solo devuelve productos activos
   * @param {boolean} uniqueByType - Si es true, devuelve solo un producto por tipo
   * @returns {Promise<Array>} - Promesa con los productos disponibles
   */
  static async getProductsForSelector(onlyActive = true, uniqueByType = false) {
    const products = await this.getProducts(onlyActive);

    // Si se solicita productos únicos por tipo, filtrar
    if (uniqueByType) {
      // Devolver productos únicos por tipo (para evitar duplicados)
      const uniqueProducts = [];
      const seenTypes = new Set();

      for (const product of products) {
        if (!seenTypes.has(product.type)) {
          seenTypes.add(product.type);
          uniqueProducts.push(product);
        }
      }

      return uniqueProducts;
    }

    // Si no, devolver todos los productos activos
    return products;
  }

  /**
   * Obtiene un producto por su tipo
   *
   * @param {string} type - Tipo de producto a buscar
   * @param {boolean} onlyActive - Si es true, solo busca en productos activos
   * @returns {Promise<Object|null>} - Promesa con el producto o null si no se encuentra
   */
  static async getProductByType(type, onlyActive = true) {
    const products = await this.getProducts(onlyActive);

    // Buscar el primer producto que coincida con el tipo
    return products.find(product => product.type === type) || null;
  }
}

export default ProductService;
