import ApiService from './ApiService';
import CatalogService from './CatalogService';

/**
 * Servicio para realizar cálculos de impresión digital
 */
class DigitalCalculatorService {
  /**
   * Calcula costes y tiempos para impresión digital
   * @param {Object} data - Datos para el cálculo
   * @param {string} data.machine_id - ID de la máquina digital
   * @param {number} data.copies - Número de ejemplares
   * @param {string} [data.paper_id] - ID del papel (opcional)
   * @param {number} [data.front_colors] - Número de colores en el anverso (4 para CMYK, 1 para B/N)
   * @param {number} [data.back_colors] - Número de colores en el reverso (0 para simplex, >0 para duplex)
   * @param {number} [data.num_paginas] - Número de páginas del documento
   * @param {number} [data.ancho_pagina] - Ancho de la página en mm
   * @param {number} [data.alto_pagina] - Alto de la página en mm
   * @param {number} [data.ancho_pliego] - Ancho del pliego en mm
   * @param {number} [data.alto_pliego] - Alto del pliego en mm
   * @param {number} [data.custom_print_time] - Tiempo personalizado de impresión en horas
   * @param {string} [data.binding_type] - Tipo de encuadernado ("gathering", "collection", "none")
   * @returns {Promise<Object>} - Resultado del cálculo
   */
  async calculateDigital(data) {
    try {
      console.log('Enviando solicitud al endpoint /calcular-pliegos-digital:', data);
      console.log('JSON enviado a la API:', JSON.stringify(data, null, 2));

      // Registrar la URL completa para depuración
      console.log('URL del endpoint:', '/calcular-pliegos-digital');

      const response = await ApiService.post('/calcular-pliegos-digital', data);
      console.log('Respuesta recibida del endpoint:', response);
      console.log('Status:', response.status, response.statusText);

      // Verificar si la respuesta es correcta
      if (!response.ok) {
        console.error('Error en la respuesta HTTP:', response.status, response.statusText);
        let errorText = '';
        try {
          errorText = await response.text();
          console.error('Contenido del error:', errorText);

          // Intentar parsear el error como JSON para mostrar un mensaje más descriptivo
          const errorJson = JSON.parse(errorText);
          if (errorJson.detail && Array.isArray(errorJson.detail)) {
            const errorDetails = errorJson.detail.map(err => `${err.msg} en ${err.loc.join('.')}`).join(', ');
            throw new Error(`Error en la validación: ${errorDetails}`);
          }
        } catch (parseError) {
          // Si no se puede parsear como JSON, usar el texto original
          console.error('No se pudo parsear el error como JSON:', parseError);
        }

        throw new Error(`Error en la respuesta: ${response.status} ${response.statusText}`);
      }

      // Procesar la respuesta JSON
      const result = await response.json();
      console.log('Datos procesados:', result);
      console.log('JSON recibido de la API:', JSON.stringify(result, null, 2));

      return result;
    } catch (error) {
      console.error('Error al calcular digital:', error);
      throw error;
    }
  }

  /**
   * Obtiene las máquinas digitales disponibles
   * @returns {Promise<Array>} - Lista de máquinas digitales
   */
  async getDigitalMachines() {
    try {
      // Usar CatalogService que maneja correctamente la respuesta JSON
      const machines = await CatalogService.getAll('machines');

      // Filtrar solo máquinas digitales
      return machines.filter(machine => machine.type === 'Digital');
    } catch (error) {
      console.error('Error al obtener máquinas digitales:', error);
      return []; // Devolver un array vacío en caso de error
    }
  }

  /**
   * Obtiene los detalles de una máquina digital
   * @param {string} machineId - ID de la máquina
   * @returns {Promise<Object>} - Detalles de la máquina
   */
  async getDigitalMachineDetails(machineId) {
    try {
      // Usar CatalogService que maneja correctamente la respuesta JSON
      return await CatalogService.getById('machines', machineId);
    } catch (error) {
      console.error(`Error al obtener detalles de la máquina ${machineId}:`, error);
      return null; // Devolver null en caso de error
    }
  }

  /**
   * Obtiene los papeles disponibles, filtrados por compatibilidad con la máquina si se proporciona un machineId
   * @param {string} [machineId] - ID de la máquina para filtrar papeles compatibles
   * @returns {Promise<Array>} - Lista de papeles filtrados
   */
  async getPapers(machineId = null) {
    try {
      // Obtener todos los papeles
      const papers = await CatalogService.getAll('papers');
      console.log('Todos los papeles:', papers);

      // Si no se proporciona machineId, devolver todos los papeles
      if (!machineId) {
        console.log('No se proporcionó machineId, devolviendo todos los papeles');
        return papers;
      }

      console.log('Filtrando papeles para la máquina:', machineId);

      // Obtener detalles de la máquina para conocer sus limitaciones de formato
      const machine = await this.getDigitalMachineDetails(machineId);
      console.log('Detalles de la máquina:', machine);

      if (!machine) {
        console.log('No se pudo obtener la máquina, devolviendo todos los papeles');
        return papers; // Si no se puede obtener la máquina, devolver todos los papeles
      }

      // Para máquinas digitales, generalmente usamos SRA3 o A3+
      // Si la máquina no tiene información específica, asumimos SRA3 (320x450mm)
      // Pero algunas variantes de SRA3 pueden ser más grandes, como 480x330mm
      const maxWidth = machine.max_sheet_width || 480;
      const maxHeight = machine.max_sheet_height || 450;
      const machineFormat = machine.format || 'SRA3';

      console.log(`Limitaciones de la máquina: ${maxWidth}x${maxHeight}mm, formato: ${machineFormat}`);

      // Filtrar papeles según las limitaciones de la máquina
      const filteredPapers = papers.filter(paper => {
        // Obtener dimensiones del papel
        const paperWidth = paper.dimension_width || 0;
        const paperHeight = paper.dimension_height || 0;
        const paperFormat = paper.format || '';
        const paperName = paper.descriptive_name || paper.name || '';

        // Verificar si el papel tiene dimensiones
        if (paperWidth === 0 || paperHeight === 0) {
          console.log(`Papel ${paperName} sin dimensiones, excluyendo`);
          return false;
        }

        // Comprobar si el nombre del papel contiene "SRA3"
        const nameContainsSRA3 = paperName.toUpperCase().includes('SRA3');

        // Comprobar si las dimensiones son cercanas a SRA3 (con margen de tolerancia)
        // SRA3 puede variar entre 320x450mm y 480x330mm aproximadamente
        const isSRA3ByDimensions = (
          (paperWidth >= 310 && paperWidth <= 490 && paperHeight >= 320 && paperHeight <= 460) ||
          (paperHeight >= 310 && paperHeight <= 490 && paperWidth >= 320 && paperWidth <= 460)
        );

        // Comprobar si el papel cabe en la máquina en cualquier orientación
        const fitsNormal = paperWidth <= maxWidth && paperHeight <= maxHeight;
        const fitsRotated = paperWidth <= maxHeight && paperHeight <= maxWidth;
        const fitsSize = fitsNormal || fitsRotated;

        // Comprobar si el formato coincide (por campo format, nombre o dimensiones)
        const formatMatch =
          paperFormat.toUpperCase() === machineFormat.toUpperCase() ||
          nameContainsSRA3 ||
          isSRA3ByDimensions;

        // Un papel es compatible si cabe por tamaño o si coincide el formato
        const isCompatible = fitsSize || formatMatch;

        console.log(`Papel ${paperName} (${paperWidth}x${paperHeight}mm, ${paperFormat}): ${isCompatible ? 'COMPATIBLE' : 'NO COMPATIBLE'}`);
        console.log(`  - Cabe por tamaño: ${fitsSize} (normal: ${fitsNormal}, rotado: ${fitsRotated})`);
        console.log(`  - Coincide formato: ${formatMatch} (formato: ${paperFormat}, nombre contiene SRA3: ${nameContainsSRA3}, dimensiones SRA3: ${isSRA3ByDimensions})`);

        return isCompatible;
      });

      console.log('Papeles filtrados:', filteredPapers);
      return filteredPapers;
    } catch (error) {
      console.error('Error al obtener papeles:', error);
      return []; // Devolver un array vacío en caso de error
    }
  }
}

export default new DigitalCalculatorService();
