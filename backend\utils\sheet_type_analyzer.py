"""
Módulo para analizar y determinar el tipo de pliego óptimo para cada esquema de plegado.
"""
from typing import Dict, List, Tuple, Optional
from models.models import SheetType
from folding_schemes import get_scheme

def determine_optimal_sheet_type(
    folding_scheme: str,
    colors_front: int,
    colors_back: int,
    print_units: int,
    es_tira_retira: bool = False
) -> SheetType:
    """
    Determina el tipo de pliego óptimo para un esquema de plegado específico.

    Args:
        folding_scheme: Nombre del esquema de plegado (ej: "F4-1", "F8-7", etc.)
        colors_front: Número de colores en el anverso
        colors_back: Número de colores en el reverso
        print_units: Número de cuerpos de impresión de la máquina
        es_tira_retira: Indica si el esquema es tira-retira (imprime por ambas caras)

    Returns:
        SheetType: El tipo de pliego óptimo
    """
    # Si es tira-retira, siempre usamos WorkAndTurn independientemente de otras condiciones
    if es_tira_retira:
        return SheetType.WORK_AND_TURN
        
    # Si no hay colores en el reverso, siempre es Flat
    if colors_back == 0:
        return SheetType.FLAT

    # Si la máquina tiene 8 o más cuerpos, siempre usa Perfecting para todos los pliegos
    # (imprime ambas caras a la vez, no usa tira/retira)
    if print_units >= 8:
        return SheetType.PERFECTING

    # Obtener el esquema de plegado
    scheme = get_scheme(folding_scheme)
    if not scheme:
        # Si no hay esquema, usar WorkAndBack por defecto
        return SheetType.WORK_AND_BACK

    # Esquemas específicos con reglas especiales
    # F16-7 siempre usa WorkAndBack debido a su disposición de páginas
    # Excepto para máquinas de 8 o más cuerpos, que siempre usan Perfecting
    if folding_scheme == "F16-7" and print_units < 8:
        return SheetType.WORK_AND_BACK

    # F8-7 debe usar WorkAndBack cuando hay suficientes páginas para llenar el esquema
    # y WorkAndTurn solo cuando no hay suficientes páginas
    if folding_scheme == "F8-7":
        # Por defecto, usamos WorkAndBack para F8-7 ya que normalmente hay suficientes páginas
        # para llenar el esquema (8 páginas por pliego)
        return SheetType.WORK_AND_BACK

    # Para otros esquemas, verificar si son compatibles con WorkAndTurn
    # Para WorkAndTurn, el número de páginas debe ser par
    if scheme.pages_per_sheet % 2 == 0:
        # F4-1 es compatible con WorkAndTurn si los colores son iguales
        if folding_scheme == "F4-1" and colors_front == colors_back:
            return SheetType.WORK_AND_TURN

        # Para el resto de esquemas, usar WorkAndBack por defecto
        return SheetType.WORK_AND_BACK

    # Si no es compatible con WorkAndTurn, usar WorkAndBack
    return SheetType.WORK_AND_BACK

def analyze_sheet_breakdown(
    folding_schemes: List[Dict],
    colors_front: int,
    colors_back: int,
    print_units: int
) -> List[Dict]:
    """
    Analiza y determina el tipo de pliego óptimo para cada esquema de plegado.

    Args:
        folding_schemes: Lista de esquemas de plegado utilizados
        colors_front: Número de colores en el anverso
        colors_back: Número de colores en el reverso
        print_units: Número de cuerpos de impresión de la máquina

    Returns:
        List[Dict]: Lista de esquemas con el tipo de pliego óptimo para cada uno
    """
    result = []

    # Para máquinas de 8 o más cuerpos, siempre usamos Perfecting para todos los pliegos
    # cuando hay colores en el reverso
    use_perfecting_for_all = print_units >= 8 and colors_back > 0

    for scheme in folding_schemes:
        scheme_name = scheme.get("nombre", "")

        # Si es una máquina de 8 o más cuerpos, siempre usamos Perfecting
        if use_perfecting_for_all:
            sheet_type = SheetType.PERFECTING
        else:
            # Para máquinas con menos de 8 cuerpos, usamos la lógica original
            # Obtenemos el valor de es_tira_retira del esquema
            es_tira_retira = scheme.get("es_tira_retira", False)
            sheet_type = determine_optimal_sheet_type(
                scheme_name,
                colors_front,
                colors_back,
                print_units,
                es_tira_retira
            )

        # Añadir el tipo de pliego al esquema
        scheme_with_type = scheme.copy()
        # Convertir el objeto de enumeración SheetType a su valor de cadena de texto
        scheme_with_type["sheet_type"] = sheet_type.value

        # Determinar el número de planchas necesarias según el tipo de pliego y el esquema
        scheme_name = scheme.get("nombre", "")

        # Para máquinas de 8 o más cuerpos, siempre usamos Perfecting y el número de planchas es múltiplo de 8
        if print_units >= 8 and colors_back > 0:
            # Asegurarnos de que el tipo de pliego es Perfecting
            sheet_type = SheetType.PERFECTING
            # Convertir el objeto de enumeración SheetType a su valor de cadena de texto
            scheme_with_type["sheet_type"] = sheet_type.value

            # Usar todos los cuerpos de impresión disponibles (múltiplo del número de cuerpos)
            plates_needed = print_units
        # Calcular planchas según el tipo de pliego y el esquema específico para otros casos
        elif sheet_type == SheetType.FLAT:
            # Impresión a una cara, solo planchas para el anverso
            plates_needed = colors_front
        elif sheet_type == SheetType.WORK_AND_TURN:
            # Tira-retira, solo planchas para el anverso (se usan las mismas para ambas caras)
            plates_needed = colors_front
        elif sheet_type == SheetType.PERFECTING:
            # Perfecting, planchas para ambas caras (se imprimen a la vez)
            plates_needed = colors_front + colors_back
        else:  # WorkAndBack
            # WorkAndBack, planchas diferentes para cada cara
            # Para F16-7 en WorkAndBack, necesitamos planchas para ambas caras
            if scheme_name == "F16-7":
                plates_needed = colors_front + colors_back
            else:
                plates_needed = colors_front + colors_back

        # Multiplicar por el número de pliegos para obtener el total de planchas
        plates_needed = plates_needed * scheme.get("numero_pliegos", 1)

        scheme_with_type["plates_needed"] = plates_needed

        # Determinar si necesita dos pasadas por la máquina
        if sheet_type == SheetType.WORK_AND_BACK:
            # WorkAndBack: diferentes planchas para cada cara, dos pasadas si hay colores en el reverso
            needs_two_passes = colors_back > 0 and (colors_front + colors_back > print_units)
        else:
            # Otros tipos solo necesitan una pasada
            needs_two_passes = False

        scheme_with_type["needs_two_passes"] = needs_two_passes

        result.append(scheme_with_type)

    return result

def get_total_plates_needed(sheet_breakdown: List[Dict]) -> int:
    """
    Calcula el número total de planchas necesarias para todos los esquemas.

    Args:
        sheet_breakdown: Lista de esquemas con el tipo de pliego y planchas necesarias

    Returns:
        int: Número total de planchas necesarias
    """
    # Verificar si estamos usando una máquina de 8 o más cuerpos
    using_8_units_machine = False
    print_units = 0

    # Buscar si algún esquema usa Perfecting (indicativo de máquina de 8+ cuerpos)
    for scheme in sheet_breakdown:
        if scheme.get("sheet_type") == SheetType.PERFECTING:
            # Extraer el número de cuerpos de impresión del primer esquema que encontremos
            # Asumimos que todos los esquemas usan la misma máquina
            if "plates_needed" in scheme and "numero_pliegos" in scheme and scheme["numero_pliegos"] > 0:
                # Calcular el número de cuerpos dividiendo el número de planchas por el número de pliegos
                print_units = scheme["plates_needed"] // scheme["numero_pliegos"]
                if print_units >= 8:
                    using_8_units_machine = True
                    break

    # Calcular el total de planchas
    total_plates = sum(scheme.get("plates_needed", 0) for scheme in sheet_breakdown)

    # Si estamos usando una máquina de 8 o más cuerpos, asegurarnos de que el total sea múltiplo del número de cuerpos
    if using_8_units_machine and print_units > 0:
        # Redondear hacia arriba al múltiplo más cercano del número de cuerpos
        if total_plates % print_units != 0:
            total_plates = ((total_plates // print_units) + 1) * print_units

    return total_plates

def get_total_passes_needed(sheet_breakdown: List[Dict]) -> int:
    """
    Calcula el número total de pasadas necesarias para todos los esquemas.

    Args:
        sheet_breakdown: Lista de esquemas con el tipo de pliego y si necesita dos pasadas

    Returns:
        int: Número total de pasadas necesarias
    """
    total_passes = 0
    for scheme in sheet_breakdown:
        # Determinar el número de pasadas según el tipo de pliego
        if scheme.get("sheet_type") == SheetType.PERFECTING:
            # Para Perfecting (máquinas de 8 o más cuerpos), se reduce el tiempo a la mitad
            # porque se imprimen ambas caras a la vez
            passes = 0.5  # Media pasada por pliego (el doble de eficiente)
        elif scheme.get("needs_two_passes", False):
            # Si necesita dos pasadas (WorkAndBack con más colores que cuerpos)
            passes = 2
        else:
            # Caso normal: una pasada por pliego
            passes = 1

        # Multiplicar por el número de pliegos de este esquema
        total_passes += passes * scheme.get("numero_pliegos", 0)

    # Redondear hacia arriba para asegurar que tenemos suficientes pasadas
    return int(total_passes + 0.5)
