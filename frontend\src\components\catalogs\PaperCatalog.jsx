import {
  useState,
  useEffect,
  useCallback,
  useRef
} from 'react';
import { API_URL, buildApiUrl } from '../../config';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Alert,
  Snackbar,
  Chip,
  Switch,
  FormControlLabel
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';

// URL de la API importada desde config.js

const CATEGORIES = ['Offset', 'Estucado', 'Reciclado', 'Autocopiativo', 'Especial'];
const FINISHES = ['Mate', 'Brillo', 'Satin', 'Natural'];
const GRAIN_DIRECTIONS = ['Long', 'Short'];

const PaperCatalog = () => {
  // Mantenemos el estado papers porque lo necesitamos para actualizar filteredPapers
  const [papers, setPapers] = useState([]);
  const [filteredPapers, setFilteredPapers] = useState([]);
  const [showOnlyInStock, setShowOnlyInStock] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingPaper, setEditingPaper] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [formData, setFormData] = useState({
    product_id: '',
    descriptive_name: '',
    media_type: 'Paper',
    category: 'Offset',
    finish: 'Natural',
    dimension_width: 0,
    dimension_height: 0,
    thickness: 0,
    weight: 0,
    grainDirection: 'Long',
    manufacturer: '',
    inStock: true,
    color: 'Blanco',
    notes: '',
    price_per_1000: 0,
    cost_per_ton: 0
  });
  const fetchedRef = useRef(false);

  const fetchPapers = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/papers/'));
      if (!response.ok) throw new Error('Error al obtener el catálogo de papeles');
      const data = await response.json();
      setPapers(data);
      setFilteredPapers(showOnlyInStock ? data.filter(paper => paper.inStock) : data);
    } catch (err) {
      showSnackbar(err.message, 'error');
    }
  }, [showOnlyInStock]);

  useEffect(() => {
    if (!fetchedRef.current) {
      fetchPapers();
      fetchedRef.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOpenDialog = (paper = null) => {
    if (paper) {
      setEditingPaper(paper);
      // Asegurarse de que todos los campos tengan valores por defecto
      setFormData({
        ...paper,
        price_per_1000: paper.price_per_1000 || 0,
        cost_per_ton: paper.cost_per_ton || 0,
        notes: paper.notes || ''
      });
    } else {
      setEditingPaper(null);
      setFormData({
        product_id: '',
        descriptive_name: '',
        media_type: 'Paper',
        category: 'Offset',
        finish: 'Natural',
        dimension_width: 0,
        dimension_height: 0,
        thickness: 0,
        weight: 0,
        grainDirection: 'Long',
        manufacturer: '',
        inStock: true,
        color: 'Blanco',
        notes: '',
        price_per_1000: 0,
        cost_per_ton: 0
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingPaper(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Actualizar el estado del formulario
    setFormData(prev => {
      const newFormData = {
        ...prev,
        [name]: value
      };

      // Si se modifica el coste por tonelada, dimensiones o gramaje, calcular el precio por 1000 pliegos
      if ((name === 'cost_per_ton' || name === 'dimension_width' || name === 'dimension_height' || name === 'weight') &&
          parseFloat(newFormData.dimension_width) > 0 &&
          parseFloat(newFormData.dimension_height) > 0 &&
          parseFloat(newFormData.weight) > 0 &&
          parseFloat(newFormData.cost_per_ton) > 0) {

        // Convertir dimensiones de mm a m
        const width_m = parseFloat(newFormData.dimension_width) / 1000;
        const height_m = parseFloat(newFormData.dimension_height) / 1000;

        // Calcular área del pliego en m²
        const area_m2 = width_m * height_m;

        // Calcular peso del pliego en kg
        const weight_kg = area_m2 * parseFloat(newFormData.weight) / 1000;

        // Calcular coste por kg
        const cost_per_kg = parseFloat(newFormData.cost_per_ton) / 1000;

        // Calcular coste por pliego
        const sheet_cost = weight_kg * cost_per_kg;

        // Calcular coste por 1000 pliegos
        newFormData.price_per_1000 = (sheet_cost * 1000).toFixed(2);
      }

      return newFormData;
    });
  };

  const handleSubmit = async () => {
    try {
      const url = editingPaper
        ? `${API_URL}/papers/${editingPaper.product_id}`
        : `${API_URL}/papers`;

      const method = editingPaper ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Error al guardar el papel');
      }

      showSnackbar(
        editingPaper
          ? 'Papel actualizado correctamente'
          : 'Papel creado correctamente',
        'success'
      );

      handleCloseDialog();
      fetchPapers();
    } catch (err) {
      showSnackbar(err.message, 'error');
    }
  };

  const handleDelete = async (paperId) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar este papel?')) {
      return;
    }

    try {
      const response = await fetch(`${API_URL}/papers/${paperId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Error al eliminar el papel');
      }

      showSnackbar('Papel eliminado correctamente', 'success');
      fetchPapers();
    } catch (err) {
      showSnackbar(err.message, 'error');
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleToggleActive = () => {
    setShowOnlyInStock(!showOnlyInStock);
    // Actualizar la lista filtrada cuando cambia el switch
    setFilteredPapers(!showOnlyInStock ? papers.filter(paper => paper.inStock) : papers);
  };

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Catálogo de Papeles
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={showOnlyInStock}
                onChange={handleToggleActive}
                color="primary"
              />
            }
            label="Mostrar solo en stock"
          />
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nuevo Papel
          </Button>
        </Box>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'primary.main' }}>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>ID</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Nombre</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Categoría</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Dimensiones</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Gramaje</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Fabricante</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Coste/Ton</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Stock</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredPapers.map((paper) => (
              <TableRow key={paper.product_id} sx={{
                '&:nth-of-type(odd)': { backgroundColor: 'action.hover' },
                whiteSpace: 'nowrap'
              }}>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{paper.product_id}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{paper.descriptive_name}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{paper.category}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{paper.dimension_width} × {paper.dimension_height}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{paper.weight}g</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{paper.manufacturer}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  {paper.cost_per_ton ? `${paper.cost_per_ton.toFixed(2)} €/ton` : '0.00 €/ton'}
                </TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  <Chip
                    label={paper.inStock ? "En Stock" : "Sin Stock"}
                    color={paper.inStock ? "success" : "error"}
                    size="small"
                    variant="filled"
                    sx={{ fontWeight: 'medium' }}
                  />
                </TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  <IconButton
                    onClick={() => handleOpenDialog(paper)}
                    color="primary"
                    size="small"
                    sx={{ mr: 1 }}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    onClick={() => handleDelete(paper.product_id)}
                    color="error"
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ backgroundColor: 'primary.main', color: 'white' }}>
          {editingPaper ? 'Editar Papel' : 'Nuevo Papel'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'grid', gap: 2, gridTemplateColumns: 'repeat(2, 1fr)', mt: 2 }}>
            <TextField
              name="product_id"
              label="ID del Producto"
              value={formData.product_id}
              onChange={handleInputChange}
              disabled={!!editingPaper}
              fullWidth
            />
            <TextField
              name="descriptive_name"
              label="Nombre Descriptivo"
              value={formData.descriptive_name}
              onChange={handleInputChange}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel>Categoría</InputLabel>
              <Select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                label="Categoría"
              >
                {CATEGORIES.map(cat => (
                  <MenuItem key={cat} value={cat}>{cat}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Acabado</InputLabel>
              <Select
                name="finish"
                value={formData.finish}
                onChange={handleInputChange}
                label="Acabado"
              >
                {FINISHES.map(finish => (
                  <MenuItem key={finish} value={finish}>{finish}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              name="dimension_width"
              label="Ancho (mm)"
              type="number"
              value={formData.dimension_width}
              onChange={handleInputChange}
              fullWidth
            />
            <TextField
              name="dimension_height"
              label="Alto (mm)"
              type="number"
              value={formData.dimension_height}
              onChange={handleInputChange}
              fullWidth
            />
            <TextField
              name="thickness"
              label="Grosor (µm)"
              type="number"
              value={formData.thickness}
              onChange={handleInputChange}
              fullWidth
            />
            <TextField
              name="weight"
              label="Gramaje (g/m²)"
              type="number"
              value={formData.weight}
              onChange={handleInputChange}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel>Dirección de Fibra</InputLabel>
              <Select
                name="grainDirection"
                value={formData.grainDirection}
                onChange={handleInputChange}
                label="Dirección de Fibra"
              >
                {GRAIN_DIRECTIONS.map(direction => (
                  <MenuItem key={direction} value={direction}>{direction}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              name="manufacturer"
              label="Fabricante"
              value={formData.manufacturer}
              onChange={handleInputChange}
              fullWidth
            />
            <TextField
              name="color"
              label="Color"
              value={formData.color}
              onChange={handleInputChange}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel>Estado de Stock</InputLabel>
              <Select
                name="inStock"
                value={formData.inStock}
                onChange={handleInputChange}
                label="Estado de Stock"
              >
                <MenuItem value={true}>En Stock</MenuItem>
                <MenuItem value={false}>Sin Stock</MenuItem>
              </Select>
            </FormControl>
            <TextField
              name="price_per_1000"
              label="Precio por 1000 pliegos (€)"
              type="number"
              value={formData.price_per_1000}
              onChange={handleInputChange}
              fullWidth
              InputProps={{
                inputProps: { min: 0, step: 0.01 }
              }}
            />
            <TextField
              name="cost_per_ton"
              label="Coste por Tonelada (€)"
              type="number"
              value={formData.cost_per_ton}
              onChange={handleInputChange}
              fullWidth
              InputProps={{
                inputProps: { min: 0, step: 0.01 }
              }}
            />
            <TextField
              name="notes"
              label="Notas"
              value={formData.notes || ''}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={2}
              sx={{ gridColumn: '1 / -1' }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCloseDialog} variant="outlined">Cancelar</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {editingPaper ? 'Actualizar' : 'Crear'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default PaperCatalog;
