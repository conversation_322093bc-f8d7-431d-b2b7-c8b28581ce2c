from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.security import OAuth2PasswordRequestForm
from typing import List, Dict, Any

from models.auth import UserCreate, UserUpdate, User, Token
from models.activity_log import ActivityType
from services.auth_service import AuthService
from services.user_service import UserService
from services.activity_log_service import ActivityLogService
from dependencies.auth import get_current_active_user, get_admin_user
from routes.activity_logs import log_activity

router = APIRouter(
    prefix="/auth",
    tags=["auth"],
    responses={401: {"description": "No autorizado"}}
)

# Crear instancias de los servicios
auth_service = AuthService()
user_service = UserService()
activity_log_service = ActivityLogService()

# Asegurar que existe un usuario administrador
user_service.create_admin_if_not_exists()

@router.post("/login", response_model=Token)
async def login(request: Request, form_data: OAuth2PasswordRequestForm = Depends()):
    """
    Endpoint para iniciar sesión y obtener un token JWT.
    """
    success, message, user_data = auth_service.authenticate_user(
        form_data.username, form_data.password
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=message,
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Registrar el inicio de sesión exitoso
    log_activity(
        request=request,
        user_id=user_data["user_id"],
        username=user_data["username"],
        activity_type=ActivityType.LOGIN,
        description=f"Inicio de sesión exitoso del usuario {user_data['username']}"
    )

    return {"access_token": user_data["token"], "token_type": "bearer"}

@router.get("/me", response_model=User)
async def get_current_user_info(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Endpoint para obtener información del usuario actual.
    """
    return current_user

@router.get("/users", response_model=List[User])
async def get_users(admin_user: Dict[str, Any] = Depends(get_admin_user)):
    """
    Endpoint para obtener todos los usuarios (solo administradores).
    """
    return user_service.get_all_users()

@router.post("/users", response_model=User)
async def create_user(user_data: UserCreate, admin_user: Dict[str, Any] = Depends(get_admin_user)):
    """
    Endpoint para crear un nuevo usuario (solo administradores).
    """
    success, message, user = user_service.create_user(user_data)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message,
        )

    return user

@router.get("/users/{user_id}", response_model=User)
async def get_user(user_id: str, admin_user: Dict[str, Any] = Depends(get_admin_user)):
    """
    Endpoint para obtener un usuario por su ID (solo administradores).
    """
    user = user_service.get_user_by_id(user_id)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuario no encontrado",
        )

    # Eliminar información sensible
    del user["password_hash"]
    del user["salt"]

    return user

@router.put("/users/{user_id}", response_model=User)
async def update_user(user_id: str, user_data: UserUpdate, admin_user: Dict[str, Any] = Depends(get_admin_user)):
    """
    Endpoint para actualizar un usuario (solo administradores).
    """
    success, message, user = user_service.update_user(user_id, user_data)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message,
        )

    return user

@router.delete("/users/{user_id}")
async def delete_user(user_id: str, admin_user: Dict[str, Any] = Depends(get_admin_user)):
    """
    Endpoint para eliminar un usuario (solo administradores).
    """
    success, message = user_service.delete_user(user_id)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=message,
        )

    return {"message": message}
