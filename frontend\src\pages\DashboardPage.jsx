import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import DashboardService from '../services/DashboardService';
import CostProfitDashboard from '../components/dashboard/CostProfitDashboard';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Chip
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';

// Colores para los gráficos
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

// Componente principal del Dashboard
const DashboardPage = () => {
  const { token } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);

  // Estados para los datos
  const [budgetStats, setBudgetStats] = useState({
    total: 0,
    byStatus: [],
    byMonth: [],
    recentBudgets: []
  });

  const [productionStats, setProductionStats] = useState({
    activeJobs: 0,
    completedJobs: 0,
    shippedJobs: 0,
    byStatus: [],
    byMachine: [],
    recentJobs: []
  });

  // Función para cargar todas las estadísticas del dashboard
  const fetchDashboardStats = useCallback(async () => {
    try {
      const data = await DashboardService.getDashboardStats(token);

      // Actualizar los estados con los datos recibidos
      // Adaptar la estructura de datos del backend a la estructura esperada por el frontend
      if (data.budget_stats) {
        setBudgetStats({
          total: data.budget_stats.total || 0,
          byStatus: data.budget_stats.by_status || [],
          byMonth: data.budget_stats.by_month || [],
          recentBudgets: data.budget_stats.recent_budgets || []
        });
      }

      if (data.production_stats) {
        setProductionStats({
          activeJobs: data.production_stats.active_jobs || 0,
          completedJobs: data.production_stats.completed_jobs || 0,
          shippedJobs: data.production_stats.shipped_jobs || 0,
          byStatus: data.production_stats.by_status || [],
          byMachine: data.production_stats.by_machine || [],
          recentJobs: data.production_stats.recent_jobs || []
        });
      }
    } catch (err) {
      console.error('Error al cargar estadísticas del dashboard:', err);
      setError(err.message);
    }
  }, [token]);

  // Cargar datos al montar el componente
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);

      try {
        await fetchDashboardStats();
      } catch (err) {
        setError('Error al cargar datos del dashboard');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [fetchDashboardStats]);

  // Función para cambiar de pestaña
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Función para formatear fechas
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Función para renderizar el chip de estado
  const renderStatusChip = (status) => {
    let color = 'default';

    switch (status) {
      case 'Pendiente':
        color = 'warning';
        break;
      case 'Actualizado':
        color = 'secondary';
        break;
      case 'Aprobado':
        color = 'success';
        break;
      case 'Enviado':
        color = 'primary';
        break;
      case 'Rechazado':
        color = 'error';
        break;
      case 'Completado':
        color = 'info';
        break;
      case 'Facturado':
        color = 'default';
        break;
      default:
        color = 'default';
    }

    return (
      <Chip
        label={status}
        color={color}
        size="small"
        sx={{ fontWeight: 'bold', minWidth: '100px' }}
      />
    );
  };

  // Renderizar pantalla de carga
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Renderizar mensaje de error
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>

      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        sx={{ mb: 3 }}
      >
        <Tab label="Resumen General" />
        <Tab label="Presupuestos" />
        <Tab label="Producción" />
        <Tab label="Costes y Beneficios" />
      </Tabs>

      {/* Pestaña de Resumen General */}
      {tabValue === 0 && (
        <Box>
          <Grid container spacing={3}>
            {/* Tarjetas de resumen */}
            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Total Presupuestos
                  </Typography>
                  <Typography variant="h3">
                    {budgetStats.total}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Trabajos Activos
                  </Typography>
                  <Typography variant="h3">
                    {productionStats.activeJobs}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Trabajos Completados
                  </Typography>
                  <Typography variant="h3">
                    {productionStats.completedJobs}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6} lg={3}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Presupuestos Facturados
                  </Typography>
                  <Typography variant="h3">
                    {(budgetStats.byStatus || []).find(s => s.name === 'Facturado')?.value || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Gráfico de presupuestos por mes */}
            <Grid item xs={12} lg={8}>
              <Card>
                <CardHeader title="Presupuestos por Mes" />
                <Divider />
                <CardContent sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={budgetStats.byMonth || []}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="total" name="Total" fill="#8884d8" />
                      <Bar dataKey="aprobados" name="Aprobados" fill="#82ca9d" />
                      <Bar dataKey="completados" name="Completados" fill="#ffc658" />
                      <Bar dataKey="facturados" name="Facturados" fill="#ff8042" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Gráfico de estado de presupuestos */}
            <Grid item xs={12} md={6} lg={4}>
              <Card>
                <CardHeader title="Estado de Presupuestos" />
                <Divider />
                <CardContent sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={budgetStats.byStatus || []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {(budgetStats.byStatus || []).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} presupuestos`, 'Cantidad']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Presupuestos recientes */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Presupuestos Recientes" />
                <Divider />
                <CardContent sx={{ maxHeight: 400, overflow: 'auto' }}>
                  {(budgetStats.recentBudgets || []).length > 0 ? (
                    (budgetStats.recentBudgets || []).map((budget) => (
                      <Box key={budget.budget_id} sx={{ mb: 2, p: 1, borderRadius: 1, bgcolor: 'background.default' }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {budget.ot_number} - {budget.description.substring(0, 50)}{budget.description.length > 50 ? '...' : ''}
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            Fecha: {formatDate(budget.created_at)}
                          </Typography>
                          {renderStatusChip(budget.status)}
                        </Box>
                      </Box>
                    ))
                  ) : (
                    <Typography variant="body1">No hay presupuestos recientes</Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Trabajos de producción recientes */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Trabajos de Producción Recientes" />
                <Divider />
                <CardContent sx={{ maxHeight: 400, overflow: 'auto' }}>
                  {(productionStats.recentJobs || []).length > 0 ? (
                    (productionStats.recentJobs || []).map((job) => {
                      const allCompleted = job.processes.every(process => process.status === 'Completado');
                      const status = job.shipped ? 'Enviado' : (allCompleted ? 'Completado' : 'En Proceso');

                      return (
                        <Box key={job.ot_number} sx={{ mb: 2, p: 1, borderRadius: 1, bgcolor: 'background.default' }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                            {job.ot_number} - {job.description ? `${job.description.substring(0, 50)}${job.description.length > 50 ? '...' : ''}` : 'Sin descripción'}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Procesos: {job.processes ? `${job.processes.filter(p => p.status === 'Completado').length}/${job.processes.length} completados` : 'No hay procesos'}
                            </Typography>
                            <Chip
                              label={status}
                              color={status === 'Enviado' ? 'primary' : (status === 'Completado' ? 'success' : 'warning')}
                              size="small"
                              sx={{ fontWeight: 'bold', minWidth: '100px' }}
                            />
                          </Box>
                        </Box>
                      );
                    })
                  ) : (
                    <Typography variant="body1">No hay trabajos de producción recientes</Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Pestaña de Presupuestos */}
      {tabValue === 1 && (
        <Box>
          <Grid container spacing={3}>
            {/* Gráfico de presupuestos por estado */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Presupuestos por Estado" />
                <Divider />
                <CardContent sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={budgetStats.byStatus || []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {(budgetStats.byStatus || []).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} presupuestos`, 'Cantidad']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Gráfico de presupuestos por mes */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Tendencia de Presupuestos" />
                <Divider />
                <CardContent sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={budgetStats.byMonth || []}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="total" name="Total" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Lista detallada de presupuestos recientes */}
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Presupuestos Recientes" />
                <Divider />
                <CardContent sx={{ maxHeight: 400, overflow: 'auto' }}>
                  {(budgetStats.recentBudgets || []).length > 0 ? (
                    (budgetStats.recentBudgets || []).map((budget) => (
                      <Box key={budget.budget_id} sx={{ mb: 2, p: 2, borderRadius: 1, bgcolor: 'background.default' }}>
                        <Typography variant="h6">
                          {budget.ot_number}
                        </Typography>
                        <Typography variant="body1" gutterBottom>
                          {budget.description || 'Sin descripción'}
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Cliente: {budget.client_data?.company?.name || 'No especificado'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Fecha: {formatDate(budget.created_at)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Cantidad: {budget.quantity}
                            </Typography>
                          </Box>
                          {renderStatusChip(budget.status)}
                        </Box>
                      </Box>
                    ))
                  ) : (
                    <Typography variant="body1">No hay presupuestos recientes</Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Pestaña de Producción */}
      {tabValue === 2 && (
        <Box>
          <Grid container spacing={3}>
            {/* Tarjetas de resumen */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Trabajos Activos
                  </Typography>
                  <Typography variant="h3">
                    {productionStats.activeJobs}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Trabajos Completados
                  </Typography>
                  <Typography variant="h3">
                    {productionStats.completedJobs}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Trabajos Enviados
                  </Typography>
                  <Typography variant="h3">
                    {productionStats.shippedJobs || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Gráfico de trabajos por estado */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Trabajos por Estado" />
                <Divider />
                <CardContent sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={productionStats.byStatus || []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {(productionStats.byStatus || []).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} trabajos`, 'Cantidad']} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Gráfico de trabajos por máquina */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Trabajos por Máquina" />
                <Divider />
                <CardContent sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={productionStats.byMachine || []}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="total" name="Total" fill="#8884d8" />
                      <Bar dataKey="completed" name="Completados" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Lista detallada de trabajos recientes */}
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Trabajos de Producción Recientes" />
                <Divider />
                <CardContent sx={{ maxHeight: 400, overflow: 'auto' }}>
                  {(productionStats.recentJobs || []).length > 0 ? (
                    (productionStats.recentJobs || []).map((job) => {
                      const allCompleted = job.processes && job.processes.length > 0 && job.processes.every(process => process && process.status === 'Completado');
                      const status = job.shipped ? 'Enviado' : (allCompleted ? 'Completado' : 'En Proceso');

                      return (
                        <Box key={job.ot_number} sx={{ mb: 2, p: 2, borderRadius: 1, bgcolor: 'background.default' }}>
                          <Typography variant="h6">
                            {job.ot_number}
                          </Typography>
                          <Typography variant="body1" gutterBottom>
                            {job.description || 'Sin descripción'}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                Cliente: {job.client_name || 'No especificado'}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Procesos: {job.processes ? `${job.processes.filter(p => p.status === 'Completado').length}/${job.processes.length} completados` : 'No hay procesos'}
                              </Typography>
                            </Box>
                            <Chip
                              label={status}
                              color={status === 'Enviado' ? 'primary' : (status === 'Completado' ? 'success' : 'warning')}
                              size="small"
                              sx={{ fontWeight: 'bold', minWidth: '100px' }}
                            />
                          </Box>
                        </Box>
                      );
                    })
                  ) : (
                    <Typography variant="body1">No hay trabajos de producción recientes</Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Pestaña de Costes y Beneficios */}
      {tabValue === 3 && (
        <CostProfitDashboard />
      )}
    </Paper>
  );
};

export default DashboardPage;
