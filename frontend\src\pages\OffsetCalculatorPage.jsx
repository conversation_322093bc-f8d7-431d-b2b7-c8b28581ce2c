import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Snackbar,
  CircularProgress,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  TableHead,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CalculateIcon from '@mui/icons-material/Calculate';
import PrintIcon from '@mui/icons-material/Print';
import TimerIcon from '@mui/icons-material/Timer';
import SpeedIcon from '@mui/icons-material/Speed';
import EuroIcon from '@mui/icons-material/Euro';
import TuneIcon from '@mui/icons-material/Tune';
import SettingsIcon from '@mui/icons-material/Settings';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CloseIcon from '@mui/icons-material/Close';
import { API_URL } from '../config';
import SheetPreview from '../components/SheetPreview';

const OffsetCalculatorPage = () => {
  // Estado para las máquinas offset
  const [machines, setMachines] = useState([]);
  const [selectedMachine, setSelectedMachine] = useState('');
  const [machineDetails, setMachineDetails] = useState(null);

  // Estado para los papeles
  const [papers, setPapers] = useState([]);
  const [selectedPaper, setSelectedPaper] = useState('');

  // Ya no necesitamos los esquemas de plegado, se calculan automáticamente

  // Ya no necesitamos un modo de cálculo, siempre es automático

  // Estado para el formulario
  const [formData, setFormData] = useState({
    // Campos comunes
    copies: 500,
    colors_front: 4,
    colors_back: 4,
    custom_setup_time: '',
    custom_sheets_per_hour: '',
    custom_maculatura: '',
    binding_type: 'gathering', // Tipo de encuadernado: "gathering" (alzado), "collection" (grapado) o "none" (sin encuadernado)

    // Campos para el cálculo
    num_paginas: 40,
    ancho_pagina: 210,
    alto_pagina: 297
  });

  // Estado para los resultados
  const [calculationResult, setCalculationResult] = useState(null);
  const [pliegosResult, setPliegosResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showCustomParams, setShowCustomParams] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Estado para el modal de visualización de esquemas
  const [showSheetPreview, setShowSheetPreview] = useState(false);
  const [selectedEsquema, setSelectedEsquema] = useState(null);

  // Parámetros de desperdicio para la visualización (valores por defecto)
  const [wasteParams] = useState({
    sangrado: 3.0,
    pinzas: 10.0,
    margen_lateral: 5.0,
    margen_superior: 5.0,
    margen_inferior: 5.0,
    marcas_registro: true,
    tiras_control: true
  });

  // Cargar máquinas y papeles al montar el componente
  useEffect(() => {
    fetchMachines();
    fetchPapers();
  }, []);

  // Cargar detalles de la máquina seleccionada
  useEffect(() => {
    if (selectedMachine) {
      fetchMachineDetails(selectedMachine);
    } else {
      setMachineDetails(null);
    }
  }, [selectedMachine]);

  // Función para cargar las máquinas offset
  const fetchMachines = async () => {
    try {
      const response = await fetch(`${API_URL}/machines`);
      if (!response.ok) {
        throw new Error('Error al cargar las máquinas');
      }

      const data = await response.json();
      // Filtrar solo máquinas offset
      const offsetMachines = data.filter(machine => machine.type === 'Offset');
      setMachines(offsetMachines);
    } catch (error) {
      console.error('Error al cargar las máquinas:', error);
      setSnackbar({
        open: true,
        message: 'Error al cargar las máquinas. Por favor, inténtelo de nuevo.',
        severity: 'error'
      });
    }
  };

  // Función para cargar los papeles
  const fetchPapers = async () => {
    try {
      const response = await fetch(`${API_URL}/papers`);
      if (!response.ok) {
        throw new Error('Error al cargar los papeles');
      }

      const data = await response.json();
      setPapers(data);
    } catch (error) {
      console.error('Error al cargar los papeles:', error);
      setSnackbar({
        open: true,
        message: 'Error al cargar los papeles. Por favor, inténtelo de nuevo.',
        severity: 'error'
      });
    }
  };

  // Función para cargar los detalles de una máquina
  const fetchMachineDetails = async (machineId) => {
    try {
      const response = await fetch(`${API_URL}/machines/${machineId}`);
      if (!response.ok) {
        throw new Error('Error al cargar los detalles de la máquina');
      }

      const data = await response.json();
      setMachineDetails(data);
    } catch (error) {
      console.error('Error al cargar los detalles de la máquina:', error);
      setSnackbar({
        open: true,
        message: 'Error al cargar los detalles de la máquina. Por favor, inténtelo de nuevo.',
        severity: 'error'
      });
    }
  };

  // Función para manejar cambios en el formulario
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Para campos numéricos, convertir a número
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? '' : Number(value)
      });
    } else if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Función para manejar el cambio de máquina
  const handleMachineChange = (e) => {
    setSelectedMachine(e.target.value);
    // Limpiar los campos personalizados
    setFormData({
      ...formData,
      custom_setup_time: '',
      custom_sheets_per_hour: '',
      custom_maculatura: ''
    });
  };

  // Función para manejar el cambio de papel
  const handlePaperChange = (e) => {
    setSelectedPaper(e.target.value);
  };

  // Función para calcular costes y tiempos
  const handleCalculate = async () => {
    if (!selectedMachine) {
      setSnackbar({
        open: true,
        message: 'Por favor, seleccione una máquina',
        severity: 'warning'
      });
      return;
    }

    try {
      setLoading(true);

      // Preparar los datos para el cálculo de offset usando únicamente v2/calculate-offset
      const requestData = {
        machine_id: selectedMachine,
        copies: parseInt(formData.copies),
        colors_front: parseInt(formData.colors_front),
        colors_back: parseInt(formData.colors_back),
        paper_id: selectedPaper || undefined,
        num_paginas: parseInt(formData.num_paginas),
        ancho_pagina: parseFloat(formData.ancho_pagina),
        alto_pagina: parseFloat(formData.alto_pagina),
        binding_type: formData.binding_type
      };

      // Añadir campos personalizados si tienen valor
      if (formData.custom_setup_time !== '') {
        requestData.custom_setup_time = parseInt(formData.custom_setup_time);
      }

      if (formData.custom_sheets_per_hour !== '') {
        requestData.custom_sheets_per_hour = parseInt(formData.custom_sheets_per_hour);
      }

      if (formData.custom_maculatura !== '') {
        requestData.custom_maculatura = parseInt(formData.custom_maculatura);
      }

      console.log('Enviando datos para cálculo de offset:', requestData);
      console.log('JSON a enviar a v2/calculate-offset:', JSON.stringify(requestData, null, 2));

      // Realizar la solicitud al endpoint v2/calculate-offset
      console.log('URL del endpoint:', `${API_URL}/v2/calculate-offset`);

      const response = await fetch(`${API_URL}/v2/calculate-offset`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      // Verificar si la respuesta es un error
      if (!response.ok) {
        // Intentar obtener el cuerpo de la respuesta como texto para ver el error completo
        const errorText = await response.text();
        console.error('Respuesta de error completa:', errorText);

        try {
          // Intentar parsear como JSON si es posible
          const errorJson = JSON.parse(errorText);
          throw new Error(JSON.stringify(errorJson));
        } catch {
          // Si no se puede parsear como JSON, usar el texto completo
          throw new Error(`Error ${response.status}: ${errorText}`);
        }
      }

      const data = await response.json();
      console.log('Resultado del cálculo de offset:', data);

      setCalculationResult(data);

      // Extraer información de esquemas del resultado si está disponible
      if (data.esquemas_utilizados && data.esquemas_utilizados.length > 0) {
        // Crear un objeto similar al formato anterior para compatibilidad con la UI
        const pliegosResultData = {
          mejor_combinacion: {
            total_pliegos: data.total_sheets || 0,
            total_planchas: data.total_plates || 0,
            total_passes: data.calculo_pliegos_info?.total_passes || 0,
            esquemas_utilizados: data.esquemas_utilizados.map(esquema => ({
              nombre: esquema.nombre || 'Esquema',
              numero_pliegos: esquema.numero_pliegos || 0,
              paginas_por_pliego: esquema.paginas_por_pliego || 0,
              disposicion: {
                paginas_ancho: esquema.disposicion?.paginas_ancho || 1,
                paginas_alto: esquema.disposicion?.paginas_alto || 1,
                orientacion: esquema.disposicion?.orientacion || 'vertical_normal'
              },
              es_tira_retira: esquema.es_tira_retira || false,
              sheet_type: esquema.sheet_type || 'Flat',
              plates_needed: esquema.plates_needed || 0,
              needs_two_passes: esquema.needs_two_passes || false
            }))
          }
        };

        setPliegosResult(pliegosResultData);
        console.log('Esquemas extraídos del resultado:', pliegosResultData);
      } else {
        console.warn('No se encontraron esquemas en el resultado del cálculo');
        console.log('Estructura completa de la respuesta:', data);
        setPliegosResult(null);
      }

      // Mostrar mensaje de éxito
      setSnackbar({
        open: true,
        message: 'Cálculo realizado con éxito',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error al calcular:', error);

      // Mostrar mensaje de error detallado
      let errorMessage = 'Error desconocido';

      if (error.message) {
        try {
          // Intentar parsear el mensaje de error si es un JSON
          if (error.message.startsWith('{') || error.message.startsWith('[')) {
            const parsedError = JSON.parse(error.message);
            errorMessage = JSON.stringify(parsedError, null, 2);
          } else {
            errorMessage = error.message;
          }
        } catch {
          // Si hay un error al parsear, usamos el mensaje original
          errorMessage = error.message;
        }
      }

      setSnackbar({
        open: true,
        message: `Error: ${errorMessage}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Función para cerrar el snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Función para mostrar la visualización del pliego
  const handleShowPreview = (esquema) => {
    setSelectedEsquema(esquema);
    setShowSheetPreview(true);
  };

  // Función para resetear el formulario
  const handleReset = () => {
    setFormData({
      // Campos comunes
      copies: 500,
      colors_front: 4,
      colors_back: 4,
      custom_setup_time: '',
      custom_sheets_per_hour: '',
      custom_maculatura: '',
      binding_type: 'gathering',

      // Campos para el cálculo automático
      num_paginas: 40,
      ancho_pagina: 210,
      alto_pagina: 297
    });
    setSelectedMachine('');
    setSelectedPaper('');
    setCalculationResult(null);
    setPliegosResult(null);
    setShowSheetPreview(false);
    setSelectedEsquema(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <CalculateIcon sx={{ mr: 1 }} />
        Calculadora Avanzada de Offset
      </Typography>



      <Grid container spacing={3}>
        {/* Columna izquierda - Formulario */}
        <Grid item xs={12} md={6}>
          <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Parámetros de Cálculo
            </Typography>



            {pliegosResult?.mejor_combinacion && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Esquema utilizado
                </Typography>
                <Paper elevation={1} sx={{ p: 2, bgcolor: '#f9f9f9', mb: 2 }}>
                  <Typography variant="body1" gutterBottom>
                    <strong>Total: {pliegosResult.mejor_combinacion.total_pliegos} pliegos / {pliegosResult.mejor_combinacion.total_planchas} planchas</strong>
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Configuración de colores: {formData.colors_front}/{formData.colors_back}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    Total de pasadas por máquina: {pliegosResult.mejor_combinacion.total_passes} pasadas
                    {pliegosResult.mejor_combinacion.esquemas_utilizados.some(e => e.needs_two_passes) && machineDetails?.print_units < 8 ? ' (algunos pliegos requieren dos pasadas)' : ''}
                  </Typography>

                  <TableContainer component={Paper} sx={{ mt: 2 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow sx={{ bgcolor: '#f0f0f0' }}>
                          <TableCell>Esquema</TableCell>
                          <TableCell>Pliegos</TableCell>
                          <TableCell>Págs/Pliego</TableCell>
                          <TableCell>Disposición</TableCell>
                          <TableCell>Tipo Pliego</TableCell>
                          <TableCell>Planchas</TableCell>
                          <TableCell>Visualizar</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {pliegosResult.mejor_combinacion.esquemas_utilizados.map((esquema, index) => (
                          <TableRow key={index} sx={{
                            bgcolor: esquema.sheet_type === 'WorkAndTurn' ? '#e8f5e9' :
                                    esquema.sheet_type === 'WorkAndBack' ? '#fff8e1' :
                                    esquema.sheet_type === 'Perfecting' ? '#e3f2fd' :
                                    '#f5f5f5'
                          }}>
                            <TableCell>{esquema.nombre}</TableCell>
                            <TableCell>{esquema.numero_pliegos}</TableCell>
                            <TableCell>{esquema.paginas_por_pliego}</TableCell>
                            <TableCell>
                              {esquema.disposicion.paginas_ancho} x {esquema.disposicion.paginas_alto}
                            </TableCell>
                            <TableCell>
                              {esquema.sheet_type === 'WorkAndTurn' ? 'WorkAndTurn' :
                               esquema.sheet_type === 'WorkAndBack' ? 'WorkAndBack' :
                               esquema.sheet_type === 'Perfecting' ? 'Perfecting' : 'Normal'}
                              <Typography variant="caption" display="block">
                                {esquema.sheet_type === 'WorkAndTurn' ? 'Tira-retira (mismas planchas)' :
                                 esquema.sheet_type === 'WorkAndBack' ? 'Diferentes planchas' :
                                 esquema.sheet_type === 'Perfecting' ? 'Ambas caras a la vez' : ''}
                                {esquema.needs_two_passes && machineDetails?.print_units < 8 && <span style={{color: '#f44336'}}><br/>Requiere 2 pasadas</span>}
                              </Typography>
                            </TableCell>
                            <TableCell>{esquema.plates_needed}</TableCell>
                            <TableCell>
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => handleShowPreview(esquema)}
                                title="Visualizar pliego"
                              >
                                <VisibilityIcon fontSize="small" />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>


                </Paper>
              </Box>
            )}

            <Grid container spacing={2}>
              {/* Selección de máquina */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <FormControl fullWidth>
                    <InputLabel id="machine-select-label">Máquina Offset</InputLabel>
                    <Select
                      labelId="machine-select-label"
                      id="machine-select"
                      value={selectedMachine}
                      label="Máquina Offset"
                      onChange={handleMachineChange}
                    >
                      <MenuItem value="">
                        <em>Seleccione una máquina</em>
                      </MenuItem>
                      {machines.map((machine) => (
                        <MenuItem key={machine.machine_id} value={machine.machine_id}>
                          {machine.name} ({machine.model})
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>Seleccione la máquina offset para el cálculo</FormHelperText>
                  </FormControl>

                  <Button
                    sx={{ ml: 1, minWidth: 'auto', mt: 1 }}
                    color="primary"
                    variant={showCustomParams ? "contained" : "outlined"}
                    onClick={() => setShowCustomParams(!showCustomParams)}
                    disabled={!selectedMachine}
                    title="Parámetros personalizados"
                  >
                    <TuneIcon />
                  </Button>
                </Box>
              </Grid>

              {/* Parámetros personalizados */}
              {showCustomParams && selectedMachine && (
                <Grid item xs={12}>
                  <Paper elevation={1} sx={{ p: 2, bgcolor: '#f8f9fa', mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <SettingsIcon sx={{ mr: 1, fontSize: '1rem' }} />
                      Parámetros Personalizados
                    </Typography>

                    <Grid container spacing={2}>
                      {/* Tiempo de arranque personalizado */}
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Tiempo de arranque"
                          name="custom_setup_time"
                          type="number"
                          value={formData.custom_setup_time}
                          onChange={handleInputChange}
                          InputProps={{
                            inputProps: { min: 0 },
                            endAdornment: <InputAdornment position="end">min</InputAdornment>,
                          }}
                          helperText={`Por defecto: ${machineDetails?.setup_time || 30} min`}
                          size="small"
                        />
                      </Grid>

                      {/* Velocidad personalizada */}
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Velocidad"
                          name="custom_sheets_per_hour"
                          type="number"
                          value={formData.custom_sheets_per_hour}
                          onChange={handleInputChange}
                          InputProps={{
                            inputProps: { min: 0, step: 100 },
                            endAdornment: <InputAdornment position="end">p/h</InputAdornment>,
                          }}
                          helperText={`Por defecto: ${machineDetails?.sheets_per_hour || 'N/D'} p/h`}
                          size="small"
                        />
                      </Grid>

                      {/* Maculatura personalizada */}
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Maculatura"
                          name="custom_maculatura"
                          type="number"
                          value={formData.custom_maculatura}
                          onChange={handleInputChange}
                          InputProps={{
                            inputProps: { min: 0 },
                            endAdornment: <InputAdornment position="end">pliegos</InputAdornment>,
                          }}
                          helperText={`Por defecto: ${machineDetails?.maculatura || 150} pliegos`}
                          size="small"
                        />
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              )}

              {/* Selección de papel (opcional) */}
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="paper-select-label">Papel (opcional)</InputLabel>
                  <Select
                    labelId="paper-select-label"
                    id="paper-select"
                    value={selectedPaper}
                    label="Papel (opcional)"
                    onChange={handlePaperChange}
                  >
                    <MenuItem value="">
                      <em>Sin papel</em>
                    </MenuItem>
                    {papers.map((paper) => (
                      <MenuItem key={paper.product_id} value={paper.product_id}>
                        {paper.descriptive_name} ({paper.dimension_width}x{paper.dimension_height}mm, {paper.weight}g)
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>Seleccione el papel para incluir su coste en el cálculo</FormHelperText>
                </FormControl>
              </Grid>

              {/* Número de ejemplares */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Número de ejemplares"
                  name="copies"
                  type="number"
                  value={formData.copies}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 1 }
                  }}
                  helperText="Número de copias a imprimir"
                />
              </Grid>

              {/* Número de páginas */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Número de páginas"
                  name="num_paginas"
                  type="number"
                  value={formData.num_paginas}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: {
                      min: formData.binding_type === 'none' ? 1 : 2,
                      step: formData.binding_type === 'none' ? 1 : 2
                    }
                  }}
                  helperText={formData.binding_type === 'none'
                    ? "Número total de páginas (puede ser cualquier número)"
                    : "Número total de páginas (debe ser par)"}
                />
              </Grid>

              {/* Ancho de página */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Ancho de página (mm)"
                  name="ancho_pagina"
                  type="number"
                  value={formData.ancho_pagina}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 1 },
                    endAdornment: <InputAdornment position="end">mm</InputAdornment>,
                  }}
                  helperText="Ancho de la página en milímetros (ej: 210 para A4)"
                />
              </Grid>

              {/* Alto de página */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Alto de página (mm)"
                  name="alto_pagina"
                  type="number"
                  value={formData.alto_pagina}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 1 },
                    endAdornment: <InputAdornment position="end">mm</InputAdornment>,
                  }}
                  helperText="Alto de la página en milímetros (ej: 297 para A4)"
                />
              </Grid>

              {/* Colores anverso */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Colores anverso"
                  name="colors_front"
                  type="number"
                  value={formData.colors_front}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 1, max: 6 }
                  }}
                  helperText="Número de colores en el anverso"
                />
              </Grid>

              {/* Colores reverso */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Colores reverso"
                  name="colors_back"
                  type="number"
                  value={formData.colors_back}
                  onChange={handleInputChange}
                  InputProps={{
                    inputProps: { min: 0, max: 6 }
                  }}
                  helperText="Número de colores en el reverso (0 para impresión a una cara)"
                />
              </Grid>

              {/* Tipo de encuadernado */}
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="binding-type-label">Tipo de Encuadernado</InputLabel>
                  <Select
                    labelId="binding-type-label"
                    id="binding-type"
                    name="binding_type"
                    value={formData.binding_type}
                    label="Tipo de Encuadernado"
                    onChange={handleInputChange}
                  >
                    <MenuItem value="gathering">Alzado (Gathering)</MenuItem>
                    <MenuItem value="collection">Grapado (Collection)</MenuItem>
                    <MenuItem value="none">Sin Encuadernado</MenuItem>
                  </Select>
                  <FormHelperText>
                    {formData.binding_type === 'none'
                      ? 'Sin encuadernado: Llena los pliegos con el máximo número de páginas posible'
                      : formData.binding_type === 'gathering'
                        ? 'Alzado: Usa esquemas tradicionales (F8-7, F16-7, etc.)'
                        : 'Grapado: Usa esquemas tradicionales (F8-7, F16-7, etc.)'}
                  </FormHelperText>
                </FormControl>
              </Grid>


            </Grid>
          </Paper>



          {/* Botones de acción */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleReset}
              disabled={loading}
            >
              Resetear
            </Button>

            <Button
              variant="contained"
              color="primary"
              onClick={handleCalculate}
              disabled={loading || !selectedMachine}
              startIcon={loading ? <CircularProgress size={24} /> : <CalculateIcon />}
            >
              {loading ? 'Calculando...' : 'Calcular'}
            </Button>
          </Box>
        </Grid>

        {/* Columna derecha - Resultados */}
        <Grid item xs={12} md={6}>
          {machineDetails && (
            <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <PrintIcon sx={{ mr: 1 }} />
                Información de la Máquina
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Nombre:</Typography>
                  <Typography variant="body1">{machineDetails.name}</Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Modelo:</Typography>
                  <Typography variant="body1">{machineDetails.model}</Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Coste por hora:</Typography>
                  <Typography variant="body1">{machineDetails.hourly_cost} €/h</Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">CFA:</Typography>
                  <Typography variant="body1">{machineDetails.cfa_percentage}%</Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Tiempo de arranque:</Typography>
                  <Typography variant="body1">{machineDetails.setup_time || 30} minutos</Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Velocidad:</Typography>
                  <Typography variant="body1">
                    {machineDetails.sheets_per_hour ? `${machineDetails.sheets_per_hour} pliegos/hora` : 'No definida'}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Maculatura:</Typography>
                  <Typography variant="body1">
                    {machineDetails.maculatura ? `${machineDetails.maculatura} pliegos` : '150 pliegos'}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Consumo de tinta:</Typography>
                  <Typography variant="body1">
                    {machineDetails.ink_consumption ? `${machineDetails.ink_consumption} gramos/m²` : '2.0 gramos/m²'}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Cuerpos de impresión:</Typography>
                  <Typography variant="body1">
                    {machineDetails.print_units ? machineDetails.print_units : 4}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          )}

          {calculationResult && (
            <Paper elevation={3} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <EuroIcon sx={{ mr: 1 }} />
                Resultados del Cálculo
              </Typography>

              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center' }}>
                    <TimerIcon sx={{ mr: 1, fontSize: '1.2rem' }} />
                    Tiempos de Impresión
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableBody>
                        <TableRow>
                          <TableCell component="th" scope="row">Tiempo de arranque:</TableCell>
                          <TableCell align="right">
                            {calculationResult.setup_time_minutes} minutos
                          </TableCell>
                        </TableRow>

                        {calculationResult.total_plates > 0 && (
                          <TableRow>
                            <TableCell component="th" scope="row">Cambio de planchas:</TableCell>
                            <TableCell align="right">
                              {(() => {
                                // Usar el tiempo de cambio de planchas devuelto por el endpoint
                                if (calculationResult.plate_change_time_minutes !== undefined &&
                                    calculationResult.plate_changes !== undefined) {
                                  return (
                                    <>
                                      {calculationResult.plate_change_time_minutes.toFixed(2)} minutos
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        {calculationResult.plate_changes.toFixed(1)} cambios × {(calculationResult.plate_change_time_minutes / calculationResult.plate_changes).toFixed(1)} min/cambio
                                      </Typography>
                                    </>
                                  );
                                } else {
                                  // Cálculo manual como respaldo
                                  const printUnits = calculationResult.print_units || 4; // Valor por defecto: 4 cuerpos
                                  const cambiosPlanchas = Math.ceil(calculationResult.total_plates / printUnits);
                                  // Cada cambio toma el 50% del tiempo de arranque
                                  const tiempoPorCambio = Math.round(calculationResult.setup_time_minutes * 0.5);
                                  // El tiempo total es el tiempo por cambio multiplicado por el número de cambios
                                  const tiempoTotalCambios = tiempoPorCambio * cambiosPlanchas;

                                  return (
                                    <>
                                      {tiempoTotalCambios} minutos
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        {cambiosPlanchas} cambios × {tiempoPorCambio} min/cambio
                                      </Typography>
                                    </>
                                  );
                                }
                              })()}
                            </TableCell>
                          </TableRow>
                        )}
                        <TableRow>
                          <TableCell component="th" scope="row">Tiempo de impresión:</TableCell>
                          <TableCell align="right">{calculationResult.printing_time_minutes.toFixed(2)} minutos</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Tiempo total:</TableCell>
                          <TableCell align="right">
                            {(() => {
                              let tiempoTotalCambios;
                              let tiempoTotal;
                              let tiempoTotalHoras;





                              // Usar el tiempo total devuelto por el endpoint si está disponible
                              if (calculationResult.total_time_minutes !== undefined) {
                                tiempoTotal = calculationResult.total_time_minutes;
                                tiempoTotalHoras = calculationResult.total_time_hours;

                                // Usar el tiempo de cambio de planchas devuelto por el endpoint
                                tiempoTotalCambios = calculationResult.plate_change_time_minutes;
                              } else {
                                // Cálculo manual como respaldo
                                const printUnits = calculationResult.print_units || 4; // Valor por defecto: 4 cuerpos
                                const cambiosPlanchas = Math.ceil(calculationResult.total_plates / printUnits);
                                // Cada cambio toma el 50% del tiempo de arranque
                                const tiempoPorCambio = Math.round(calculationResult.setup_time_minutes * 0.5);
                                // El tiempo total de cambios es el tiempo por cambio multiplicado por el número de cambios
                                tiempoTotalCambios = tiempoPorCambio * cambiosPlanchas;

                                // Tiempo total = tiempo de arranque + tiempo total de cambios + tiempo de impresión
                                tiempoTotal = calculationResult.setup_time_minutes + tiempoTotalCambios + calculationResult.printing_time_minutes;
                                tiempoTotalHoras = tiempoTotal / 60;
                              }

                              return (
                                <>
                                  <Typography fontWeight="bold">
                                    {tiempoTotal.toFixed(2)} minutos
                                    ({tiempoTotalHoras.toFixed(2)} horas)
                                  </Typography>
                                  <Typography variant="caption" display="block" color="text.secondary">
                                    Arranque: {calculationResult.setup_time_minutes !== undefined ?
                                      calculationResult.setup_time_minutes.toFixed(2) : '0.00'} min +
                                    Cambios: {calculationResult.plate_change_time_minutes !== undefined && calculationResult.plate_change_time_minutes > 0 ?
                                      calculationResult.plate_change_time_minutes.toFixed(2) : '0.00'} min +
                                    Impresión: {calculationResult.printing_time_minutes.toFixed(2)} min
                                  </Typography>
                                </>
                              );
                            })()}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>

              <Accordion defaultExpanded sx={{ mt: 2 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center' }}>
                    <EuroIcon sx={{ mr: 1, fontSize: '1.2rem' }} />
                    Costes de Impresión
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableBody>
                        <TableRow>
                          <TableCell component="th" scope="row">Coste fijo de arranque (CFA):</TableCell>
                          <TableCell align="right">
                            {calculationResult.cfa_cost.toFixed(2)} €
                            <Typography variant="caption" display="block" color="text.secondary">
                              {calculationResult.cfa_percentage}% del coste por hora ({calculationResult.hourly_cost} €/h)
                            </Typography>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Coste de impresión:</TableCell>
                          <TableCell align="right">
                            {(() => {
                              let tiempoTotalHoras;
                              let costeImpresion;

                              // Usar el tiempo total devuelto por el endpoint si está disponible
                              if (calculationResult.total_time_hours !== undefined) {
                                tiempoTotalHoras = calculationResult.total_time_hours;
                                // Calcular el coste de impresión basado en el tiempo total del endpoint
                                costeImpresion = calculationResult.hourly_cost * tiempoTotalHoras;
                              } else {
                                // Cálculo manual como respaldo
                                const printUnits = calculationResult.print_units || 4; // Valor por defecto: 4 cuerpos
                                const cambiosPlanchas = Math.ceil(calculationResult.total_plates / printUnits);
                                // Cada cambio toma el 50% del tiempo de arranque
                                const tiempoPorCambio = Math.round(calculationResult.setup_time_minutes * 0.5);
                                // El tiempo total de cambios es el tiempo por cambio multiplicado por el número de cambios
                                const tiempoTotalCambios = tiempoPorCambio * cambiosPlanchas;

                                // Tiempo total = tiempo de arranque + tiempo total de cambios + tiempo de impresión
                                const tiempoTotal = calculationResult.setup_time_minutes + tiempoTotalCambios + calculationResult.printing_time_minutes;
                                tiempoTotalHoras = tiempoTotal / 60;

                                // Calcular el coste de impresión basado en el nuevo tiempo total
                                costeImpresion = calculationResult.hourly_cost * tiempoTotalHoras;
                              }

                              return (
                                <>
                                  {costeImpresion.toFixed(2)} €
                                  <Typography variant="caption" display="block" color="text.secondary">
                                    {calculationResult.hourly_cost} €/h × {tiempoTotalHoras.toFixed(2)} h
                                    <br />
                                    {calculationResult.total_physical_sheets} pliegos ({(calculationResult.total_sheets * calculationResult.copies)} + {calculationResult.maculatura} maculatura) a {calculationResult.sheets_per_hour} pliegos/hora
                                  </Typography>
                                </>
                              );
                            })()}
                          </TableCell>
                        </TableRow>
                        {calculationResult.paper_cost !== null && (
                          <TableRow>
                            <TableCell component="th" scope="row">Coste del papel:</TableCell>
                            <TableCell align="right">
                              {calculationResult.paper_cost.toFixed(2)} €
                              <Typography variant="caption" display="block" color="text.secondary">
                                {calculationResult.paper_info && `${calculationResult.paper_info.descriptive_name}`}
                                <br />
                                {(calculationResult.paper_cost_per_1000 / 1000).toFixed(4)} € por pliego × {calculationResult.total_physical_sheets} pliegos
                              </Typography>
                            </TableCell>
                          </TableRow>
                        )}
                        {calculationResult.plates_cost !== null && (
                          <TableRow>
                            <TableCell component="th" scope="row">Coste de las planchas:</TableCell>
                            <TableCell align="right">
                              {calculationResult.plates_cost.toFixed(2)} €
                              <Typography variant="caption" display="block" color="text.secondary">
                                {(calculationResult.plates_cost / calculationResult.total_plates).toFixed(2)} € por plancha × {calculationResult.total_plates} planchas
                              </Typography>
                            </TableCell>
                          </TableRow>
                        )}
                        {calculationResult.ink_cost !== null && (
                          <TableRow>
                            <TableCell component="th" scope="row">Coste de la tinta:</TableCell>
                            <TableCell align="right">
                              {calculationResult.ink_cost.toFixed(2)} €
                              <Typography variant="caption" display="block" color="text.secondary">
                                Consumo de tinta: {calculationResult.ink_consumption} gramos/m²
                                a {calculationResult.ink_price_per_kg.toFixed(2)} €/kg (precio medio CMYK)
                              </Typography>

                            </TableCell>
                          </TableRow>
                        )}
                        <TableRow>
                          <TableCell component="th" scope="row">Coste total:</TableCell>
                          <TableCell align="right">
                            {(() => {
                              let tiempoTotalHoras;
                              let costeImpresion;

                              // Usar el tiempo total devuelto por el endpoint si está disponible
                              if (calculationResult.total_time_hours !== undefined) {
                                tiempoTotalHoras = calculationResult.total_time_hours;
                                // Calcular el coste de impresión basado en el tiempo total del endpoint
                                costeImpresion = calculationResult.hourly_cost * tiempoTotalHoras;
                              } else {
                                // Cálculo manual como respaldo
                                const printUnits = calculationResult.print_units || 4; // Valor por defecto: 4 cuerpos
                                const cambiosPlanchas = Math.ceil(calculationResult.total_plates / printUnits);
                                // Cada cambio toma el 50% del tiempo de arranque
                                const tiempoPorCambio = Math.round(calculationResult.setup_time_minutes * 0.5);
                                // El tiempo total de cambios es el tiempo por cambio multiplicado por el número de cambios
                                const tiempoTotalCambios = tiempoPorCambio * cambiosPlanchas;

                                // Tiempo total = tiempo de arranque + tiempo total de cambios + tiempo de impresión
                                const tiempoTotal = calculationResult.setup_time_minutes + tiempoTotalCambios + calculationResult.printing_time_minutes;
                                tiempoTotalHoras = tiempoTotal / 60;

                                // Calcular el coste de impresión basado en el nuevo tiempo total
                                costeImpresion = calculationResult.hourly_cost * tiempoTotalHoras;
                              }

                              // Calcular el coste total
                              let costeTotal = costeImpresion + calculationResult.cfa_cost;
                              if (calculationResult.paper_cost !== null) costeTotal += calculationResult.paper_cost;
                              if (calculationResult.plates_cost !== null) costeTotal += calculationResult.plates_cost;
                              if (calculationResult.ink_cost !== null) costeTotal += calculationResult.ink_cost;

                              return (
                                <>
                                  <Typography fontWeight="bold">
                                    {costeTotal.toFixed(2)} €
                                  </Typography>
                                  <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 1 }}>
                                    <Box component="span" sx={{ display: 'block' }}>Impresión: {costeImpresion.toFixed(2)} €</Box>
                                    <Box component="span" sx={{ display: 'block' }}>CFA: {calculationResult.cfa_cost.toFixed(2)} €</Box>
                                    {calculationResult.paper_cost !== null && (
                                      <Box component="span" sx={{ display: 'block' }}>Papel: {calculationResult.paper_cost.toFixed(2)} €</Box>
                                    )}
                                    {calculationResult.plates_cost !== null && (
                                      <Box component="span" sx={{ display: 'block' }}>Planchas: {calculationResult.plates_cost.toFixed(2)} €</Box>
                                    )}
                                    {calculationResult.ink_cost !== null && (
                                      <Box component="span" sx={{ display: 'block' }}>
                                        Tinta: {calculationResult.ink_cost.toFixed(2)} €
                                      </Box>
                                    )}
                                  </Typography>
                                </>
                              );
                            })()}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>

              <Accordion sx={{ mt: 2 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center' }}>
                    <SpeedIcon sx={{ mr: 1, fontSize: '1.2rem' }} />
                    Detalles del Trabajo
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableBody>
                        {/* Información del cálculo automático de pliegos */}
                        {calculationResult.num_paginas && (
                          <>
                            <TableRow>
                              <TableCell component="th" scope="row">Número de páginas:</TableCell>
                              <TableCell align="right">{calculationResult.num_paginas}</TableCell>
                            </TableRow>
                            {calculationResult.esquemas_utilizados && (
                              <TableRow>
                                <TableCell component="th" scope="row">Esquemas utilizados:</TableCell>
                                <TableCell align="right">
                                  {calculationResult.esquemas_utilizados.map((esquema, index) => (
                                    <Box key={index} sx={{ mb: 1 }}>
                                      <Typography variant="body2">
                                        {esquema.nombre}: {esquema.numero_pliegos} pliegos ({esquema.paginas_por_pliego} páginas/pliego)
                                      </Typography>
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        Tipo: {esquema.sheet_type}, {esquema.es_tira_retira ? 'Tira-retira' : 'Normal'}
                                      </Typography>
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        Planchas: {esquema.plates_needed}
                                      </Typography>
                                    </Box>
                                  ))}
                                </TableCell>
                              </TableRow>
                            )}
                          </>
                        )}

                        <TableRow>
                          <TableCell component="th" scope="row">Pliegos por esquema:</TableCell>
                          <TableCell align="right">{calculationResult.total_sheets}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Número de ejemplares:</TableCell>
                          <TableCell align="right">{calculationResult.copies}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Total pliegos físicos:</TableCell>
                          <TableCell align="right">{calculationResult.total_physical_sheets}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Total planchas:</TableCell>
                          <TableCell align="right">
                            {calculationResult.total_plates}

                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Colores anverso/reverso:</TableCell>
                          <TableCell align="right">{calculationResult.colors_front}/{calculationResult.colors_back}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Impresión:</TableCell>
                          <TableCell align="right">
                            {calculationResult.is_duplex ? 'Doble cara' : 'Una cara'}
                            <Typography variant="caption" display="block" color="text.secondary">
                              Tipo de pliego: {calculationResult.sheet_type}
                            </Typography>

                          </TableCell>
                        </TableRow>


                        <TableRow>
                          <TableCell component="th" scope="row">Velocidad utilizada:</TableCell>
                          <TableCell align="right">{calculationResult.sheets_per_hour} pliegos/hora</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Maculatura:</TableCell>
                          <TableCell align="right">{calculationResult.maculatura} pliegos adicionales</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell component="th" scope="row">Consumo de tinta:</TableCell>
                          <TableCell align="right">
                            {calculationResult.ink_consumption} gramos/m²

                          </TableCell>
                        </TableRow>
                        {calculationResult.paper_info && (
                          <>
                            <TableRow>
                              <TableCell component="th" scope="row">Formato del papel:</TableCell>
                              <TableCell align="right">{calculationResult.paper_info.dimension_width} × {calculationResult.paper_info.dimension_height} mm</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell component="th" scope="row">Gramaje:</TableCell>
                              <TableCell align="right">{calculationResult.paper_info.weight} g/m²</TableCell>
                            </TableRow>
                          </>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Modal para visualizar el pliego */}
      <Dialog
        open={showSheetPreview}
        onClose={() => setShowSheetPreview(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Visualización del Pliego
          <IconButton
            aria-label="close"
            onClick={() => setShowSheetPreview(false)}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {selectedEsquema && machineDetails && (
            <SheetPreview
              anchoPliego={machineDetails.sheet_height || 1000}
              altoPliego={machineDetails.sheet_width || 700}
              anchoPagina={parseFloat(formData.ancho_pagina)}
              altoPagina={parseFloat(formData.alto_pagina)}
              paginasAncho={selectedEsquema.disposicion.paginas_ancho}
              paginasAlto={selectedEsquema.disposicion.paginas_alto}
              sangrado={wasteParams.sangrado}
              pinzas={wasteParams.pinzas}
              margenLateral={wasteParams.margen_lateral}
              margenSuperior={wasteParams.margen_superior}
              margenInferior={wasteParams.margen_inferior}
              marcasRegistro={wasteParams.marcas_registro}
              tirasControl={wasteParams.tiras_control}
              esTiraRetira={selectedEsquema.es_tira_retira}
              sheetType={selectedEsquema.sheet_type}
              pageLayout={selectedEsquema.page_layout}
              esquemaNombre={selectedEsquema.nombre}
              orientacion={selectedEsquema.disposicion?.orientacion || 'Rotate0'}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Snackbar para mensajes */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default OffsetCalculatorPage;
