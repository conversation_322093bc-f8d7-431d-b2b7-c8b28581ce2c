import React from 'react';
import { Box, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import MachineCard from './MachineCard';

const MachineContainer = ({ 
  title, 
  colorCategory, 
  machines, 
  selectedMachine, 
  onMachineSelect,
  machineFilter
}) => {
  return (
    <Box sx={{ 
      display: 'inline-block', 
      width: 'auto',
      height: '100%',
      borderRadius: '8px',
      overflow: 'hidden',
      boxShadow: 1
    }}>
      <Typography variant="subtitle2" component="div" 
        sx={{ 
          bgcolor: `${colorCategory}.main`, 
          color: 'white', 
          py: 0.3,
          px: 1, 
          fontWeight: 'bold',
          fontSize: '0.75rem',
          display: 'block',
          width: '100%',
          textAlign: 'center',
          height: '24px',
          lineHeight: '18px'
        }}
      >
        {title}
      </Typography>
      
      <Box sx={{ 
        display: 'flex', 
        flexWrap: 'nowrap', 
        overflowX: 'auto', 
        p: 0.5, 
        bgcolor: `${colorCategory}.light`, 
        width: '100%',
        height: '62px',
        '&::-webkit-scrollbar': {
          height: '4px',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: 'rgba(0,0,0,0.2)',
          borderRadius: '2px',
        }
      }}>
        {machines
          .filter(machineFilter)
          .sort((a, b) => {
            // Ordenar por tipo y luego por nombre
            if (a.type !== b.type) {
              return a.type.localeCompare(b.type);
            }
            return a.name.localeCompare(b.name);
          })
          .map((machine) => (
            <MachineCard
              key={machine.machine_id}
              machine={machine}
              isSelected={selectedMachine?.machine_id === machine.machine_id}
              onClick={() => onMachineSelect(machine)}
              colorCategory={colorCategory}
            />
          ))
        }
      </Box>
    </Box>
  );
};

MachineContainer.propTypes = {
  title: PropTypes.string.isRequired,
  colorCategory: PropTypes.oneOf(['primary', 'success']).isRequired,
  machines: PropTypes.array.isRequired,
  selectedMachine: PropTypes.object,
  onMachineSelect: PropTypes.func.isRequired,
  machineFilter: PropTypes.func.isRequired
};

export default MachineContainer;
