# Acceso a Datos en el Backend

Este documento explica cómo se accede a las diferentes estructuras de datos en los directorios `/data` y `/config` del backend.

## Estructura General

El backend utiliza archivos JSON como base de datos para almacenar información persistente. Estos archivos se encuentran en los siguientes directorios:

- `/data`: Contiene los datos principales de la aplicación (presupuestos, clientes, papeles, máquinas, etc.)
- `/config`: Contiene archivos de configuración para la aplicación

## Acceso a Datos en `/data`

### Estructura del Directorio `/data`

El directorio `/data` contiene varios archivos JSON, cada uno dedicado a un tipo específico de datos:

- `budgets.json`: Presupuestos
- `clients.json`: Clientes
- `papers.json`: Papeles
- `machines.json`: Máquinas
- `processes.json`: Procesos
- `users.json`: Usuarios

### Patrón de Acceso a Datos

Para cada tipo de datos, se sigue un patrón similar:

1. **Carga de Datos**: Se lee el archivo JSON correspondiente
2. **Manipulación de Datos**: Se realizan operaciones CRUD (Crear, Leer, Actualizar, Eliminar)
3. **Guardado de Datos**: Se escribe de vuelta al archivo JSON

### Ejemplo: Acceso a Presupuestos (`budgets.json`)

```python
import json
import os
from fastapi import HTTPException, status

# Ruta al archivo de presupuestos
BUDGETS_FILE = "data/budgets.json"

# Función para cargar presupuestos desde el archivo JSON
def load_budgets():
    try:
        if os.path.exists(BUDGETS_FILE):
            with open(BUDGETS_FILE, "r", encoding="utf-8") as file:
                return json.load(file)
        return []
    except Exception as e:
        print(f"Error al cargar presupuestos: {e}")
        return []

# Función para guardar presupuestos en el archivo JSON
def save_budgets(budgets):
    try:
        with open(BUDGETS_FILE, "w", encoding="utf-8") as file:
            json.dump(budgets, file, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Error al guardar presupuestos: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al guardar presupuestos: {e}"
        )
```

### Estructura de Datos en `budgets.json`

Los presupuestos tienen una estructura compleja que incluye:

- Información básica (ID, descripción, cliente, etc.)
- Partes (`parts`): Cada parte tiene su propio papel, máquina, costes, etc.
- Costes de procesos (`process_costs`): Costes adicionales como acabados

Ejemplo de estructura:

```json
[
  {
    "budget_id": "PRES-001",
    "description": "Folletos promocionales",
    "client_id": "CLI-001",
    "created_at": "2023-04-15T10:30:00",
    "ot_number": "OT-001",
    "quantity": 1000,
    "parts": [
      {
        "name": "Portada",
        "paper_id": "PAP-001",
        "machine_id": "MAQ-001",
        "paper_cost": 25.50,
        "machine_cost": 45.75,
        "total_cost": 71.25,
        "paper_data": {
          "name": "Estucado Brillo",
          "weight": 170,
          "size": "A1",
          "price_per_ton": 1200.00
        },
        "machine_data": {
          "name": "Prensa B1",
          "hourly_cost": 120.00,
          "cfa_percentage": 10.0
        }
      }
    ],
    "process_costs": [
      {
        "name": "Plastificado",
        "quantity": 1000,
        "unit_type": "unidades",
        "unit_cost": 0.05,
        "total_cost": 50.00
      }
    ],
    "process_total_cost": 50.00,
    "total_cost": 121.25
  }
]
```

### Enriquecimiento de Datos

Cuando se solicita un presupuesto a través de la API, se enriquecen los datos con información adicional:

```python
def get_complete_data(budget):
    # Enriquecer con datos del cliente
    if budget.get("client_id"):
        client = get_client_by_id(budget["client_id"])
        if client:
            budget["client_data"] = client
    
    # Enriquecer partes con datos de papel y máquina
    if "parts" in budget:
        for part in budget["parts"]:
            if part.get("paper_id"):
                paper = get_paper_by_id(part["paper_id"])
                if paper:
                    part["paper_data"] = paper
            
            if part.get("machine_id"):
                machine = get_machine_by_id(part["machine_id"])
                if machine:
                    part["machine_data"] = machine
    
    return budget
```

## Acceso a Datos en `/config`

### Estructura del Directorio `/config`

El directorio `/config` contiene archivos de configuración para diferentes aspectos de la aplicación:

- `app_config.json`: Configuración general de la aplicación
- `logging_config.json`: Configuración de logging
- `pricing_config.json`: Configuración de precios y tarifas

### Ejemplo: Acceso a Configuración de la Aplicación

```python
import json
import os

# Ruta al archivo de configuración
CONFIG_FILE = "config/app_config.json"

# Función para cargar la configuración
def load_config():
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r", encoding="utf-8") as file:
                return json.load(file)
        return {}
    except Exception as e:
        print(f"Error al cargar configuración: {e}")
        return {}

# Función para guardar la configuración
def save_config(config):
    try:
        with open(CONFIG_FILE, "w", encoding="utf-8") as file:
            json.dump(config, file, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Error al guardar configuración: {e}")
        raise Exception(f"Error al guardar configuración: {e}")
```

### Estructura de Datos en `app_config.json`

```json
{
  "app_name": "TrikyPrinter",
  "version": "1.0.0",
  "company": {
    "name": "TrikyPrinter",
    "address": "c/Costa Rica 11",
    "city": "Madrid",
    "postal_code": "28016",
    "phone": "+34 91 000 00 00",
    "email": "<EMAIL>",
    "cif": "B12345678"
  },
  "default_settings": {
    "budget_validity_days": 30,
    "default_currency": "EUR",
    "default_language": "es"
  }
}
```

## Flujo de Datos en la Aplicación

### Del Backend al Frontend

1. **Solicitud del Frontend**:
   - El frontend hace una petición HTTP a la API del backend.
   - Ejemplo: `GET /budgets/PRES-001`

2. **Procesamiento en el Backend**:
   - El backend recibe la petición.
   - Carga los datos del archivo JSON correspondiente.
   - Procesa los datos según sea necesario.
   - Devuelve los datos como respuesta HTTP.

3. **Recepción en el Frontend**:
   - El frontend recibe los datos.
   - Procesa y muestra los datos en la interfaz de usuario.

### Ejemplo de Flujo Completo para un Presupuesto

```
Frontend                          Backend                          Almacenamiento
┌─────────────┐                  ┌─────────────┐                  ┌─────────────┐
│             │  HTTP Request    │             │  File I/O        │             │
│  BudgetPDF  │ ───────────────> │  API Route  │ ───────────────> │ budgets.json│
│  Component  │                  │ /budgets/id │                  │             │
│             │ <─────────────── │             │ <─────────────── │             │
└─────────────┘  HTTP Response   └─────────────┘                  └─────────────┘
```

## Consideraciones Importantes

### Seguridad

- Los archivos JSON no deben ser accesibles directamente desde el exterior.
- Todo acceso debe pasar por la API, que implementa la lógica de negocio y las validaciones necesarias.

### Concurrencia

- El sistema actual no maneja concurrencia de manera óptima.
- Si múltiples usuarios intentan modificar el mismo archivo simultáneamente, podría haber problemas de consistencia.

### Respaldo

- Es recomendable implementar un sistema de respaldo para los archivos JSON.
- Se podría considerar migrar a una base de datos relacional o NoSQL en el futuro para mejorar la escalabilidad y la gestión de concurrencia.

## Conclusión

El backend utiliza archivos JSON como base de datos para almacenar información persistente. El acceso a estos datos sigue un patrón consistente de carga, manipulación y guardado. La API del backend expone estos datos al frontend, que los consume y muestra en la interfaz de usuario.

Esta arquitectura es simple y efectiva para aplicaciones pequeñas a medianas, pero podría requerir mejoras para escalar a aplicaciones más grandes o con mayor concurrencia de usuarios.
