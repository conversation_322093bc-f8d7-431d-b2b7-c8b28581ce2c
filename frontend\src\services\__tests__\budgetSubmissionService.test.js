/**
 * Tests para budgetSubmissionService
 */
import budgetSubmissionService, {
  validatePartsCalculations,
  calculatePartCost,
  calculatePartsCostTotal,
  calculateBudgetTotalCost,
  prepareBudgetForSubmission,
  handlePostSubmissionNavigation,
  handleBudgetSubmission,
  getBudgetStats
} from '../budgetSubmissionService';

// Mock de window.dispatchEvent
const mockDispatchEvent = jest.fn();
Object.defineProperty(window, 'dispatchEvent', {
  value: mockDispatchEvent
});

// Mock de setTimeout
jest.useFakeTimers();

describe('budgetSubmissionService', () => {
  beforeEach(() => {
    mockDispatchEvent.mockClear();
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
  });

  describe('validatePartsCalculations', () => {
    test('should return valid for non-edit mode', () => {
      const budgetParts = [{ paper: {}, machine: {}, pageCount: 10 }];
      const result = validatePartsCalculations(budgetParts, false);
      
      expect(result).toEqual({
        isValid: true,
        error: null,
        partsCount: 0
      });
    });

    test('should return valid when all parts have calculations in edit mode', () => {
      const budgetParts = [
        {
          paper: { id: 1 },
          machine: { id: 1 },
          pageCount: 10,
          sheetCalculation: { paper_cost: 100 }
        }
      ];
      const result = validatePartsCalculations(budgetParts, true);
      
      expect(result).toEqual({
        isValid: true,
        error: null,
        partsCount: 0
      });
    });

    test('should return invalid when parts missing calculations in edit mode', () => {
      const budgetParts = [
        {
          paper: { id: 1 },
          machine: { id: 1 },
          pageCount: 10
          // Missing sheetCalculation
        },
        {
          paper: { id: 2 },
          machine: { id: 2 },
          pageCount: 5,
          sheetCalculation: { outdated: true }
        }
      ];
      const result = validatePartsCalculations(budgetParts, true);
      
      expect(result.isValid).toBe(false);
      expect(result.partsCount).toBe(2);
      expect(result.error).toContain('2 partes necesitan ser recalculadas');
    });

    test('should ignore parts without basic data', () => {
      const budgetParts = [
        { paper: null, machine: null, pageCount: null }, // Should be ignored
        {
          paper: { id: 1 },
          machine: { id: 1 },
          pageCount: 10
          // Missing sheetCalculation but has basic data
        }
      ];
      const result = validatePartsCalculations(budgetParts, true);
      
      expect(result.isValid).toBe(false);
      expect(result.partsCount).toBe(1);
    });
  });

  describe('calculatePartCost', () => {
    test('should return 0 for part without sheet calculation', () => {
      const part = { machine: { type: 'Offset' } };
      const result = calculatePartCost(part);
      
      expect(result).toBe(0);
    });

    test('should calculate digital machine cost correctly', () => {
      const part = {
        machine: { type: 'Digital' },
        sheetCalculation: {
          paper_cost: 100,
          machine_cost: 50,
          click_cost: 25
        }
      };
      const result = calculatePartCost(part);
      
      expect(result).toBe(175); // 100 + 50 + 25
    });

    test('should calculate offset machine cost correctly', () => {
      const part = {
        machine: { type: 'Offset' },
        sheetCalculation: {
          paper_cost: 100,
          machine_cost: 50,
          plates_cost: 30,
          ink_cost: 20
        }
      };
      const result = calculatePartCost(part);
      
      expect(result).toBe(200); // 100 + 50 + 30 + 20
    });

    test('should calculate offset cost with maculatura', () => {
      const part = {
        machine: { type: 'Offset' },
        paper: { price_per_1000: 50 },
        sheetCalculation: {
          paper_cost: 100,
          machine_cost: 50,
          plates_cost: 30,
          ink_cost: 20,
          total_maculatura: 100,
          paper_cost_per_1000: 60
        }
      };
      const result = calculatePartCost(part);
      
      expect(result).toBe(206); // 100 + 50 + 30 + 20 + (100 * 60 / 1000)
    });
  });

  describe('calculatePartsCostTotal', () => {
    test('should return 0 for empty or null parts', () => {
      expect(calculatePartsCostTotal([])).toBe(0);
      expect(calculatePartsCostTotal(null)).toBe(0);
    });

    test('should calculate total cost for multiple parts', () => {
      const budgetParts = [
        {
          machine: { type: 'Digital' },
          sheetCalculation: {
            paper_cost: 100,
            machine_cost: 50,
            click_cost: 25
          }
        },
        {
          machine: { type: 'Offset' },
          sheetCalculation: {
            paper_cost: 80,
            machine_cost: 40,
            plates_cost: 20,
            ink_cost: 15
          }
        }
      ];
      const result = calculatePartsCostTotal(budgetParts);
      
      expect(result).toBe(330); // 175 + 155
    });
  });

  describe('calculateBudgetTotalCost', () => {
    test('should calculate complete budget cost breakdown', () => {
      const budgetParts = [
        {
          machine: { type: 'Digital' },
          sheetCalculation: {
            paper_cost: 100,
            machine_cost: 50,
            click_cost: 25
          }
        }
      ];
      const calculatedProcessCost = 30;
      const budget = { shipping: { cost: 15 } };

      const result = calculateBudgetTotalCost(budgetParts, calculatedProcessCost, budget);
      
      expect(result).toEqual({
        partsCostTotal: 175,
        processCost: 30,
        shippingCost: 15,
        totalCost: 220
      });
    });

    test('should handle missing process cost and shipping', () => {
      const budgetParts = [];
      const calculatedProcessCost = null;
      const budget = {};

      const result = calculateBudgetTotalCost(budgetParts, calculatedProcessCost, budget);
      
      expect(result).toEqual({
        partsCostTotal: 0,
        processCost: 0,
        shippingCost: 0,
        totalCost: 0
      });
    });
  });

  describe('prepareBudgetForSubmission', () => {
    test('should prepare budget with cost breakdown', () => {
      const budget = {
        otNumber: 'OT-001',
        shipping: { cost: 15, weight: 5 }
      };
      const costBreakdown = {
        partsCostTotal: 175,
        processCost: 30,
        shippingCost: 15,
        totalCost: 220
      };

      const result = prepareBudgetForSubmission(budget, costBreakdown);
      
      expect(result).toEqual({
        otNumber: 'OT-001',
        shipping: { cost: 15, weight: 5 },
        shipping_cost: 15,
        total_paper_weight_kg: 5,
        total_cost: 220,
        cost_breakdown: {
          parts_cost: 175,
          process_cost: 30,
          shipping_cost: 15,
          total_cost: 220
        }
      });
    });
  });

  describe('handlePostSubmissionNavigation', () => {
    test('should dispatch navigation event after delay', () => {
      handlePostSubmissionNavigation(1000);
      
      expect(mockDispatchEvent).not.toHaveBeenCalled();
      
      jest.advanceTimersByTime(1000);
      
      expect(mockDispatchEvent).toHaveBeenCalledWith(
        new CustomEvent('navigate-to-budget-list')
      );
    });

    test('should use default delay of 1500ms', () => {
      handlePostSubmissionNavigation();
      
      jest.advanceTimersByTime(1499);
      expect(mockDispatchEvent).not.toHaveBeenCalled();
      
      jest.advanceTimersByTime(1);
      expect(mockDispatchEvent).toHaveBeenCalled();
    });
  });

  describe('handleBudgetSubmission', () => {
    const mockBudgetSubmitService = {
      submitBudget: jest.fn()
    };
    const mockShowSnackbar = jest.fn();

    beforeEach(() => {
      mockBudgetSubmitService.submitBudget.mockClear();
      mockShowSnackbar.mockClear();
    });

    test('should handle successful submission', async () => {
      const mockEvent = { preventDefault: jest.fn() };
      const savedBudget = { id: 'budget-123' };
      mockBudgetSubmitService.submitBudget.mockResolvedValue(savedBudget);

      const params = {
        e: mockEvent,
        budget: { otNumber: 'OT-001' },
        budgetParts: [],
        selectedProcesses: [],
        calculatedProcessCost: 0,
        selectedPdf: null,
        pdfInfo: null,
        colorConfig: {},
        productConfig: {},
        budgetId: null,
        isEditMode: false,
        showSnackbar: mockShowSnackbar,
        setCalculatedProcessCost: jest.fn(),
        buildApiUrl: jest.fn(),
        budgetSubmitService: mockBudgetSubmitService
      };

      const result = await handleBudgetSubmission(params);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(mockBudgetSubmitService.submitBudget).toHaveBeenCalled();
      expect(result).toBe(savedBudget);
    });

    test('should handle validation failure', async () => {
      const mockEvent = { preventDefault: jest.fn() };
      const budgetParts = [
        {
          paper: { id: 1 },
          machine: { id: 1 },
          pageCount: 10
          // Missing sheetCalculation
        }
      ];

      const params = {
        e: mockEvent,
        budget: { otNumber: 'OT-001' },
        budgetParts,
        selectedProcesses: [],
        calculatedProcessCost: 0,
        selectedPdf: null,
        pdfInfo: null,
        colorConfig: {},
        productConfig: {},
        budgetId: null,
        isEditMode: true, // Edit mode triggers validation
        showSnackbar: mockShowSnackbar,
        setCalculatedProcessCost: jest.fn(),
        buildApiUrl: jest.fn(),
        budgetSubmitService: mockBudgetSubmitService
      };

      const result = await handleBudgetSubmission(params);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('parte necesita ser recalculada'),
        'error'
      );
      expect(mockBudgetSubmitService.submitBudget).not.toHaveBeenCalled();
      expect(result).toBeNull();
    });

    test('should handle submission error', async () => {
      const mockEvent = { preventDefault: jest.fn() };
      const error = new Error('Submission failed');
      mockBudgetSubmitService.submitBudget.mockRejectedValue(error);

      const params = {
        e: mockEvent,
        budget: { otNumber: 'OT-001' },
        budgetParts: [],
        selectedProcesses: [],
        calculatedProcessCost: 0,
        selectedPdf: null,
        pdfInfo: null,
        colorConfig: {},
        productConfig: {},
        budgetId: null,
        isEditMode: false,
        showSnackbar: mockShowSnackbar,
        setCalculatedProcessCost: jest.fn(),
        buildApiUrl: jest.fn(),
        budgetSubmitService: mockBudgetSubmitService
      };

      const result = await handleBudgetSubmission(params);

      expect(mockShowSnackbar).toHaveBeenCalledWith(
        'Error al procesar el presupuesto: Submission failed',
        'error'
      );
      expect(result).toBeNull();
    });
  });

  describe('getBudgetStats', () => {
    test('should calculate budget statistics correctly', () => {
      const budgetParts = [
        {
          paper: { id: 1 },
          machine: { id: 1 },
          pageCount: 10,
          sheetCalculation: { paper_cost: 100 }
        },
        {
          paper: { id: 2 },
          machine: { id: 2 },
          pageCount: 5
          // Missing sheetCalculation
        },
        {
          // Missing basic data - should not affect validation
        }
      ];

      const stats = getBudgetStats(budgetParts, true);

      expect(stats).toEqual({
        totalParts: 3,
        partsWithCalculations: 1,
        partsWithoutCalculations: 2,
        isValid: false,
        validationError: expect.stringContaining('parte necesita ser recalculada'),
        needsRecalculation: true
      });
    });

    test('should show valid stats for non-edit mode', () => {
      const budgetParts = [
        { paper: { id: 1 }, machine: { id: 1 }, pageCount: 10 }
      ];

      const stats = getBudgetStats(budgetParts, false);

      expect(stats.isValid).toBe(true);
      expect(stats.needsRecalculation).toBe(false);
    });
  });
});
