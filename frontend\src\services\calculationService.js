import { buildApiUrl } from '../config';

/**
 * Servicio para realizar cálculos de costes en el backend
 */
const calculationService = {
  /**
   * Calcula el coste del papel
   * @param {string|Object} paperId - ID del papel o un objeto con paper_id y total_sheets
   * @param {number} [totalSheets] - Total de pliegos (opcional si paperId es un objeto)
   * @returns {Promise<Object>} - Resultado del cálculo
   */
  calculatePaperCost: async (paperId, totalSheets) => {
    try {
      let paper_id, total_sheets;

      // Comprobar si se pasó un objeto o parámetros separados
      if (typeof paperId === 'object' && paperId !== null) {
        paper_id = paperId.paper_id;
        total_sheets = paperId.total_sheets;
      } else {
        paper_id = paperId;
        total_sheets = totalSheets;
      }

      console.log('Enviando datos para cálculo de papel:', {
        paper_id,
        total_sheets,
      });

      const response = await fetch(buildApiUrl('/calculations/paper-cost'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paper_id,
          total_sheets,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error detallado del servidor:', errorData);
        throw new Error(`Error al calcular coste de papel: ${errorData.detail || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error en calculatePaperCost:', error);
      throw error;
    }
  },

  /**
   * DEPRECATED: calculateMachineCost ha sido eliminado
   * Usar en su lugar:
   * - /v2/calculate-offset para máquinas offset
   * - /v2/calculate-digital para máquinas digitales
   */
  calculateMachineCost: async () => {
    throw new Error('calculateMachineCost ha sido eliminado. Usar /v2/calculate-offset o /v2/calculate-digital');
  },

  /**
   * Calcula el coste de un proceso
   * @param {string|Object} processId - ID del proceso o un objeto con process_id, quantity y unit_type
   * @param {number} [quantity] - Cantidad (opcional si processId es un objeto)
   * @param {string} [unitType] - Tipo de unidad ('hour' o 'unit') (opcional si processId es un objeto)
   * @returns {Promise<Object>} - Resultado del cálculo
   */
  calculateProcessCost: async (processId, quantity, unitType) => {
    try {
      let process_id, qty, unit_type;

      // Comprobar si se pasó un objeto o parámetros separados
      if (typeof processId === 'object' && processId !== null) {
        process_id = processId.process_id;
        qty = processId.quantity;
        unit_type = processId.unit_type;
      } else {
        process_id = processId;
        qty = quantity;
        unit_type = unitType;
      }

      console.log('Enviando datos para cálculo de proceso:', {
        process_id,
        quantity: qty,
        unit_type,
      });

      const response = await fetch(buildApiUrl('/calculations/process-cost'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          process_id,
          quantity: qty,
          unit_type,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Error al calcular coste de proceso: ${errorData.detail || response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error en calculateProcessCost:', error);
      throw error;
    }
  },

  /**
   * Calcula el coste de las planchas
   * @param {number|Object} totalPlates - Número total de planchas o un objeto con total_plates
   * @param {string} [plateType] - Tipo de plancha (opcional)
   * @returns {Promise<Object>} - Resultado del cálculo
   */
  calculatePlateCost: async (totalPlates, plateType) => {
    try {
      let total_plates, plate_type;

      // Comprobar si se pasó un objeto o parámetros separados
      if (typeof totalPlates === 'object' && totalPlates !== null) {
        total_plates = totalPlates.total_plates;
        plate_type = totalPlates.plate_type;
      } else {
        total_plates = totalPlates;
        plate_type = plateType;
      }

      console.log('Enviando datos para cálculo de planchas:', {
        total_plates,
        plate_type,
      });

      const response = await fetch(buildApiUrl('/calculations/plate-cost'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          total_plates,
          plate_type,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Error al calcular coste de planchas: ${errorData.detail || response.statusText}`);
      }

      const result = await response.json();
      console.log('Resultado del cálculo de planchas:', result);
      return result;
    } catch (error) {
      console.error('Error en calculatePlateCost:', error);
      throw error;
    }
  },

  /**
   * Calcula el coste total de una parte
   * @param {Object} partData - Datos de la parte
   * @returns {Promise<Object>} - Resultado del cálculo
   */
  calculatePartCost: async (partData) => {
    try {
      console.log('Enviando datos para cálculo de parte:', partData);
      const response = await fetch(buildApiUrl('/calculations/part-cost'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paper_id: partData.paper_id,
          machine_id: partData.machine_id,
          total_sheets: partData.total_sheets,
          total_plates: partData.total_plates, // Añadir el número de planchas
          custom_print_time: partData.custom_print_time,
          color_sides: partData.color_sides, // Número de caras impresas a color
          bw_sides: partData.bw_sides, // Número de caras impresas en blanco y negro
          processes: partData.processes || [],
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Error al calcular coste de parte: ${errorData.detail || response.statusText}`);
      }

      const result = await response.json();
      console.log('Resultado del cálculo de parte:', result);
      return result;
    } catch (error) {
      console.error('Error en calculatePartCost:', error);
      throw error;
    }
  },

  /**
   * Calcula el coste total - ELIMINADO
   * Usar endpoints V2 que incluyen cálculo completo de costes
   * @param {Array} paperCosts - Lista de costes de papel
   * @param {Array} machineCosts - Lista de costes de máquina
   * @param {Array} processCosts - Lista de costes de procesos
   * @param {Array} [plateCosts] - Lista de costes de planchas (opcional)
   * @returns {Promise<Object>} - Resultado del cálculo
   */
  calculateTotalCost: async (paperCosts, machineCosts, processCosts, plateCosts = []) => {
    console.warn('calculateTotalCost está obsoleto. Usar endpoints V2 que incluyen cálculo completo de costes.');
    throw new Error('Endpoint /calculations/total-cost eliminado. Usar endpoints V2 (/v2/calculate-offset, /v2/calculate-digital) que incluyen cálculo completo de costes.');
  },
};

export default calculationService;
