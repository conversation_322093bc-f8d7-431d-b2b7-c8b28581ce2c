/**
 * Tests para budgetFormChangeService
 */
import budgetFormChangeService, {
  updateBudgetField,
  markPartsAsOutdated,
  parseColorConfig,
  calculateProcessesTotalCost,
  prepareProcessesWithQuantities,
  loadDefaultFinishing,
  createPartsFromProduct,
  createDefaultPart,
  handleJobTypeChange,
  handleFormChange
} from '../budgetFormChangeService';

// Mock de ProductService
jest.mock('../ProductService', () => ({
  getProductByType: jest.fn()
}));

import ProductService from '../ProductService';

describe('budgetFormChangeService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('updateBudgetField', () => {
    test('should update budget field correctly', () => {
      const mockSetBudget = jest.fn();
      
      updateBudgetField('description', 'Test Description', mockSetBudget);
      
      expect(mockSetBudget).toHaveBeenCalledWith(expect.any(Function));
      
      // Verificar que la función pasada actualiza correctamente
      const updateFunction = mockSetBudget.mock.calls[0][0];
      const result = updateFunction({ otNumber: 'OT-001' });
      
      expect(result).toEqual({
        otNumber: 'OT-001',
        description: 'Test Description'
      });
    });
  });

  describe('markPartsAsOutdated', () => {
    test('should mark parts with calculations as outdated', () => {
      const budgetParts = [
        { id: 1, sheetCalculation: { total_cost: 100 } },
        { id: 2 }, // Sin cálculos
        { id: 3, sheetCalculation: { total_cost: 200 } }
      ];
      const mockSetBudgetParts = jest.fn();
      const mockShowSnackbar = jest.fn();

      const result = markPartsAsOutdated(budgetParts, mockSetBudgetParts, mockShowSnackbar);

      expect(result).toBe(true);
      expect(mockSetBudgetParts).toHaveBeenCalled();
      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('Se ha modificado el número de ejemplares'),
        'warning'
      );

      // Verificar que se marcaron como desactualizadas
      const updateFunction = mockSetBudgetParts.mock.calls[0][0];
      const updatedParts = updateFunction(budgetParts);
      
      expect(updatedParts[0].sheetCalculation.outdated).toBe(true);
      expect(updatedParts[1]).toEqual({ id: 2 }); // Sin cambios
      expect(updatedParts[2].sheetCalculation.outdated).toBe(true);
    });

    test('should return false when no parts have calculations', () => {
      const budgetParts = [{ id: 1 }, { id: 2 }];
      const mockSetBudgetParts = jest.fn();
      const mockShowSnackbar = jest.fn();

      const result = markPartsAsOutdated(budgetParts, mockSetBudgetParts, mockShowSnackbar);

      expect(result).toBe(false);
      expect(mockSetBudgetParts).not.toHaveBeenCalled();
      expect(mockShowSnackbar).not.toHaveBeenCalled();
    });
  });

  describe('parseColorConfig', () => {
    test('should parse color string correctly', () => {
      const result = parseColorConfig('4/4');
      
      expect(result).toEqual({
        frontColors: 4,
        backColors: 4,
        pantones: 0
      });
    });

    test('should parse color string with different values', () => {
      const result = parseColorConfig('2/0');
      
      expect(result).toEqual({
        frontColors: 2,
        backColors: 0,
        pantones: 0
      });
    });

    test('should return null for invalid format', () => {
      expect(parseColorConfig('4')).toBeNull();
      expect(parseColorConfig('4/4/4')).toBeNull();
      expect(parseColorConfig('')).toBeNull();
      expect(parseColorConfig(null)).toBeNull();
    });
  });

  describe('calculateProcessesTotalCost', () => {
    test('should calculate total cost correctly', () => {
      const processes = [
        { quantity: 100, unit_cost: 0.5 },
        { quantity: 50, unit_cost: 1.0 },
        { quantity: 200, unit_cost: 0.25 }
      ];
      const copies = 100;

      const result = calculateProcessesTotalCost(processes, copies);
      
      expect(result).toBe(150); // (100*0.5) + (50*1.0) + (200*0.25)
    });

    test('should handle missing values', () => {
      const processes = [
        { unit_cost: 0.5 }, // Sin quantity
        { quantity: 50 }, // Sin unit_cost
        {} // Sin ninguno
      ];
      const copies = 100;

      const result = calculateProcessesTotalCost(processes, copies);
      
      expect(result).toBe(50); // Solo el segundo proceso: 50 * 1 (copies como fallback)
    });
  });

  describe('prepareProcessesWithQuantities', () => {
    test('should prepare processes with quantities', () => {
      const processes = [
        { name: 'Corte', unit_cost: 0.1 },
        { name: 'Plegado', unit_cost: 0.2 }
      ];
      const copies = 500;

      const result = prepareProcessesWithQuantities(processes, copies);

      expect(result).toEqual([
        { name: 'Corte', unit_cost: 0.1, quantity: 500, quantityModified: false },
        { name: 'Plegado', unit_cost: 0.2, quantity: 500, quantityModified: false }
      ]);
    });
  });

  describe('createPartsFromProduct', () => {
    test('should create parts from product', () => {
      const product = {
        parts: [
          { name: 'Portada', description: 'Portada del libro', default_colors: '4/0' },
          { name: 'Interior', description: 'Páginas interiores', default_colors: '1/1' }
        ],
        assembly_order: 'Gathering'
      };
      
      const mockCreateEmptyPart = jest.fn((index, name) => ({
        part_id: `part-${index}`,
        name,
        colorConfig: { frontColors: 4, backColors: 4, pantones: 0 }
      }));

      const result = createPartsFromProduct(product, mockCreateEmptyPart, parseColorConfig);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(expect.objectContaining({
        name: 'Portada',
        description: 'Portada del libro',
        assembly_order: 'Gathering',
        colorConfig: { frontColors: 4, backColors: 0, pantones: 0 }
      }));
      expect(result[1]).toEqual(expect.objectContaining({
        name: 'Interior',
        description: 'Páginas interiores',
        assembly_order: 'Gathering',
        colorConfig: { frontColors: 1, backColors: 1, pantones: 0 }
      }));
    });

    test('should return null when no parts in product', () => {
      const product = {};
      const mockCreateEmptyPart = jest.fn();

      const result = createPartsFromProduct(product, mockCreateEmptyPart, parseColorConfig);

      expect(result).toBeNull();
      expect(mockCreateEmptyPart).not.toHaveBeenCalled();
    });
  });

  describe('handleJobTypeChange', () => {
    test('should handle job type change successfully', async () => {
      const mockProduct = {
        work_style: 'Flat',
        parts: [
          { name: 'Portada', default_colors: '4/0' }
        ],
        default_finishing: ['Corte'],
        finishing_processes: [
          { name: 'Corte', unit_cost: 0.1 }
        ]
      };

      ProductService.getProductByType.mockResolvedValue(mockProduct);

      const mockSetProductConfig = jest.fn();
      const mockSetBudgetParts = jest.fn();
      const mockSetSelectedProcesses = jest.fn();
      const mockSetCalculatedProcessCost = jest.fn();
      const mockCreateEmptyPart = jest.fn(() => ({ part_id: 'test' }));
      const mockShowSnackbar = jest.fn();

      const params = {
        jobType: 'Libro',
        budget: { copies: '500' },
        processes: [],
        setProductConfig: mockSetProductConfig,
        setBudgetParts: mockSetBudgetParts,
        setSelectedProcesses: mockSetSelectedProcesses,
        setCalculatedProcessCost: mockSetCalculatedProcessCost,
        createEmptyPart: mockCreateEmptyPart,
        showSnackbar: mockShowSnackbar
      };

      await handleJobTypeChange(params);

      expect(ProductService.getProductByType).toHaveBeenCalledWith('Libro');
      expect(mockSetProductConfig).toHaveBeenCalled();
      expect(mockSetBudgetParts).toHaveBeenCalled();
      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('Se cargaron 1 partes del producto'),
        'success'
      );
    });

    test('should handle error in job type change', async () => {
      ProductService.getProductByType.mockRejectedValue(new Error('Product not found'));

      const mockShowSnackbar = jest.fn();
      const params = {
        jobType: 'InvalidType',
        budget: {},
        processes: [],
        setProductConfig: jest.fn(),
        setBudgetParts: jest.fn(),
        setSelectedProcesses: jest.fn(),
        setCalculatedProcessCost: jest.fn(),
        createEmptyPart: jest.fn(),
        showSnackbar: mockShowSnackbar
      };

      await handleJobTypeChange(params);

      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('Error al cargar la información del producto'),
        'error'
      );
    });
  });

  describe('handleFormChange', () => {
    test('should handle form change for regular field', async () => {
      const mockEvent = {
        target: { name: 'description', value: 'Test Description' }
      };

      const mockSetBudget = jest.fn();
      const params = {
        event: mockEvent,
        budget: {},
        budgetParts: [],
        processes: [],
        isEditMode: false,
        setBudget: mockSetBudget,
        setBudgetParts: jest.fn(),
        setProductConfig: jest.fn(),
        setSelectedProcesses: jest.fn(),
        setCalculatedProcessCost: jest.fn(),
        createEmptyPart: jest.fn(),
        showSnackbar: jest.fn()
      };

      await handleFormChange(params);

      expect(mockSetBudget).toHaveBeenCalled();
    });

    test('should handle copies change in edit mode', async () => {
      const mockEvent = {
        target: { name: 'copies', value: '1000' }
      };

      const budgetParts = [
        { id: 1, sheetCalculation: { total_cost: 100 } }
      ];

      const mockSetBudget = jest.fn();
      const mockSetBudgetParts = jest.fn();
      const mockShowSnackbar = jest.fn();

      const params = {
        event: mockEvent,
        budget: {},
        budgetParts,
        processes: [],
        isEditMode: true,
        setBudget: mockSetBudget,
        setBudgetParts: mockSetBudgetParts,
        setProductConfig: jest.fn(),
        setSelectedProcesses: jest.fn(),
        setCalculatedProcessCost: jest.fn(),
        createEmptyPart: jest.fn(),
        showSnackbar: mockShowSnackbar
      };

      await handleFormChange(params);

      expect(mockSetBudget).toHaveBeenCalled();
      expect(mockSetBudgetParts).toHaveBeenCalled();
      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('Se ha modificado el número de ejemplares'),
        'warning'
      );
    });

    test('should handle job type change', async () => {
      const mockProduct = {
        work_style: 'Flat',
        parts: []
      };

      ProductService.getProductByType.mockResolvedValue(mockProduct);

      const mockEvent = {
        target: { name: 'jobType', value: 'Libro' }
      };

      const mockSetBudget = jest.fn();
      const mockSetProductConfig = jest.fn();

      const params = {
        event: mockEvent,
        budget: {},
        budgetParts: [],
        processes: [],
        isEditMode: false,
        setBudget: mockSetBudget,
        setBudgetParts: jest.fn(),
        setProductConfig: mockSetProductConfig,
        setSelectedProcesses: jest.fn(),
        setCalculatedProcessCost: jest.fn(),
        createEmptyPart: jest.fn(),
        showSnackbar: jest.fn()
      };

      await handleFormChange(params);

      expect(mockSetBudget).toHaveBeenCalled();
      expect(ProductService.getProductByType).toHaveBeenCalledWith('Libro');
    });
  });
});
