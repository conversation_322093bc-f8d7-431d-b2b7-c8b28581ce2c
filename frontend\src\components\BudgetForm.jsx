import { useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { buildApiUrl } from '../config';
import budgetSubmitService from '../services/budgetSubmitService';
import budgetFetchService from '../services/budgetFetchService';
import MachineInfoModal from './BudgetForm/MachineInfoModal';
import ClientInfoModal from './BudgetForm/ClientInfoModal';
import ProductConfigModal from './BudgetForm/ProductConfigModal';
import SheetCalculationModal from './BudgetForm/SheetCalculationModal';

// Importar servicios
import { useSilentSheetCalculation, calculateTotalPaperWeight, recalculateShippingCost } from '../services/silentSheetCalculationService';
import { calculateSheetsPart as calculateSheetsV2 } from '../services/unifiedSheetCalculationService';
import SheetCalculationModalSelector from './BudgetForm/SheetCalculationModalSelector';
import {
  Paper,
  Typography,
  Button,
  Grid,
  Box,
  Snackbar,
  Alert
} from '@mui/material';
import ProductService from '../services/ProductService';
import printTimeUpdateService from '../services/printTimeUpdateService';
import budgetFormChangeService from '../services/budgetFormChangeService';
import shippingCalculationService from '../services/shippingCalculationService';
import budgetCatalogService from '../services/budgetCatalogService';
import budgetSubmissionService from '../services/budgetSubmissionService';
import budgetUtilsService from '../services/budgetUtilsService';

// Importar subcomponentes
import BasicInfoSection from './BudgetForm/BasicInfoSection';
import BudgetParts from './BudgetForm/BudgetParts';
import FinishingSection from './BudgetForm/FinishingSection';
import TotalCostsSection from './BudgetForm/TotalCostsSection';
import PdfSelectorDialog from './BudgetForm/PdfSelectorDialog';


// URL de la API importada desde config.js

// Nota: Ya no necesitamos definir los tipos de trabajo aquí
// porque los obtendremos del catálogo de productos

const BudgetForm = ({ budgetId }) => {
  // Estado para el formulario
  const [budget, setBudget] = useState({
    otNumber: '',
    description: '',
    client: null,
    jobType: '',
    copies: '',
    parts: [], // Ahora las partes se manejan como un array
    shipping_cost: 0,
    total_paper_weight_kg: 0
  });

  // Estado para el diálogo de PDF
  const [pdfDialogOpen, setPdfDialogOpen] = useState(false);

  // Estado para el PDF seleccionado
  const [selectedPdf, setSelectedPdf] = useState(null);

  // Estado para las partes del presupuesto
  const [budgetParts, setBudgetParts] = useState([]);





  // El manejador para la información del PDF se ha movido a los componentes de diálogo

  // Funciones auxiliares del servicio de utilidades
  const createEmptyPart = budgetUtilsService.createEmptyPart;
  const getPageSizeFromDimensions = budgetUtilsService.getPageSizeFromDimensions;

  // Estado para la información del PDF
  const [pdfInfo, setPdfInfo] = useState(null);

  // Manejador para cerrar el diálogo de PDF
  const handleClosePdfDialog = () => {
    budgetUtilsService.handleClosePdfDialog(setPdfDialogOpen);
  };

  // Estado para indicar si estamos en modo edición
  const [isEditMode, setIsEditMode] = useState(false);

  // Estados para datos de catálogos
  const [clients, setClients] = useState([]);
  const [papers, setPapers] = useState([]);
  const [machines, setMachines] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [selectedProcesses, setSelectedProcesses] = useState([]);

  // Estado para el snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Estado para el modal de información del cliente
  const [clientInfoModal, setClientInfoModal] = useState(false);

  // Estado para los modales de cálculo de pliegos
  const [sheetCalculationModal, setSheetCalculationModal] = useState(false);
  const [sheetCalculationV2Modal, setSheetCalculationV2Modal] = useState(false);
  const [calculationResults, setCalculationResults] = useState(null);
  const [currentCalculatedPart, setCurrentCalculatedPart] = useState(null);
  const [calculatingSheets, setCalculatingSheets] = useState(false);

  // Estados para modales eliminados (JDF y JSON)

  // Estado para el modal de información de máquina
  const [machineInfoDialog, setMachineInfoDialog] = useState(false);

  // Estado para la máquina seleccionada y el índice de la parte
  const [selectedMachine, setSelectedMachine] = useState(null);
  const [selectedPartIndex, setSelectedPartIndex] = useState(null);

  // Estado para el modal de información de colores
  const [colorInfoDialog, setColorInfoDialog] = useState(false);

  // Estado para la configuración del producto
  const [productConfig, setProductConfig] = useState(budgetUtilsService.createInitialProductConfig());

  // Para mantener compatibilidad con el código existente
  const colorConfig = budgetUtilsService.createColorConfig(productConfig);

  // Función para actualizar la configuración de colores manteniendo compatibilidad
  const setColorConfig = (newColorConfig) => {
    budgetUtilsService.updateColorConfig(newColorConfig, setProductConfig);
  };

  // Estado para el tiempo personalizado de la máquina
  const [customMachinePrintTime, setCustomMachinePrintTime] = useState(null);

  // Estado para almacenar el coste total de los procesos (acabados)
  const [calculatedProcessCost, setCalculatedProcessCost] = useState(0);

  // Referencias para controlar si ya se han cargado los datos
  const fetchedClientsRef = useRef(false);
  const fetchedPapersRef = useRef(false);
  const fetchedMachinesRef = useRef(false);
  const fetchedProcessesRef = useRef(false);

  // Función para mostrar mensajes en snackbar
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // Hook personalizado para cálculo silencioso
  const silentCalculation = useSilentSheetCalculation({
    budgetParts,
    budget,
    budgetId,
    buildApiUrl,
    showSnackbar,
    setBudgetParts,
    setBudget,
    setCalculatingSheets,
    setCurrentCalculatedPart,
    setCalculationResults
  });

  // Cerrar snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  // Función para resetear el formulario
  const resetForm = useCallback(() => {
    setBudget(budgetUtilsService.createEmptyBudget());

    // Inicializar con una parte por defecto
    setBudgetParts([budgetUtilsService.createEmptyPart(0, 'General')]);

    // Resetear el archivo PDF seleccionado
    setSelectedPdf(null);

    // Resetear los procesos seleccionados
    setSelectedProcesses([]);

    // Resetear los costos calculados
    setCalculatedProcessCost(0);
  }, []);

  // Funciones para obtener catálogos usando el servicio especializado
  const fetchClients = useCallback(async () => {
    try {
      const data = await budgetCatalogService.fetchClients(buildApiUrl);
      setClients(data);
    } catch (err) {
      console.error('Error al obtener clientes:', err);
      showSnackbar(err.message, 'error');
    }
  }, []);

  const fetchPapers = useCallback(async () => {
    try {
      const data = await budgetCatalogService.fetchPapers(buildApiUrl);
      setPapers(data);
    } catch (err) {
      console.error('Error al obtener papeles:', err);
      showSnackbar(err.message, 'error');
    }
  }, []);

  const fetchMachines = useCallback(async () => {
    try {
      const data = await budgetCatalogService.fetchMachines(buildApiUrl);
      setMachines(data);
    } catch (err) {
      console.error('Error al obtener máquinas:', err);
      showSnackbar(err.message, 'error');
    }
  }, []);

  const fetchProcesses = useCallback(async () => {
    try {
      const data = await budgetCatalogService.fetchProcesses(buildApiUrl);
      setProcesses(data);
    } catch (err) {
      console.error('Error al obtener procesos:', err);
      showSnackbar(err.message, 'error');
    }
  }, []);

  // Función para obtener el total de pliegos físicos (calculado por el backend)
  const calculateTotalSheets = () => {
    return budgetUtilsService.calculateTotalSheets(selectedPartIndex, budgetParts, null);
  };

  // Función para manejar la actualización del tiempo de impresión
  const handleUpdatePrintTime = async (time) => {
    try {
      await printTimeUpdateService.handlePrintTimeUpdate({
        time,
        selectedPartIndex,
        budgetParts,
        budget,
        buildApiUrl,
        setBudgetParts,
        setCurrentCalculatedPart,
        setCustomMachinePrintTime,
        setMachineInfoDialog,
        showSnackbar
      });
    } catch (error) {
      console.error('Error al actualizar tiempo de impresión:', error);
      // El error ya fue mostrado por el servicio
    }
  };


  // Función para obtener un presupuesto por ID
  const fetchBudgetById = useCallback(async (id) => {
    // Usar el servicio para obtener el presupuesto
    return await budgetFetchService.fetchBudgetById(id, {
      // Funciones de actualización de estado
      setBudget,
      setBudgetParts,
      setSelectedProcesses,
      setCalculatedProcessCost,
      setPdfInfo,
      setSelectedPdf,
      setColorConfig,
      setProductConfig,
      setIsEditMode,

      // Datos de catálogos
      clients,
      papers,
      machines,
      processes,

      // Funciones para obtener datos
      fetchClients,
      fetchPapers,
      fetchMachines,

      // Referencias
      fetchedClientsRef,
      fetchedPapersRef,
      fetchedMachinesRef,

      // Funciones auxiliares
      createEmptyPart,
      getPageSizeFromDimensions,
      showSnackbar
    });
  }, [clients, papers, machines, processes, fetchClients, fetchPapers, fetchMachines]);

  // Cargar datos iniciales
  useEffect(() => {
    // Cargar todos los datos necesarios si no se han cargado ya
    const loadInitialData = async () => {
      const loadPromises = [];

      if (!fetchedClientsRef.current) {
        loadPromises.push(fetchClients());
        fetchedClientsRef.current = true;
      }

      if (!fetchedPapersRef.current) {
        loadPromises.push(fetchPapers());
        fetchedPapersRef.current = true;
      }

      if (!fetchedMachinesRef.current) {
        loadPromises.push(fetchMachines());
        fetchedMachinesRef.current = true;
      }

      if (!fetchedProcessesRef.current) {
        loadPromises.push(fetchProcesses());
        fetchedProcessesRef.current = true;
      }

      // Esperar a que se carguen todos los datos en paralelo
      if (loadPromises.length > 0) {
        await Promise.all(loadPromises);
      }
    };

    loadInitialData();

    // Limpiar las referencias al desmontar el componente
    return () => {
      fetchedClientsRef.current = false;
      fetchedPapersRef.current = false;
      fetchedMachinesRef.current = false;
      fetchedProcessesRef.current = false;
    };
  }, [fetchClients, fetchPapers, fetchMachines, fetchProcesses]);



  // Efecto para cargar el presupuesto si se proporciona un ID
  useEffect(() => {
    if (budgetId) {
      fetchBudgetById(budgetId);
    } else {
      // Si no hay ID, resetear el formulario
      resetForm();
      setIsEditMode(false);
    }
  }, [budgetId, fetchBudgetById, resetForm]);

  // Generar número de OT automáticamente solo si no estamos en modo edición
  useEffect(() => {
    if (!budget.otNumber && !isEditMode) {
      const date = new Date();
      const year = date.getFullYear().toString().slice(2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const randomNum = Math.floor(1000 + Math.random() * 9000);

      setBudget(prev => ({
        ...prev,
        otNumber: `OT-${year}${month}${randomNum}`
      }));
    }
  }, [budget.otNumber, isEditMode]);

  // Manejar cambios en el formulario
  const handleChange = async (e) => {
    try {
      await budgetFormChangeService.handleFormChange({
        event: e,
        budget,
        budgetParts,
        processes,
        isEditMode,
        setBudget,
        setBudgetParts,
        setProductConfig,
        setSelectedProcesses,
        setCalculatedProcessCost,
        createEmptyPart,
        showSnackbar
      });
    } catch (error) {
      console.error('Error al manejar cambio en el formulario:', error);
      showSnackbar(`Error al procesar el cambio: ${error.message}`, 'error');
    }
  };

  // Manejar cambios en selecciones de autocomplete
  const handleAutocompleteChange = (name, value) => {
    budgetUtilsService.handleAutocompleteChange(name, value, setBudget);
  };

  // La verificación de compatibilidad entre papel y máquina ahora se realiza en el backend

  // Efecto para actualizar las cantidades de los procesos cuando cambia el número de ejemplares
  useEffect(() => {
    if (selectedProcesses.length > 0 && budget.copies) {
      const updatedProcesses = budgetUtilsService.updateProcessQuantities(selectedProcesses, budget.copies);
      setSelectedProcesses(updatedProcesses);
    }
  }, [budget.copies, selectedProcesses.length]);

  // Los cálculos de costes ahora se realizan en el backend

  // La funcionalidad de manejo de PDF se ha movido al componente PdfUploader

  // La función handleCalculateSheets ha sido eliminada porque ahora se usa directamente handleCalculateSheetsPart

  // Cerrar modal de cálculo de pliegos
  const handleCloseSheetCalculationModal = () => {
    // Si se ha modificado el tiempo y el costo de la máquina, actualizar la parte correspondiente
    if (currentCalculatedPart && customMachinePrintTime !== null) {
      // Buscar la parte en el array de partes
      const partIndex = budgetParts.findIndex(part =>
        part.part_id === currentCalculatedPart.part_id);

      if (partIndex !== -1) {
        // Crear una copia del array de partes
        const updatedParts = [...budgetParts];

        // Determinar si es una máquina digital para usar clickCost o plateCost
        const part = updatedParts[partIndex];
        const isDigital = part.machine && part.machine.type === 'Digital';
        const plateCost = part.plateCost || 0;
        const clickCost = part.clickCost || 0;

        // Calcular el costo total actualizado
        const totalCost = (part.paperCost || 0) + currentCalculatedPart.machineCost +
          (isDigital ? clickCost : plateCost);

        // Actualizar la parte con los nuevos valores
        updatedParts[partIndex] = {
          ...updatedParts[partIndex],
          machineCost: currentCalculatedPart.machineCost,
          customPrintTime: currentCalculatedPart.customPrintTime,
          totalCost: totalCost, // Actualizar el costo total
          clicksData: currentCalculatedPart.clicksData // Asegurarnos de que los datos de clicks se mantengan
        };

        // Actualizar el estado
        setBudgetParts(updatedParts);


      }
    }

    // Cerrar el modal
    setSheetCalculationModal(false);
  };

  // Manejar reseteo del tiempo personalizado de la máquina
  const handleCustomMachineTimeChange = async (value) => {
    // Solo manejar el caso de resetear el tiempo personalizado (value === null)
    if (value === null) {
      try {
        await printTimeUpdateService.handleCustomMachineTimeReset({
          currentCalculatedPart,
          selectedPartIndex,
          setCustomMachinePrintTime,
          handleUpdatePrintTime,
          showSnackbar
        });
      } catch (error) {
        console.error('Error al resetear tiempo personalizado:', error);
        // El error ya fue mostrado por el servicio
      }
    }
  };

  // Manejar el clic en el botón de información de la máquina para una parte específica
  const handleMachineInfoClickPart = (machine, partIndex) => {
    budgetUtilsService.handleMachineInfoClick(
      machine,
      partIndex,
      setSelectedMachine,
      setSelectedPartIndex,
      setMachineInfoDialog
    );
  };

  // ELIMINADO: handleCalculateSheetsPart (V1) - Migrado completamente a V2
  // Usar handleCalculateSheetsV2Part en su lugar

  // Función para recalcular el costo de envío usando el endpoint de la API
  const recalculateShippingCost = async () => {
    try {
      return await shippingCalculationService.recalculateShippingCost({
        budgetParts,
        budget,
        calculateTotalPaperWeight,
        buildApiUrl,
        setBudget,
        showSnackbar
      });
    } catch (error) {
      console.error('Error en recalculateShippingCost:', error);
      return { weight: 0, cost: 0, error: true };
    }
  };



  // Calcular pliegos V2 para una parte específica sin mostrar el modal
  const handleCalculateSheetsV2Silent = async (partIndex) => {
    // Usar el servicio externo de cálculo silencioso
    return await silentCalculation(partIndex, {
      showModal: false,
      showSuccessMessage: true,
      recalculateShipping: true
    });
  };

  // Calcular pliegos V2 para una parte específica usando el nuevo servicio unificado
  const handleCalculateSheetsV2Part = async (partIndex) => {
    try {
      // Obtener la parte seleccionada
      const part = budgetParts[partIndex];

      // Verificar que la parte tenga los datos necesarios
      if (!part || !part.machine || !part.paper) {
        showSnackbar('Selecciona una máquina y un papel para calcular pliegos', 'warning');
        return;
      }

      // Verificar que se haya ingresado una cantidad de copias
      if (!budget.copies || parseInt(budget.copies) <= 0) {
        showSnackbar('Ingresa una cantidad válida de copias', 'warning');
        return;
      }

      // Indicar que estamos calculando
      setCalculatingSheets(true);

      // Guardar la parte actual para que esté disponible cuando se abra el modal
      setCurrentCalculatedPart(part);

      // Llamar al servicio unificado de cálculo de pliegos
      const results = await calculateSheetsV2({
        part: budgetParts[partIndex],
        partIndex,
        copies: budget.copies,
        buildApiUrl,
        showSnackbar,
        setCalculatingSheets,
        setBudgetParts,
        setBudget,
        setCurrentCalculatedPart,
        setSheetCalculationV2Modal, // Usamos el modal V2 en lugar del original
        budgetParts,
        budget,
        budgetId,
        setCalculationResults // Guardamos los resultados en el estado para el modal V2
      });

      // Si tenemos resultados, actualizar el estado
      if (results) {
        // Guardar los resultados
        setCalculationResults(results);

        // Actualizar la parte con los resultados del cálculo
        const updatedParts = [...budgetParts];
        updatedParts[partIndex] = {
          ...part,
          sheetCalculation: results,
          paperCost: results.costos?.costo_papel || 0,
          machineCost: results.costos?.costo_maquina || 0,
          plateCost: results.costos?.costo_planchas || 0,
          clickCost: results.costos?.costo_click || 0,
          inkCost: results.costos?.costo_tinta || 0,
          totalCost: results.costos?.costo_total || 0
        };

        // Actualizar el estado de las partes primero
        setBudgetParts(updatedParts);
        setCurrentCalculatedPart(updatedParts[partIndex]);

        // Recalcular el costo de envío usando la función de silentSheetCalculationService
        try {
          // Calcular el peso total del papel y obtener los pesos por parte
          const weightData = calculateTotalPaperWeight(updatedParts, budget.copies);
          
          // Llamar a recalculateShippingCost con los parámetros correctos
          await recalculateShippingCost(weightData, budget, buildApiUrl, setBudget);
          
          console.log('Costo de envío recalculado correctamente desde handleCalculateSheetsV2Part');
        } catch (error) {
          console.error('Error al recalcular el costo de envío:', error);
          showSnackbar(`Error al calcular el costo de envío: ${error.message}`, 'warning');
        }

        // Mostrar el modal de resultados
        setSheetCalculationV2Modal(true);
      }
    } catch (error) {
      showSnackbar(`Error al calcular pliegos: ${error.message}`, 'error');
      setCalculatingSheets(false);
    }
  };





  // Abrir modal con información del cliente
  const handleClientInfoClick = () => {
    budgetUtilsService.handleClientInfoClick(budget.client, setClientInfoModal, showSnackbar);
  };

  // Cerrar modal de información del cliente
  const handleCloseClientInfo = () => {
    budgetUtilsService.handleCloseClientInfo(setClientInfoModal);
  };

  // Función para enviar el formulario
  const handleSubmit = async (e) => {
    try {
      await budgetSubmissionService.handleBudgetSubmission({
        e,
        budget,
        budgetParts,
        selectedProcesses,
        calculatedProcessCost,
        selectedPdf,
        pdfInfo,
        colorConfig,
        productConfig,
        budgetId,
        isEditMode,
        showSnackbar,
        setCalculatedProcessCost,
        buildApiUrl,
        budgetSubmitService
      });
    } catch (error) {
      console.error('Error al enviar el presupuesto:', error);
      showSnackbar(`Error al enviar el presupuesto: ${error.message}`, 'error');
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Typography variant="h4" align="center" gutterBottom>
        {isEditMode ? `Editar Presupuesto: ${budget.otNumber}` : 'Nuevo Presupuesto'}
      </Typography>

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Sección de información básica */}
          <BasicInfoSection
            budget={budget}
            handleChange={handleChange}
            handleAutocompleteChange={handleAutocompleteChange}
            handleClientInfoClick={handleClientInfoClick}
            clients={clients}
            jobTypes={[]}
          />

          {/* Sección de partes del presupuesto - Solo visible cuando hay un tipo de trabajo seleccionado */}
          {budget.jobType && (
            <BudgetParts
              budgetParts={budgetParts}
              setBudgetParts={setBudgetParts}
              papers={papers}
              machines={machines}
              processes={processes}
              handleCalculateSheetsV2Part={handleCalculateSheetsV2Part}
              handleCalculateSheetsV2Silent={handleCalculateSheetsV2Silent}
              handleMachineInfoClickPart={handleMachineInfoClickPart}
              showSnackbar={showSnackbar}
              calculatingSheets={calculatingSheets}
              createEmptyPart={createEmptyPart}
              isEditMode={isEditMode}
              budgetCopies={budget.copies}
            />
          )}

          {/* Acabados - Solo visible cuando hay un tipo de trabajo seleccionado */}
          {budget.jobType && (
            <FinishingSection
              selectedProcesses={selectedProcesses}
              setSelectedProcesses={setSelectedProcesses}
              calculatedProcessCost={calculatedProcessCost}
              setCalculatedProcessCost={setCalculatedProcessCost}
              budget={budget}
            />
          )}

          {/* Información de costos totales - Visible siempre */}
          <TotalCostsSection
            budgetParts={budgetParts}
            calculatedProcessCost={calculatedProcessCost}
            budget={budget}
          />

          {/* El selector de archivo PDF se mostrará en un diálogo separado */}

          {/* Botones de acción */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', gap: 2, mb: 4 }}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={() => setPdfDialogOpen(true)}
                  sx={{ minWidth: '120px' }}
                >
                  PDF
                </Button>

                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  sx={{ minWidth: '220px' }}
                >
                  {isEditMode ? 'ACTUALIZAR PRESUPUESTO' : 'GUARDAR PRESUPUESTO'}
                </Button>
              </Box>

              <Button
                variant="outlined"
                color="error"
                onClick={budgetUtilsService.handleCloseNavigation}
                sx={{ minWidth: '120px' }}
              >
                CERRAR
              </Button>
            </Box>
          </Grid>

        </Grid>
      </Box>

      {/* Modal para mostrar resultados del cálculo de pliegos */}
      <SheetCalculationModal
        open={sheetCalculationModal}
        onClose={handleCloseSheetCalculationModal}
        sheetCalculation={currentCalculatedPart?.sheetCalculation || null}
        budget={currentCalculatedPart}
        customMachinePrintTime={customMachinePrintTime}
        handleCustomMachineTimeChange={handleCustomMachineTimeChange}
        selectedProcesses={selectedProcesses}
        calculatedProcessCost={calculatedProcessCost}
      />

      {/* Modal para seleccionar y subir el archivo PDF */}
      <PdfSelectorDialog
        open={pdfDialogOpen}
        onClose={handleClosePdfDialog}
        selectedPdf={selectedPdf}
        setSelectedPdf={setSelectedPdf}
        onPdfInfoChange={(info) => {
          budgetUtilsService.handlePdfInfoChange(info, setPdfInfo, setBudget);
        }}
        showSnackbar={showSnackbar}
        onPageCountChange={(count) => {
          budgetUtilsService.handlePageCountChange(count, budgetParts, setBudgetParts);
        }}
        onPageSizeChange={(pageSize, customPageSize) => {
          budgetUtilsService.handlePageSizeChange(pageSize, customPageSize, budgetParts, setBudgetParts);
        }}
        pdfFilename={budget.pdf_filename}
      />

      {/* Modal para mostrar información del cliente */}
      {/* Modal para configuración del producto - Componente movido a BudgetForm/ProductConfigModal.jsx */}
      {colorInfoDialog && (
        <ProductConfigModal
          open={colorInfoDialog}
          onClose={() => setColorInfoDialog(false)}
          colorConfig={colorConfig}
          setColorConfig={setColorConfig}
          productConfig={productConfig}
          setProductConfig={setProductConfig}
          budget={budget}
          showSnackbar={showSnackbar}
        />
      )}

      {/* Modal para mostrar información del cliente - Componente movido a BudgetForm/ClientInfoModal.jsx */}
      {clientInfoModal && (
        <ClientInfoModal
          open={clientInfoModal}
          onClose={handleCloseClientInfo}
          client={budget.client}
        />
      )}

      {/* Diálogos para JDF y JSON eliminados */}

      {/* Modal de información de máquina - Componente movido a BudgetForm/MachineInfoModal.jsx */}
      {machineInfoDialog && (
        <MachineInfoModal
          open={machineInfoDialog}
          onClose={() => setMachineInfoDialog(false)}
          machine={selectedMachine || budget.machine}
          totalSheets={calculateTotalSheets()}
          budget={selectedPartIndex !== null ? budgetParts[selectedPartIndex] : budget}
          onUpdatePrintTime={handleUpdatePrintTime}
          sheetCalculation={selectedPartIndex !== null ? budgetParts[selectedPartIndex]?.sheetCalculation : null}
          selectedProcesses={selectedPartIndex !== null ? budgetParts[selectedPartIndex]?.processCosts : selectedProcesses}
        />
      )}

      {/* Diálogo para el selector de PDF */}
      {/* Este diálogo ya está definido anteriormente en el código */}

      {/* Modal de cálculo de pliegos V2 (selector entre offset y digital) */}
      {sheetCalculationV2Modal && (
        <SheetCalculationModalSelector
          open={sheetCalculationV2Modal}
          onClose={() => setSheetCalculationV2Modal(false)}
          calculationResults={calculationResults}
          copies={budget.copies}
          part={currentCalculatedPart}
          budget={budget}
          sheetCalculation={currentCalculatedPart?.sheetCalculation}
          customMachinePrintTime={customMachinePrintTime}
          handleCustomMachineTimeChange={handleCustomMachineTimeChange}
          selectedProcesses={selectedProcesses}
          calculatedProcessCost={calculatedProcessCost}
        />
      )}

      {/* Snackbar para mostrar mensajes */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

// Validación de props
BudgetForm.propTypes = {
  budgetId: PropTypes.string
};

// Props por defecto
BudgetForm.defaultProps = {
  budgetId: null
};

export default BudgetForm;
