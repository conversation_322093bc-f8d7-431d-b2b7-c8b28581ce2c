import { buildApiUrl } from '../config';
import ApiInterceptor from './ApiInterceptor';
import LogService from './LogService';

class FoldingSchemesService {
  /**
   * Obtiene todos los esquemas de plegado disponibles
   * @returns {Promise<Array>} - Array de esquemas de plegado
   */
  static async getAllSchemes() {
    try {
      const url = buildApiUrl('/folding-schemes');
      const response = await ApiInterceptor.fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al obtener los esquemas de plegado');
      }

      const data = await response.json();

      LogService.logUserAction('get_folding_schemes', {
        success: true,
        schemes_count: data.length
      });

      return data;
    } catch (error) {
      console.error('Error al obtener esquemas de plegado:', error);

      LogService.logError('Error al obtener esquemas de plegado', {
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Obtiene un esquema de plegado específico por su nombre
   * @param {string} schemeName - Nombre del esquema
   * @returns {Promise<Object>} - Esquema de plegado
   */
  static async getScheme(schemeName) {
    try {
      const url = buildApiUrl(`/folding-schemes/${schemeName}`);
      const response = await ApiInterceptor.fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `Error al obtener el esquema '${schemeName}'`);
      }

      const data = await response.json();

      LogService.logUserAction('get_folding_scheme', {
        scheme_name: schemeName,
        success: true
      });

      return data;
    } catch (error) {
      console.error(`Error al obtener esquema '${schemeName}':`, error);

      LogService.logError(`Error al obtener esquema '${schemeName}'`, {
        scheme_name: schemeName,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Formatea los pasos de plegado para mostrar en la UI
   * @param {Array} foldingSteps - Array de pasos de plegado
   * @returns {string} - Texto formateado de los pasos
   */
  static formatFoldingSteps(foldingSteps) {
    if (!foldingSteps || foldingSteps.length === 0) {
      return 'Sin pasos de plegado definidos';
    }

    return foldingSteps.map((step, index) => 
      `${index + 1}. ${step.description}`
    ).join('\n');
  }

  /**
   * Obtiene el color de fondo según el número de páginas por pliego
   * @param {number} pagesPerSheet - Número de páginas por pliego
   * @returns {string} - Color hexadecimal
   */
  static getSchemeColor(pagesPerSheet) {
    const colors = {
      4: '#e3f2fd',   // Azul claro
      6: '#f3e5f5',   // Púrpura claro
      8: '#e8f5e9',   // Verde claro
      12: '#fff3e0',  // Naranja claro
      16: '#fce4ec',  // Rosa claro
      32: '#f1f8e9'   // Verde lima claro
    };

    return colors[pagesPerSheet] || '#f5f5f5'; // Gris claro por defecto
  }

  /**
   * Calcula las dimensiones del pliego para visualización
   * @param {Object} scheme - Esquema de plegado
   * @param {number} pageWidth - Ancho de página en mm
   * @param {number} pageHeight - Alto de página en mm
   * @returns {Object} - Dimensiones calculadas
   */
  static calculateSheetDimensions(scheme, pageWidth = 210, pageHeight = 297) {
    const margin = scheme.min_margin || 10;
    const spacing = scheme.space_between_pages || 5;

    const sheetWidth = (scheme.cols * pageWidth) + ((scheme.cols - 1) * spacing) + (2 * margin);
    const sheetHeight = (scheme.rows * pageHeight) + ((scheme.rows - 1) * spacing) + (2 * margin);

    return {
      sheetWidth,
      sheetHeight,
      pageWidth,
      pageHeight,
      margin,
      spacing
    };
  }
}

export default FoldingSchemesService;
