/**
 * Servicio para manejar la lógica de envío de presupuestos
 */
import { buildApiUrl } from '../config';

/**
 * Valida los campos básicos del presupuesto
 * @param {Object} budget - Presupuesto a validar
 * @param {Function} showSnackbar - Función para mostrar mensajes
 * @returns {boolean} - true si el presupuesto es válido, false en caso contrario
 */
export const validateBasicFields = (budget, showSnackbar) => {
  if (!budget.client || !budget.description || !budget.jobType || !budget.copies) {
    showSnackbar('Por favor, completa todos los campos requeridos', 'error');
    return false;
  }
  return true;
};

/**
 * Valida las partes del presupuesto
 * @param {Array} budgetParts - Partes del presupuesto a validar
 * @param {Function} showSnackbar - Función para mostrar mensajes
 * @returns {boolean} - true si las partes son válidas, false en caso contrario
 */
export const validateBudgetParts = (budgetParts, showSnackbar) => {
  // Validar que haya al menos una parte
  if (budgetParts.length === 0) {
    showSnackbar('Debe haber al menos una parte en el presupuesto', 'error');
    return false;
  }

  // Validar que todas las partes tengan los campos requeridos
  for (const part of budgetParts) {
    if (!part.name || !part.pageCount || !part.paper || !part.machine) {
      showSnackbar(`La parte "${part.name || 'sin nombre'}" tiene campos incompletos`, 'error');
      return false;
    }

    // Validar el tamaño personalizado si es necesario
    if (part.pageSize === 'Personalizado' && (!part.customPageSize || !part.customPageSize.includes('x'))) {
      showSnackbar(`La parte "${part.name}" tiene un formato de tamaño personalizado incorrecto`, 'error');
      return false;
    }
  }

  return true;
};

/**
 * Verifica y actualiza el costo de acabados si es necesario
 * @param {Array} selectedProcesses - Procesos seleccionados
 * @param {number} calculatedProcessCost - Costo calculado de procesos
 * @param {Function} setCalculatedProcessCost - Función para actualizar el costo calculado
 * @returns {number} - Costo actualizado de procesos
 */
export const verifyProcessCost = (selectedProcesses, calculatedProcessCost, setCalculatedProcessCost) => {
  console.log('Verificando costo de acabados:', calculatedProcessCost);
  if (selectedProcesses.length > 0 && calculatedProcessCost === 0) {
    // Calcular manualmente como respaldo
    const manualProcessCost = selectedProcesses.reduce((total, process) => {
      const quantity = process.quantity || 1;
      const unitCost = process.unit_cost || 0;
      return total + (unitCost * quantity);
    }, 0);

    console.log('Costo de acabados calculado manualmente:', manualProcessCost);
    if (manualProcessCost > 0) {
      setCalculatedProcessCost(manualProcessCost);
      return manualProcessCost;
    }
  }
  return calculatedProcessCost;
};

/**
 * Prepara las partes del presupuesto para enviar a la API
 * @param {Array} budgetParts - Partes del presupuesto
 * @returns {Array} - Partes preparadas para enviar a la API
 */
export const prepareBudgetParts = (budgetParts) => {
  return budgetParts.map(part => {
    // Determinar el tamaño de página para esta parte
    let pageSize;

    if (part.pageSize === 'Personalizado') {
      // Si es personalizado, usar el valor del campo customPageSize
      const dimensions = part.customPageSize.split('x').map(dim => parseFloat(dim.trim()));
      if (dimensions.length !== 2 || isNaN(dimensions[0]) || isNaN(dimensions[1])) {
        throw new Error(`Formato de tamaño personalizado incorrecto en la parte "${part.name}". Usa el formato "ancho x alto"`);
      }
      pageSize = {
        width: dimensions[0],
        height: dimensions[1]
      };
    } else {
      // Obtener dimensiones según el tamaño seleccionado
      switch (part.pageSize) {
        case 'A4':
          pageSize = { width: 210, height: 297 };
          break;
        case 'A5':
          pageSize = { width: 148, height: 210 };
          break;
        case 'A3':
          pageSize = { width: 297, height: 420 };
          break;
        case 'Carta':
          pageSize = { width: 216, height: 279 };
          break;
        default:
          pageSize = { width: 210, height: 297 }; // Por defecto A4
      }
    }

    // Determinar si es una máquina digital
    const isDigital = part.machine && part.machine.type === 'Digital';

    // Calcular los costos correctamente antes de guardar
    let paperCost = 0;
    let machineCost = 0;
    let plateCost = 0;
    let inkCost = 0;
    let totalCost = 0;

    // Calcular costos solo si tenemos los datos necesarios
    if (part.sheetCalculation && part.sheetCalculation.mejor_combinacion) {
      // Obtener el número de pliegos y planchas
      const totalPliegos = part.sheetCalculation.mejor_combinacion.total_pliegos || 0;
      const totalPlanchas = part.sheetCalculation.mejor_combinacion.total_planchas || 0;

      // Calcular el coste del papel basado en el número de pliegos
      const pliegosProduccion = totalPliegos * (part.budget_copies || parseInt(part.copies) || 500);

      // Variable para almacenar el costo de maculatura
      let maculaturaCost = 0;

      // Para máquinas digitales, no hay maculatura
      // Usar el valor de maculatura del cálculo si está disponible, o un valor por defecto basado en el tipo de máquina
      const maculaturaPorPliego = isDigital ? 0 :
                               (part.sheetCalculation.maculatura_por_pliego ||
                                part.machine?.maculatura_por_pliego || 150);
      const pliegosMaculatura = totalPliegos * maculaturaPorPliego;
      const pliegosTotales = pliegosProduccion + pliegosMaculatura;

      // Obtener el precio por millar del papel seleccionado
      const precioPorMillar = part.paper && part.paper.price_per_1000 ?
                             part.paper.price_per_1000 :
                             (part.sheetCalculation.precio_papel_por_millar || 0);

      // Calcular el coste del papel
      // Si ya tenemos un costo de papel calculado, usarlo
      if (part.sheetCalculation.paper_cost && part.sheetCalculation.paper_cost > 0) {
        paperCost = part.sheetCalculation.paper_cost;
      } else if (precioPorMillar > 0) {
        // Calcular basado en el precio por millar
        paperCost = (pliegosTotales * precioPorMillar) / 1000;
      }

      // Calcular el costo de maculatura para máquinas offset
      if (!isDigital && pliegosMaculatura > 0 && precioPorMillar > 0) {
        maculaturaCost = (pliegosMaculatura * precioPorMillar) / 1000;
      }

      // Calcular el coste de las planchas
      // Usar el costo por plancha del cálculo si está disponible
      const costePorPlancha = part.sheetCalculation.plate_unit_cost ||
                            part.machine?.plate_unit_cost || 5.75;

      // Si ya tenemos un costo de planchas calculado, usarlo
      if (part.sheetCalculation.plate_cost && part.sheetCalculation.plate_cost > 0) {
        plateCost = part.sheetCalculation.plate_cost;
      } else {
        plateCost = totalPlanchas * costePorPlancha;
      }

      // Calcular costos según el tipo de máquina
      if (isDigital) {
        // Para máquinas digitales, usar el costo de máquina del cálculo
        if (part.sheetCalculation.machine_cost) {
          machineCost = part.sheetCalculation.machine_cost;
        }

        // Para máquinas digitales, usar el costo de clicks en lugar de tinta
        if (part.sheetCalculation.click_cost) {
          inkCost = part.sheetCalculation.click_cost;
        }
      } else {
        // Para máquinas offset, calcular el costo basado en el tiempo y la tarifa horaria
        if (part.sheetCalculation.machine_cost && part.sheetCalculation.machine_cost > 0) {
          // Si ya tenemos un costo de máquina calculado, usarlo directamente
          machineCost = part.sheetCalculation.machine_cost;
        } else if (part.sheetCalculation.total_time_hours && part.machine) {
          // Calcular basado en el tiempo y la tarifa horaria
          const timeHours = part.sheetCalculation.total_time_hours || 0;
          // Usar la tarifa horaria de la máquina o un valor por defecto de la configuración
          const hourlyRate = part.machine.hourly_cost ||
                           part.sheetCalculation.hourly_rate || 0;

          if (hourlyRate > 0 && timeHours > 0) {
            machineCost = timeHours * hourlyRate;
          }
        }

        // Calcular el costo de tinta
        if (part.sheetCalculation.ink_cost) {
          inkCost = part.sheetCalculation.ink_cost;
        }
      }

      // Guardar el costo de maculatura para usarlo más adelante
      part.maculaturaCost = maculaturaCost;

      // Calcular el costo total sumando los componentes
      totalCost = paperCost + machineCost + plateCost + inkCost;
    } else {
      // Si no hay cálculo de pliegos pero hay costos individuales, usarlos
      paperCost = parseFloat(part.paperCost || 0);
      machineCost = parseFloat(part.machineCost || 0);
      plateCost = parseFloat(part.plateCost || 0);
      inkCost = isDigital ? parseFloat(part.clickCost || 0) : parseFloat(part.inkCost || 0);
      totalCost = paperCost + machineCost + plateCost + inkCost;
    }

    // Calcular el costo total incluyendo maculatura para máquinas offset
    if (!isDigital && part.maculaturaCost) {
      totalCost += part.maculaturaCost;
    }

    console.log(`Costos calculados para parte ${part.name}:`, {
      paperCost,
      machineCost,
      plateCost,
      inkCost,
      maculaturaCost: part.maculaturaCost || 0,
      totalCost
    });

    return {
      part_id: part.part_id,
      name: part.name,
      description: part.description || '',
      assembly_order: part.assembly_order || 'None', // Tipo de encuadernado
      page_size: pageSize,
      page_count: parseInt(part.pageCount) || 0,
      paper_id: part.paper?.product_id || null,
      machine_id: part.machine?.machine_id || null,
      sheet_calculation: part.sheetCalculation,
      paper_cost: paperCost,
      machine_cost: machineCost,
      plate_cost: plateCost,
      ink_cost: inkCost,
      click_cost: isDigital ? inkCost : 0, // Incluir click_cost para máquinas digitales
      maculatura_cost: !isDigital ? (part.maculaturaCost || 0) : 0, // Incluir maculatura_cost para máquinas offset
      // Calculamos el costo total sumando todos los componentes
      total_cost: totalCost,
      custom_print_time: part.customPrintTime,
      color_config: part.colorConfig || {
        frontColors: 4,
        backColors: 4,
        pantones: 0
      },
      // Incluir datos completos del papel y la máquina
      paper_data: part.paper || null,
      machine_data: part.machine || null
    };
  });
};

/**
 * Prepara los procesos de acabado para enviar a la API
 * @param {Array} selectedProcesses - Procesos seleccionados
 * @returns {Array} - Procesos preparados para enviar a la API
 */
export const prepareProcessCosts = (selectedProcesses) => {
  return selectedProcesses.map(process => {
    // Calcular el costo total para este proceso
    const quantity = process.quantity || 1;
    const unitCost = process.unit_cost || 0;
    const totalCost = unitCost * quantity;

    console.log(`Proceso en budgetData: ${process.name}, Cantidad: ${quantity}, Costo unitario: ${unitCost}, Costo total: ${totalCost}`);

    return {
      process_id: process.process_id,
      name: process.name,
      type: process.type,
      unit_cost: unitCost,
      unit_type: process.unit_type || 'Unidad',
      quantity: quantity,
      total_cost: totalCost
    };
  });
};

/**
 * Prepara los datos del presupuesto para enviar a la API
 * @param {Object} options - Opciones para preparar los datos
 * @returns {Object} - Datos del presupuesto preparados para enviar a la API
 */
export const prepareBudgetData = (options) => {
  const {
    budget,
    budgetId,
    isEditMode,
    budgetParts,
    selectedProcesses,
    calculatedProcessCost,
    selectedPdf,
    pdfInfo,
    colorConfig,
    productConfig
  } = options;

  // Preparar las partes del presupuesto
  const preparedParts = prepareBudgetParts(budgetParts);

  // Usar valores por defecto para los campos requeridos por el modelo Budget
  // Usamos un tamaño de página estándar para el presupuesto principal
  let pageSize = { width: 210, height: 297 }; // Por defecto A4

  // Calcular los costos totales sumando los costos de todas las partes
  const totalPaperCost = budgetParts.reduce((sum, part) => sum + (part.paperCost || 0), 0);
  const totalMachineCost = budgetParts.reduce((sum, part) => sum + (part.machineCost || 0), 0);
  const totalPartsCost = budgetParts.reduce((sum, part) => sum + (part.totalCost || 0), 0);

  console.log('Costos calculados:', {
    totalPaperCost,
    totalMachineCost,
    totalPartsCost,
    shipping: budget.shipping,
    shipping_cost: budget.shipping?.cost || budget.shipping_cost || 0,
    total_paper_weight_kg: budget.shipping?.weight || budget.total_paper_weight_kg || 0,
    budgetParts: budgetParts.map(part => ({
      name: part.name,
      paperCost: part.paperCost,
      machineCost: part.machineCost,
      totalCost: part.totalCost
    }))
  });

  // Preparar el objeto de presupuesto para enviar al backend
  const budgetData = {
    budget_id: budgetId, // Solo para actualizaciones
    ot_number: budget.otNumber,
    description: budget.description,
    client_id: budget.client.client_id,
    job_type: budget.jobType,
    quantity: parseInt(budget.copies) || 0,
    // Usar un tamaño de página estándar para el presupuesto principal
    page_size: pageSize,
    // Información adicional
    // Usar el nombre del archivo PDF del estado del presupuesto
    pdf_filename: budget.pdf_filename || null,
    // Agregar un log para depuración
    _debug_pdf_info: {
      selectedPdf: selectedPdf ? {
        original: selectedPdf.name,
        normalized: selectedPdf.normalizedName
      } : null,
      fromBudget: budget.pdf_filename,
      finalValue: budget.pdf_filename || null
    },
    pdf_info: pdfInfo || {},
    color_config: colorConfig || {},
    // Incluir la configuración del producto
    product_config: {
      ...(productConfig || {})
    },
    // Establecer el estado a "Actualizado" cuando se está actualizando un presupuesto
    status: isEditMode ? "Actualizado" : "Pendiente",
    // Incluir información de envío
    shipping_cost: budget.shipping?.cost || budget.shipping_cost || 0,
    total_paper_weight_kg: budget.shipping?.weight || budget.total_paper_weight_kg || 0,
    // Incluir el costo total calculado
    total_cost: budget.total_cost || totalPartsCost + calculatedProcessCost + (budget.shipping?.cost || budget.shipping_cost || 0),
    // Los costes totales se calculan sumando los costes de todas las partes
    // pero también se incluyen en el presupuesto principal para facilitar consultas
  };

  // Añadir el campo parts como campo principal
  // Esto es necesario para que el backend pueda calcular los costos correctamente
  budgetData.parts = preparedParts;

  // Añadir los procesos de acabado después de las partes
  budgetData.process_costs = prepareProcessCosts(selectedProcesses);

  // Añadir el costo total de acabados
  budgetData.process_total_cost = calculatedProcessCost;

  // Si no es modo edición, eliminar el budget_id
  if (!isEditMode) {
    delete budgetData.budget_id;
  }

  return budgetData;
};

/**
 * Sube un archivo PDF al servidor
 * @param {File} selectedPdf - Archivo PDF seleccionado
 * @param {Function} buildApiUrl - Función para construir la URL de la API
 * @returns {Promise<Object>} - Resultado de la subida
 */
export const uploadPdf = async (selectedPdf, buildApiUrl) => {
  console.log('Subiendo archivo PDF:', selectedPdf.name);

  // Crear un FormData para enviar el archivo
  const formData = new FormData();
  formData.append('file', selectedPdf);

  // Obtener el token de autenticación del localStorage
  const token = localStorage.getItem('auth_token');

  if (!token) {
    throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
  }

  // Subir el archivo al servidor
  const uploadResponse = await fetch(buildApiUrl('/uploads/upload-pdf'), {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  if (!uploadResponse.ok) {
    throw new Error('Error al subir el archivo PDF');
  }

  const uploadResult = await uploadResponse.json();
  console.log('Archivo PDF subido correctamente:', uploadResult);
  return uploadResult;
};

/**
 * Envía el presupuesto al servidor
 * @param {Object} budgetData - Datos del presupuesto
 * @param {string} url - URL de la API
 * @param {string} method - Método HTTP (POST o PUT)
 * @returns {Promise<Object>} - Presupuesto guardado
 */
export const submitBudgetToServer = async (budgetData, url, method) => {
  // Obtener el token de autenticación del localStorage
  const token = localStorage.getItem('auth_token');

  if (!token) {
    throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
  }

  // Llamar a la API
  const response = await fetch(url, {
    method: method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(budgetData)
  });

  if (!response.ok) {
    // Intentar obtener el mensaje de error detallado del servidor
    try {
      const errorData = await response.json();
      console.error('Error detallado del servidor:', errorData);
      throw new Error(`Error al ${method === 'PUT' ? 'actualizar' : 'guardar'} el presupuesto: ${errorData.detail || JSON.stringify(errorData)}`);
    } catch (error) {
      // Si no podemos obtener el JSON, usar el statusText
      console.error('Error al procesar JSON:', error);
      throw new Error(`Error al ${method === 'PUT' ? 'actualizar' : 'guardar'} el presupuesto: ${response.statusText}`);
    }
  }

  const savedBudget = await response.json();
  console.log(`Presupuesto ${method === 'PUT' ? 'actualizado' : 'guardado'}:`, savedBudget);
  return savedBudget;
};

/**
 * Función principal para enviar un presupuesto
 * @param {Object} options - Opciones para enviar el presupuesto
 * @returns {Promise<Object>} - Presupuesto guardado
 */
export const submitBudget = async (options) => {
  const {
    e,
    budget,
    budgetParts,
    selectedProcesses,
    calculatedProcessCost,
    selectedPdf,
    pdfInfo,
    colorConfig,
    productConfig,
    budgetId,
    isEditMode,
    showSnackbar,
    setCalculatedProcessCost,
    buildApiUrl
  } = options;

  // Prevenir el comportamiento por defecto del formulario
  if (e) e.preventDefault();

  // Validar campos básicos
  if (!validateBasicFields(budget, showSnackbar)) {
    return null;
  }

  try {
    // Si hay un archivo PDF seleccionado pero no se ha procesado todavía, subirlo primero
    if (selectedPdf && !pdfInfo) {
      await uploadPdf(selectedPdf, buildApiUrl);
    }

    // Validar las partes del presupuesto
    if (!validateBudgetParts(budgetParts, showSnackbar)) {
      return null;
    }

    // Verificar y actualizar el costo de acabados si es necesario
    const updatedProcessCost = verifyProcessCost(selectedProcesses, calculatedProcessCost, setCalculatedProcessCost);

    // Preparar los datos del presupuesto
    const budgetData = prepareBudgetData({
      budget,
      budgetId,
      isEditMode,
      budgetParts,
      selectedProcesses,
      calculatedProcessCost: updatedProcessCost,
      selectedPdf,
      pdfInfo,
      colorConfig,
      productConfig
    });

    console.log('Enviando datos del presupuesto:', budgetData);
    console.log('Partes del presupuesto:', {
      'preparedParts': budgetData.parts,
      'budgetData.product_config': budgetData.product_config,
      'shipping_cost': budgetData.shipping_cost,
      'total_paper_weight_kg': budgetData.total_paper_weight_kg,
      'total_cost': budgetData.total_cost
    });

    // Determinar si es una creación o actualización
    const url = isEditMode ? buildApiUrl(`/budgets/${budgetId}`) : buildApiUrl('/budgets');
    const method = isEditMode ? 'PUT' : 'POST';

    // Enviar el presupuesto al servidor
    const savedBudget = await submitBudgetToServer(budgetData, url, method);

    // Mostrar mensaje de éxito
    showSnackbar(`Presupuesto ${isEditMode ? 'actualizado' : 'guardado'} correctamente`);

    return savedBudget;
  } catch (err) {
    console.error(`Error al ${isEditMode ? 'actualizar' : 'guardar'} el presupuesto:`, err);
    showSnackbar(err.message, 'error');
    return null;
  }
};

export default {
  submitBudget,
  validateBasicFields,
  validateBudgetParts,
  verifyProcessCost,
  prepareBudgetParts,
  prepareProcessCosts,
  prepareBudgetData,
  uploadPdf,
  submitBudgetToServer
};
