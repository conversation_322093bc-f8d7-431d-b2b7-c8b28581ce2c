// services/ApiInterceptor.js
import LogService from './logService';
import { buildApiUrl } from '../config';

/**
 * Interceptor para las peticiones HTTP
 */
class ApiInterceptor {
  /**
   * Realiza una petición HTTP y registra la operación en el log
   *
   * @param {string} url - URL de la petición
   * @param {object} options - Opciones de la petición
   * @returns {Promise} - Promesa con la respuesta
   */
  static async fetch(url, options = {}) {
    const method = options.method || 'GET';
    const isApiUrl = url.startsWith('/') || url.includes(buildApiUrl(''));

    // Determinar el endpoint para el log
    let endpoint = url;
    if (url.startsWith('/')) {
      endpoint = buildApiUrl(url);
    }

    // Extraer datos para el log
    const data = options.body ? JSON.parse(options.body) : null;

    try {
      // Realizar la petición
      const response = await fetch(url, options);

      // Registrar la operación en el log si es una petición a la API
      if (isApiUrl) {
        LogService.logApiOperation(
          endpoint,
          method,
          data,
          response.status
        );
      }

      return response;
    } catch (error) {
      // Registrar el error en el log
      if (isApiUrl) {
        LogService.logError(`Error en petición ${method} a ${endpoint}`, {
          error: error.message,
          data
        });
      }

      throw error;
    }
  }
}

export default ApiInterceptor;
