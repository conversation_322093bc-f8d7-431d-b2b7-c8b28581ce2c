import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Optional, Any

from models.activity_log import ActivityLog, ActivityType


class ActivityLogService:
    def __init__(self):
        self.logs_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "activity_logs.json")
        self._ensure_logs_file_exists()
        self.logs = self._load_logs()

    def _ensure_logs_file_exists(self):
        """Asegura que el archivo de logs exista, creándolo si es necesario."""
        os.makedirs(os.path.dirname(self.logs_file), exist_ok=True)
        if not os.path.exists(self.logs_file):
            with open(self.logs_file, "w") as f:
                json.dump([], f)

    def _load_logs(self) -> List[Dict[str, Any]]:
        """Carga los logs desde el archivo JSON."""
        try:
            with open(self.logs_file, "r") as f:
                return json.load(f)
        except json.JSONDecodeError:
            # Si el archivo está vacío o mal formateado, devolver una lista vacía
            return []

    def _save_logs(self):
        """Guarda los logs en el archivo JSON."""
        with open(self.logs_file, "w") as f:
            json.dump(self.logs, f, indent=2, default=str)

    def add_log(self, 
                user_id: str, 
                username: str, 
                activity_type: ActivityType, 
                description: str, 
                entity_id: Optional[str] = None,
                ip_address: Optional[str] = None,
                additional_data: Optional[dict] = None) -> Dict[str, Any]:
        """
        Añade un nuevo registro de actividad.
        
        Args:
            user_id: ID del usuario que realizó la acción
            username: Nombre del usuario que realizó la acción
            activity_type: Tipo de actividad
            description: Descripción de la actividad
            entity_id: ID de la entidad relacionada (presupuesto, usuario, etc.)
            ip_address: Dirección IP del usuario
            additional_data: Datos adicionales relacionados con la actividad
            
        Returns:
            Dict[str, Any]: Datos del registro de actividad
        """
        log_entry = {
            "log_id": str(uuid.uuid4()),
            "user_id": user_id,
            "username": username,
            "activity_type": activity_type,
            "description": description,
            "entity_id": entity_id,
            "timestamp": datetime.now().isoformat(),
            "ip_address": ip_address,
            "additional_data": additional_data
        }
        
        # Añadir el log a la lista y guardar
        self.logs.append(log_entry)
        self._save_logs()
        
        return log_entry

    def get_logs(self, 
                 limit: int = 100, 
                 skip: int = 0, 
                 user_id: Optional[str] = None,
                 activity_type: Optional[ActivityType] = None,
                 entity_id: Optional[str] = None,
                 start_date: Optional[datetime] = None,
                 end_date: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        Obtiene los registros de actividad con filtros opcionales.
        
        Args:
            limit: Número máximo de registros a devolver
            skip: Número de registros a omitir (para paginación)
            user_id: Filtrar por ID de usuario
            activity_type: Filtrar por tipo de actividad
            entity_id: Filtrar por ID de entidad
            start_date: Fecha de inicio para filtrar
            end_date: Fecha de fin para filtrar
            
        Returns:
            List[Dict[str, Any]]: Lista de registros de actividad
        """
        filtered_logs = self.logs
        
        # Aplicar filtros
        if user_id:
            filtered_logs = [log for log in filtered_logs if log.get("user_id") == user_id]
        
        if activity_type:
            filtered_logs = [log for log in filtered_logs if log.get("activity_type") == activity_type]
        
        if entity_id:
            filtered_logs = [log for log in filtered_logs if log.get("entity_id") == entity_id]
        
        if start_date:
            start_date_str = start_date.isoformat()
            filtered_logs = [log for log in filtered_logs if log.get("timestamp", "") >= start_date_str]
        
        if end_date:
            end_date_str = end_date.isoformat()
            filtered_logs = [log for log in filtered_logs if log.get("timestamp", "") <= end_date_str]
        
        # Ordenar por fecha (más reciente primero)
        filtered_logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        
        # Aplicar paginación
        paginated_logs = filtered_logs[skip:skip + limit]
        
        return paginated_logs

    def get_log_by_id(self, log_id: str) -> Optional[Dict[str, Any]]:
        """
        Obtiene un registro de actividad por su ID.
        
        Args:
            log_id: ID del registro de actividad
            
        Returns:
            Optional[Dict[str, Any]]: Datos del registro de actividad o None si no existe
        """
        for log in self.logs:
            if log.get("log_id") == log_id:
                return log
        return None

    def delete_logs_older_than(self, date: datetime) -> int:
        """
        Elimina los registros de actividad anteriores a una fecha.
        
        Args:
            date: Fecha límite
            
        Returns:
            int: Número de registros eliminados
        """
        date_str = date.isoformat()
        original_count = len(self.logs)
        
        self.logs = [log for log in self.logs if log.get("timestamp", "") >= date_str]
        self._save_logs()
        
        return original_count - len(self.logs)

    def clear_all_logs(self) -> int:
        """
        Elimina todos los registros de actividad.
        
        Returns:
            int: Número de registros eliminados
        """
        count = len(self.logs)
        self.logs = []
        self._save_logs()
        return count
