from fastapi import APIRouter, UploadFile, File, HTTPException, status, Depends
from fastapi.responses import FileResponse
import os
import shutil
import re
import unicodedata
from typing import List, Dict, Any
from pathlib import Path
import PyPDF2
import datetime
import httpx
import aiofiles
from dependencies.auth import get_current_active_user
from utils.logger import log_info, log_error

# Crear el router
router = APIRouter(
    tags=["uploads"],
    responses={404: {"description": "Archivo no encontrado"}}
)

# Definir las carpetas de uploads, albaranes y facturas
UPLOAD_DIR = Path("uploads")
DELIVERY_NOTES_DIR = Path("data/delivery_notes")
INVOICES_DIR = Path("data/facturas")

# Asegurarse de que las carpetas existen
if not DELIVERY_NOTES_DIR.exists():
    DELIVERY_NOTES_DIR.mkdir(parents=True, exist_ok=True)

# Asegurarse de que la carpeta de uploads existe
if not UPLOAD_DIR.exists():
    UPLOAD_DIR.mkdir(parents=True)

# Asegurarse de que la carpeta de facturas existe
if not INVOICES_DIR.exists():
    INVOICES_DIR.mkdir(parents=True, exist_ok=True)


def normalize_filename(filename: str) -> str:
    """
    Normaliza un nombre de archivo eliminando caracteres especiales, espacios y acentos.

    Args:
        filename: Nombre original del archivo

    Returns:
        Nombre normalizado del archivo
    """
    # Separar el nombre base y la extensión
    name, ext = os.path.splitext(filename)

    # Convertir a ASCII (eliminar acentos)
    name_ascii = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')

    # Reemplazar espacios y caracteres especiales por guiones bajos
    name_normalized = re.sub(r'[^\w\-]', '_', name_ascii)

    # Eliminar guiones bajos múltiples
    name_normalized = re.sub(r'_+', '_', name_normalized)

    # Eliminar guiones bajos al principio y al final
    name_normalized = name_normalized.strip('_')

    # Añadir timestamp para evitar colisiones
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')

    # Devolver el nombre normalizado con la extensión original
    return f"{name_normalized}_{timestamp}{ext}"


@router.post("/upload-pdf", status_code=status.HTTP_201_CREATED)
async def upload_pdf(file: UploadFile = File(...), skip_external: bool = False):
    """
    Sube un archivo PDF al servidor, normaliza su nombre y lo envía al servidor externo.
    """
    # Verificar que el archivo es un PDF
    if not file.content_type == "application/pdf":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="El archivo debe ser un PDF"
        )

    # Normalizar el nombre del archivo
    original_filename = file.filename
    normalized_filename = normalize_filename(original_filename)

    # Guardar el archivo con el nombre normalizado
    file_path = UPLOAD_DIR / normalized_filename

    try:
        # Guardar el contenido del archivo en una variable para poder usarlo más tarde
        file_content = await file.read()

        # Guardar el archivo en el servidor local
        async with aiofiles.open(file_path, "wb") as buffer:
            await buffer.write(file_content)

        log_info(f"Archivo PDF guardado localmente: {normalized_filename}")

        # Enviar el archivo al servidor externo solo si no se solicita omitir este paso
        external_success = False
        if not skip_external:
            try:
                # Crear un FormData para enviar al servidor externo
                external_url = "https://control2.triky.es/api/uploadpdf"

                # Crear un cliente HTTP para hacer la solicitud
                async with httpx.AsyncClient() as client:
                    # Crear un FormData con el archivo
                    files = {"file": (normalized_filename, file_content, "application/pdf")}
                    # Añadir el nombre normalizado como parámetro adicional
                    data = {"filename": normalized_filename}

                    # Enviar la solicitud al servidor externo
                    external_response = await client.post(external_url, files=files, data=data)

                    if external_response.status_code == 200:
                        log_info(f"Archivo PDF enviado correctamente al servidor externo: {normalized_filename}")
                        external_success = True
                    else:
                        log_error(f"Error al enviar el archivo PDF al servidor externo: {external_response.text}")
                        external_success = False
            except Exception as external_error:
                log_error(f"Error al enviar el archivo PDF al servidor externo: {str(external_error)}")
                external_success = False
        else:
            log_info(f"Omitiendo el envío al servidor externo para el archivo: {normalized_filename}")
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al guardar el archivo: {str(e)}"
        )
    finally:
        await file.seek(0)  # Reiniciar el puntero del archivo por si acaso
        await file.close()

    return {
        "filename": normalized_filename,
        "original_filename": original_filename,
        "status": "success",
        "external_upload": external_success
    }

@router.get("/files", response_model=List[str])
async def list_files(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Lista todos los archivos subidos.
    """
    files = []
    for file in UPLOAD_DIR.iterdir():
        if file.is_file():
            files.append(file.name)
    return files

@router.get("/{filename}")
async def get_file(filename: str):
    """
    Obtiene un archivo por su nombre.
    """
    file_path = UPLOAD_DIR / filename

    if not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Archivo {filename} no encontrado"
        )

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="application/octet-stream"
    )

@router.get("/download/{filename}")
async def download_file(filename: str):
    """
    Descarga un archivo por su nombre, buscando primero en la carpeta de albaranes
    y luego en la carpeta de uploads.
    """
    # Primero buscar en la carpeta de facturas
    file_path = INVOICES_DIR / filename

    # Si no existe en facturas, buscar en la carpeta de albaranes
    if not file_path.exists():
        # Buscar en todas las subcarpetas de facturas (por año/mes)
        for year_dir in INVOICES_DIR.iterdir():
            if year_dir.is_dir():
                for month_dir in year_dir.iterdir():
                    if month_dir.is_dir():
                        invoice_path = month_dir / filename
                        if invoice_path.exists():
                            file_path = invoice_path
                            break
                if file_path.exists():
                    break

        # Si aún no se encuentra, intentar extraer el año y mes del nombre del archivo
        if not file_path.exists() and filename.startswith("FACT-"):
            try:
                # Extraer año y mes del formato FACT-YYYYMM-XXXX.pdf
                parts = filename.split("-")
                if len(parts) >= 3:
                    year_month = parts[1]
                    if len(year_month) >= 6:
                        year = year_month[:4]
                        month = year_month[4:6]
                        # Buscar en la carpeta específica
                        specific_path = INVOICES_DIR / year / month / filename
                        if specific_path.exists():
                            file_path = specific_path
            except Exception as e:
                print(f"Error al intentar extraer año/mes del nombre de factura: {str(e)}")

    # Si no existe en facturas, buscar en albaranes
    if not file_path.exists():
        file_path = DELIVERY_NOTES_DIR / filename

    # Si no existe en albaranes, buscar en uploads
    if not file_path.exists():
        file_path = UPLOAD_DIR / filename

    if not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Archivo {filename} no encontrado"
        )

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="application/pdf" if filename.lower().endswith(".pdf") else "application/octet-stream"
    )

@router.get("/download-delivery-note/{shipping_id}")
async def download_delivery_note(shipping_id: str):
    """
    Descarga un albarán de entrega por el ID de envío.
    Busca el archivo en la carpeta de albaranes que coincida con el patrón.
    """
    from utils.shipping_manager import get_shipping_record_by_id

    # Obtener el registro de envío
    shipping_record = get_shipping_record_by_id(shipping_id)
    if not shipping_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Registro de envío con ID {shipping_id} no encontrado"
        )

    # Buscar archivos que coincidan con el patrón
    pattern = f"albaran_{shipping_id}_*.pdf"
    matching_files = list(DELIVERY_NOTES_DIR.glob(pattern))

    if not matching_files:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No se encontró albarán para el envío {shipping_id}"
        )

    # Ordenar por fecha de modificación (más reciente primero)
    matching_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

    # Tomar el archivo más reciente
    file_path = matching_files[0]
    filename = file_path.name

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="application/pdf"
    )

@router.delete("/{filename}")
async def delete_file(filename: str):
    """
    Elimina un archivo por su nombre.
    """
    file_path = UPLOAD_DIR / filename

    if not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Archivo {filename} no encontrado"
        )

    try:
        os.remove(file_path)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al eliminar el archivo: {str(e)}"
        )

    return {"filename": filename, "status": "deleted"}

@router.get("/pdf-info/{filename}", response_model=Dict[str, Any])
async def get_pdf_info(filename: str):
    """
    Obtiene información sobre un archivo PDF: número de páginas y tamaño.

    Args:
        filename: Nombre del archivo PDF

    Returns:
        Diccionario con información del PDF (páginas, tamaño, etc.)
    """
    file_path = UPLOAD_DIR / filename

    if not file_path.exists():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Archivo {filename} no encontrado"
        )

    # Verificar que es un archivo PDF
    if not filename.lower().endswith(".pdf"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="El archivo debe ser un PDF"
        )

    try:
        # Obtener el tamaño del archivo
        file_size_bytes = os.path.getsize(file_path)

        # Convertir a KB, MB o GB según corresponda
        if file_size_bytes < 1024:
            file_size = f"{file_size_bytes} bytes"
        elif file_size_bytes < 1024 * 1024:
            file_size = f"{file_size_bytes / 1024:.2f} KB"
        elif file_size_bytes < 1024 * 1024 * 1024:
            file_size = f"{file_size_bytes / (1024 * 1024):.2f} MB"
        else:
            file_size = f"{file_size_bytes / (1024 * 1024 * 1024):.2f} GB"

        # Obtener la fecha de modificación
        mod_time = os.path.getmtime(file_path)
        mod_date = datetime.datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')

        # Abrir el PDF y obtener información
        with open(file_path, 'rb') as pdf_file:
            pdf_reader = PyPDF2.PdfReader(pdf_file)

            # Obtener el número de páginas
            num_pages = len(pdf_reader.pages)

            # Intentar obtener información adicional si está disponible
            metadata = {}
            try:
                if pdf_reader.metadata:
                    for key, value in pdf_reader.metadata.items():
                        try:
                            if isinstance(key, str) and key.startswith('/'):
                                clean_key = key[1:]
                                # Asegurarse de que el valor sea serializable
                                if isinstance(value, (str, int, float, bool, type(None))):
                                    metadata[clean_key] = value
                                else:
                                    metadata[clean_key] = str(value)
                        except Exception as key_error:
                            print(f"Error al procesar metadato {key}: {str(key_error)}")
                            continue
            except Exception as meta_error:
                print(f"Error al obtener metadatos: {str(meta_error)}")
                # Continuar sin metadatos si hay error

            # Obtener tamaño de la primera página
            page_size = None
            try:
                if num_pages > 0:
                    page = pdf_reader.pages[0]
                    print(f"Analizando página del PDF {filename}")
                    print(f"Atributos disponibles: {dir(page)}")
                    print(f"MediaBox en page: {'/MediaBox' in page}")
                    print(f"mediabox como atributo: {hasattr(page, 'mediabox')}")

                    # Intentar obtener MediaBox
                    if '/MediaBox' in page:
                        media_box = page['/MediaBox']
                        print(f"MediaBox encontrado: {media_box}")
                        # MediaBox es [x1, y1, x2, y2] donde (x1,y1) es la esquina inferior izquierda
                        # y (x2,y2) es la esquina superior derecha
                        width = media_box[2] - media_box[0]  # Ancho en puntos (1/72 pulgadas)
                        height = media_box[3] - media_box[1]  # Alto en puntos

                        print(f"Dimensiones en puntos: {width} x {height}")

                        # Convertir de puntos a mm (1 punto = 0.352778 mm)
                        # Convertir a float para evitar problemas con Decimal
                        width_float = float(width)
                        height_float = float(height)
                        width_mm = width_float * 0.352778
                        height_mm = height_float * 0.352778

                        print(f"Dimensiones en mm: {width_mm} x {height_mm}")

                        page_size = {
                            "width": round(width_mm, 2),
                            "height": round(height_mm, 2),
                            "unit": "mm"
                        }
                        print(f"Page size establecido: {page_size}")
                    # Si no hay MediaBox, intentar con otros métodos
                    elif hasattr(page, 'mediabox'):
                        media_box = page.mediabox
                        print(f"mediabox como atributo encontrado: {media_box}")
                        width = float(media_box.width)
                        height = float(media_box.height)

                        print(f"Dimensiones en puntos (mediabox): {width} x {height}")

                        # Convertir de puntos a mm
                        # Convertir a float para evitar problemas con Decimal
                        width_float = float(width)
                        height_float = float(height)
                        width_mm = width_float * 0.352778
                        height_mm = height_float * 0.352778

                        print(f"Dimensiones en mm (mediabox): {width_mm} x {height_mm}")

                        page_size = {
                            "width": round(width_mm, 2),
                            "height": round(height_mm, 2),
                            "unit": "mm"
                        }
                        print(f"Page size establecido (mediabox): {page_size}")
                    else:
                        # Intentar otros métodos para obtener el tamaño
                        print("Intentando métodos alternativos para obtener el tamaño de página")
                        # Método 1: Intentar con CropBox
                        if '/CropBox' in page:
                            crop_box = page['/CropBox']
                            print(f"CropBox encontrado: {crop_box}")
                            width = crop_box[2] - crop_box[0]
                            height = crop_box[3] - crop_box[1]
                            # Convertir a float para evitar problemas con Decimal
                            width_float = float(width)
                            height_float = float(height)
                            width_mm = width_float * 0.352778
                            height_mm = height_float * 0.352778
                            page_size = {
                                "width": round(width_mm, 2),
                                "height": round(height_mm, 2),
                                "unit": "mm"
                            }
                            print(f"Page size establecido (CropBox): {page_size}")
                        # Método 2: Usar un tamaño predeterminado (A4)
                        else:
                            # Intentar obtener el tamaño de página de otras formas
                            try:
                                # Intentar obtener el tamaño de la página usando PyPDF2 de otra manera
                                rotation = page.get('/Rotate', 0)
                                print(f"Rotación de la página: {rotation}")

                                # Intentar obtener el tamaño usando el método get_contents()
                                if hasattr(page, 'get_contents'):
                                    contents = page.get_contents()
                                    print(f"Contenidos de la página disponibles: {contents is not None}")
                            except Exception as e:
                                print(f"Error al intentar métodos alternativos: {str(e)}")

                            print("No se pudo determinar el tamaño de página, usando A4 por defecto")
                            page_size = {
                                "width": 210.0,
                                "height": 297.0,
                                "unit": "mm",
                                "note": "Tamaño predeterminado (A4)"
                            }
            except Exception as page_error:
                error_type = type(page_error).__name__
                error_msg = str(page_error)
                print(f"Error al obtener tamaño de página: {error_type} - {error_msg}")

                # Establecer un tamaño predeterminado (A4) en caso de error
                page_size = {
                    "width": 210.0,
                    "height": 297.0,
                    "unit": "mm",
                    "note": f"Tamaño predeterminado (A4) - Error al extraer: {error_type}"
                }

        # Preparar la respuesta
        response = {
            "filename": filename,
            "num_pages": num_pages,
            "file_size": file_size,
            "file_size_bytes": file_size_bytes,
            "last_modified": mod_date
        }

        # Añadir metadatos si están disponibles
        if metadata:
            response["metadata"] = metadata

        # Añadir tamaño de página si está disponible
        if page_size:
            response["page_size"] = page_size

        return response

    except PyPDF2.errors.PdfReadError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error al leer el PDF: {str(e)}"
        )
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"Error al procesar PDF {filename}: {str(e)}\n{error_details}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al procesar el archivo: {str(e)}"
        )
