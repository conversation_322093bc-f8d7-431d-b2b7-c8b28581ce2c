/**
 * Servicio especializado para el cálculo de costos de envío
 * Extrae la lógica compleja de recalculateShippingCost del BudgetForm
 */

/**
 * Prepara los datos de solicitud para el cálculo de envío
 * @param {number} totalPaperWeight - Peso total del papel en kg
 * @param {Object} client - Cliente seleccionado (opcional)
 * @returns {Object} - Datos preparados para la API
 */
export const prepareShippingRequestData = (totalPaperWeight, client = null) => {
  const requestData = {
    weight_kg: totalPaperWeight
  };

  // Si tenemos un cliente seleccionado, añadir su ID y país
  if (client && client.client_id) {
    requestData.client_id = client.client_id;
    
    // Usar el país del cliente si está disponible
    if (client.company && client.company.address && client.company.address.country) {
      requestData.country = client.company.address.country;
    }
  }

  return requestData;
};

/**
 * Realiza la llamada a la API para calcular el costo de envío
 * @param {Object} requestData - Datos de la solicitud
 * @param {Function} buildApiUrl - Función para construir URLs
 * @returns {Promise<Object>} - Datos de envío calculados por el backend
 */
export const callShippingCalculationAPI = async (requestData, buildApiUrl) => {
  const response = await fetch(buildApiUrl('/calculations/shipping-cost'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
    },
    body: JSON.stringify(requestData)
  });

  if (!response.ok) {
    throw new Error(`Error al calcular el costo de envío: ${response.statusText}`);
  }

  return await response.json();
};

// La función de cálculo local ha sido eliminada conforme a los requisitos

/**
 * Actualiza el estado del presupuesto con los datos de envío
 * @param {Object} shippingData - Datos de envío calculados
 * @param {number} totalPaperWeight - Peso total del papel
 * @param {Function} setBudget - Función para actualizar el presupuesto
 * @returns {Object} - Datos de envío procesados
 */
export const updateBudgetWithShippingData = (shippingData, totalPaperWeight, setBudget) => {
  // El backend ya aplica el factor de distancia al calcular shipping_cost
  // Aseguramos que estamos usando el valor correcto
  const shippingCost = shippingData.shipping_cost || shippingData.cost || 0;
  const distanceFactor = shippingData.distance_factor || 1;
  const country = shippingData.country || "";
  
  console.log(`Actualizando presupuesto con datos de envío:`);
  console.log(`- Peso: ${totalPaperWeight} kg`);
  console.log(`- País: ${country}`);
  console.log(`- Factor de distancia: ${distanceFactor}`);
  console.log(`- Costo de envío: ${shippingCost}€`);
  console.log(`- Datos completos:`, shippingData);
  
  setBudget(prevBudget => {
    const updatedBudget = {
      ...prevBudget,
      total_paper_weight_kg: totalPaperWeight,
      shipping: {
        ...(prevBudget.shipping || {}),
        weight_kg: totalPaperWeight,  // Usar weight_kg en lugar de weight
        cost: shippingCost,
        country: country,
        distance_factor: distanceFactor,
        calculated_locally: false
      }
    };
    
    // Asegurar que costs.shipping también se actualice si existe
    if (updatedBudget.costs && updatedBudget.costs.shipping) {
      updatedBudget.costs.shipping = {
        ...updatedBudget.costs.shipping,
        weight_kg: totalPaperWeight,
        cost: shippingCost,
        country: country,
        distance_factor: distanceFactor
      };
    }
    
    return updatedBudget;
  });

  return {
    weight_kg: totalPaperWeight,  // Usar weight_kg en lugar de weight
    cost: shippingCost,
    country: country,
    distance_factor: distanceFactor,
    calculated_locally: false
  };
};

/**
 * Valida si el peso es válido para el cálculo de envío
 * @param {number} totalPaperWeight - Peso total del papel
 * @returns {boolean} - true si el peso es válido
 */
export const validateShippingWeight = (totalPaperWeight) => {
  return totalPaperWeight > 0;
};

/**
 * Función principal para recalcular el costo de envío
 * @param {Object} params - Parámetros para el cálculo
 * @returns {Promise<Object>} - Resultado del cálculo de envío
 */
export const recalculateShippingCost = async (params) => {
  const {
    budgetParts,
    budget,
    calculateTotalPaperWeight,
    buildApiUrl,
    setBudget,
    showSnackbar
  } = params;

  try {
    // 1. Calcular el peso total del papel usando la función auxiliar
    const totalPaperWeight = calculateTotalPaperWeight(budgetParts, budget.copies);

    // 2. Validar el peso
    if (!validateShippingWeight(totalPaperWeight)) {
      return { weight: 0, cost: 0 };
    }

    // 3. Preparar los datos para la llamada al endpoint
    const requestData = prepareShippingRequestData(totalPaperWeight, budget.client);

    // 4. Llamar al endpoint para calcular el costo de envío
    const shippingData = await callShippingCalculationAPI(requestData, buildApiUrl);

    // 5. Actualizar el estado del presupuesto con los nuevos datos
    const result = updateBudgetWithShippingData(shippingData, totalPaperWeight, setBudget);

    return result;
  } catch (error) {
    // Mostrar error al usuario
    showSnackbar(`Error al calcular el costo de envío: ${error.message}`, 'error');
    console.error('Error en el cálculo de envío:', error);
    
    // Devolver un objeto con peso y costo cero, y un flag de error
    return { weight: 0, cost: 0, error: true };
  }
};

/**
 * Función simplificada para recalcular envío con parámetros mínimos
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {Object} budget - Presupuesto actual
 * @param {Function} calculateTotalPaperWeight - Función para calcular peso total
 * @param {Function} buildApiUrl - Función para construir URLs
 * @param {Function} setBudget - Función para actualizar presupuesto
 * @param {Function} showSnackbar - Función para mostrar mensajes
 * @returns {Promise<Object>} - Resultado del cálculo
 */
export const recalculateShippingCostSimple = async (
  budgetParts,
  budget,
  calculateTotalPaperWeight,
  buildApiUrl,
  setBudget,
  showSnackbar
) => {
  return await recalculateShippingCost({
    budgetParts,
    budget,
    calculateTotalPaperWeight,
    buildApiUrl,
    setBudget,
    showSnackbar
  });
};

/**
 * Obtiene el costo de envío actual del presupuesto
 * @param {Object} budget - Presupuesto actual
 * @returns {number} - Costo de envío actual
 */
export const getCurrentShippingCost = (budget) => {
  return budget.shipping?.cost || budget.shipping_cost || 0;
};

/**
 * Obtiene el peso total actual del presupuesto
 * @param {Object} budget - Presupuesto actual
 * @returns {number} - Peso total actual en kg
 */
export const getCurrentPaperWeight = (budget) => {
  return budget.shipping?.weight_kg || budget.total_paper_weight_kg || 0;
};

/**
 * Verifica si el envío fue calculado localmente
 * @param {Object} budget - Presupuesto actual
 * @returns {boolean} - true si fue calculado localmente
 */
export const isShippingCalculatedLocally = (budget) => {
  return budget.shipping?.calculated_locally || false;
};

export default {
  recalculateShippingCost,
  recalculateShippingCostSimple,
  prepareShippingRequestData,
  callShippingCalculationAPI,
  updateBudgetWithShippingData,
  validateShippingWeight,
  getCurrentShippingCost,
  getCurrentPaperWeight,
  isShippingCalculatedLocally
};
