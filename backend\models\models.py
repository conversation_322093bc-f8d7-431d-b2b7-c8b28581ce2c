from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum, auto
import uuid
from datetime import datetime

# Enumeración para estilos de trabajo
class WorkStyle(str, Enum):
    WORK_AND_BACK = "WorkAndBack"
    PERFECTING = "Perfecting"
    WORK_AND_TURN = "WorkAndTurn"
    FLAT = "Flat"

# Enumeración para tipos de encuadernación
class AssemblyOrder(str, Enum):
    GATHERING = "Gathering"  # Alzado (como en libros)
    STITCHING = "Collecting"  # Embuchado (como en revistas, ahora 'Collecting')
    NONE = "None"  # Sin encuadernación (tarjetas, carteles)

class MediaType(str, Enum):
    PAPER = "Paper"
    CARD = "Card"

class Category(str, Enum):
    OFFSET = "Offset"
    ESTUCADO = "Estucado"
    RECICLADO = "Reciclado"
    AUTOCOPIATIVO = "Autocopiativo"
    ESPECIAL = "Especial"

class Finish(str, Enum):
    MATE = "Mate"
    BRILLO = "Brillo"
    SATIN = "Satin"
    NATURAL = "Natural"

class GrainDirection(str, Enum):
    LONG = "Long"
    SHORT = "Short"

class MachineType(str, Enum):
    OFFSET = "Offset"
    DIGITAL = "Digital"
    PLOTTER = "Plotter"
    ENCUADERNADORA = "Encuadernadora"
    GUILLOTINA = "Guillotina"
    PLEGADORA = "Plegadora"
    CTP = "CTP"

class MachineStatus(str, Enum):
    ACTIVA = "Activa"
    INACTIVA = "Inactiva"
    MANTENIMIENTO = "Mantenimiento"

# Modelos para Papel
class PaperBase(BaseModel):
    product_id: str
    descriptive_name: str
    media_type: MediaType = MediaType.PAPER
    category: Category
    finish: Finish
    dimension_width: float
    dimension_height: float
    thickness: float
    weight: float
    grainDirection: GrainDirection
    manufacturer: str
    inStock: bool = True
    color: str = "Blanco"
    notes: Optional[str] = None
    price_per_1000: Optional[float] = 0.0
    cost_per_ton: float = 0.0

class PaperCreate(PaperBase):
    pass

class PaperUpdate(BaseModel):
    descriptive_name: Optional[str] = None
    media_type: Optional[MediaType] = None
    category: Optional[Category] = None
    finish: Optional[Finish] = None
    dimension_width: Optional[float] = None
    dimension_height: Optional[float] = None
    thickness: Optional[float] = None
    weight: Optional[float] = None
    grainDirection: Optional[GrainDirection] = None
    manufacturer: Optional[str] = None
    inStock: Optional[bool] = None
    color: Optional[str] = None
    notes: Optional[str] = None
    price_per_1000: Optional[float] = None
    cost_per_ton: Optional[float] = None

class Paper(PaperBase):
    class Config:
        from_attributes = True

# Modelos para Máquinas
class MachineBase(BaseModel):
    machine_id: str
    name: str
    type: MachineType
    manufacturer: str
    model: str
    max_width: float
    max_height: float
    min_width: float
    min_height: float
    max_thickness: float
    status: MachineStatus
    purchase_date: str
    last_maintenance: str
    hourly_cost: float = 0.0
    cfa_percentage: float = 0.0  # Coste Fijo de Arranque como porcentaje del coste por hora
    setup_time: int = 30  # Tiempo de arranque en minutos para máquinas offset
    sheets_per_hour: Optional[int] = None  # Velocidad en pliegos/hora para máquinas offset
    maculatura: int = 150  # Pliegos adicionales para arranque y ajuste (maculatura)
    ink_consumption: float = 2.0  # Consumo de tinta medio en gramos/m² para máquinas offset
    print_units: int = 4  # Número de cuerpos de impresión (1, 4, 8, etc.)
    click_color_cost: Optional[float] = 0.0  # Coste por click de color (CMYK) en formato SRA3
    click_bw_cost: Optional[float] = 0.0  # Coste por click en blanco y negro en formato SRA3
    speed: Optional[int] = None  # Velocidad en A4/minuto para máquinas digitales
    notes: Optional[str] = None
    planificar: bool = True  # Indica si la máquina debe mostrarse en el planificador

class MachineCreate(MachineBase):
    pass

class MachineUpdate(BaseModel):
    name: Optional[str] = None
    type: Optional[MachineType] = None
    manufacturer: Optional[str] = None
    model: Optional[str] = None
    max_width: Optional[float] = None
    max_height: Optional[float] = None
    min_width: Optional[float] = None
    min_height: Optional[float] = None
    max_thickness: Optional[float] = None
    status: Optional[MachineStatus] = None
    purchase_date: Optional[str] = None
    last_maintenance: Optional[str] = None
    hourly_cost: Optional[float] = None
    cfa_percentage: Optional[float] = None
    setup_time: Optional[int] = None
    sheets_per_hour: Optional[int] = None
    maculatura: Optional[int] = None
    ink_consumption: Optional[float] = None
    print_units: Optional[int] = None
    click_color_cost: Optional[float] = None
    click_bw_cost: Optional[float] = None
    speed: Optional[int] = None
    notes: Optional[str] = None
    planificar: Optional[bool] = None

class Machine(MachineBase):
    class Config:
        from_attributes = True

# Modelos para Clientes
class Address(BaseModel):
    country: str
    region: str
    city: str
    street: str
    postal_code: str

class CompanyInfo(BaseModel):
    name: str
    address: Address

class ContactInfo(BaseModel):
    position: str
    first_name: str
    last_name: str
    phone: str
    fax: str
    email: str

class ClientBase(BaseModel):
    client_id: str
    billing_code: str
    order_id: str
    company: CompanyInfo
    contact: ContactInfo
    active: bool = True
    discount_percentage: float = 10.0  # Porcentaje de descuento por defecto (10%)
    created_at: str
    updated_at: str
    notes: Optional[str] = None

class ClientCreate(ClientBase):
    pass

class ClientUpdate(BaseModel):
    billing_code: Optional[str] = None
    order_id: Optional[str] = None
    company: Optional[CompanyInfo] = None
    contact: Optional[ContactInfo] = None
    active: Optional[bool] = None
    discount_percentage: Optional[float] = None
    notes: Optional[str] = None

class Client(ClientBase):
    class Config:
        from_attributes = True

# Modelos para Partes de Productos
class ProductPartBase(BaseModel):
    part_id: str = Field(default_factory=lambda: f"PART-{uuid.uuid4().hex[:6].upper()}")
    name: str
    description: str
    default_colors: str = "4/4"

class ProductPartCreate(ProductPartBase):
    pass

class ProductPartUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    default_colors: Optional[str] = None

class ProductPart(ProductPartBase):
    class Config:
        from_attributes = True

# Modelos para Productos
class ProductBase(BaseModel):
    product_id: str = Field(default_factory=lambda: f"PROD-{uuid.uuid4().hex[:6].upper()}")
    name: str
    type: str
    assembly_order: AssemblyOrder = AssemblyOrder.GATHERING
    description: str
    default_colors: str = "4/4"
    default_finishing: Optional[List[str]] = []
    finishing_processes: Optional[List[Dict[str, Any]]] = []
    active: bool = True
    parts: Optional[List[Dict[str, Any]]] = []

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    assembly_order: Optional[AssemblyOrder] = None
    description: Optional[str] = None
    default_colors: Optional[str] = None
    default_finishing: Optional[List[str]] = None
    finishing_processes: Optional[List[Dict[str, Any]]] = None
    active: Optional[bool] = None
    parts: Optional[List[ProductPart]] = None

class Product(ProductBase):
    class Config:
        from_attributes = True

    facturado: bool = False
    fecha_factura: Optional[str] = None
    numero_factura: Optional[str] = None

# Modelo para presupuesto simplificado
class SimpleBudget(BaseModel):
    job_type: str
    quantity: int
    page_count: int = 4  # Número de páginas por defecto
    description: Optional[str] = None
    pdf_filename: Optional[str] = None  # Nombre del archivo PDF

    class Config:
        json_schema_extra = {
            "example": {
                "job_type": "Libro",
                "quantity": 1000,
                "page_count": 200,
                "description": "Libro de 200 páginas",
                "pdf_filename": "diseno.pdf"
            }
        }

# Definición de tipos de pliego
class SheetType(str, Enum):
    FLAT = "Flat"  # Impresión a una cara o sin volteo
    WORK_AND_TURN = "WorkAndTurn"  # Tira-retira (mismas planchas, volteando el pliego)
    WORK_AND_BACK = "WorkAndBack"  # Diferentes planchas para cada cara
    PERFECTING = "Perfecting"  # Impresión de ambas caras en una pasada (máquinas de 8+ cuerpos)

# Modelos para el Calculador de Pliegos
class PaginasRequest(BaseModel):
    num_paginas: int
    ancho_pagina: float
    alto_pagina: float
    ancho_pliego: float
    alto_pliego: float
    front_colors: int = 4
    back_colors: int = 4
    machine_id: Optional[str] = None
    machine_type: str = "Offset"
    copies: int = 1
    waste_params: Optional[Dict[str, Any]] = None

class PaginasOffsetRequest(BaseModel):
    num_paginas: int
    ancho_pagina: float
    alto_pagina: float
    ancho_pliego: float
    alto_pliego: float
    front_colors: int = 4  # Número de colores en el anverso (por defecto 4 para CMYK)
    back_colors: int = 4   # Número de colores en el reverso (por defecto 4 para CMYK)
    print_units: Optional[int] = 4   # Número de cuerpos de impresión (por defecto 4)
    binding_type: Optional[str] = "gathering"  # Tipo de encuadernado: "gathering" (alzado), "collection" (grapado) o "none" (sin encuadernado)

class DisposicionResponse(BaseModel):
    paginas_ancho: int
    paginas_alto: int
    orientacion: str

class EsquemaResponse(BaseModel):
    nombre: str
    numero_pliegos: int
    paginas_por_pliego: int
    disposicion: DisposicionResponse
    es_tira_retira: bool
    sheet_type: Optional[SheetType] = None
    plates_needed: Optional[int] = None
    needs_two_passes: Optional[bool] = None
    page_layout: Optional[Dict[str, List[List[int]]]] = None  # Disposición de páginas en el pliego (front/back)

class CombinacionResponse(BaseModel):
    esquemas_utilizados: List[EsquemaResponse]
    total_pliegos: int
    total_planchas: int
    total_passes: Optional[int] = None  # Total de pasadas por la máquina
    total_clicks: Optional[int] = 0  # Total de clicks para máquinas digitales
    a4_per_sheet: Optional[int] = None  # Número de A4 que caben en cada hoja (por cara)
    print_speed: Optional[int] = None  # Velocidad de la máquina en A4/minuto
    setup_time: Optional[int] = None  # Tiempo de arranque en minutos para máquinas offset
    sheets_per_hour: Optional[int] = None  # Velocidad en pliegos/hora para máquinas offset
    estimated_time_minutes: Optional[float] = None  # Tiempo estimado en minutos
    estimated_time_hours: Optional[float] = None  # Tiempo estimado en horas
    # Información de desperdicio de papel
    area_pliego_mm2: Optional[float] = None  # Área total del pliego en mm²
    area_utilizada_mm2: Optional[float] = None  # Área utilizada del pliego en mm²
    area_desperdiciada_mm2: Optional[float] = None  # Área desperdiciada del pliego en mm²
    porcentaje_desperdicio: Optional[float] = None  # Porcentaje de desperdicio
    porcentaje_aprovechamiento: Optional[float] = None  # Porcentaje de aprovechamiento
    recomendaciones_desperdicio: Optional[List[str]] = None  # Recomendaciones para reducir el desperdicio

class EsquemaPosibleResponse(BaseModel):
    paginas_por_pliego: int
    paginas_ancho: int
    paginas_alto: int
    orientacion: str
    es_tira_retira: bool

class CombinacionOffsetResponse(BaseModel):
    esquemas_utilizados: List[EsquemaResponse]
    total_pliegos: int
    total_planchas: int
    total_passes: Optional[int] = None

class CalculoPaginasResponse(BaseModel):
    mejor_combinacion: CombinacionResponse

class CalculoPaginasOffsetResponse(BaseModel):
    mejor_combinacion: CombinacionOffsetResponse

class PaginasDigitalRequest(BaseModel):
    num_paginas: int
    ancho_pagina: float
    alto_pagina: float
    ancho_pliego: float
    alto_pliego: float
    front_colors: int = 4  # Número de colores en el anverso (por defecto 4 para CMYK, 1 para B/N)
    back_colors: int = 0   # Número de colores en el reverso (0 para simplex, >0 para duplex)
    copies: int = 1  # Número de copias/ejemplares
    binding_type: Optional[str] = "gathering"  # Tipo de encuadernado: "gathering" (alzado), "collection" (grapado) o "none" (sin encuadernado)

class CombinacionDigitalResponse(BaseModel):
    esquemas_utilizados: List[EsquemaResponse]
    total_pliegos: int
    total_clicks: int
    is_duplex: bool
    is_color: bool
    a4_per_sheet: Optional[int] = 2  # Número de A4 que caben en cada hoja (por cara)

class CalculoPaginasDigitalResponse(BaseModel):
    mejor_combinacion: CombinacionDigitalResponse

# Modelo para el Calculador de Clicks Digitales
class ClickCalculationRequest(BaseModel):
    machine_id: str  # ID de la máquina digital
    sheets: int  # Número de pliegos
    copies: int  # Número de ejemplares
    is_color: bool = True  # Si es impresión a color (True) o blanco y negro (False)
    is_duplex: bool = True  # Si es impresión a doble cara (True) o una cara (False)

    # Información adicional para cálculo más preciso
    paper_id: Optional[str] = None  # ID del papel (opcional)
    folding_scheme: Optional[str] = None  # Esquema de plegado (ej: "F4-1")
    pages_per_sheet: Optional[int] = None  # Páginas por pliego según el esquema
    page_width_mm: Optional[float] = None  # Ancho de la página en mm
    page_height_mm: Optional[float] = None  # Alto de la página en mm
    sheet_width_mm: Optional[float] = None  # Ancho del pliego en mm
    sheet_height_mm: Optional[float] = None  # Alto del pliego en mm

class ClickCalculationResponse(BaseModel):
    machine_id: str
    machine_name: str
    sheets: int
    copies: int
    total_sheets: int  # Total de pliegos (sheets * copies)
    clicks_per_sheet: int  # 1 para simplex, 2 para duplex
    total_clicks: int  # Total de clicks (total_sheets * clicks_per_sheet)
    is_color: bool
    is_duplex: bool
    click_unit_cost: float  # Coste por click (color o B/N)
    total_cost: float  # Coste total (total_clicks * click_unit_cost)

    # Información sobre el papel y la disposición
    paper_id: Optional[str] = None  # ID del papel
    paper_name: Optional[str] = None  # Nombre descriptivo del papel
    sheet_width_mm: Optional[float] = None  # Ancho del pliego en mm
    sheet_height_mm: Optional[float] = None  # Alto del pliego en mm

    # Información sobre el esquema de plegado
    folding_scheme: Optional[str] = None  # Esquema de plegado utilizado
    pages_per_sheet: Optional[int] = None  # Páginas por pliego según el esquema

    # Información sobre el cálculo de tiempo
    a4_per_sheet: int = 2  # Número de A4 que caben en cada hoja (por cara)
    print_speed: Optional[int] = None  # Velocidad de la máquina en A4/minuto
    total_pages: Optional[int] = None  # Total de páginas (no pliegos)
    estimated_time_minutes: Optional[float] = None  # Tiempo estimado en minutos
    estimated_time_hours: Optional[float] = None  # Tiempo estimado en horas

# Modelos para el Calculador de Peso de Papel
class PaperWeightRequest(BaseModel):
    paper_id: Optional[str] = None  # ID del papel (opcional si se proporcionan dimensiones y gramaje)
    sheets: int  # Número de pliegos
    width_mm: Optional[float] = None  # Ancho del pliego en mm (opcional si se proporciona paper_id)
    height_mm: Optional[float] = None  # Alto del pliego en mm (opcional si se proporciona paper_id)
    weight_gsm: Optional[float] = None  # Gramaje del papel en g/m² (opcional si se proporciona paper_id)

class PaperWeightResponse(BaseModel):
    paper_id: Optional[str] = None
    paper_name: Optional[str] = None
    sheets: int
    width_mm: float
    height_mm: float
    weight_gsm: float
    weight_per_sheet_g: float  # Peso por pliego en gramos
    total_weight_kg: float  # Peso total en kg

# Modelos para Procesos de Impresión
class ProcessType(str, Enum):
    IMPRESION = "Impresión"
    CORTE = "Corte"
    PLEGADO = "Plegado"
    ENCUADERNACION = "Encuadernación"
    ACABADO = "Acabado"
    TROQUELADO = "Troquelado"
    BARNIZADO = "Barnizado"
    LAMINADO = "Laminado"
    OTROS = "Otros"

class ProcessBase(BaseModel):
    process_id: str = Field(default_factory=lambda: f"PROC-{uuid.uuid4().hex[:6].upper()}")
    name: str
    type: ProcessType
    description: str
    machine_type: Optional[MachineType] = None
    unit_cost: float = 0.0
    unit_type: str = "Unidad"  # Unidad, Hora, Pliego, etc.
    notes: Optional[str] = None
    compatible_machines: Optional[List[str]] = []  # Lista de machine_id compatibles

class ProcessCreate(ProcessBase):
    pass

class ProcessUpdate(BaseModel):
    name: Optional[str] = None
    type: Optional[ProcessType] = None
    description: Optional[str] = None
    machine_type: Optional[MachineType] = None
    unit_cost: Optional[float] = None
    unit_type: Optional[str] = None
    notes: Optional[str] = None
    compatible_machines: Optional[List[str]] = None

class Process(ProcessBase):
    class Config:
        from_attributes = True

# Modelos para el Calculador Avanzado de Costes y Tiempos para Máquinas Offset

class OffsetCalculationRequest(BaseModel):
    # Parámetros obligatorios
    machine_id: str  # ID de la máquina offset
    copies: int  # Número de ejemplares
    num_paginas: int  # Número de páginas a imprimir
    ancho_pagina: float  # Ancho de la página en mm
    alto_pagina: float  # Alto de la página en mm

    # Parámetros opcionales
    colors_front: int = 4  # Número de colores en el anverso (CMYK por defecto)
    colors_back: int = 4  # Número de colores en el reverso (CMYK por defecto)
    paper_id: Optional[str] = None  # ID del papel (opcional)
    ancho_pliego: Optional[float] = None  # Ancho del pliego en mm (opcional, se puede obtener del papel)
    alto_pliego: Optional[float] = None  # Alto del pliego en mm (opcional, se puede obtener del papel)

    # Parámetros personalizados para ajustes finos
    custom_setup_time: Optional[int] = None  # Tiempo de arranque personalizado (en minutos)
    custom_sheets_per_hour: Optional[int] = None  # Velocidad personalizada (pliegos/hora)
    custom_maculatura: Optional[int] = None  # Maculatura personalizada (pliegos adicionales)

class OffsetCalculationResponse(BaseModel):
    machine_id: str
    machine_name: str
    total_sheets: int
    copies: int
    total_physical_sheets: int  # Total de pliegos físicos (considerando tira/retira y multiplicado por el número de ejemplares)
    total_plates: int
    colors_front: int
    colors_back: int
    maculatura: int  # Número de pliegos de maculatura por cada esquema de imposición
    total_maculatura: int  # Total de pliegos de maculatura para todo el trabajo (maculatura × total_sheets)
    total_paper: int  # Total de papel necesario (total_physical_sheets + total_maculatura)

    # Información de la máquina
    hourly_cost: float  # Coste por hora de la máquina
    cfa_percentage: float  # Porcentaje de CFA
    setup_time: int  # Tiempo de arranque en minutos
    sheets_per_hour: int  # Velocidad en pliegos/hora
    maculatura: int  # Pliegos adicionales para arranque y ajuste
    ink_consumption: float  # Consumo de tinta medio en gramos/m²
    ink_price_per_kg: float  # Precio medio de la tinta por kg
    print_units: int = 4  # Número de cuerpos de impresión

    # Información del papel (si se proporciona)
    paper_id: Optional[str] = None
    paper_name: Optional[str] = None
    paper_cost_per_1000: Optional[float] = None
    paper_info: Optional[dict] = None  # Información completa del papel

    # Cálculos de tiempo
    setup_time_minutes: float  # Tiempo de preparación base en minutos
    setup_time_total_minutes: float  # Tiempo de preparación total en minutos (considerando pasadas adicionales)
    plate_change_time_minutes: float  # Tiempo de cambio de planchas en minutos
    plate_changes: float  # Número de cambios de planchas
    printing_time_minutes: float  # Tiempo de impresión en minutos
    total_time_minutes: float  # Tiempo total en minutos
    total_time_hours: float  # Tiempo total en horas

    # Cálculos de coste
    cfa_cost: float  # Coste fijo de arranque
    printing_cost: float  # Coste de impresión
    paper_cost: Optional[float] = None  # Coste del papel
    plates_cost: Optional[float] = None  # Coste de las planchas
    ink_cost: Optional[float] = None  # Coste de la tinta
    ink_weight_kg: Optional[float] = None  # Peso de la tinta en kg
    maculatura_cost: Optional[float] = None  # Coste de la maculatura
    total_cost: float  # Coste total

    # Información del cálculo de pliegos (si se utilizó el cálculo automático)
    num_paginas: Optional[int] = None  # Número de páginas
    esquemas_utilizados: Optional[List[EsquemaResponse]] = None  # Esquemas de plegado utilizados
    calculo_pliegos_info: Optional[dict] = None  # Información adicional del cálculo de pliegos
