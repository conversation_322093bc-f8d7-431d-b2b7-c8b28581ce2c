from fastapi import APIRouter, HTTPException, status
from typing import List
import json
import os
from datetime import datetime

from models.consumable import Consumable, ConsumableCreate, ConsumableUpdate, ConsumableType, ConsumableStatus

router = APIRouter(
    prefix="/consumables",
    tags=["consumables"],
    responses={404: {"description": "Consumible no encontrado"}},
)

# Ruta al archivo JSON de consumibles
# Usar ruta absoluta para asegurar que el archivo se encuentra correctamente
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CONSUMABLES_FILE = os.path.join(BASE_DIR, "config", "consumibles_catalog.json")
print(f"Ruta del archivo de consumibles: {CONSUMABLES_FILE}")

# Función para cargar los consumibles desde el archivo JSON
def load_consumables():
    print(f"Intentando cargar consumibles desde: {CONSUMABLES_FILE}")
    if not os.path.exists(CONSUMABLES_FILE):
        print(f"El archivo {CONSUMABLES_FILE} no existe, creando uno vacío")
        # Si el archivo no existe, crear uno vacío
        with open(CONSUMABLES_FILE, "w") as f:
            json.dump([], f)
        return []

    print(f"El archivo {CONSUMABLES_FILE} existe, cargando datos")
    with open(CONSUMABLES_FILE, "r", encoding='utf-8') as f:
        try:
            data = json.load(f)
            print(f"Datos cargados correctamente: {len(data)} consumibles encontrados")
            return data
        except json.JSONDecodeError as e:
            print(f"Error al decodificar JSON: {e}")
            # Si el archivo está vacío o mal formateado, devolver una lista vacía
            return []

# Función para guardar los consumibles en el archivo JSON
def save_consumables(consumables):
    print(f"Guardando {len(consumables)} consumibles en {CONSUMABLES_FILE}")
    with open(CONSUMABLES_FILE, "w", encoding='utf-8') as f:
        json.dump(consumables, f, indent=4, ensure_ascii=False)
    print("Consumibles guardados correctamente")


@router.get("/", response_model=List[Consumable])
async def get_all_consumables():
    """
    Obtiene todos los consumibles del catálogo
    """
    print("Endpoint get_all_consumables llamado")
    consumables = load_consumables()
    print(f"Devolviendo {len(consumables)} consumibles")
    return consumables


@router.get("/{consumable_id}", response_model=Consumable)
async def get_consumable_by_id(consumable_id: str):
    """
    Obtiene un consumible específico por su ID
    """
    consumables = load_consumables()
    for consumable in consumables:
        if consumable["consumable_id"] == consumable_id:
            return consumable

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Consumible con ID {consumable_id} no encontrado"
    )


@router.post("/", response_model=Consumable)
async def create_consumable(consumable: ConsumableCreate):
    """
    Crea un nuevo consumible en el catálogo
    """
    consumables = load_consumables()

    # Verificar si ya existe un consumible con el mismo ID
    for existing_consumable in consumables:
        if existing_consumable["consumable_id"] == consumable.consumable_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Ya existe un consumible con el ID {consumable.consumable_id}"
            )

    # Crear el nuevo consumible
    now = datetime.now().isoformat()
    new_consumable = {
        **consumable.dict(),
        "created_at": now,
        "updated_at": now
    }

    # Añadir el nuevo consumible a la lista
    consumables.append(new_consumable)

    # Guardar la lista actualizada
    save_consumables(consumables)

    return new_consumable


@router.put("/{consumable_id}", response_model=Consumable)
async def update_consumable(consumable_id: str, consumable_update: ConsumableUpdate):
    """
    Actualiza un consumible existente en el catálogo
    """
    consumables = load_consumables()

    # Buscar el consumible a actualizar
    for i, consumable in enumerate(consumables):
        if consumable["consumable_id"] == consumable_id:
            # Actualizar solo los campos proporcionados
            update_data = consumable_update.dict(exclude_unset=True)

            # Si no hay datos para actualizar, devolver el consumible sin cambios
            if not update_data:
                return consumable

            # Actualizar el consumible
            consumables[i] = {
                **consumable,
                **update_data,
                "updated_at": datetime.now().isoformat()
            }

            # Guardar la lista actualizada
            save_consumables(consumables)

            return consumables[i]

    # Si no se encuentra el consumible, lanzar una excepción
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Consumible con ID {consumable_id} no encontrado"
    )


@router.delete("/{consumable_id}")
async def delete_consumible(consumable_id: str):
    """
    Elimina un consumible del catálogo
    """
    consumables = load_consumables()

    # Buscar el consumible a eliminar
    for i, consumable in enumerate(consumables):
        if consumable["consumable_id"] == consumable_id:
            # Eliminar el consumible
            del consumables[i]

            # Guardar la lista actualizada
            save_consumables(consumables)

            return {"message": f"Consumible con ID {consumable_id} eliminado correctamente"}

    # Si no se encuentra el consumible, lanzar una excepción
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Consumible con ID {consumable_id} no encontrado"
    )


@router.get("/type/{consumable_type}", response_model=List[Consumable])
async def get_consumables_by_type(consumable_type: ConsumableType):
    """
    Obtiene todos los consumibles de un tipo específico
    """
    consumables = load_consumables()
    filtered_consumables = [c for c in consumables if c["type"] == consumable_type]

    return filtered_consumables


@router.get("/status/{status}", response_model=List[Consumable])
async def get_consumables_by_status(status: ConsumableStatus):
    """
    Obtiene todos los consumibles con un estado específico
    """
    consumables = load_consumables()
    filtered_consumables = [c for c in consumables if c["status"] == status]

    return filtered_consumables
