import React from 'react';
import PropTypes from 'prop-types';
import { Paper, Typography, Box, useTheme, Stack } from '@mui/material';

// Importar iconos
import ReceiptIcon from '@mui/icons-material/Receipt';
import FactoryIcon from '@mui/icons-material/Factory';
import EventNoteIcon from '@mui/icons-material/EventNote';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import DashboardIcon from '@mui/icons-material/Dashboard';

const HomePage = ({ onNavigate }) => {
  const theme = useTheme();

  // Definir las secciones principales con sus iconos y nombres
  const mainSections = [
    { name: 'Presupuestos', icon: <ReceiptIcon sx={{ fontSize: 80 }} />, tabIndex: 0 },
    { name: 'Producción', icon: <FactoryIcon sx={{ fontSize: 80 }} />, tabIndex: 1 },
    { name: 'Planificación', icon: <EventNoteIcon sx={{ fontSize: 80 }} />, tabIndex: 2 },
    { name: 'Envíos', icon: <LocalShippingIcon sx={{ fontSize: 80 }} />, tabIndex: 3 },
    { name: 'Facturación', icon: <ReceiptLongIcon sx={{ fontSize: 80 }} />, tabIndex: 4 },
    { name: 'Dashboard', icon: <DashboardIcon sx={{ fontSize: 80 }} />, tabIndex: 5 },
  ];

  // Función para manejar la navegación
  const handleSectionClick = (tabIndex) => {
    if (onNavigate) {
      onNavigate(tabIndex);
    } else {
      console.error('No se proporcionó la función onNavigate');
    }
  };

  return (
    <Box sx={{ p: 4, width: '100%', maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" align="center" gutterBottom sx={{ mb: 6 }}>
        Bienvenido a Gestión de Imprenta
      </Typography>

      <Box sx={{ maxWidth: 900, mx: 'auto' }}>
        <Stack
          direction="row"
          flexWrap="wrap"
          spacing={4}
          justifyContent="center"
          sx={{ mb: 4 }}
        >
          {/* Primera fila: 3 iconos */}
          {mainSections.slice(0, 3).map((section, index) => (
            <Box key={index} sx={{ width: { xs: '100%', sm: 'auto' }, display: 'flex', justifyContent: 'center', mb: { xs: 2, sm: 0 } }}>
              <Paper
                elevation={3}
                sx={{
                  p: 3,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: 180,
                  width: 180,
                  borderRadius: 2,
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  backgroundColor: theme.palette.primary.main,
                  color: 'white',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: 6,
                    backgroundColor: theme.palette.primary.dark,
                  },
                }}
                onClick={() => handleSectionClick(section.tabIndex)}
              >
                {section.icon}
                <Typography variant="h6" sx={{ mt: 2 }}>
                  {section.name}
                </Typography>
              </Paper>
            </Box>
          ))}
        </Stack>

        <Stack
          direction="row"
          flexWrap="wrap"
          spacing={4}
          justifyContent="center"
        >
          {/* Segunda fila: 3 iconos */}
          {mainSections.slice(3, 6).map((section, index) => (
            <Box key={index + 3} sx={{ width: { xs: '100%', sm: 'auto' }, display: 'flex', justifyContent: 'center', mb: { xs: 2, sm: 0 } }}>
              <Paper
                elevation={3}
                sx={{
                  p: 3,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: 180,
                  width: 180,
                  borderRadius: 2,
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  backgroundColor: theme.palette.primary.main,
                  color: 'white',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: 6,
                    backgroundColor: theme.palette.primary.dark,
                  },
                }}
                onClick={() => handleSectionClick(section.tabIndex)}
              >
                {section.icon}
                <Typography variant="h6" sx={{ mt: 2 }}>
                  {section.name}
                </Typography>
              </Paper>
            </Box>
          ))}
        </Stack>
      </Box>
    </Box>
  );
};

// Validación de props
HomePage.propTypes = {
  onNavigate: PropTypes.func
};

HomePage.defaultProps = {
  onNavigate: null
};

export default HomePage;
