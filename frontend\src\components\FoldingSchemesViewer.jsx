import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  useTheme
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import FoldingSchemesService from '../services/FoldingSchemesService';
import SheetPreview from './SheetPreview';

const FoldingSchemesViewer = () => {
  const theme = useTheme();
  const [schemes, setSchemes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedScheme, setSelectedScheme] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Cargar esquemas al montar el componente
  useEffect(() => {
    const fetchSchemes = async () => {
      try {
        setLoading(true);
        const data = await FoldingSchemesService.getAllSchemes();
        setSchemes(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar esquemas:', err);

        // Si hay error, mostrar datos de prueba con estructura correcta
        const testData = [
          {
            name: "F4-1",
            pages_per_sheet: 4,
            rows: 1,
            cols: 2,
            page_layout: {
              front: [[4, 1]],
              back: [[2, 3]]
            },
            folding_steps: [
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro"
              }
            ],
            min_margin: 10.0,
            space_between_pages: 5.0
          },
          {
            name: "F8-7",
            pages_per_sheet: 8,
            rows: 2,
            cols: 2,
            page_layout: {
              front: [[8, 1], [7, 2]],
              back: [[6, 3], [5, 4]]
            },
            folding_steps: [
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro"
              },
              {
                orientation: "horizontal",
                description: "Doblar horizontalmente por el centro"
              }
            ],
            min_margin: 10.0,
            space_between_pages: 5.0
          },
          {
            name: "F16-7",
            pages_per_sheet: 16,
            rows: 2,
            cols: 4,
            page_layout: {
              front: [
                [16, 1, 2, 15],
                [14, 3, 4, 13]
              ],
              back: [
                [12, 5, 6, 11],
                [10, 7, 8, 9]
              ]
            },
            folding_steps: [
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro"
              },
              {
                orientation: "horizontal",
                description: "Doblar horizontalmente por el centro"
              },
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro nuevamente"
              }
            ],
            min_margin: 10.0,
            space_between_pages: 5.0
          }
        ];

        setSchemes(testData);
        setError(`Usando datos de prueba (backend no disponible)`);
      } finally {
        setLoading(false);
      }
    };

    fetchSchemes();
  }, []);

  const handlePreviewScheme = (scheme) => {
    setSelectedScheme(scheme);
    setPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setPreviewOpen(false);
    setSelectedScheme(null);
  };

  // Función para calcular los parámetros correctos del SheetPreview según el esquema
  const calculateSheetPreviewParams = (scheme) => {
    // Dimensiones estándar de página A4
    const pageWidth = 210; // mm
    const pageHeight = 297; // mm

    // Calcular dimensiones del pliego basado en el esquema
    const margin = scheme.min_margin || 10;
    const spacing = scheme.space_between_pages || 5;

    // Para esquemas F, necesitamos considerar el medianil central
    const isF8 = scheme.name === "F8-7";
    const isF16 = scheme.name === "F16-7";
    const isF32 = scheme.name === "F32-7";
    const isFoldingScheme = scheme.name.startsWith('F');

    let sheetWidth, sheetHeight;
    let esTiraRetira = false;
    let sheetType = "Flat";

    if (isFoldingScheme) {
      // Para esquemas F, calculamos las dimensiones considerando el plegado
      if (isF8) {
        // F8-7: 2x2 páginas con medianil central
        sheetWidth = (pageWidth * 2) + (spacing * 3) + (margin * 2) + 10; // +10 para medianil
        sheetHeight = (pageHeight * 2) + (spacing * 1) + (margin * 2) + 20; // +20 para pinzas
        esTiraRetira = true;
        sheetType = "WorkAndTurn";
      } else if (isF16) {
        // F16-7: 4x2 páginas con medianil central
        sheetWidth = (pageWidth * 4) + (spacing * 3) + (margin * 2) + 10;
        sheetHeight = (pageHeight * 2) + (spacing * 1) + (margin * 2) + 20;
        esTiraRetira = true;
        sheetType = "WorkAndTurn";
      } else if (isF32) {
        // F32-7: 4x4 páginas con medianil central
        sheetWidth = (pageWidth * 4) + (spacing * 3) + (margin * 2) + 10;
        sheetHeight = (pageHeight * 4) + (spacing * 3) + (margin * 2) + 20;
        esTiraRetira = true;
        sheetType = "WorkAndTurn";
      } else {
        // Otros esquemas F
        sheetWidth = (pageWidth * scheme.cols) + (spacing * (scheme.cols - 1)) + (margin * 2) + 10;
        sheetHeight = (pageHeight * scheme.rows) + (spacing * (scheme.rows - 1)) + (margin * 2) + 20;
      }
    } else {
      // Para esquemas no-F, cálculo estándar
      sheetWidth = (pageWidth * scheme.cols) + (spacing * (scheme.cols + 1)) + (margin * 2);
      sheetHeight = (pageHeight * scheme.rows) + (spacing * (scheme.rows + 1)) + (margin * 2) + 20;
    }

    return {
      anchoPliego: sheetWidth,
      altoPliego: sheetHeight,
      anchoPagina: pageWidth,
      altoPagina: pageHeight,
      paginasAncho: scheme.cols,
      paginasAlto: scheme.rows,
      sangrado: 3,
      pinzas: 10,
      margenLateral: margin,
      margenSuperior: margin,
      margenInferior: margin,
      marcasRegistro: true,
      tirasControl: true,
      esTiraRetira: esTiraRetira,
      sheetType: sheetType,
      pageLayout: scheme.page_layout,
      esquemaNombre: scheme.name,
      medianil: spacing,
      orientacion: 'Rotate0'
    };
  };

  // Componente simple para visualizar el layout de páginas
  const SimpleSchemeVisualization = ({ scheme }) => {
    const { page_layout } = scheme;
    if (!page_layout || !page_layout.front) return null;

    const cellSize = 60;
    const gap = 4;

    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
        <Typography variant="h6">Disposición de Páginas</Typography>

        {/* Front */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1, textAlign: 'center' }}>
            Cara Frontal
          </Typography>
          <Box sx={{
            display: 'grid',
            gridTemplateColumns: `repeat(${page_layout.front[0].length}, ${cellSize}px)`,
            gap: `${gap}px`,
            p: 2,
            border: '2px solid #1976d2',
            borderRadius: 1,
            bgcolor: '#e3f2fd'
          }}>
            {page_layout.front.flat().map((pageNum, index) => (
              <Box
                key={`front-${index}`}
                sx={{
                  width: cellSize,
                  height: cellSize * 1.4, // Proporción A4
                  bgcolor: 'white',
                  border: '1px solid #666',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: pageNum <= 4 ? 'green' : 'brown'
                }}
              >
                {pageNum}
              </Box>
            ))}
          </Box>
        </Box>

        {/* Back */}
        {page_layout.back && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1, textAlign: 'center' }}>
              Cara Posterior
            </Typography>
            <Box sx={{
              display: 'grid',
              gridTemplateColumns: `repeat(${page_layout.back[0].length}, ${cellSize}px)`,
              gap: `${gap}px`,
              p: 2,
              border: '2px solid #d32f2f',
              borderRadius: 1,
              bgcolor: '#ffebee'
            }}>
              {page_layout.back.flat().map((pageNum, index) => (
                <Box
                  key={`back-${index}`}
                  sx={{
                    width: cellSize,
                    height: cellSize * 1.4,
                    bgcolor: 'white',
                    border: '1px solid #666',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: pageNum <= 4 ? 'green' : 'brown'
                  }}
                >
                  {pageNum}
                </Box>
              ))}
            </Box>
          </Box>
        )}

        {/* Información adicional */}
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Páginas 1-4: <span style={{ color: 'green' }}>■</span> Primer cuadernillo |
            Páginas 5+: <span style={{ color: 'brown' }}>■</span> Siguientes cuadernillos
          </Typography>
        </Box>

        {/* Pasos de plegado */}
        {scheme.folding_steps && scheme.folding_steps.length > 0 && (
          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
              Pasos de Plegado:
            </Typography>
            <Box component="ol" sx={{ m: 0, pl: 2 }}>
              {scheme.folding_steps.map((step, index) => (
                <Box component="li" key={index} sx={{ mb: 0.5 }}>
                  <Typography variant="caption">
                    {step.description} ({step.orientation})
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        )}
      </Box>
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Cargando esquemas de plegado...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <InfoIcon sx={{ mr: 1 }} />
        Esquemas de Plegado Disponibles
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Visualiza todos los esquemas de plegado disponibles en el sistema.
        Cada esquema muestra la disposición de páginas y los pasos de plegado necesarios.
      </Typography>

      <Grid container spacing={2}>
        {schemes.map((scheme) => (
          <Grid item xs={12} sm={6} md={4} key={scheme.name}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                bgcolor: FoldingSchemesService.getSchemeColor(scheme.pages_per_sheet),
                border: `1px solid ${theme.palette.divider}`,
                '&:hover': {
                  boxShadow: theme.shadows[4],
                  transform: 'translateY(-2px)',
                  transition: 'all 0.3s ease-in-out'
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" component="h3" gutterBottom>
                  {scheme.name}
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Chip
                    label={`${scheme.pages_per_sheet} páginas/pliego`}
                    color="primary"
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip
                    label={`${scheme.rows}×${scheme.cols}`}
                    color="secondary"
                    size="small"
                    sx={{ mb: 1 }}
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <strong>Disposición:</strong> {scheme.rows} filas × {scheme.cols} columnas
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  <strong>Margen mínimo:</strong> {scheme.min_margin}mm
                </Typography>

                <Accordion size="small">
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="caption">
                      Pasos de Plegado ({scheme.folding_steps.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {scheme.folding_steps.map((step, index) => (
                        <ListItem key={index} sx={{ py: 0.25 }}>
                          <ListItemText
                            primary={`${index + 1}. ${step.description}`}
                            secondary={`Orientación: ${step.orientation}`}
                            primaryTypographyProps={{ variant: 'caption' }}
                            secondaryTypographyProps={{ variant: 'caption' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  fullWidth
                  variant="contained"
                  size="small"
                  startIcon={<VisibilityIcon />}
                  onClick={() => handlePreviewScheme(scheme)}
                >
                  Visualizar
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Modal de previsualización */}
      <Dialog
        open={previewOpen}
        onClose={handleClosePreview}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Visualización del Esquema: {selectedScheme?.name}
        </DialogTitle>
        <DialogContent>
          {selectedScheme && (
            <Box>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Información del Esquema
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Páginas por pliego:</strong> {selectedScheme.pages_per_sheet}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Disposición:</strong> {selectedScheme.rows}×{selectedScheme.cols}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Margen mínimo:</strong> {selectedScheme.min_margin}mm
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Espacio entre páginas:</strong> {selectedScheme.space_between_pages}mm
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Visualización simple del esquema */}
              <SimpleSchemeVisualization scheme={selectedScheme} />

              <Divider sx={{ my: 3 }} />

              {/* Visualización avanzada con SheetPreview */}
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Visualización Técnica del Pliego
                  </Typography>
                  <SheetPreview
                    {...calculateSheetPreviewParams(selectedScheme)}
                  />
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreview} color="primary">
            Cerrar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FoldingSchemesViewer;
