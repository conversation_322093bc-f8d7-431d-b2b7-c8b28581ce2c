import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  useTheme
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import FoldingSchemesService from '../services/FoldingSchemesService';
import SheetPreview from './SheetPreview';

const FoldingSchemesViewer = () => {
  const theme = useTheme();
  const [schemes, setSchemes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedScheme, setSelectedScheme] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Cargar esquemas al montar el componente
  useEffect(() => {
    const fetchSchemes = async () => {
      try {
        setLoading(true);
        const data = await FoldingSchemesService.getAllSchemes();
        setSchemes(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar esquemas:', err);

        // Si hay error, mostrar datos de prueba
        const testData = [
          {
            name: "F4-1",
            pages_per_sheet: 4,
            rows: 1,
            cols: 2,
            page_layout: {
              front: [[4, 1]],
              back: [[2, 3]]
            },
            folding_steps: [
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro"
              }
            ],
            min_margin: 10.0,
            space_between_pages: 5.0
          },
          {
            name: "F8-7",
            pages_per_sheet: 8,
            rows: 2,
            cols: 2,
            page_layout: {
              front: [[8, 1], [7, 2]],
              back: [[6, 3], [5, 4]]
            },
            folding_steps: [
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro"
              },
              {
                orientation: "horizontal",
                description: "Doblar horizontalmente por el centro"
              }
            ],
            min_margin: 10.0,
            space_between_pages: 5.0
          }
        ];

        setSchemes(testData);
        setError(`Usando datos de prueba (backend no disponible)`);
      } finally {
        setLoading(false);
      }
    };

    fetchSchemes();
  }, []);

  const handlePreviewScheme = (scheme) => {
    setSelectedScheme(scheme);
    setPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setPreviewOpen(false);
    setSelectedScheme(null);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Cargando esquemas de plegado...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <InfoIcon sx={{ mr: 1 }} />
        Esquemas de Plegado Disponibles
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Visualiza todos los esquemas de plegado disponibles en el sistema.
        Cada esquema muestra la disposición de páginas y los pasos de plegado necesarios.
      </Typography>

      <Grid container spacing={2}>
        {schemes.map((scheme) => (
          <Grid item xs={12} sm={6} md={4} key={scheme.name}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                bgcolor: FoldingSchemesService.getSchemeColor(scheme.pages_per_sheet),
                border: `1px solid ${theme.palette.divider}`,
                '&:hover': {
                  boxShadow: theme.shadows[4],
                  transform: 'translateY(-2px)',
                  transition: 'all 0.3s ease-in-out'
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" component="h3" gutterBottom>
                  {scheme.name}
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Chip
                    label={`${scheme.pages_per_sheet} páginas/pliego`}
                    color="primary"
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip
                    label={`${scheme.rows}×${scheme.cols}`}
                    color="secondary"
                    size="small"
                    sx={{ mb: 1 }}
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <strong>Disposición:</strong> {scheme.rows} filas × {scheme.cols} columnas
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  <strong>Margen mínimo:</strong> {scheme.min_margin}mm
                </Typography>

                <Accordion size="small">
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="caption">
                      Pasos de Plegado ({scheme.folding_steps.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {scheme.folding_steps.map((step, index) => (
                        <ListItem key={index} sx={{ py: 0.25 }}>
                          <ListItemText
                            primary={`${index + 1}. ${step.description}`}
                            secondary={`Orientación: ${step.orientation}`}
                            primaryTypographyProps={{ variant: 'caption' }}
                            secondaryTypographyProps={{ variant: 'caption' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  fullWidth
                  variant="contained"
                  size="small"
                  startIcon={<VisibilityIcon />}
                  onClick={() => handlePreviewScheme(scheme)}
                >
                  Visualizar
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Modal de previsualización */}
      <Dialog
        open={previewOpen}
        onClose={handleClosePreview}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Visualización del Esquema: {selectedScheme?.name}
        </DialogTitle>
        <DialogContent>
          {selectedScheme && (
            <Box>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Información del Esquema
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Páginas por pliego:</strong> {selectedScheme.pages_per_sheet}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Disposición:</strong> {selectedScheme.rows}×{selectedScheme.cols}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Margen mínimo:</strong> {selectedScheme.min_margin}mm
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Espacio entre páginas:</strong> {selectedScheme.space_between_pages}mm
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <SheetPreview
                  anchoPliego={420}
                  altoPliego={594}
                  anchoPagina={210}
                  altoPagina={297}
                  paginasAncho={selectedScheme.cols}
                  paginasAlto={selectedScheme.rows}
                  sangrado={3}
                  pinzas={10}
                  margenLateral={selectedScheme.min_margin}
                  margenSuperior={selectedScheme.min_margin}
                  margenInferior={selectedScheme.min_margin}
                  marcasRegistro={true}
                  tirasControl={true}
                  esTiraRetira={false}
                  sheetType="Flat"
                  pageLayout={selectedScheme.page_layout}
                  esquemaNombre={selectedScheme.name}
                  medianil={selectedScheme.space_between_pages}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreview} color="primary">
            Cerrar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FoldingSchemesViewer;
