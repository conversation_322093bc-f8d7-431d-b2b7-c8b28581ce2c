import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  useTheme
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import FoldingSchemesService from '../services/FoldingSchemesService';
import SheetPreview from './SheetPreview';

const FoldingSchemesViewer = () => {
  const theme = useTheme();
  const [schemes, setSchemes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedScheme, setSelectedScheme] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Cargar esquemas al montar el componente
  useEffect(() => {
    const fetchSchemes = async () => {
      try {
        setLoading(true);
        const data = await FoldingSchemesService.getAllSchemes();
        setSchemes(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar esquemas:', err);

        // Si hay error, mostrar datos de prueba con estructura correcta
        const testData = [
          {
            name: "F4-1",
            pages_per_sheet: 4,
            rows: 1,
            cols: 2,
            page_layout: {
              front: [[4, 1]],
              back: [[2, 3]]
            },
            folding_steps: [
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro"
              }
            ],
            min_margin: 10.0,
            space_between_pages: 5.0
          },
          {
            name: "F8-7",
            pages_per_sheet: 8,
            rows: 2,
            cols: 2,
            page_layout: {
              front: [[8, 1], [7, 2]],
              back: [[6, 3], [5, 4]]
            },
            folding_steps: [
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro"
              },
              {
                orientation: "horizontal",
                description: "Doblar horizontalmente por el centro"
              }
            ],
            min_margin: 10.0,
            space_between_pages: 5.0
          },
          {
            name: "F16-7",
            pages_per_sheet: 16,
            rows: 2,
            cols: 4,
            page_layout: {
              front: [
                [16, 1, 2, 15],
                [14, 3, 4, 13]
              ],
              back: [
                [12, 5, 6, 11],
                [10, 7, 8, 9]
              ]
            },
            folding_steps: [
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro"
              },
              {
                orientation: "horizontal",
                description: "Doblar horizontalmente por el centro"
              },
              {
                orientation: "vertical",
                description: "Doblar verticalmente por el centro nuevamente"
              }
            ],
            min_margin: 10.0,
            space_between_pages: 5.0
          }
        ];

        setSchemes(testData);
        setError(`Usando datos de prueba (backend no disponible)`);
      } finally {
        setLoading(false);
      }
    };

    fetchSchemes();
  }, []);

  const handlePreviewScheme = (scheme) => {
    setSelectedScheme(scheme);
    setPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setPreviewOpen(false);
    setSelectedScheme(null);
  };

  // Función para calcular los parámetros correctos del SheetPreview según el esquema
  const calculateSheetPreviewParams = (scheme) => {
    // Dimensiones estándar de página A4
    const pageWidth = 210; // mm
    const pageHeight = 297; // mm

    // Calcular dimensiones del pliego basado en el esquema
    const margin = scheme.min_margin || 10;
    const spacing = scheme.space_between_pages || 5;

    // Para esquemas F, necesitamos considerar el medianil central
    const isF8 = scheme.name === "F8-7";
    const isF16 = scheme.name === "F16-7";
    const isF32 = scheme.name === "F32-7";
    const isFoldingScheme = scheme.name.startsWith('F');

    let sheetWidth, sheetHeight;
    let esTiraRetira = false;
    let sheetType = "Flat";

    if (isFoldingScheme) {
      // Para esquemas F, calculamos las dimensiones considerando el plegado
      if (isF8) {
        // F8-7: 2x2 páginas con medianil central
        sheetWidth = (pageWidth * 2) + (spacing * 3) + (margin * 2) + 10; // +10 para medianil
        sheetHeight = (pageHeight * 2) + (spacing * 1) + (margin * 2) + 20; // +20 para pinzas
        esTiraRetira = true;
        sheetType = "WorkAndTurn";
      } else if (isF16) {
        // F16-7: 4x2 páginas con medianil central
        sheetWidth = (pageWidth * 4) + (spacing * 3) + (margin * 2) + 10;
        sheetHeight = (pageHeight * 2) + (spacing * 1) + (margin * 2) + 20;
        esTiraRetira = true;
        sheetType = "WorkAndTurn";
      } else if (isF32) {
        // F32-7: 4x4 páginas con medianil central
        sheetWidth = (pageWidth * 4) + (spacing * 3) + (margin * 2) + 10;
        sheetHeight = (pageHeight * 4) + (spacing * 3) + (margin * 2) + 20;
        esTiraRetira = true;
        sheetType = "WorkAndTurn";
      } else {
        // Otros esquemas F
        sheetWidth = (pageWidth * scheme.cols) + (spacing * (scheme.cols - 1)) + (margin * 2) + 10;
        sheetHeight = (pageHeight * scheme.rows) + (spacing * (scheme.rows - 1)) + (margin * 2) + 20;
      }
    } else {
      // Para esquemas no-F, cálculo estándar
      sheetWidth = (pageWidth * scheme.cols) + (spacing * (scheme.cols + 1)) + (margin * 2);
      sheetHeight = (pageHeight * scheme.rows) + (spacing * (scheme.rows + 1)) + (margin * 2) + 20;
    }

    return {
      anchoPliego: sheetWidth,
      altoPliego: sheetHeight,
      anchoPagina: pageWidth,
      altoPagina: pageHeight,
      paginasAncho: scheme.cols,
      paginasAlto: scheme.rows,
      sangrado: 3,
      pinzas: 10,
      margenLateral: margin,
      margenSuperior: margin,
      margenInferior: margin,
      marcasRegistro: true,
      tirasControl: true,
      esTiraRetira: esTiraRetira,
      sheetType: sheetType,
      pageLayout: scheme.page_layout,
      esquemaNombre: scheme.name,
      medianil: spacing,
      orientacion: 'Rotate0'
    };
  };

  // Función para detectar si un esquema está rotado según el estándar CIP4
  // Los esquemas están rotados cuando el ancho es menor que el alto
  // porque las máquinas de plegado usan el lado ancho para trabajar
  const isSchemeRotated = (scheme) => {
    // Calculamos las dimensiones del pliego basado en el esquema
    const pageWidth = 210; // A4 estándar
    const pageHeight = 297; // A4 estándar
    const margin = scheme.min_margin || 10;
    const spacing = scheme.space_between_pages || 5;

    // Dimensiones del pliego sin rotación
    const sheetWidth = (pageWidth * scheme.cols) + (spacing * (scheme.cols + 1)) + (margin * 2);
    const sheetHeight = (pageHeight * scheme.rows) + (spacing * (scheme.rows + 1)) + (margin * 2) + 20; // +20 para pinzas

    // Si el ancho es menor que el alto, está rotado
    return sheetWidth < sheetHeight;
  };

  // Función para rotar el layout de páginas 90° en sentido horario
  const rotateLayout = (layout) => {
    if (!layout || layout.length === 0) return layout;

    const rows = layout.length;
    const cols = layout[0].length;
    const rotated = [];

    // Rotar 90° en sentido horario: nueva[j][rows-1-i] = original[i][j]
    for (let j = 0; j < cols; j++) {
      const newRow = [];
      for (let i = rows - 1; i >= 0; i--) {
        newRow.push(layout[i][j]);
      }
      rotated.push(newRow);
    }

    return rotated;
  };

  // Componente para visualizar esquemas F (fold/plegado) con dípticos
  const SimpleSchemeVisualization = ({ scheme }) => {
    const { page_layout } = scheme;
    if (!page_layout || !page_layout.front) return null;

    const cellSize = 50;
    const lomoWidth = 2; // Ancho del lomo (muy delgado)
    const dipticoGap = 8; // Espacio entre dípticos

    // Detectar si el esquema está rotado y corregirlo
    const isRotated = isSchemeRotated(scheme);
    const correctedLayout = isRotated ? {
      front: rotateLayout(page_layout.front),
      back: page_layout.back ? rotateLayout(page_layout.back) : null
    } : page_layout;

    // Función para agrupar páginas en dípticos (pares de páginas unidas por el lomo)
    const createDipticos = (layout) => {
      const dipticos = [];
      for (let row = 0; row < layout.length; row++) {
        const rowDipticos = [];
        for (let col = 0; col < layout[row].length; col += 2) {
          if (col + 1 < layout[row].length) {
            // Díptico completo (2 páginas)
            rowDipticos.push([layout[row][col], layout[row][col + 1]]);
          } else {
            // Página individual (caso raro en esquemas F)
            rowDipticos.push([layout[row][col]]);
          }
        }
        dipticos.push(rowDipticos);
      }
      return dipticos;
    };

    const frontDipticos = createDipticos(correctedLayout.front);
    const backDipticos = correctedLayout.back ? createDipticos(correctedLayout.back) : null;

    // Función para renderizar un díptico
    const renderDiptico = (diptico, key, isBack = false) => {
      const color1 = diptico[0] <= 4 ? 'green' : 'brown';
      const color2 = diptico[1] ? (diptico[1] <= 4 ? 'green' : 'brown') : color1;

      return (
        <Box key={key} sx={{ display: 'flex', alignItems: 'center' }}>
          {/* Primera página del díptico */}
          <Box
            sx={{
              width: cellSize,
              height: cellSize * 1.4,
              bgcolor: 'white',
              border: '2px solid #666',
              borderRight: diptico[1] ? `${lomoWidth}px solid #333` : '2px solid #666', // Lomo
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '12px',
              fontWeight: 'bold',
              color: color1,
              position: 'relative'
            }}
          >
            {diptico[0]}
            {diptico[1] && (
              <Box
                sx={{
                  position: 'absolute',
                  right: -1,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  fontSize: '8px',
                  color: '#666',
                  writingMode: 'vertical-rl',
                  textOrientation: 'mixed'
                }}
              >
                LOMO
              </Box>
            )}
          </Box>

          {/* Segunda página del díptico (si existe) */}
          {diptico[1] && (
            <Box
              sx={{
                width: cellSize,
                height: cellSize * 1.4,
                bgcolor: 'white',
                border: '2px solid #666',
                borderLeft: 'none', // Se une con el lomo
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '12px',
                fontWeight: 'bold',
                color: color2
              }}
            >
              {diptico[1]}
            </Box>
          )}
        </Box>
      );
    };

    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 3 }}>
        <Typography variant="h6">Esquema de Plegado - Dípticos</Typography>

        {/* Indicador de rotación corregida */}
        {isRotated && (
          <Box sx={{
            p: 1,
            bgcolor: '#fff3e0',
            borderRadius: 1,
            border: '1px solid #ff9800',
            textAlign: 'center'
          }}>
            <Typography variant="caption" color="#f57c00" sx={{ fontWeight: 'bold' }}>
              🔄 Esquema corregido: Rotado 90° porque las máquinas de plegado usan el lado ancho
            </Typography>
          </Box>
        )}

        {/* Explicación */}
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Typography variant="caption" color="text.secondary">
            Los esquemas F agrupan páginas en <strong>dípticos</strong> (2 páginas unidas por el lomo)
          </Typography>
        </Box>

        {/* Front */}
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 1, textAlign: 'center', color: '#1976d2' }}>
            🔵 Cara Frontal
          </Typography>
          <Box sx={{
            p: 2,
            border: '2px solid #1976d2',
            borderRadius: 1,
            bgcolor: '#e3f2fd'
          }}>
            {frontDipticos.map((row, rowIndex) => (
              <Box
                key={`front-row-${rowIndex}`}
                sx={{
                  display: 'flex',
                  gap: `${dipticoGap}px`,
                  mb: rowIndex < frontDipticos.length - 1 ? `${dipticoGap}px` : 0,
                  justifyContent: 'center'
                }}
              >
                {row.map((diptico, dipticoIndex) =>
                  renderDiptico(diptico, `front-${rowIndex}-${dipticoIndex}`)
                )}
              </Box>
            ))}
          </Box>
        </Box>

        {/* Back */}
        {backDipticos && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1, textAlign: 'center', color: '#d32f2f' }}>
              🔴 Cara Posterior
            </Typography>
            <Box sx={{
              p: 2,
              border: '2px solid #d32f2f',
              borderRadius: 1,
              bgcolor: '#ffebee'
            }}>
              {backDipticos.map((row, rowIndex) => (
                <Box
                  key={`back-row-${rowIndex}`}
                  sx={{
                    display: 'flex',
                    gap: `${dipticoGap}px`,
                    mb: rowIndex < backDipticos.length - 1 ? `${dipticoGap}px` : 0,
                    justifyContent: 'center'
                  }}
                >
                  {row.map((diptico, dipticoIndex) =>
                    renderDiptico(diptico, `back-${rowIndex}-${dipticoIndex}`, true)
                  )}
                </Box>
              ))}
            </Box>
          </Box>
        )}

        {/* Leyenda */}
        <Box sx={{ mt: 2, p: 2, bgcolor: '#f9f9f9', borderRadius: 1, textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
            <strong>Leyenda:</strong>
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
            📄 Páginas 1-4: <span style={{ color: 'green', fontWeight: 'bold' }}>■</span> Primer cuadernillo |
            📄 Páginas 5+: <span style={{ color: 'brown', fontWeight: 'bold' }}>■</span> Siguientes cuadernillos
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
            📏 <strong>LOMO</strong>: Línea de plegado (0mm) que une las páginas del díptico
          </Typography>
          {isRotated && (
            <Typography variant="caption" color="#f57c00" sx={{ display: 'block', mt: 1, fontWeight: 'bold' }}>
              🔄 <strong>ROTACIÓN</strong>: Esquema corregido porque ancho &lt; alto (máquinas usan lado ancho)
            </Typography>
          )}
        </Box>

        {/* Pasos de plegado */}
        {scheme.folding_steps && scheme.folding_steps.length > 0 && (
          <Box sx={{ mt: 2, p: 2, bgcolor: '#fff3e0', borderRadius: 1, border: '1px solid #ff9800' }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold', color: '#f57c00' }}>
              📋 Secuencia de Plegado:
            </Typography>
            <Box component="ol" sx={{ m: 0, pl: 2 }}>
              {scheme.folding_steps.map((step, index) => (
                <Box component="li" key={index} sx={{ mb: 0.5 }}>
                  <Typography variant="caption">
                    <strong>Paso {index + 1}:</strong> {step.description}
                    <em> ({step.orientation})</em>
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        )}
      </Box>
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Cargando esquemas de plegado...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <InfoIcon sx={{ mr: 1 }} />
        Esquemas de Plegado Disponibles
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Visualiza todos los esquemas de plegado disponibles en el sistema.
        Cada esquema muestra la disposición de páginas y los pasos de plegado necesarios.
      </Typography>

      <Grid container spacing={2}>
        {schemes.map((scheme) => (
          <Grid item xs={12} sm={6} md={4} key={scheme.name}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                bgcolor: FoldingSchemesService.getSchemeColor(scheme.pages_per_sheet),
                border: `1px solid ${theme.palette.divider}`,
                '&:hover': {
                  boxShadow: theme.shadows[4],
                  transform: 'translateY(-2px)',
                  transition: 'all 0.3s ease-in-out'
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" component="h3" gutterBottom>
                  {scheme.name}
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Chip
                    label={`${scheme.pages_per_sheet} páginas/pliego`}
                    color="primary"
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip
                    label={`${scheme.cols}×${scheme.rows}`}
                    color="secondary"
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  {isSchemeRotated(scheme) && (
                    <Chip
                      label="🔄 Rotado"
                      color="warning"
                      size="small"
                      sx={{ mb: 1 }}
                    />
                  )}
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <strong>Dípticos:</strong> {scheme.cols / 2} × {scheme.rows} (columnas × filas)
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  <strong>Páginas totales:</strong> {scheme.cols} × {scheme.rows} = {scheme.pages_per_sheet}
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  <strong>Margen mínimo:</strong> {scheme.min_margin}mm
                </Typography>

                <Accordion size="small">
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="caption">
                      Pasos de Plegado ({scheme.folding_steps.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {scheme.folding_steps.map((step, index) => (
                        <ListItem key={index} sx={{ py: 0.25 }}>
                          <ListItemText
                            primary={`${index + 1}. ${step.description}`}
                            secondary={`Orientación: ${step.orientation}`}
                            primaryTypographyProps={{ variant: 'caption' }}
                            secondaryTypographyProps={{ variant: 'caption' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  fullWidth
                  variant="contained"
                  size="small"
                  startIcon={<VisibilityIcon />}
                  onClick={() => handlePreviewScheme(scheme)}
                >
                  Visualizar
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Modal de previsualización */}
      <Dialog
        open={previewOpen}
        onClose={handleClosePreview}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Visualización del Esquema: {selectedScheme?.name}
        </DialogTitle>
        <DialogContent>
          {selectedScheme && (
            <Box>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Información del Esquema
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Páginas por pliego:</strong> {selectedScheme.pages_per_sheet}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Dípticos:</strong> {selectedScheme.cols / 2} × {selectedScheme.rows}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Disposición total:</strong> {selectedScheme.cols}×{selectedScheme.rows} páginas
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Lomo:</strong> 0mm (línea de plegado)
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Margen mínimo:</strong> {selectedScheme.min_margin}mm
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Espacio entre dípticos:</strong> {selectedScheme.space_between_pages}mm
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Visualización simple del esquema */}
              <SimpleSchemeVisualization scheme={selectedScheme} />

              <Divider sx={{ my: 3 }} />

              {/* Visualización avanzada con SheetPreview */}
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Visualización Técnica del Pliego
                  </Typography>
                  <SheetPreview
                    {...calculateSheetPreviewParams(selectedScheme)}
                  />
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreview} color="primary">
            Cerrar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FoldingSchemesViewer;
