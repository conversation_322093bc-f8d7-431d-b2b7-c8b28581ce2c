import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  useTheme
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import FoldingSchemesService from '../services/FoldingSchemesService';
import SheetPreview from './SheetPreview';

const FoldingSchemesViewer = () => {
  const theme = useTheme();
  const [schemes, setSchemes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedScheme, setSelectedScheme] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Cargar esquemas al montar el componente
  useEffect(() => {
    const fetchSchemes = async () => {
      try {
        setLoading(true);
        const data = await FoldingSchemesService.getAllSchemes();
        setSchemes(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar esquemas:', err);
        setError('Error al cargar los esquemas de plegado. Por favor, inténtelo de nuevo.');
      } finally {
        setLoading(false);
      }
    };

    fetchSchemes();
  }, []);

  const handlePreviewScheme = (scheme) => {
    setSelectedScheme(scheme);
    setPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setPreviewOpen(false);
    setSelectedScheme(null);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Cargando esquemas de plegado...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <InfoIcon sx={{ mr: 1 }} />
        Esquemas de Plegado
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Visualiza todos los esquemas de plegado disponibles en el sistema. 
        Cada esquema muestra la disposición de páginas y los pasos de plegado necesarios.
      </Typography>

      <Grid container spacing={3}>
        {schemes.map((scheme) => (
          <Grid item xs={12} sm={6} md={4} key={scheme.name}>
            <Card 
              sx={{ 
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                bgcolor: FoldingSchemesService.getSchemeColor(scheme.pages_per_sheet),
                border: `1px solid ${theme.palette.divider}`,
                '&:hover': {
                  boxShadow: theme.shadows[4],
                  transform: 'translateY(-2px)',
                  transition: 'all 0.3s ease-in-out'
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h5" component="h2" gutterBottom>
                  {scheme.name}
                </Typography>
                
                <Box sx={{ mb: 2 }}>
                  <Chip 
                    label={`${scheme.pages_per_sheet} páginas/pliego`}
                    color="primary"
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip 
                    label={`${scheme.rows}×${scheme.cols}`}
                    color="secondary"
                    size="small"
                    sx={{ mb: 1 }}
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  <strong>Disposición:</strong> {scheme.rows} filas × {scheme.cols} columnas
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  <strong>Margen mínimo:</strong> {scheme.min_margin}mm
                </Typography>

                <Accordion sx={{ mt: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle2">
                      Pasos de Plegado ({scheme.folding_steps.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {scheme.folding_steps.map((step, index) => (
                        <ListItem key={index} sx={{ py: 0.5 }}>
                          <ListItemText
                            primary={`${index + 1}. ${step.description}`}
                            secondary={`Orientación: ${step.orientation}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </CardContent>

              <Box sx={{ p: 2, pt: 0 }}>
                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<VisibilityIcon />}
                  onClick={() => handlePreviewScheme(scheme)}
                  sx={{ mt: 1 }}
                >
                  Visualizar Esquema
                </Button>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Modal de previsualización */}
      <Dialog
        open={previewOpen}
        onClose={handleClosePreview}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Visualización del Esquema: {selectedScheme?.name}
        </DialogTitle>
        <DialogContent>
          {selectedScheme && (
            <Box>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Información del Esquema
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Páginas por pliego:</strong> {selectedScheme.pages_per_sheet}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Disposición:</strong> {selectedScheme.rows}×{selectedScheme.cols}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Margen mínimo:</strong> {selectedScheme.min_margin}mm
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Espacio entre páginas:</strong> {selectedScheme.space_between_pages}mm
                    </Typography>
                  </Grid>
                </Grid>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                <SheetPreview
                  anchoPliego={420}
                  altoPliego={594}
                  anchoPagina={210}
                  altoPagina={297}
                  paginasAncho={selectedScheme.cols}
                  paginasAlto={selectedScheme.rows}
                  sangrado={3}
                  pinzas={10}
                  margenLateral={selectedScheme.min_margin}
                  margenSuperior={selectedScheme.min_margin}
                  margenInferior={selectedScheme.min_margin}
                  marcasRegistro={true}
                  tirasControl={true}
                  esTiraRetira={false}
                  sheetType="Flat"
                  pageLayout={selectedScheme.page_layout}
                  esquemaNombre={selectedScheme.name}
                  medianil={selectedScheme.space_between_pages}
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreview} color="primary">
            Cerrar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FoldingSchemesViewer;
