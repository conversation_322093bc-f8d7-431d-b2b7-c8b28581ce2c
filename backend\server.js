const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

// Importar rutas
const budgetRoutes = require('./routes/budgetRoutes');
const clientRoutes = require('./routes/clientRoutes');
const machineRoutes = require('./routes/machineRoutes');
const paperRoutes = require('./routes/paperRoutes');
const productRoutes = require('./routes/productRoutes');
const jdfRoutes = require('./routes/jdfRoutes');
const productionRoutes = require('./routes/productionRoutes');

const app = express();
const PORT = process.env.PORT || 3005;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, 'public')));

// Rutas
app.use('/budgets', budgetRoutes);
app.use('/clients', clientRoutes);
app.use('/machines', machineRoutes);
app.use('/papers', paperRoutes);
app.use('/products', productRoutes);
app.use('/jdf', jdfRoutes);
app.use('/production', productionRoutes);

// Log de rutas disponibles para depuración
console.log('Rutas JDF registradas:', Object.keys(jdfRoutes.stack || {}).map(r => r.route?.path).filter(Boolean));

// Ruta para servir archivos estáticos
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`Servidor iniciado en el puerto ${PORT}`);
});
