# Estructura Detallada de BudgetForm

Este documento describe detalladamente la estructura y el flujo de componentes del formulario de presupuestos (`BudgetForm`) en la aplicación Imprenta, siguiendo el orden de aparición en la interfaz.

---

## 1. Campos Generales del Presupuesto
- **ClienteSelector**: Selector/autocompletado para elegir el cliente.
- **JobDescription**: Campo de texto para la descripción del trabajo.
- **Cantidad de Ejemplares**: Input numérico para la cantidad total a producir.
- **Otros campos generales**: Número de OT, estado, fechas, etc.

## 2. BudgetParts
Componente que gestiona la lista de partes/componentes del presupuesto.

- **Barra de herramientas de partes**
  - Botón “Añadir Parte”
  - Contador de partes actuales
- **Lista de partes**
  - Para cada parte:
    - **BudgetPartForm**
      - Nombre y descripción de la parte
      - Tipo de encuadernado
      - Tamaño de página (predefinido o personalizado)
      - Configuración de colores (inputs y diálogo)
      - Número de páginas
      - Selección de máquina (autocomplete)
      - Selección de papel (autocomplete, filtrado)
      - Botón “Calcular Pliegos” (V2)
      - Desglose de costes parciales:
        - Costo de máquina
        - Costo de papel
        - Costo de planchas/tinta o clicks
        - Costo de maculatura (si aplica)
        - Costo total de la parte
      - Botón eliminar parte
      - Colapso/expansión de detalles

## 3. ProcessCostsSection (opcional)
- Sección para añadir y mostrar procesos/acabados adicionales (troquelados, barnices, etc.)
- Inputs para nombre, tipo, coste unitario, etc.
- Suma de costes de procesos

## 4. ShippingCostSection (opcional)
- Campo/input para el coste de envío
- Puede incluir selector de modalidad de envío

## 5. TotalCostsSection
- Resumen de costes totales:
  - Suma de todas las partes
  - Suma de procesos/acabados
  - Coste de envío
  - **Total global del presupuesto**
- Presentación destacada del total

## 6. Acciones Finales
- Botones para guardar, enviar, imprimir o cancelar el presupuesto

---

## Ejemplo de flujo visual (en orden)

```
BudgetForm
│
├─ [1] Campos Generales (cliente, descripción, cantidad, etc.)
│
├─ [2] BudgetParts
│     ├─ Barra de herramientas (añadir parte)
│     └─ BudgetPartForm (xN)
│           ├─ Datos de la parte
│           ├─ Botón calcular pliegos
│           └─ Costes parciales
│
├─ [3] ProcessCostsSection (opcional)
│
├─ [4] ShippingCostSection (opcional)
│
├─ [5] TotalCostsSection
│
└─ [6] Acciones finales (guardar, imprimir, etc.)
```

---

Esta estructura permite comprender el flujo de interacción y la organización interna del formulario de presupuestos, facilitando su mantenimiento y evolución.
