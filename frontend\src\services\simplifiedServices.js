// Servicios simplificados para todos los componentes
import { buildApiUrl } from '../config';

// Servicio de log simplificado
export const LogService = {
  logNavigation: (route, params = null) => {
    // Desactivado para reducir ruido en la consola
    return true;
  },

  logUserAction: (action, data = null) => {
    // Desactivado para reducir ruido en la consola
    return true;
  },

  logError: (message, errorData = null) => {
    // Mantener solo errores críticos
    if (message.includes('critical')) {
      console.error(`[LOG] Error: ${message}`, errorData || {});
    }
    return true;
  },

  logApiOperation: (endpoint, method, data = null, status = null) => {
    // Desactivado para reducir ruido en la consola
    return true;
  }
};

// Interceptor de API simplificado
export const ApiInterceptor = {
  fetch: async (url, options = {}) => {
    const method = options.method || 'GET';

    try {
      const response = await fetch(url, options);

      // Log desactivado para reducir ruido en la consola

      return response;
    } catch (error) {
      // Mantener solo errores críticos
      console.error(`[API] Error in ${method} ${url}`, error);
      throw error;
    }
  }
};

// Servicio JDF simplificado
export const JDFService = {
  generateJDF: async (budget, useCompleteSchema = false) => {
    try {
      const url = buildApiUrl('/jdf/generate');
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          budget_id: budget.budget_id,
          use_complete_schema: useCompleteSchema
        })
      });

      if (!response.ok) {
        throw new Error(`Error al generar JDF: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      // Mantener el error pero sin detalles
      throw error;
    }
  },

  sendJDF: async (jdfData) => {
    try {
      const url = buildApiUrl('/jdf/send');
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(jdfData)
      });

      if (!response.ok) {
        throw new Error(`Error al enviar JDF: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      // Mantener el error pero sin detalles
      throw error;
    }
  }
};
