from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional
import httpx
import os
import json
import logging
from datetime import datetime, timedelta
import jwt
from dotenv import load_dotenv

# Configurar logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Cargar variables de entorno
load_dotenv()

# Crear router
router = APIRouter(prefix="/external", tags=["external"])

# Obtener variables de entorno
AUTH_API_URL = os.getenv("AUTH_API_URL", "https://auth-service.triky.app/token")
AUTH_USERNAME = os.getenv("AUTH_USERNAME", "<EMAIL>")
AUTH_PASSWORD = os.getenv("AUTH_PASSWORD", "Masketu.123$")
JDF_API_URL = os.getenv("JDF_API_URL", "http://localhost:3002/api/generate-and-send")
JDF_TEST_MODE = os.getenv("JDF_TEST_MODE", "true").lower() == "true"

# Modelo para la respuesta del token
class TokenResponse(BaseModel):
    access_token: str
    token_type: str

# Modelo para la respuesta del envío de JDF
class JDFSendResponse(BaseModel):
    success: bool
    message: str
    details: Optional[Dict[str, Any]] = None

# Modelo para la respuesta del token
class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"

# Modelo para solicitud de envío de JSON_OT por ID de presupuesto
class SendJsonOTRequest(BaseModel):
    budget_id: str

@router.get("/get-token", response_model=TokenResponse)
async def get_token():
    """
    Endpoint para obtener un token de autenticación para el servidor externo.
    """
    try:
        token = await get_auth_token()
        return {"access_token": token, "token_type": "bearer"}
    except Exception as e:
        logger.error(f"Error al obtener token para el frontend: {str(e)}")
        # En caso de error, generamos un token local para pruebas
        if JDF_TEST_MODE:
            logger.info("Generando token local para modo de prueba")
            return {"access_token": generate_local_jwt_token(), "token_type": "bearer"}
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/send-budget-jdf/{budget_id}", response_model=JDFSendResponse)
async def send_budget_jdf(budget_id: str):
    """
    Obtiene el JSON_OT de un presupuesto y lo envía al servidor externo en una sola operación.
    Además, guarda el JSON_OT en un archivo xmf.json con la referencia del presupuesto.
    """
    try:
        logger.info(f"Recibida solicitud para enviar JSON_OT del presupuesto {budget_id}")
        
        # Obtener el JSON_OT del presupuesto
        try:
            # Llamar al endpoint interno para generar el JSON_OT
            async with httpx.AsyncClient() as client:
                json_ot_url = f"http://localhost:3005/json-ot/generate/{budget_id}"
                logger.debug(f"Obteniendo JSON_OT desde: {json_ot_url}")
                
                json_ot_response = await client.get(json_ot_url)
                
                if json_ot_response.status_code != 200:
                    logger.error(f"Error al obtener JSON_OT: {json_ot_response.status_code} - {json_ot_response.text}")
                    return {
                        "success": False,
                        "message": f"Error al obtener JSON_OT: {json_ot_response.status_code}",
                        "details": {"error": json_ot_response.text}
                    }
                
                # Extraer el JSON_OT de la respuesta
                json_ot_data = json_ot_response.json()
                json_ot = json_ot_data.get("json_ot", json_ot_data)
                
                logger.debug(f"JSON_OT obtenido correctamente: {json.dumps(json_ot)[:100]}...")
                
                # Guardar el JSON_OT en un archivo xmf.json con la referencia del presupuesto
                try:
                    # Obtener la referencia del presupuesto (ot_number o budget_id si no hay ot_number)
                    # Primero, intentar obtener el presupuesto completo para extraer el ot_number
                    budget_ref = budget_id
                    try:
                        budget_url = f"http://localhost:3005/budgets/{budget_id}"
                        budget_response = await client.get(budget_url)
                        if budget_response.status_code == 200:
                            budget_data = budget_response.json()
                            if budget_data.get("ot_number"):
                                budget_ref = budget_data["ot_number"]
                    except Exception as e:
                        logger.warning(f"No se pudo obtener el ot_number del presupuesto: {str(e)}. Usando budget_id como referencia.")
                    
                    # Crear directorio para archivos XMF si no existe
                    xmf_dir = os.path.join(os.path.dirname(__file__), "..", "data", "xmf")
                    os.makedirs(xmf_dir, exist_ok=True)
                    
                    # Guardar el archivo con la referencia del presupuesto
                    file_path = os.path.join(xmf_dir, f"xmf_{budget_ref}.json")
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(json_ot, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"JSON_OT guardado correctamente en: {file_path}")
                except Exception as e:
                    logger.error(f"Error al guardar el archivo XMF: {str(e)}")
                    # No interrumpir el flujo si falla el guardado del archivo
        except Exception as e:
            logger.error(f"Error al obtener JSON_OT: {str(e)}")
            return {
                "success": False,
                "message": f"Error al obtener JSON_OT: {str(e)}",
                "details": {"error_type": "json_ot_generation_error"}
            }
        
        # Enviar el JSON_OT al servidor externo
        # Reutilizar la función existente para enviar el JDF
        # Indicar que se omita el guardado ya que ya lo hemos hecho aquí
        return await send_jdf_to_external_service(json_ot, skip_save=True)
        
    except Exception as e:
        logger.error(f"Error general al procesar la solicitud: {str(e)}")
        return {
            "success": False,
            "message": f"Error al procesar la solicitud: {str(e)}",
            "details": {"error_type": "general_error"}
        }

async def get_auth_token() -> str:
    """
    Obtiene un token JWT del servicio de autenticación.
    """
    try:
        logger.debug(f"Intentando obtener token de autenticación de: {AUTH_API_URL}")
        logger.debug(f"Usando credenciales: {AUTH_USERNAME}")
        
        # Para producción, obtener token del servicio de autenticación
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    AUTH_API_URL,
                    json={
                        "username": AUTH_USERNAME,
                        "password": AUTH_PASSWORD
                    },
                    headers={
                        "Content-Type": "application/json"
                    }
                )
                
                logger.debug(f"Respuesta del servicio de autenticación: {response.status_code}")
                
                if response.status_code != 200:
                    logger.error(f"Error al obtener token: {response.text}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Error al obtener token: {response.text}"
                    )
                
                # Mostrar la respuesta completa para depuración
                logger.debug(f"Respuesta completa: {response.text}")
                
                try:
                    token_data = response.json()
                    logger.info("Token obtenido correctamente")
                    
                    # Verificar la estructura de la respuesta
                    if "access_token" in token_data:
                        token = token_data.get("access_token")
                        logger.debug(f"Token obtenido (primeros 20 caracteres): {token[:20]}...")
                        return token
                    else:
                        # Si la estructura es diferente, intentar extraer el token de otra manera
                        logger.warning("Estructura de respuesta inesperada, intentando extraer token...")
                        logger.debug(f"Claves disponibles: {token_data.keys()}")
                        
                        # Buscar cualquier clave que pueda contener el token
                        potential_token_keys = ["token", "jwt", "id_token", "accessToken"]
                        for key in potential_token_keys:
                            if key in token_data:
                                token = token_data.get(key)
                                logger.info(f"Token encontrado en clave '{key}'")
                                return token
                        
                        # Si llegamos aquí, no se encontró el token
                        logger.error("No se pudo encontrar el token en la respuesta")
                        raise ValueError("Estructura de respuesta inesperada, no se pudo extraer el token")
                except Exception as e:
                    logger.error(f"Error al procesar la respuesta JSON: {str(e)}")
                    raise
        except httpx.RequestError as e:
            logger.error(f"Error de conexión al servicio de autenticación: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error de conexión al servicio de autenticación: {str(e)}"
            )
    except Exception as e:
        logger.error(f"Error al obtener token de autenticación: {str(e)}")
        logger.exception("Detalles del error:")
        raise HTTPException(
            status_code=500,
            detail=f"Error al obtener token de autenticación: {str(e)}"
        )

def generate_local_jwt_token() -> str:
    """
    Genera un token JWT fresco para uso local.
    Este token se genera en el momento y tiene una validez de 1 hora.
    """
    try:
        logger.info("Iniciando generación de token JWT local")
        
        # Crear payload del token
        now = datetime.utcnow()
        logger.debug(f"Timestamp actual: {now.isoformat()}")
        
        # Calcular la fecha de expiración (1 hora después)
        expiration = now + timedelta(hours=1)
        logger.debug(f"Timestamp de expiración: {expiration.isoformat()}")
        
        # Crear el payload completo
        payload = {
            "sub": AUTH_USERNAME,
            "name": "Imprenta App",
            "iat": int(now.timestamp()),
            "exp": int(expiration.timestamp())
        }
        logger.debug(f"Payload del token: {payload}")
        
        # Clave secreta para firmar el token
        secret_key = "imprenta-app-secret-key-for-local-development"
        
        # Generar token
        token = jwt.encode(payload, secret_key, algorithm="HS256")
        logger.info("Token JWT generado localmente con éxito")
        logger.debug(f"Token generado: {token}")
        
        return token
    except Exception as e:
        logger.error(f"Error al generar token JWT local: {str(e)}")
        logger.exception("Detalles del error:")
        raise

@router.post("/send-jdf", response_model=JDFSendResponse)
async def send_jdf_to_external_service(jdf_data: Dict[str, Any], skip_save: bool = False):
    """
    Envía un documento JDF al servicio externo.
    También guarda los datos JDF en un archivo xmf.json si skip_save es False.
    
    Args:
        jdf_data: Datos JDF a enviar
        skip_save: Si es True, no guarda el archivo localmente (usado cuando ya se guardó en send_budget_jdf)
    """
    try:
        logger.debug(f"Recibida solicitud para enviar JDF: {json.dumps(jdf_data)[:100]}...")
        
        # Guardar los datos JDF en un archivo xmf.json solo si no se debe omitir
        if not skip_save:
            try:
                # Obtener un identificador para el archivo
                job_id = jdf_data.get("job_id", str(datetime.now().timestamp()).replace(".", ""))
                
                # Crear directorio para archivos XMF si no existe
                xmf_dir = os.path.join(os.path.dirname(__file__), "..", "data", "xmf")
                os.makedirs(xmf_dir, exist_ok=True)
                
                # Guardar el archivo con el job_id como referencia
                file_path = os.path.join(xmf_dir, f"xmf_{job_id}.json")
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(jdf_data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"Datos JDF guardados correctamente en: {file_path}")
            except Exception as e:
                logger.error(f"Error al guardar el archivo XMF: {str(e)}")
                # No interrumpir el flujo si falla el guardado del archivo
        
        # Verificar si estamos en modo de prueba
        if JDF_TEST_MODE:
            # MODO DE PRUEBA: Simular respuesta exitosa para desarrollo local
            # Esto permite probar la funcionalidad sin depender del servicio externo
            logger.info("MODO DE PRUEBA: Simulando respuesta exitosa del servicio JDF")
            
            # Extraer información relevante del JDF para la respuesta simulada
            job_id = jdf_data.get("job_id", "unknown")
            descriptive_name = jdf_data.get("descriptive_name", "Trabajo de impresión")
            
            # Simular una respuesta exitosa
            return {
                "success": True,
                "message": "JDF enviado correctamente al servicio externo (simulado)",
                "details": {
                    "job_id": job_id,
                    "status": "processed",
                    "timestamp": datetime.now().isoformat(),
                    "description": f"Simulación de envío exitoso para: {descriptive_name}"
                }
            }
        
        # Código para entorno de producción
        # Obtener token de autenticación
        token = None
        
        try:
            # Siempre obtener un token fresco del servicio de autenticación
            logger.info("Obteniendo token fresco del servicio de autenticación")
            token = await get_auth_token()
            logger.debug(f"Token obtenido (primeros 20 caracteres): {token[:20]}...")
        except Exception as e:
            logger.error(f"Error al obtener token de autenticación: {str(e)}")
            return {
                "success": False,
                "message": f"Error al obtener token de autenticación: {str(e)}",
                "details": {"error_type": "auth_error"}
            }
        
        if not token:
            logger.error("No se pudo obtener el token de autenticación")
            return {
                "success": False,
                "message": "No se pudo obtener el token de autenticación",
                "details": {"error_type": "auth_error"}
            }
        
        # Preparar la URL del servicio externo
        service_url = JDF_API_URL
        if "localhost" not in JDF_API_URL and "127.0.0.1" not in JDF_API_URL:
            # En producción, usar la URL de producción
            service_url = "https://jdf-maker-back.triky.app/api/generate-and-send"
            logger.info(f"Usando URL de producción: {service_url}")
        else:
            logger.info(f"Usando URL local: {service_url}")
        
        logger.debug(f"Enviando JDF al servicio externo: {service_url}")
        
        # Enviar JDF al servicio externo
        async with httpx.AsyncClient() as client:
            logger.info(f"Realizando solicitud POST a {service_url}")
            
            # Probar con el prefijo "Bearer" ya que el error indica que el token de autenticación es requerido
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            logger.debug(f"Headers configurados: {headers}")
            logger.debug(f"Datos JDF (primeros 100 caracteres): {json.dumps(jdf_data)[:100]}...")
            
            try:
                response = await client.post(
                    service_url,
                    json=jdf_data,
                    headers=headers,
                    timeout=30.0  # Aumentar el tiempo de espera a 30 segundos
                )
                
                logger.debug(f"Respuesta del servicio externo: {response.status_code}")
                logger.debug(f"Contenido de la respuesta: {response.text[:200]}...")
                
                # Verificar respuesta
                if response.status_code == 200:
                    try:
                        response_data = response.json()
                        logger.info(f"JDF enviado correctamente: {response_data}")
                    except Exception as e:
                        logger.warning(f"No se pudo parsear la respuesta como JSON: {str(e)}")
                        response_data = {"raw_response": response.text}
                    
                    return {
                        "success": True,
                        "message": "JDF enviado correctamente al servicio externo",
                        "details": response_data
                    }
                else:
                    error_text = response.text
                    logger.error(f"Error al enviar JDF: {response.status_code} - {error_text}")
                    
                    # Si el error es 401 (Unauthorized), es probable que sea un problema con el token
                    if response.status_code == 401:
                        return {
                            "success": False,
                            "message": "Error de autenticación al enviar JDF. El token no es válido.",
                            "details": {
                                "error": error_text,
                                "error_type": "auth_error",
                                "suggestion": "Verifica que el token sea válido y esté en el formato correcto."
                            }
                        }
                    
                    return {
                        "success": False,
                        "message": f"Error al enviar JDF: {response.status_code}",
                        "details": {"error": error_text}
                    }
            except httpx.TimeoutException:
                logger.error("Tiempo de espera agotado al conectar con el servicio externo")
                return {
                    "success": False,
                    "message": "Tiempo de espera agotado al conectar con el servicio externo",
                    "details": {"error_type": "timeout"}
                }
            except httpx.ConnectError:
                logger.error(f"No se pudo conectar al servicio externo: {service_url}")
                return {
                    "success": False,
                    "message": f"No se pudo conectar al servicio externo: {service_url}",
                    "details": {"error_type": "connection_error"}
                }
    
    except Exception as e:
        logger.exception(f"Excepción al enviar JDF: {str(e)}")
        return {
            "success": False,
            "message": f"Error al enviar JDF al servicio externo: {str(e)}",
            "details": {"error_type": str(type(e).__name__)}
        }
