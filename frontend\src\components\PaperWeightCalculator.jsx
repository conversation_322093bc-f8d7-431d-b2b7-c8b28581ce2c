import { useState, useEffect, useCallback } from 'react';
import { buildApiUrl } from '../config';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  Alert,
  Snackbar,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  InputAdornment
} from '@mui/material';
import ScaleIcon from '@mui/icons-material/Scale';

const PaperWeightCalculator = () => {
  // Estados
  const [papers, setPapers] = useState([]);
  const [formData, setFormData] = useState({
    paper_id: '',
    sheets: 1000,
    width_mm: 700,
    height_mm: 1000,
    weight_gsm: 90
  });
  const [result, setResult] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [useCustomValues, setUseCustomValues] = useState(false);

  // Cargar la lista de papeles
  const fetchPapers = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/papers/'));
      if (!response.ok) throw new Error('Error al obtener el catálogo de papeles');
      const data = await response.json();
      setPapers(data);
    } catch (err) {
      setSnackbar({
        open: true,
        message: err.message,
        severity: 'error'
      });
    }
  }, []);

  useEffect(() => {
    fetchPapers();
  }, [fetchPapers]);

  // Manejar cambios en el formulario
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Manejar cambio de modo (papel seleccionado o valores personalizados)
  const handleModeChange = (e) => {
    setUseCustomValues(e.target.value === 'custom');
    // Si cambiamos a modo personalizado, limpiar el paper_id
    if (e.target.value === 'custom') {
      setFormData(prev => ({
        ...prev,
        paper_id: ''
      }));
    }
  };

  // Calcular peso del papel
  const handleCalculate = async () => {
    try {
      // Validar datos
      if (!useCustomValues && !formData.paper_id) {
        throw new Error('Debe seleccionar un papel');
      }
      if (useCustomValues && (!formData.width_mm || !formData.height_mm || !formData.weight_gsm)) {
        throw new Error('Debe proporcionar dimensiones y gramaje del papel');
      }
      if (formData.sheets < 1) {
        throw new Error('El número de pliegos debe ser al menos 1');
      }

      // Preparar datos para la solicitud
      const requestData = {
        sheets: parseInt(formData.sheets)
      };

      // Añadir paper_id o dimensiones según el modo
      if (!useCustomValues) {
        requestData.paper_id = formData.paper_id;
      } else {
        requestData.width_mm = parseFloat(formData.width_mm);
        requestData.height_mm = parseFloat(formData.height_mm);
        requestData.weight_gsm = parseFloat(formData.weight_gsm);
      }

      // Enviar solicitud
      const response = await fetch(buildApiUrl('/calcular-peso-papel'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al calcular el peso del papel');
      }

      // Procesar resultado
      const data = await response.json();
      setResult(data);
      setSnackbar({
        open: true,
        message: 'Cálculo realizado correctamente',
        severity: 'success'
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: err.message,
        severity: 'error'
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Obtener el papel seleccionado
  const selectedPaper = papers.find(p => p.product_id === formData.paper_id);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Calculadora de Peso de Papel
      </Typography>
      <Typography variant="body1" paragraph>
        Esta herramienta calcula el peso total de un número de pliegos de papel basado en sus dimensiones y gramaje.
      </Typography>

      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Modo de cálculo</InputLabel>
              <Select
                value={useCustomValues ? 'custom' : 'paper'}
                onChange={handleModeChange}
                label="Modo de cálculo"
              >
                <MenuItem value="paper">Seleccionar papel del catálogo</MenuItem>
                <MenuItem value="custom">Usar valores personalizados</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {!useCustomValues ? (
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Papel</InputLabel>
                <Select
                  name="paper_id"
                  value={formData.paper_id}
                  onChange={handleInputChange}
                  label="Papel"
                >
                  {papers.map(paper => (
                    <MenuItem key={paper.product_id} value={paper.product_id}>
                      {paper.descriptive_name} ({paper.dimension_width}×{paper.dimension_height}mm, {paper.weight}g)
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          ) : (
            <>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Ancho del pliego (mm)"
                  name="width_mm"
                  type="number"
                  value={formData.width_mm}
                  onChange={handleInputChange}
                  required
                  InputProps={{
                    inputProps: { min: 1 },
                    endAdornment: <InputAdornment position="end">mm</InputAdornment>
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Alto del pliego (mm)"
                  name="height_mm"
                  type="number"
                  value={formData.height_mm}
                  onChange={handleInputChange}
                  required
                  InputProps={{
                    inputProps: { min: 1 },
                    endAdornment: <InputAdornment position="end">mm</InputAdornment>
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Gramaje (g/m²)"
                  name="weight_gsm"
                  type="number"
                  value={formData.weight_gsm}
                  onChange={handleInputChange}
                  required
                  InputProps={{
                    inputProps: { min: 1 },
                    endAdornment: <InputAdornment position="end">g/m²</InputAdornment>
                  }}
                />
              </Grid>
            </>
          )}

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Número de pliegos"
              name="sheets"
              type="number"
              value={formData.sheets}
              onChange={handleInputChange}
              required
              InputProps={{
                inputProps: { min: 1 }
              }}
              helperText="Cantidad total de pliegos"
            />
          </Grid>

          <Grid item xs={12}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<ScaleIcon />}
              onClick={handleCalculate}
              fullWidth
              size="large"
              sx={{ mt: 2 }}
            >
              Calcular Peso
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {selectedPaper && !useCustomValues && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Información del papel seleccionado
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2">
                  <strong>Nombre:</strong> {selectedPaper.descriptive_name}
                </Typography>
                <Typography variant="body2">
                  <strong>Dimensiones:</strong> {selectedPaper.dimension_width} × {selectedPaper.dimension_height} mm
                </Typography>
                <Typography variant="body2">
                  <strong>Gramaje:</strong> {selectedPaper.weight} g/m²
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2">
                  <strong>Fabricante:</strong> {selectedPaper.manufacturer}
                </Typography>
                <Typography variant="body2">
                  <strong>En stock:</strong> {selectedPaper.inStock ? 'Sí' : 'No'}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {result && (
        <Paper elevation={3} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Resultado del cálculo
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <TableContainer component={Paper} variant="outlined" sx={{ mb: 3 }}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: 'primary.main' }}>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Concepto</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Valor</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {result.paper_name && (
                  <TableRow>
                    <TableCell>Papel</TableCell>
                    <TableCell>{result.paper_name}</TableCell>
                  </TableRow>
                )}
                <TableRow>
                  <TableCell>Dimensiones</TableCell>
                  <TableCell>{result.width_mm} × {result.height_mm} mm</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Gramaje</TableCell>
                  <TableCell>{result.weight_gsm} g/m²</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Número de pliegos</TableCell>
                  <TableCell>{result.sheets.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Peso por pliego</TableCell>
                  <TableCell>{result.weight_per_sheet_g.toFixed(2)} g</TableCell>
                </TableRow>
                <TableRow sx={{ backgroundColor: 'action.hover' }}>
                  <TableCell sx={{ fontWeight: 'bold' }}>Peso total</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>{result.total_weight_kg.toFixed(2)} kg</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
          
          <Alert severity="info" sx={{ mb: 2 }}>
            {result.sheets.toLocaleString()} pliegos de papel de {result.weight_gsm} g/m² con dimensiones {result.width_mm} × {result.height_mm} mm
            pesan un total de <strong>{result.total_weight_kg.toFixed(2)} kg</strong>.
          </Alert>
        </Paper>
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PaperWeightCalculator;
