import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { buildApiUrl } from '../config';
import { useAuth } from '../contexts/AuthContext';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Grid,
  Chip,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  TextField,
  IconButton,
  Tooltip,
  Link
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import ClientInfoModal from './BudgetForm/ClientInfoModal';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import SendIcon from '@mui/icons-material/Send';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import JDFExternalService from '../services/JDFExternalService';
import ConfigService from '../services/ConfigService';

/**
 * Componente para mostrar los detalles de un presupuesto en un diálogo
 */
// Los nombres de papel y máquina se obtienen directamente del presupuesto
// No se necesitan funciones de mapeo estático

const BudgetDetailsDialog = ({
  open,
  budget,
  onClose,
  onGeneratePdf,
  onSendToXMF,
  onStatusChange,
  showSnackbar
}) => {
  const { token } = useAuth();
  const [sendingJdf, setSendingJdf] = useState(false);
  const [config, setConfig] = useState(null);
  const [loading, setLoading] = useState(false);
  const [clientData, setClientData] = useState(null);
  const [editingDiscount, setEditingDiscount] = useState(false);
  const [discountValue, setDiscountValue] = useState(0);
  const [savingDiscount, setSavingDiscount] = useState(false);
  const [clientModalOpen, setClientModalOpen] = useState(false);
  const [updateCounter, setUpdateCounter] = useState(0);

  // Nota: Esta función fue eliminada porque no se estaba utilizando
  // Si se necesita calcular el envío en el futuro, se puede reimplementar

  // Función para iniciar la edición del descuento
  const handleEditDiscount = () => {
    setDiscountValue(clientData?.discount_percentage || 0);
    setEditingDiscount(true);
  };

  // Función para guardar el descuento actualizado
  const handleSaveDiscount = async () => {
    if (!budget?.budget_id) return;

    try {
      setSavingDiscount(true);
      showSnackbar('Actualizando descuento...', 'info');

      // Obtener el presupuesto actual
      const getBudgetResponse = await fetch(buildApiUrl(`/budgets/${budget.budget_id}`), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!getBudgetResponse.ok) {
        throw new Error(`Error al obtener presupuesto: ${getBudgetResponse.statusText}`);
      }

      const currentBudget = await getBudgetResponse.json();

      // Crear una copia profunda del presupuesto para modificarlo
      const updatedBudget = JSON.parse(JSON.stringify(currentBudget));

      // Asegurarse de que client_data existe
      if (!updatedBudget.client_data) {
        updatedBudget.client_data = {};
      }

      // Actualizar el descuento
      const newDiscountValue = parseFloat(discountValue);
      updatedBudget.client_data.discount_percentage = newDiscountValue;

      // Enviar el presupuesto actualizado
      const response = await fetch(buildApiUrl(`/budgets/${budget.budget_id}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updatedBudget),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error del servidor:', errorText);
        throw new Error(`Error al actualizar descuento: ${response.statusText}. ${errorText}`);
      }

      // Obtener el presupuesto actualizado
      const responseData = await response.json();

      // Actualizar los datos del cliente en el estado local
      if (responseData.client_data) {
        setClientData(responseData.client_data);

        // Actualizar también el presupuesto local
        if (budget) {
          budget.client_data = responseData.client_data;
        }
      } else {
        // Si no hay client_data en la respuesta, actualizar manualmente
        const updatedClientData = clientData ? { ...clientData } : {};
        updatedClientData.discount_percentage = newDiscountValue;

        // Actualizar el estado con la nueva copia
        setClientData(updatedClientData);

        // Actualizar también los datos en el presupuesto
        if (budget) {
          if (budget.client_data) {
            budget.client_data = { ...budget.client_data, discount_percentage: newDiscountValue };
          } else {
            budget.client_data = { ...updatedClientData };
          }
        }
      }

      // Forzar actualización de la UI
      setUpdateCounter(prev => prev + 1);
      setEditingDiscount(false);
      showSnackbar('Descuento actualizado correctamente', 'success');
    } catch (error) {
      console.error('Error al actualizar descuento:', error);
      showSnackbar(`Error al actualizar descuento: ${error.message}. Actualizando localmente.`, 'warning');

      // Actualizar localmente si falla la llamada al servidor
      console.log('Actualizando localmente debido a error');
      const newDiscountValue = parseFloat(discountValue);

      // Crear una copia del objeto clientData para forzar la actualización del estado
      const updatedClientData = clientData ? { ...clientData } : {};
      updatedClientData.discount_percentage = newDiscountValue;
      console.log('Actualizando clientData de', clientData?.discount_percentage, 'a', newDiscountValue);

      // Actualizar el estado con la nueva copia
      setClientData(updatedClientData);

      // Actualizar también los datos en el presupuesto
      if (budget) {
        if (budget.client_data) {
          console.log('Actualizando budget.client_data de', budget.client_data.discount_percentage, 'a', newDiscountValue);
          budget.client_data = { ...budget.client_data, discount_percentage: newDiscountValue };
        } else {
          console.log('Creando budget.client_data con descuento', newDiscountValue);
          budget.client_data = { ...updatedClientData };
        }
      }

      // Forzar actualización de la UI
      setUpdateCounter(prev => prev + 1);
      setEditingDiscount(false);
    } finally {
      setSavingDiscount(false);
    }
  };

  // Función para cancelar la edición del descuento
  const handleCancelEditDiscount = () => {
    setEditingDiscount(false);
  };

  // Función para cambiar el estado del presupuesto a "Aprobado"
  const [approvingBudget, setApprovingBudget] = useState(false);

  const handleApproveBudget = async () => {
    if (!budget?.budget_id) return;

    try {
      setApprovingBudget(true);
      showSnackbar('Cambiando estado del presupuesto a Aprobado...', 'info');

      // Crear una copia del presupuesto con el nuevo estado
      const updatedBudget = {
        ...budget,
        status: 'Aprobado'
      };

      // Enviar la actualización a la API usando el mismo endpoint que en BudgetList
      const response = await fetch(buildApiUrl(`/budgets/${budget.budget_id}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updatedBudget)
      });

      if (!response.ok) {
        throw new Error(`Error al cambiar el estado: ${response.statusText}`);
      }

      // Actualizar el estado local del presupuesto
      const data = await response.json();
      Object.assign(budget, data); // Actualizar el objeto budget con los datos recibidos

      showSnackbar('Presupuesto aprobado correctamente', 'success');

      // Forzar actualización de la UI
      setUpdateCounter(prev => prev + 1);

      // Notificar al componente padre si es necesario
      if (onStatusChange) onStatusChange(budget.budget_id, 'Aprobado');
    } catch (error) {
      console.error('Error al aprobar el presupuesto:', error);
      showSnackbar(`Error al aprobar el presupuesto: ${error.message}`, 'error');
    } finally {
      setApprovingBudget(false);
    }
  };

  // Función para enviar el JSON_OT al servidor externo
  const handleSendToXMF = async () => {
    if (!budget?.budget_id) return;

    try {
      setSendingJdf(true);
      showSnackbar('Enviando orden de trabajo al servidor externo...', 'info');

      const response = await JDFExternalService.sendJDFToExternalServer(budget.budget_id);
      showSnackbar(response.message || 'Orden de trabajo enviada correctamente al servidor externo', 'success');

      // Cerrar el diálogo y notificar al componente padre
      if (onSendToXMF) onSendToXMF(budget.budget_id);
    } catch (error) {
      console.error('Error al enviar orden de trabajo al servidor externo:', error);
      showSnackbar(`Error al enviar orden: ${error.message}`, 'error');
    } finally {
      setSendingJdf(false);
    }
  };

  // Cargar la configuración del sistema al abrir el diálogo
  useEffect(() => {
    if (open && budget) {
      const fetchConfig = async () => {
        try {
          setLoading(true);
          const configData = await ConfigService.getConfig();
          setConfig(configData);

          // Obtener los datos del cliente
          // Primero verificamos si ya tenemos los datos del cliente en el presupuesto
          if (budget.client_data) {
            setClientData(budget.client_data);
            // Inicializar el valor del descuento
            setDiscountValue(budget.client_data.discount_percentage || 0);
          }
          // Si no, los obtenemos de la API
          else if (budget.client_id) {
            const clientResponse = await fetch(buildApiUrl(`/clients/${budget.client_id}`), {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });
            if (clientResponse.ok) {
              const clientData = await clientResponse.json();
              setClientData(clientData);
              // Inicializar el valor del descuento
              setDiscountValue(clientData.discount_percentage || 0);
            }
          }
        } catch (error) {
          console.error('Error al cargar la configuración:', error);
          showSnackbar('Error al cargar la configuración', 'error');
        } finally {
          setLoading(false);
        }
      };

      fetchConfig();
    }
  }, [open, budget, showSnackbar, token]);

  // Efecto para forzar la actualización cuando cambia el descuento
  useEffect(() => {
    if (clientData) {
      setUpdateCounter(prev => prev + 1);
    }
  }, [clientData, clientData?.discount_percentage]);

  // Depurar la estructura del cliente y el país
  useEffect(() => {
    if (budget) {
      console.log('Estructura del presupuesto:', budget);
      console.log('Información del cliente:', budget.client_data);
      
      if (budget.client_data?.company?.address) {
        console.log('País del cliente:', budget.client_data.company.address.country);
      }
      
      console.log('Información de envío:', budget.costs?.shipping);
      if (budget.costs?.shipping) {
        console.log('País en shipping:', budget.costs.shipping.country);
      }
    }
  }, [budget]);

  // Si no hay presupuesto, no mostrar nada
  if (!budget) return null;
  
  // Calcular el costo total del presupuesto
  const calculateTotalCost = () => {
    let total = 0;

    // Sumar costos de partes con el costo de máquina correcto
    if (budget.parts && budget.parts.length > 0) {
      total += budget.parts.reduce((sum, part) => {
        // Calcular el costo de máquina correcto
        let machineCost = part.machine_cost || 0;

        // Si tenemos los datos necesarios, calcular el costo correcto
        if (part.sheet_calculation?.total_time_hours && part.machine_data?.hourly_cost) {
          const hourlyRate = part.machine_data.hourly_cost;
          const timeHours = part.sheet_calculation.total_time_hours;

          // Calcular el CFA (Costo Fijo de Arranque)
          // Usar el valor exacto de cfa_percentage, incluso si es 0
          const cfaPercentage = part.machine_data.cfa_percentage !== undefined ? part.machine_data.cfa_percentage : 0;
          const cfaCost = (hourlyRate * cfaPercentage / 100);

          // Calcular el costo de impresión
          const printingCost = hourlyRate * timeHours;

          // Calcular el costo total de máquina
          machineCost = cfaCost + printingCost;
        } else if (part.sheet_calculation?.cfa_cost && part.sheet_calculation?.printing_cost) {
          // Si no tenemos los datos para calcular, usar los valores del cálculo
          machineCost = part.sheet_calculation.cfa_cost + part.sheet_calculation.printing_cost;
        }

        // Calcular el costo total de la parte
        const partTotal = (part.total_cost || 0) - (part.machine_cost || 0) + machineCost;

        return sum + partTotal;
      }, 0);
    }

    // Sumar costos de procesos
    if (budget.process_total_cost) {
      total += budget.process_total_cost;
    } else if (budget.process_costs && budget.process_costs.length > 0) {
      total += budget.process_costs.reduce((sum, process) => sum + (process.total_cost || 0), 0);
    }

    return total;
  };

  // Calcular el precio de lista (costo + porcentaje de beneficio)
  const calculateListPrice = (cost) => {
    if (!config) return cost;
    const profitPercentage = config.profit_percentage || 30; // Valor por defecto: 30%
    return cost * (1 + profitPercentage / 100);
  };

  // Calcular el PVP (precio de lista - descuento del cliente)
  const calculatePVP = (listPrice) => {
    if (!clientData) return listPrice;
    const discountPercentage = clientData.discount_percentage || 0;
    // Usar updateCounter para forzar la actualización cuando cambia el descuento
    // eslint-disable-next-line no-unused-vars
    const _ = updateCounter; // Esto fuerza a React a recalcular cuando updateCounter cambia
    return listPrice * (1 - discountPercentage / 100);
  };



  // Obtener el total de costos
  const totalCost = calculateTotalCost();
  // Calcular el precio de lista total
  const totalListPrice = calculateListPrice(totalCost);
  // Calcular el PVP total
  const totalPVP = calculatePVP(totalListPrice);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      aria-labelledby="budget-details-dialog-title"
    >
      <DialogTitle id="budget-details-dialog-title">
        <Grid container alignItems="center" spacing={2}>
          <Grid item xs>
            <Typography variant="h6">
              Detalles del Presupuesto
            </Typography>
          </Grid>
          <Grid item>
            <Chip
              label={budget.ot_number || 'Sin OT'}
              color="primary"
              variant="outlined"
              sx={{ fontWeight: 'bold' }}
            />
          </Grid>
          <Grid item>
            <Chip
              label={budget.status}
              color={
                budget.status === 'Pendiente' ? 'warning' :
                budget.status === 'Aprobado' ? 'success' :
                budget.status === 'Rechazado' ? 'error' :
                budget.status === 'Completado' ? 'info' :
                budget.status === 'Actualizado' ? 'secondary' : 'default'
              }
              sx={{ fontWeight: 'bold' }}
            />
          </Grid>
        </Grid>
      </DialogTitle>

      <DialogContent dividers>
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
            <CircularProgress size={24} />
          </Box>
        )}
        {/* Información general */}
        <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: 'background.paper' }}>
          <Typography variant="subtitle1" gutterBottom fontWeight="bold">
            Información General
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="body2">
                  <strong>Cliente:</strong>{' '}
                  {clientData?.company?.name || budget.client_data?.company?.name || 'No especificado'}
                </Typography>
                <IconButton 
                  size="small" 
                  color="primary" 
                  onClick={() => setClientModalOpen(true)}
                  sx={{ ml: 1, p: 0.5 }}
                  aria-label="Ver información detallada del cliente"
                >
                  <InfoOutlinedIcon fontSize="small" />
                </IconButton>
              </Box>
              <Typography variant="body2"><strong>País:</strong> {clientData?.company?.address?.country || budget.client_data?.company?.address?.country || ''}</Typography>
              
              {/* Modal de información detallada del cliente */}
              <ClientInfoModal 
                open={clientModalOpen} 
                onClose={() => setClientModalOpen(false)} 
                client={clientData || budget.client_data} 
              />

              {/* Campo de descuento editable */}
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {editingDiscount ? (
                  <>
                    <TextField
                      label="Descuento (%)"
                      type="number"
                      size="small"
                      value={discountValue}
                      onChange={(e) => setDiscountValue(e.target.value)}
                      InputProps={{
                        inputProps: { min: 0, max: 100, step: 0.1 }
                      }}
                      sx={{ width: 120, mr: 1 }}
                    />
                    <Tooltip title="Guardar">
                      <IconButton
                        color="primary"
                        onClick={handleSaveDiscount}
                        disabled={savingDiscount}
                        size="small"
                      >
                        <SaveIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Button
                      variant="text"
                      size="small"
                      onClick={handleCancelEditDiscount}
                      disabled={savingDiscount}
                    >
                      Cancelar
                    </Button>
                    {savingDiscount && <CircularProgress size={20} sx={{ ml: 1 }} />}
                  </>
                ) : (
                  <>
                    <Typography variant="body2">
                      <strong>Descuento:</strong> {clientData?.discount_percentage || 0}%
                    </Typography>
                    <Tooltip title="Editar descuento">
                      <IconButton
                        color="primary"
                        onClick={handleEditDiscount}
                        size="small"
                        sx={{ ml: 1 }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </>
                )}
              </Box>
              <Typography variant="body2"><strong>Tipo de trabajo:</strong> {budget.job_type || 'No especificado'}</Typography>
              <Typography variant="body2"><strong>Cantidad:</strong> {budget.quantity || 'No especificado'}</Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2"><strong>Fecha de creación:</strong> {new Date(budget.created_at).toLocaleDateString()}</Typography>
              <Typography variant="body2"><strong>Creado por:</strong> {budget.created_by?.username || 'No disponible'}</Typography>
              <Typography variant="body2"><strong>Última actualización:</strong> {new Date(budget.updated_at).toLocaleDateString()}</Typography>
              <Typography variant="body2"><strong>Actualizado por:</strong> {budget.updated_by?.username || 'No disponible'}</Typography>
              <Typography variant="body2"><strong>PDF:</strong> {budget.pdf_filename || 'No disponible'}</Typography>
            </Grid>
          </Grid>
        </Paper>

        {/* Descripción */}
        {budget.description && (
          <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: 'background.paper' }}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Descripción
            </Typography>
            <Typography variant="body2" style={{ whiteSpace: 'pre-line' }}>
              {budget.description}
            </Typography>
          </Paper>
        )}

        {/* Especificaciones */}
        {budget.parts && budget.parts.length > 0 && (
          <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: 'background.paper' }}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Especificaciones
            </Typography>
            {budget.parts.map((part, index) => (
              <Box key={part.part_id || index} sx={{ mb: index < budget.parts.length - 1 ? 3 : 0 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Parte {index + 1}: {part.name}
                  </Typography>
                  {/* Etiqueta de tipo de máquina */}
                  <Chip
                    label={part.machine_type || part.machine_data?.type || part.sheet_calculation?.machine_data?.type || 'Sin tipo'}
                    size="small"
                    color={(part.machine_type || part.machine_data?.type || part.sheet_calculation?.machine_data?.type) === 'Digital' ? 'info' : 'error'}
                    sx={{ height: 20, fontSize: '0.7rem', fontWeight: 'bold' }}
                  />
                </Box>
                
                {/* Tabla de especificaciones */}
                <Box sx={{ border: '1px solid #e0e0e0', borderRadius: '4px', p: 2, mb: 1 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2"><strong>Tamaño:</strong> {part.page_size?.width || 0}mm x {part.page_size?.height || 0}mm</Typography>
                      <Typography variant="body2"><strong>Páginas:</strong> {part.page_count || 0}</Typography>
                      <Typography variant="body2">
                        <strong>Papel:</strong> {part.paper_data?.descriptive_name || part.sheet_calculation?.paper_name || 'No especificado'}
                      </Typography>
                      <Typography variant="body2"><strong>Total de pliegos:</strong> {part.sheet_calculation?.total_sheets || 'No especificado'}</Typography>
                      {/* Total de clicks para digital */}
                      {(part.machine_type === 'Digital' || part.machine_data?.type === 'Digital') && (
                        <Typography variant="body2"><strong>Total de clicks:</strong> {part.sheet_calculation?.total_clicks || 'No especificado'}</Typography>
                      )}
                      {/* Total de planchas para offset */}
                      {(part.machine_type === 'Offset' || part.machine_data?.type === 'Offset') && (
                        <Typography variant="body2"><strong>Total de planchas:</strong> {part.sheet_calculation?.total_plates || part.sheet_calculation?.mejor_combinacion?.total_planchas || 'No especificado'}</Typography>
                      )}
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2">
                        <strong>Máquina:</strong> {part.sheet_calculation?.machine_name || part.machine_data?.name || part.sheet_calculation?.machine_data?.name || 'No especificada'}
                      </Typography>
                      <Typography variant="body2"><strong>Tiempo de impresión:</strong> {part.sheet_calculation?.machine_data?.total_time_hours ? `${part.sheet_calculation.machine_data.total_time_hours.toFixed(2)} horas` : 'No especificado'}</Typography>
                      <Typography variant="body2"><strong>Colores:</strong> {
                        part.machine_type === 'Digital' || part.machine_data?.type === 'Digital' ?
                          (part.sheet_calculation?.is_color && part.sheet_calculation?.is_duplex ? '4/4' :
                           !part.sheet_calculation?.is_color && part.sheet_calculation?.is_duplex ? '1/1' :
                           part.sheet_calculation?.is_color && !part.sheet_calculation?.is_duplex ? '4/0' :
                           '1/0') :
                          (part.color_config ? `${part.color_config.frontColors}/${part.color_config.backColors}` : 
                           (part.sheet_calculation ? `${part.sheet_calculation.colors_front || 0}/${part.sheet_calculation.colors_back || 0}` : 'No especificado'))
                      }</Typography>
                      <Typography variant="body2"><strong>Ensamblado:</strong> {part.assembly_order || 'None'}</Typography>
                    </Grid>
                  </Grid>
                  <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic', color: 'text.secondary' }}>
                    {part.description || 'Sin descripción adicional'}
                  </Typography>
                </Box>
                
                {/* La información específica según el tipo de máquina ahora está dentro del rectángulo de especificaciones */}
              </Box>
            ))}
          </Paper>
        )}

        {/* La sección de Información de Envío fue eliminada porque esta información ya se muestra en la tabla de costos */}

        {/* Costos */}
        <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: 'background.paper' }}>
          <Typography variant="subtitle1" gutterBottom fontWeight="bold">
            Costos
          </Typography>
          <TableContainer component={Box}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell><strong>Concepto</strong></TableCell>
                  <TableCell align="right"><strong>Coste</strong></TableCell>
                  <TableCell align="right"><strong>Precio Lista</strong></TableCell>
                  <TableCell align="right"><strong>PVP</strong></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {/* Sección 1: Costos de Impresión - Encabezado eliminado */}
                {budget.costs?.parts && budget.costs.parts.length > 0 && (
                  <>
                    
                    {budget.costs.parts.map((partCost, index) => {
                      // Buscar la parte correspondiente para obtener datos adicionales
                      const part = budget.parts?.find(p => p.part_id === partCost.part_id) || {};
                      const isDigital = part.machine_type === 'Digital' || part.machine_data?.type === 'Digital';
                      
                      return (
                        <React.Fragment key={partCost.part_id || index}>
                          {/* Encabezado de la parte con fondo de color según tipo */}
                          <TableRow>
                            <TableCell colSpan={4} sx={{ 
                              bgcolor: isDigital ? '#ffebee' : '#e3f2fd', 
                              fontWeight: 'bold', 
                              pl: 3 
                            }}>
                              {partCost.name || `Parte ${index + 1}`} ({isDigital ? 'Digital' : 'Offset'})
                            </TableCell>
                          </TableRow>
                          {/* Papel */}
                          {partCost.costs?.paper > 0 && (
                            <TableRow>
                              <TableCell>
                                Papel ({part.paper_data?.descriptive_name || partCost.name || `Parte ${index + 1}`})
                                <Typography variant="caption" display="block" color="text.secondary">
                                  {isDigital && part.sheet_calculation?.total_sheets ? 
                                    `${part.sheet_calculation.total_sheets} pliegos impresos` : 
                                    part.sheet_calculation?.total_physical_sheets ? 
                                      `${part.sheet_calculation.total_physical_sheets} pliegos` : 
                                      ''
                                  }
                                  {part.sheet_calculation?.paper_weight_kg && (
                                    <span> ({part.sheet_calculation.paper_weight_kg.toFixed(2)} kg)</span>
                                  )}
                                </Typography>

                              </TableCell>
                              <TableCell align="right">{partCost.costs.paper.toFixed(2)}€</TableCell>
                              <TableCell align="right">{calculateListPrice(partCost.costs.paper).toFixed(2)}€</TableCell>
                              <TableCell align="right">{calculatePVP(calculateListPrice(partCost.costs.paper)).toFixed(2)}€</TableCell>
                            </TableRow>
                          )}
                          
                          {/* Máquina */}
                          {partCost.costs?.machine > 0 && (
                            <TableRow>
                              <TableCell>
                                Impresión ({part.machine_data?.name || partCost.name || `Parte ${index + 1}`})
                                {part.sheet_calculation?.total_time_minutes && (
                                  <Typography variant="caption" display="block" color="text.secondary">
                                    Tiempo: {part.sheet_calculation.total_time_minutes} minutos
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell align="right">{partCost.costs.machine.toFixed(2)}€</TableCell>
                              <TableCell align="right">{calculateListPrice(partCost.costs.machine).toFixed(2)}€</TableCell>
                              <TableCell align="right">{calculatePVP(calculateListPrice(partCost.costs.machine)).toFixed(2)}€</TableCell>
                            </TableRow>
                          )}
                          
                          {/* Para máquinas digitales */}
                          {isDigital && (
                            <>
                              {/* Clicks para digital */}
                              {partCost.costs?.click > 0 && (
                                <TableRow>
                                  <TableCell>
                                    Clicks
                                    {part.sheet_calculation?.clicks_data?.total_clicks > 0 && (
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        {part.sheet_calculation.clicks_data.total_clicks} clicks
                                        {part.sheet_calculation.clicks_data.click_unit_cost && (
                                          <span> ({part.sheet_calculation.clicks_data.click_unit_cost.toFixed(4)}€/click)</span>
                                        )}

                                      </Typography>
                                    )}
                                    {!part.sheet_calculation?.clicks_data?.total_clicks && part.clicks_data?.total_clicks > 0 && (
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        {part.clicks_data.total_clicks} clicks
                                        {part.clicks_data.click_unit_cost && (
                                          <span> ({part.clicks_data.click_unit_cost.toFixed(4)}€/click)</span>
                                        )}

                                      </Typography>
                                    )}
                                  </TableCell>
                                  <TableCell align="right">{partCost.costs.click.toFixed(2)}€</TableCell>
                                  <TableCell align="right">{calculateListPrice(partCost.costs.click).toFixed(2)}€</TableCell>
                                  <TableCell align="right">{calculatePVP(calculateListPrice(partCost.costs.click)).toFixed(2)}€</TableCell>
                                </TableRow>
                              )}
                            </>
                          )}
                          
                          {/* Para máquinas offset */}
                          {!isDigital && (
                            <>
                              {/* Planchas para offset - Solo mostrar en parte interior */}
                              {partCost.costs?.plates > 0 && partCost.name?.toLowerCase().includes('interior') && (
                                <TableRow>
                                  <TableCell>
                                    Planchas
                                    {part.sheet_calculation?.mejor_combinacion?.total_planchas && (
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        {part.sheet_calculation.mejor_combinacion.total_planchas} planchas
                                      </Typography>
                                    )}
                                  </TableCell>
                                  <TableCell align="right">{partCost.costs.plates.toFixed(2)}€</TableCell>
                                  <TableCell align="right">{calculateListPrice(partCost.costs.plates).toFixed(2)}€</TableCell>
                                  <TableCell align="right">{calculatePVP(calculateListPrice(partCost.costs.plates)).toFixed(2)}€</TableCell>
                                </TableRow>
                              )}
                              
                              {/* Tinta para offset - No mostrar si hay clicks */}
                              {partCost.costs?.ink > 0 && partCost.costs?.click <= 0 && (
                                <TableRow>
                                  <TableCell>
                                    Tinta
                                    {part.sheet_calculation?.ink_weight_kg && (
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        {part.sheet_calculation.ink_weight_kg.toFixed(2)} kg
                                      </Typography>
                                    )}
                                  </TableCell>
                                  <TableCell align="right">{partCost.costs.ink.toFixed(2)}€</TableCell>
                                  <TableCell align="right">{calculateListPrice(partCost.costs.ink).toFixed(2)}€</TableCell>
                                  <TableCell align="right">{calculatePVP(calculateListPrice(partCost.costs.ink)).toFixed(2)}€</TableCell>
                                </TableRow>
                              )}
                              
                              {/* Maculatura para offset */}
                              {partCost.costs?.maculatura > 0 && (
                                <TableRow>
                                  <TableCell>
                                    Maculatura
                                    {part.sheet_calculation?.total_maculatura && (
                                      <Typography variant="caption" display="block" color="text.secondary">
                                        {part.sheet_calculation.total_maculatura} pliegos
                                      </Typography>
                                    )}
                                  </TableCell>
                                  <TableCell align="right">{partCost.costs.maculatura.toFixed(2)}€</TableCell>
                                  <TableCell align="right">{calculateListPrice(partCost.costs.maculatura).toFixed(2)}€</TableCell>
                                  <TableCell align="right">{calculatePVP(calculateListPrice(partCost.costs.maculatura)).toFixed(2)}€</TableCell>
                                </TableRow>
                              )}
                            </>
                          )}
                        </React.Fragment>
                      );
                    })}
                  </>
                )}

                {/* Sección 2: Costos de Acabados */}
                {(budget.costs?.processes || budget.process_costs) && (
                  <>
                    <TableRow>
                      <TableCell colSpan={4} sx={{ bgcolor: '#e8f5e9', fontWeight: 'bold' }}>
                        Costos de Acabados
                      </TableCell>
                    </TableRow>
                    
                    {/* Si tenemos la estructura costs.processes, la usamos */}
                    {budget.costs?.processes && budget.costs.processes.map((process, index) => (
                      <TableRow key={process.process_id || index}>
                        <TableCell>{process.name}</TableCell>
                        <TableCell align="right">{process.cost.toFixed(2)}€</TableCell>
                        <TableCell align="right">{calculateListPrice(process.cost).toFixed(2)}€</TableCell>
                        <TableCell align="right">{calculatePVP(calculateListPrice(process.cost)).toFixed(2)}€</TableCell>
                      </TableRow>
                    ))}
                    
                    {/* Si no tenemos la estructura costs.processes, usamos la antigua */}
                    {!budget.costs?.processes && budget.process_costs && budget.process_costs.map((process, index) => (
                      <TableRow key={process.process_id || index}>
                        <TableCell>{process.name} ({process.quantity} {process.unit_type})</TableCell>
                        <TableCell align="right">{process.total_cost.toFixed(2)}€</TableCell>
                        <TableCell align="right">{calculateListPrice(process.total_cost).toFixed(2)}€</TableCell>
                        <TableCell align="right">{calculatePVP(calculateListPrice(process.total_cost)).toFixed(2)}€</TableCell>
                      </TableRow>
                    ))}
                  </>
                )}

                {/* Sección 3: Costos de Envío */}
                {(budget.shipping || budget.costs?.shipping || budget.shipping_cost > 0) && (
                  <>
                    <TableRow>
                      <TableCell colSpan={4} sx={{ bgcolor: '#fff8e1', fontWeight: 'bold' }}>
                        Costos de Envío
                      </TableCell>
                    </TableRow>
                    
                    {/* Priorizar la estructura budget.costs.shipping que es la más actualizada */}
                    <TableRow>
                      <TableCell>
                        {/* Usar el peso de costs.shipping.weight_kg como prioridad, luego total_paper_weight_kg */}
                        Envío ({budget.costs?.shipping?.weight_kg || budget.total_paper_weight_kg || 0} kg)
                        <Typography variant="caption" display="block" color="text.secondary">
                          {/* Usar el país de costs.shipping.country como prioridad, luego el país del cliente */}
                          País: {budget.costs?.shipping?.country || budget.client_data?.company?.address?.country || ""}
                          {budget.costs?.shipping?.distance_factor && budget.costs.shipping.distance_factor !== 1 && (
                            <span> (Factor de distancia: {budget.costs.shipping.distance_factor})</span>
                          )}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        {/* Usar el costo de shipping.cost como prioridad, luego costs.shipping.cost, luego shipping_cost */}
                        {(budget.shipping?.cost || budget.costs?.shipping?.cost || budget.shipping_cost || 0).toFixed(2)}€
                      </TableCell>
                      <TableCell align="right">
                        {calculateListPrice(budget.shipping?.cost || budget.costs?.shipping?.cost || budget.shipping_cost || 0).toFixed(2)}€
                      </TableCell>
                      <TableCell align="right">
                        {calculatePVP(calculateListPrice(budget.shipping?.cost || budget.costs?.shipping?.cost || budget.shipping_cost || 0)).toFixed(2)}€
                      </TableCell>
                    </TableRow>
                  </>
                )}

                {/* Sección 4: Resumen de Costos */}
                <TableRow>
                  <TableCell colSpan={4} sx={{ bgcolor: '#ede7f6', fontWeight: 'bold' }}>
                    Resumen de Costos
                  </TableCell>
                </TableRow>
                
                {/* Subtotales si tenemos la estructura costs.summary */}
                {budget.costs?.summary && (
                  <>
                    <TableRow>
                      <TableCell sx={{ pl: 4, bgcolor: '#f3eef8' }}>Subtotal Impresión</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{budget.costs.summary.parts_total.toFixed(2)}€</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{calculateListPrice(budget.costs.summary.parts_total).toFixed(2)}€</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{calculatePVP(calculateListPrice(budget.costs.summary.parts_total)).toFixed(2)}€</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ pl: 4, bgcolor: '#f3eef8' }}>Subtotal Acabados</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{budget.costs.summary.processes_total.toFixed(2)}€</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{calculateListPrice(budget.costs.summary.processes_total).toFixed(2)}€</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{calculatePVP(calculateListPrice(budget.costs.summary.processes_total)).toFixed(2)}€</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ pl: 4, bgcolor: '#f3eef8' }}>Subtotal Envío</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{budget.costs.summary.shipping_total.toFixed(2)}€</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{calculateListPrice(budget.costs.summary.shipping_total).toFixed(2)}€</TableCell>
                      <TableCell align="right" sx={{ bgcolor: '#f3eef8' }}>{calculatePVP(calculateListPrice(budget.costs.summary.shipping_total)).toFixed(2)}€</TableCell>
                    </TableRow>
                  </>
                )}
                
                {/* Total */}
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', fontSize: '1.1rem', bgcolor: '#e0e0e0', borderTop: '2px solid #999' }}>TOTAL</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold', fontSize: '1.1rem', bgcolor: '#e0e0e0', borderTop: '2px solid #999' }}>
                    {budget.costs?.summary ? budget.costs.summary.total.toFixed(2) : (totalCost + (budget.shipping_cost || 0)).toFixed(2)}€
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold', fontSize: '1.1rem', bgcolor: '#e0e0e0', borderTop: '2px solid #999' }}>
                    {budget.costs?.summary ? calculateListPrice(budget.costs.summary.total).toFixed(2) : (totalListPrice + calculateListPrice(budget.shipping_cost || 0)).toFixed(2)}€
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold', fontSize: '1.1rem', bgcolor: '#e0e0e0', borderTop: '2px solid #999' }}>
                    {budget.costs?.summary ? calculatePVP(calculateListPrice(budget.costs.summary.total)).toFixed(2) : (totalPVP + calculatePVP(calculateListPrice(budget.shipping_cost || 0))).toFixed(2)}€
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </DialogContent>

      <DialogActions>
        {/* Botón para aprobar el presupuesto */}
        {budget.status !== 'Aprobado' && (
          <Button
            color="success"
            onClick={handleApproveBudget}
            startIcon={<CheckCircleIcon />}
            disabled={approvingBudget}
            variant="contained"
            sx={{ mr: 'auto' }}
          >
            {approvingBudget ? 'Aprobando...' : 'Aprobar Presupuesto'}
          </Button>
        )}
        <Button
          color="primary"
          onClick={() => onGeneratePdf && onGeneratePdf(budget.budget_id)}
          startIcon={<PictureAsPdfIcon />}
          variant="contained"
        >
          Generar PDF
        </Button>
        <Button
          color="secondary"
          onClick={handleSendToXMF}
          startIcon={<SendIcon />}
          disabled={sendingJdf}
          variant="contained"
        >
          Enviar a XMF
        </Button>
        <Button onClick={onClose} variant="outlined">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

BudgetDetailsDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  budget: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onGeneratePdf: PropTypes.func,
  onSendToXMF: PropTypes.func,
  onStatusChange: PropTypes.func,
  showSnackbar: PropTypes.func.isRequired
};

export default BudgetDetailsDialog;
