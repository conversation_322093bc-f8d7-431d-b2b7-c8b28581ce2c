# Etapa de construcción
FROM node:18 AS build

# Definir directorio de trabajo dentro del contenedor
WORKDIR /app

# Copiar solo los archivos necesarios para instalar dependencias
COPY package.json package-lock.json ./

# Instalar dependencias
RUN npm ci

# Copiar el código fuente
COPY . .

# Listar archivos para depuración
RUN ls -la /app/src/components
RUN ls -la /app/src/services

# Establecer variable de entorno para indicar que estamos en Docker
ENV VITE_DOCKER_ENV=true

# Construir la aplicación en modo producción
RUN npm run build

# Verificar que la carpeta `dist/` se haya creado
RUN ls -la /app/dist || echo "Error: No se creó la carpeta dist"

# Etapa de producción
FROM node:18-slim

WORKDIR /app

# Instalar `serve` para servir la aplicación
RUN npm install -g serve

# Copiar los archivos de construcción de la etapa anterior
COPY --from=build /app/dist ./dist

# Exponer el puerto 4005
EXPOSE 4005

# Servir la aplicación desde `dist/`
CMD ["serve", "-s", "dist", "-l", "4005"]
