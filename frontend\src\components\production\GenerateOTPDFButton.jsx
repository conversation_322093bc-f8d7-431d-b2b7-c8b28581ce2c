import React, { useState } from 'react';
import {
  Icon<PERSON>utton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  CircularProgress,
  Typography,
  Alert
} from '@mui/material';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { PDFViewer, PDFDownloadLink, Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import { buildApiUrl } from '../../config';

// Estilos para el PDF
const styles = StyleSheet.create({
  page: {
    padding: 30,
    backgroundColor: '#ffffff',
    fontFamily: 'Helvetica'
  },
  header: {
    flexDirection: 'row',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#112131',
    borderBottomStyle: 'solid',
    paddingBottom: 10,
    justifyContent: 'space-between'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#112131'
  },
  subtitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#112131',
    textDecoration: 'underline'
  },
  section: {
    marginBottom: 15
  },
  row: {
    flexDirection: 'row',
    marginBottom: 5
  },
  label: {
    width: 150,
    fontWeight: 'bold'
  },
  value: {
    flex: 1
  },
  table: {
    display: 'table',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#112131',
    marginBottom: 15
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#112131',
    borderBottomStyle: 'solid'
  },
  tableHeader: {
    backgroundColor: '#f0f0f0',
    fontWeight: 'bold'
  },
  tableCell: {
    padding: 5,
    borderRightWidth: 1,
    borderRightColor: '#112131',
    borderRightStyle: 'solid',
    fontSize: 10
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 10,
    color: '#666'
  },
  logo: {
    width: 100,
    height: 50,
    objectFit: 'contain'
  },
  companyInfo: {
    fontSize: 10,
    textAlign: 'right'
  },
  processTypeHeader: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 5,
    backgroundColor: '#f0f0f0',
    padding: 5,
    borderRadius: 3
  }
});

// Componente para el PDF de OT
const OTPDFDocument = ({ otData, clientData, budgetData }) => {
  // Verificar que tenemos los datos mínimos necesarios
  if (!otData || !otData.processes || otData.processes.length === 0) {
    return (
      <Document>
        <Page size="A4" style={styles.page}>
          <View style={styles.title}>
            <Text>Error: No se pudo cargar la información de la OT</Text>
          </View>
        </Page>
      </Document>
    );
  }

  // Función para formatear la fecha
  const formatDate = (dateString) => {
    if (!dateString) return 'No definida';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  };

  // Agrupar procesos por tipo
  const processesByType = otData.processes.reduce((acc, process) => {
    if (!acc[process.process_type]) {
      acc[process.process_type] = [];
    }
    acc[process.process_type].push(process);
    return acc;
  }, {});

  // Ordenar los tipos de procesos (Impresión primero, luego Acabado, etc.)
  const processTypes = Object.keys(processesByType).sort((a, b) => {
    const order = { 'Impresión': 1, 'Acabado': 2, 'Manipulado': 3, 'Envío': 4 };
    return (order[a] || 99) - (order[b] || 99);
  });

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Cabecera con logo y datos de la empresa */}
        <View style={styles.header}>
          <View>
            {/* Aquí iría el logo si lo tuvieras */}
            <Text style={{ fontSize: 20, fontWeight: 'bold' }}>TrikyPrinter</Text>
          </View>
          <View style={styles.companyInfo}>
            <Text>TrikyPrinter</Text>
            <Text>c/Costa Rica 11</Text>
            <Text>28016 Madrid</Text>
            <Text>Tel: +34 91 000 00 00</Text>
            <Text>Email: <EMAIL></Text>
          </View>
        </View>

        {/* Título del documento */}
        <View style={styles.title}>
          <Text>ORDEN DE TRABAJO: {otData.ot_id}</Text>
        </View>

        {/* Información general */}
        <View style={styles.section}>
          <Text style={styles.subtitle}>Información General</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Cliente:</Text>
            <Text style={styles.value}>{clientData?.company?.name || 'Cliente sin nombre'}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Presupuesto:</Text>
            <Text style={styles.value}>{otData.budget_id}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Descripción:</Text>
            <Text style={styles.value}>{budgetData?.description || 'Sin descripción'}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Fecha de aprobación:</Text>
            <Text style={styles.value}>{formatDate(otData.approval_date)}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Estado:</Text>
            <Text style={styles.value}>{otData.status}</Text>
          </View>
        </View>

        {/* Procesos agrupados por tipo */}
        <View style={styles.section}>
          <Text style={styles.subtitle}>Procesos de Producción</Text>

          {processTypes.map((processType) => (
            <View key={processType}>
              <Text style={styles.processTypeHeader}>{processType}</Text>
              <View style={styles.table}>
                <View style={[styles.tableRow, styles.tableHeader]}>
                  <Text style={[styles.tableCell, { width: '30%' }]}>Proceso</Text>
                  <Text style={[styles.tableCell, { width: '15%' }]}>Cantidad</Text>
                  <Text style={[styles.tableCell, { width: '15%' }]}>Máquina</Text>
                  <Text style={[styles.tableCell, { width: '20%' }]}>Fecha Inicio</Text>
                  <Text style={[styles.tableCell, { width: '20%', borderRightWidth: 0 }]}>Fecha Fin</Text>
                </View>

                {processesByType[processType].map((process) => (
                  <View key={process.process_id} style={styles.tableRow}>
                    <Text style={[styles.tableCell, { width: '30%' }]}>{process.name}</Text>
                    <Text style={[styles.tableCell, { width: '15%' }]}>{process.quantity}</Text>
                    <Text style={[styles.tableCell, { width: '15%' }]}>{process.machine_id || 'No asignada'}</Text>
                    <Text style={[styles.tableCell, { width: '20%' }]}>{formatDate(process.start_date)}</Text>
                    <Text style={[styles.tableCell, { width: '20%', borderRightWidth: 0 }]}>{formatDate(process.end_date)}</Text>
                  </View>
                ))}
              </View>

              {/* Detalles específicos para cada proceso */}
              {processesByType[processType].map((process) => (
                <View key={`detail-${process.process_id}`} style={{ marginBottom: 10 }}>
                  <Text style={{ fontWeight: 'bold', fontSize: 12 }}>{process.name}</Text>
                  <Text style={{ fontSize: 10, marginBottom: 5 }}>Descripción: {process.description}</Text>
                  <Text style={{ fontSize: 10 }}>Notas: {process.notes || 'Sin notas adicionales'}</Text>
                </View>
              ))}
            </View>
          ))}
        </View>

        {/* Información adicional del presupuesto si está disponible */}
        {budgetData && budgetData.parts && budgetData.parts.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.subtitle}>Especificaciones Técnicas</Text>

            {budgetData.parts.map((part, index) => (
              <View key={`part-${index}`} style={{ marginBottom: 10 }}>
                <Text style={{ fontWeight: 'bold', fontSize: 12 }}>{part.name}</Text>
                {part.paper_data && (
                  <View style={styles.row}>
                    <Text style={[styles.label, { fontSize: 10 }]}>Papel:</Text>
                    <Text style={[styles.value, { fontSize: 10 }]}>
                      {part.paper_data.name}, {part.paper_data.weight}g, {part.paper_data.size}
                    </Text>
                  </View>
                )}
                {part.machine_data && (
                  <View style={styles.row}>
                    <Text style={[styles.label, { fontSize: 10 }]}>Máquina:</Text>
                    <Text style={[styles.value, { fontSize: 10 }]}>{part.machine_data.name}</Text>
                  </View>
                )}
                {part.colors && (
                  <View style={styles.row}>
                    <Text style={[styles.label, { fontSize: 10 }]}>Colores:</Text>
                    <Text style={[styles.value, { fontSize: 10 }]}>{part.colors}</Text>
                  </View>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Pie de página */}
        <View style={styles.footer}>
          <Text>Documento generado el {formatDate(new Date().toISOString())}</Text>
          <Text>TrikyPrinter - c/Costa Rica 11, 28016 Madrid - CIF: B12345678</Text>
        </View>
      </Page>
    </Document>
  );
};

// Componente principal para el botón y el diálogo
const GenerateOTPDFButton = ({ otNumber, size = 'small', color = 'secondary' }) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [otData, setOtData] = useState(null);
  const [clientData, setClientData] = useState(null);
  const [budgetData, setBudgetData] = useState(null);
  const [isSaving, setIsSaving] = useState(false);

  // Función para abrir el diálogo y cargar los datos
  const handleOpen = async () => {
    setOpen(true);
    setLoading(true);
    setError(null);

    try {
      // Obtener el token de autenticación
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
      }

      // 1. Obtener todos los procesos de producción
      const productionResponse = await fetch(buildApiUrl('/production/'), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!productionResponse.ok) {
        throw new Error(`Error al cargar los datos de producción: ${productionResponse.statusText}`);
      }

      const allProcesses = await productionResponse.json();

      // Filtrar los procesos por OT
      const otProcesses = allProcesses.filter(process => process.ot_number === otNumber);

      if (otProcesses.length === 0) {
        throw new Error(`No se encontraron procesos para la OT: ${otNumber}`);
      }

      // Crear estructura de datos similar a la que esperaría del endpoint /production/ot/{ot_number}
      const firstProcess = otProcesses[0];
      const otDataResult = {
        ot_id: otNumber,
        budget_id: firstProcess.budget_id,
        client_id: firstProcess.client_id,
        approval_date: firstProcess.created_at,
        status: "Pendiente",
        processes: otProcesses
      };

      // Determinar el estado general de la OT
      if (otProcesses.every(p => p.status === "Completado")) {
        otDataResult.status = "Completado";
      } else if (otProcesses.some(p => p.status === "En Proceso")) {
        otDataResult.status = "En Proceso";
      } else if (otProcesses.some(p => p.status === "Cancelado")) {
        otDataResult.status = "Cancelado";
      }

      setOtData(otDataResult);

      // 2. Obtener los datos del cliente
      if (otDataResult.client_id) {
        const clientResponse = await fetch(buildApiUrl(`/clients/${otDataResult.client_id}`), {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (clientResponse.ok) {
          const clientDataResult = await clientResponse.json();
          setClientData(clientDataResult);
        } else {
          console.warn(`No se pudo cargar la información del cliente: ${clientResponse.statusText}`);
        }
      }

      // 3. Obtener los datos del presupuesto
      if (otDataResult.budget_id) {
        const budgetResponse = await fetch(buildApiUrl(`/budgets/${otDataResult.budget_id}`), {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (budgetResponse.ok) {
          const budgetDataResult = await budgetResponse.json();
          setBudgetData(budgetDataResult);
        } else {
          console.warn(`No se pudo cargar la información del presupuesto: ${budgetResponse.statusText}`);
        }
      }

    } catch (err) {
      console.error('Error al cargar los datos para el PDF:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Función para cerrar el diálogo
  const handleClose = () => {
    setOpen(false);
  };

  // Función para guardar el PDF en el servidor
  const handleSavePDF = async () => {
    try {
      setIsSaving(true);

      // Llamar al endpoint para guardar el PDF
      const response = await fetch(buildApiUrl(`/uploads/save-ot-pdf/${otNumber}`), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ot_data: otData,
          client_data: clientData,
          budget_data: budgetData
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error al guardar el PDF: ${errorText}`);
      }

      const result = await response.json();

      // Mostrar mensaje de éxito
      alert(`PDF guardado correctamente en: ${result.file_path}`);

    } catch (err) {
      console.error('Error al guardar el PDF:', err);
      alert(`Error al guardar el PDF: ${err.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <>
      <Tooltip title="Generar PDF de OT">
        <IconButton
          color={color}
          size={size}
          onClick={handleOpen}
        >
          <PictureAsPdfIcon fontSize={size} />
        </IconButton>
      </Tooltip>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="xl"
        fullWidth
      >
        <DialogTitle>
          Orden de Trabajo: {otNumber}
          {!loading && !error && otData && (
            <Button
              variant="contained"
              color="primary"
              style={{ position: 'absolute', right: '24px' }}
            >
              <PDFDownloadLink
                document={<OTPDFDocument otData={otData} clientData={clientData} budgetData={budgetData} />}
                fileName={`OT_${otNumber}.pdf`}
                style={{ textDecoration: 'none', color: 'white' }}
              >
                {({ loading: pdfLoading, error: pdfError }) => {
                  if (pdfError) {
                    console.error('Error al generar PDF para descargar:', pdfError);
                    return 'Error';
                  }
                  return pdfLoading ? 'Generando...' : 'Descargar PDF';
                }}
              </PDFDownloadLink>
            </Button>
          )}
        </DialogTitle>

        <DialogContent sx={{ p: 1 }}>
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
              <CircularProgress />
            </Box>
          ) : error ? (
            <Box p={3}>
              <Alert severity="error">
                <Typography>{error}</Typography>
              </Alert>
            </Box>
          ) : otData ? (
            <Box sx={{ width: '100%', height: 'calc(100vh - 150px)' }}>
              <PDFViewer width="100%" height="100%" style={{ border: 'none' }}>
                <OTPDFDocument otData={otData} clientData={clientData} budgetData={budgetData} />
              </PDFViewer>
            </Box>
          ) : (
            <Box p={3}>
              <Alert severity="warning">
                <Typography>No se encontraron datos para esta OT.</Typography>
              </Alert>
            </Box>
          )}
        </DialogContent>

        <DialogActions>
          {!loading && !error && otData && (
            <Button
              onClick={handleSavePDF}
              color="primary"
              disabled={isSaving}
              startIcon={isSaving ? <CircularProgress size={20} /> : null}
            >
              {isSaving ? 'Guardando...' : 'Guardar en Servidor'}
            </Button>
          )}
          <Button onClick={handleClose}>Cerrar</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default GenerateOTPDFButton;
