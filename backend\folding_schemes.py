from dataclasses import dataclass
from typing import List, Dict, Tuple
from enum import Enum

class FoldOrientation(Enum):
    VERTICAL = "vertical"
    HORIZONTAL = "horizontal"

@dataclass
class PagePosition:
    """Representa la posición de una página en el pliego"""
    page_number: int
    row: int
    col: int
    side: str  # "front" o "back"

@dataclass
class FoldStep:
    """Representa un paso en el proceso de plegado"""
    fold_type: FoldOrientation
    description: str

@dataclass
class FoldingScheme:
    """Representa un esquema de plegado completo"""
    name: str
    pages_per_sheet: int
    rows: int
    cols: int
    page_layout: Dict[str, List[List[int]]]
    folding_steps: List[FoldStep]
    min_margin: float = 10.0  # mm
    space_between_pages: float = 5.0  # mm
    
    def get_page_positions(self) -> List[PagePosition]:
        """Retorna una lista de todas las posiciones de página en el pliego"""
        positions = []
        for side in ["front", "back"]:
            for row in range(self.rows):
                for col in range(self.cols):
                    page_num = self.page_layout[side][row][col]
                    positions.append(PagePosition(page_num, row, col, side))
        return positions
    
    def calculate_dimensions(self, page_width: float, page_height: float, rows_multiplier: int = 1) -> Tuple[float, float]:
        """
        Calcula las dimensiones mínimas necesarias para el pliego.
        rows_multiplier: multiplicador para el número de filas (usado en tira/retira)
        """
        total_width = (self.cols * page_width) + ((self.cols - 1) * self.space_between_pages) + (2 * self.min_margin)
        total_height = (rows_multiplier * self.rows * page_height) + ((rows_multiplier * self.rows - 1) * self.space_between_pages) + (2 * self.min_margin)
        return total_width, total_height

# Definición de los esquemas de plegado
F4_1 = FoldingScheme(
    name="F4-1",
    pages_per_sheet=4,
    rows=1,
    cols=2,
    page_layout={
        "front": [[4, 1]],
        "back": [[2, 3]]
    },
    folding_steps=[
        FoldStep(FoldOrientation.VERTICAL, "Doblar verticalmente por el centro")
    ]
)

F6_2 = FoldingScheme(
    name="F6-2",
    pages_per_sheet=6,
    rows=1,
    cols=3,
    page_layout={
        "front": [[6, 1, 2]],
        "back": [[4, 3, 5]]
    },
    folding_steps=[
        FoldStep(FoldOrientation.VERTICAL, "Doblar los extremos hacia el centro (pliegue tipo puerta)")
    ]
)

F8_7 = FoldingScheme(
    name="F8-7",
    pages_per_sheet=8,
    rows=2,
    cols=2,
    page_layout={
        "front": [
            [8, 1],
            [7, 2]
        ],
        "back": [
            [6, 3],
            [5, 4]
        ]
    },
    folding_steps=[
        FoldStep(FoldOrientation.VERTICAL, "Doblar verticalmente por el centro"),
        FoldStep(FoldOrientation.HORIZONTAL, "Doblar horizontalmente por el centro")
    ]
)

F12_5 = FoldingScheme(
    name="F12-5",
    pages_per_sheet=12,
    rows=2,
    cols=3,
    page_layout={
        "front": [
            [12, 1, 2],
            [11, 6, 3]
        ],
        "back": [
            [10, 7, 4],
            [9, 8, 5]
        ]
    },
    folding_steps=[
        FoldStep(FoldOrientation.VERTICAL, "Doblar los extremos hacia el centro"),
        FoldStep(FoldOrientation.HORIZONTAL, "Doblar horizontalmente por el centro")
    ]
)

F16_7 = FoldingScheme(
    name="F16-7",
    pages_per_sheet=16,
    rows=2,
    cols=4,
    page_layout={
        "front": [
            [16, 1, 2, 15],
            [14, 3, 4, 13]
        ],
        "back": [
            [12, 5, 6, 11],
            [10, 7, 8, 9]
        ]
    },
    folding_steps=[
        FoldStep(FoldOrientation.VERTICAL, "Doblar verticalmente por el centro"),
        FoldStep(FoldOrientation.HORIZONTAL, "Doblar horizontalmente por el centro"),
        FoldStep(FoldOrientation.VERTICAL, "Doblar verticalmente por el centro nuevamente")
    ]
)

F32_7 = FoldingScheme(
    name="F32-7",
    pages_per_sheet=32,
    rows=4,
    cols=4,
    page_layout={
        "front": [
            [32, 1, 2, 31],
            [30, 3, 4, 29],
            [28, 5, 6, 27],
            [26, 7, 8, 25]
        ],
        "back": [
            [24, 9, 10, 23],
            [22, 11, 12, 21],
            [20, 13, 14, 19],
            [18, 15, 16, 17]
        ]
    },
    folding_steps=[
        FoldStep(FoldOrientation.VERTICAL, "Doblar verticalmente por el centro"),
        FoldStep(FoldOrientation.HORIZONTAL, "Doblar horizontalmente por el centro"),
        FoldStep(FoldOrientation.VERTICAL, "Doblar verticalmente por el centro nuevamente"),
        FoldStep(FoldOrientation.HORIZONTAL, "Doblar horizontalmente por el centro nuevamente")
    ]
)

# Diccionario con todos los esquemas disponibles
FOLDING_SCHEMES = {
    "F4-1": F4_1,
    "F6-2": F6_2,
    "F8-7": F8_7,
    "F12-5": F12_5,
    "F16-7": F16_7,
    "F32-7": F32_7
}

def get_scheme(name: str) -> FoldingScheme:
    """Obtiene un esquema de plegado por su nombre"""
    return FOLDING_SCHEMES.get(name)

def get_all_schemes() -> Dict[str, FoldingScheme]:
    """Retorna todos los esquemas de plegado disponibles"""
    return FOLDING_SCHEMES

def can_fit_scheme(scheme: FoldingScheme, 
                   page_width: float, page_height: float,
                   sheet_width: float, sheet_height: float) -> Tuple[bool, str, int, int]:
    """
    Verifica si un esquema de plegado cabe en un pliego dado.
    Retorna una tupla (cabe, orientación, páginas_ancho, páginas_alto).
    La orientación puede ser: "vertical_normal", "horizontal_normal", 
                             "vertical_rotada", "horizontal_rotada",
                             "vertical_tira_retira", "horizontal_tira_retira"
    """
    # Verificar si el esquema puede ser tira/retira (número par de páginas)
    can_work_and_turn = scheme.pages_per_sheet % 2 == 0
    
    def try_dimensions(width: float, height: float, is_work_and_turn: bool = False) -> Tuple[bool, str, int, int]:
        """Prueba si las dimensiones caben en el pliego en diferentes orientaciones"""
        multiplier = 2 if is_work_and_turn else 1
        dims = scheme.calculate_dimensions(width, height, rows_multiplier=multiplier)
        
        # Probar orientación vertical
        if dims[0] <= sheet_width and dims[1] <= sheet_height:
            orientation = "vertical_tira_retira" if is_work_and_turn else "vertical_normal"
            return True, orientation, scheme.cols, multiplier * scheme.rows
            
        # Probar orientación horizontal (rotada)
        if dims[0] <= sheet_height and dims[1] <= sheet_width:
            orientation = "horizontal_tira_retira" if is_work_and_turn else "horizontal_normal"
            return True, orientation, multiplier * scheme.rows, scheme.cols
            
        return False, "", 0, 0

    # Caso especial para F8-7: Priorizar la rotación de páginas si es A4 o similar
    # (donde el alto es mayor que el ancho)
    if scheme.name == "F8-7" and page_height > page_width:
        # Probar primero con página rotada para F8-7
        if can_work_and_turn:
            # Probar tira/retira con página rotada
            result = try_dimensions(page_height, page_width, True)
            if result[0]:
                return result
                
            # Probar tira/retira con página normal
            result = try_dimensions(page_width, page_height, True)
            if result[0]:
                return result
        
        # Probar normal con página rotada
        result = try_dimensions(page_height, page_width)
        if result[0]:
            return result
            
        # Probar normal con página normal
        result = try_dimensions(page_width, page_height)
        if result[0]:
            return result
    else:
        # Para otros esquemas, seguir el orden normal
        # Si el esquema tiene número par de páginas, intentar primero tira/retira
        if can_work_and_turn:
            # Probar tira/retira con página normal
            result = try_dimensions(page_width, page_height, True)
            if result[0]:
                return result
                
            # Probar tira/retira con página rotada
            result = try_dimensions(page_height, page_width, True)
            if result[0]:
                return result

        # Si tira/retira no funciona o no es posible, probar normal
        # Probar con página normal
        result = try_dimensions(page_width, page_height)
        if result[0]:
            return result
            
        # Probar con página rotada
        result = try_dimensions(page_height, page_width)
        if result[0]:
            return result

    return False, "", 0, 0
