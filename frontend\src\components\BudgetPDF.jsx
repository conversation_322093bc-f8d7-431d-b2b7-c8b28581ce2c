import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { buildApiUrl } from '../config';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  PDFViewer,
  PDFDownloadLink,
  Image
} from '@react-pdf/renderer';
import {
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Box,
  Typography
} from '@mui/material';

// Importar el logotipo directamente como un módulo
import logoSrc from '../assets/images/trikyprinter-logo.png';

// Estilos para el PDF
const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    borderBottom: '1px solid #ccc',
    paddingBottom: 10,
  },
  logo: {
    width: 150,
    height: 75,
    marginBottom: 10,
  },

  companyInfo: {
    fontSize: 10,
    textAlign: 'right',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    backgroundColor: '#f0f0f0',
    padding: 5,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  column: {
    flex: 1,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginRight: 5,
  },
  value: {
    fontSize: 10,
  },
  technicalDetails: {
    marginTop: 10,
    borderTop: '1px solid #ccc',
    paddingTop: 10,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 10,
    color: '#666',
    borderTop: '1px solid #ccc',
    paddingTop: 10,
  },
  table: {
    display: 'table',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#bfbfbf',
    marginBottom: 10,
  },
  tableRow: {
    flexDirection: 'row',
  },
  tableColHeader: {
    backgroundColor: '#f0f0f0',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#bfbfbf',
    padding: 5,
    fontWeight: 'bold',
    fontSize: 10,
  },
  tableCol: {
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#bfbfbf',
    padding: 5,
    fontSize: 10,
  },
});

// Componente para el PDF
const BudgetPDFDocument = ({ budget, client, paper, machine }) => {
  // Log para depuración
  console.log('Renderizando BudgetPDFDocument con:', { budget, client, paper, machine });

  // Función para formatear la fecha
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Verificar que tenemos los datos mínimos necesarios
  if (!budget) {
    console.error('No hay datos de presupuesto para renderizar el PDF');
    return (
      <Document>
        <Page size="A4" style={styles.page}>
          <View style={styles.title}>
            <Text>Error: No se pudo cargar el presupuesto</Text>
          </View>
        </Page>
      </Document>
    );
  }

  // Formatear el número de OT de manera segura
  const formattedOtNumber = budget.ot_number ? budget.ot_number.replace('OT-', '') : 'N/A';

  console.log('Renderizando documento PDF con datos válidos');

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Cabecera con logo y datos de la empresa */}
        <View style={styles.header}>
          <View>
            <Image src={logoSrc} style={styles.logo} />
          </View>
          <View style={styles.companyInfo}>
            <Text>TrikyPrinter</Text>
            <Text>c/Costa Rica 11</Text>
            <Text>28016 Madrid</Text>
            <Text>Tel: +34 91 000 00 00</Text>
            <Text>Email: <EMAIL></Text>
          </View>
        </View>

        {/* Título del presupuesto */}
        <View style={styles.title}>
          <Text>PRESUPUESTO {budget.budget_id || 'N/A'}</Text>
          <Text style={{ fontSize: 12 }}>Orden de Trabajo: {formattedOtNumber}</Text>
          {budget.cfa && <Text style={{ fontSize: 12 }}>CFA: {budget.cfa}</Text>}
          <Text style={{ fontSize: 10 }}>Fecha: {budget.created_at ? formatDate(budget.created_at) : 'N/A'}</Text>
        </View>

        {/* Datos del cliente */}
        {client && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>DATOS DEL CLIENTE</Text>
            <View style={styles.row}>
              <View style={styles.column}>
                <Text style={styles.label}>Empresa:</Text>
                <Text style={styles.value}>{client.company.name}</Text>
              </View>
              <View style={styles.column}>
                <Text style={styles.label}>Código de facturación:</Text>
                <Text style={styles.value}>{client.billing_code}</Text>
              </View>
            </View>
            <View style={styles.row}>
              <View style={styles.column}>
                <Text style={styles.label}>Contacto:</Text>
                <Text style={styles.value}>{client.contact.first_name} {client.contact.last_name}</Text>
              </View>
              <View style={styles.column}>
                <Text style={styles.label}>Cargo:</Text>
                <Text style={styles.value}>{client.contact.position}</Text>
              </View>
            </View>
            <View style={styles.row}>
              <View style={styles.column}>
                <Text style={styles.label}>Teléfono:</Text>
                <Text style={styles.value}>{client.contact.phone}</Text>
              </View>
              <View style={styles.column}>
                <Text style={styles.label}>Email:</Text>
                <Text style={styles.value}>{client.contact.email}</Text>
              </View>
            </View>
            <View style={styles.row}>
              <View style={styles.column}>
                <Text style={styles.label}>Dirección:</Text>
                <Text style={styles.value}>
                  {client.company.address.street}, {client.company.address.city}, {client.company.address.postal_code}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Detalles del trabajo */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>DETALLES DEL TRABAJO</Text>
          <View style={styles.row}>
            <View style={styles.column}>
              <Text style={styles.label}>Tipo de trabajo:</Text>
              <Text style={styles.value}>{budget.job_type}</Text>
            </View>
            <View style={styles.column}>
              <Text style={styles.label}>Cantidad:</Text>
              <Text style={styles.value}>{budget.quantity} unidades</Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.column}>
              <Text style={styles.label}>Tamaño de página:</Text>
              <Text style={styles.value}>{budget.page_size.width} x {budget.page_size.height} mm</Text>
            </View>
            <View style={styles.column}>
              <Text style={styles.label}>Número de páginas:</Text>
              <Text style={styles.value}>
                {budget.parts && budget.parts.length > 0
                  ? budget.parts.reduce((total, part) => total + (part.page_count || 0), 0)
                  : budget.page_count || 0}
              </Text>
            </View>
          </View>
          {/* Mostrar información de pliegos si está disponible */}
          {budget.parts && budget.parts.length > 0 && budget.parts.some(part => part.sheet_calculation && part.sheet_calculation.mejor_combinacion) && (
            <View style={styles.row}>
              <View style={styles.column}>
                <Text style={styles.label}>Pliegos totales:</Text>
                <Text style={styles.value}>
                  {(() => {
                    // Calcular pliegos totales, pliegos impresos y maculatura
                    let totalPliegos = 0;
                    let totalPliegosImpresos = 0;
                    let totalMaculatura = 0;

                    budget.parts.forEach(part => {
                      if (part.sheet_calculation && part.sheet_calculation.mejor_combinacion && part.sheet_calculation.mejor_combinacion.esquemas_utilizados) {
                        // Sumar pliegos totales
                        totalPliegos += part.sheet_calculation.mejor_combinacion.total_pliegos || 0;

                        // Calcular pliegos impresos considerando tira-retira
                        part.sheet_calculation.mejor_combinacion.esquemas_utilizados.forEach(esquema => {
                          // Si es tira-retira, cada pliego físico produce 2 unidades (factor 0.5)
                          const factor = esquema.es_tira_retira ? 0.5 : 1;
                          totalPliegosImpresos += esquema.numero_pliegos * factor * (budget.quantity || 0);
                        });

                        // Sumar maculatura si está disponible
                        if (part.sheet_calculation.total_maculatura) {
                          totalMaculatura += part.sheet_calculation.total_maculatura;
                        } else if (part.machine_data?.type === 'Offset' && part.machine_data?.maculatura) {
                          // Si no hay total_maculatura pero es una máquina offset, calcular la maculatura
                          // usando el valor de maculatura de la máquina multiplicado por el número de pliegos
                          totalMaculatura += (part.sheet_calculation.mejor_combinacion.total_pliegos || 0) * (part.machine_data.maculatura || 0);
                        }
                      }
                    });

                    // Calcular el total de pliegos físicos (impresos + maculatura)
                    const totalPliegosFisicos = Math.ceil(totalPliegosImpresos) + totalMaculatura;

                    return `${totalPliegosFisicos} pliegos`;
                  })()}
                </Text>
              </View>
              {/* Mostrar acabados si existen */}
              {budget.process_costs && budget.process_costs.length > 0 && (
                <View style={styles.column}>
                  <Text style={styles.label}>Acabados:</Text>
                  <Text style={styles.value}>
                    {budget.process_costs.map((process, index) =>
                      `${process.name}${index < budget.process_costs.length - 1 ? ', ' : ''}`
                    )}
                  </Text>
                </View>
              )}
            </View>
          )}
          {/* Mostrar información del papel o papeles */}
          {budget.parts && budget.parts.length > 0 && (
            <View style={styles.row}>
              <View style={styles.column}>
                <Text style={styles.label}>Papel:</Text>
                <Text style={styles.value}>
                  {budget.parts.map((part, index) => {
                    // Intentar obtener el nombre del papel de diferentes fuentes
                    let paperName = 'No especificado';

                    // 1. Si la parte tiene un nombre de papel directamente (añadido en el useEffect)
                    if (part.paper_name) {
                      paperName = part.paper_name;
                    }
                    // 2. Si la parte tiene datos de papel completos
                    else if (part.paper_data && part.paper_data.descriptive_name) {
                      paperName = part.paper_data.descriptive_name;
                    }
                    // 3. Si la parte tiene un objeto paper con descriptive_name
                    else if (part.paper && part.paper.descriptive_name) {
                      paperName = part.paper.descriptive_name;
                    }
                    // 4. Si hay un papel global y coincide con el paper_id de la parte
                    else if (paper && part.paper_id && paper.product_id === part.paper_id) {
                      paperName = paper.descriptive_name;
                    }

                    // Determinar el tipo de impresión (Digital u Offset)
                    let printType = 'No especificado';
                    if (part.machine_data && part.machine_data.type) {
                      printType = part.machine_data.type;
                    } else if (part.machine_id && machine && machine.type) {
                      printType = machine.type;
                    }

                    // Siempre mostrar el nombre de la parte con el tipo de impresión
                    return `${part.name} (${printType}): ${paperName}${index < budget.parts.length - 1 ? '\n' : ''}`;
                  })}
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Información de Envío - Solo mostramos el peso del papel, no el coste de envío */}
        {budget.total_paper_weight_kg > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>INFORMACIÓN DE ENVÍO</Text>
            <View style={styles.row}>
              <View style={styles.column}>
                <Text style={styles.label}>Peso Total del Papel:</Text>
                <Text style={styles.value}>{budget.total_paper_weight_kg || 0} kg</Text>
              </View>
            </View>
          </View>
        )}

        {/* Información de precios */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>INFORMACIÓN DE PRECIOS</Text>
          <View style={styles.table}>
            <View style={styles.tableRow}>
              <View style={[styles.tableColHeader, { width: '70%' }]}>
                <Text>PRECIO LISTA</Text>
              </View>
              <View style={[styles.tableColHeader, { width: '30%' }]}>
                <Text>IMPORTE</Text>
              </View>
            </View>
            <View style={styles.tableRow}>
              <View style={[styles.tableCol, { width: '70%', backgroundColor: '#f0f8ff' }]}>
                <Text style={{ fontWeight: 'bold' }}>Total Presupuesto</Text>
              </View>
              <View style={[styles.tableCol, { width: '30%', backgroundColor: '#f0f8ff' }]}>
                <Text style={{ fontWeight: 'bold' }}>
                  {(() => {
                    // Calcular el coste total sumando los costes de todas las partes y procesos
                    let totalCost = 0;

                    // Sumar los costes de las partes con el costo de máquina correcto
                    if (budget.parts && budget.parts.length > 0) {
                      totalCost += budget.parts.reduce((sum, part) => {
                        // Calcular el costo de máquina correcto
                        let machineCost = part.machine_cost || 0;

                        // Si tenemos los datos necesarios, calcular el costo correcto
                        if (part.sheet_calculation?.total_time_hours && part.machine_data?.hourly_cost) {
                          const hourlyRate = part.machine_data.hourly_cost;
                          const timeHours = part.sheet_calculation.total_time_hours;

                          // Calcular el CFA (Costo Fijo de Arranque)
                          // Usar el valor exacto de cfa_percentage, incluso si es 0
                          const cfaPercentage = part.machine_data.cfa_percentage !== undefined ? part.machine_data.cfa_percentage : 0;
                          const cfaCost = (hourlyRate * cfaPercentage / 100);

                          // Calcular el costo de impresión
                          const printingCost = hourlyRate * timeHours;

                          // Calcular el costo total de máquina
                          machineCost = cfaCost + printingCost;
                        } else if (part.sheet_calculation?.cfa_cost && part.sheet_calculation?.printing_cost) {
                          // Si no tenemos los datos para calcular, usar los valores del cálculo
                          machineCost = part.sheet_calculation.cfa_cost + part.sheet_calculation.printing_cost;
                        }

                        // Calcular el costo total de la parte
                        const partTotal = (part.total_cost || 0) - (part.machine_cost || 0) + machineCost;

                        return sum + partTotal;
                      }, 0);
                    }

                    // Sumar los costes de los procesos
                    if (budget.process_total_cost) {
                      totalCost += budget.process_total_cost;
                    }

                    // Sumar el coste de envío si existe (incluido en el cálculo pero no mostrado separadamente)
                    if (budget.shipping_cost) {
                      totalCost += budget.shipping_cost;
                    }

                    // Calcular el precio de lista (coste + 30%)
                    const listPrice = totalCost * 1.3;

                    return listPrice > 0 ? `${listPrice.toFixed(2)} €` : 'No calculado';
                  })()}
                </Text>
              </View>
            </View>

            {/* Descuento y PVP */}
            {client && client.discount_percentage > 0 && (
              <>
                <View style={styles.tableRow}>
                  <View style={[styles.tableCol, { width: '70%' }]}>
                    <Text style={{ fontWeight: 'bold' }}>Descuento ({client.discount_percentage}%)</Text>
                  </View>
                  <View style={[styles.tableCol, { width: '30%' }]}>
                    <Text style={{ fontWeight: 'bold' }}>
                      {(() => {
                        let totalCost = 0;
                        if (budget.parts && budget.parts.length > 0) {
                          totalCost += budget.parts.reduce((sum, part) => {
                            // Calcular el costo de máquina correcto
                            let machineCost = part.machine_cost || 0;

                            // Si tenemos los datos necesarios, calcular el costo correcto
                            if (part.sheet_calculation?.total_time_hours && part.machine_data?.hourly_cost) {
                              const hourlyRate = part.machine_data.hourly_cost;
                              const timeHours = part.sheet_calculation.total_time_hours;

                              // Calcular el CFA (Costo Fijo de Arranque)
                              // Usar el valor exacto de cfa_percentage, incluso si es 0
                              const cfaPercentage = part.machine_data.cfa_percentage !== undefined ? part.machine_data.cfa_percentage : 0;
                              const cfaCost = (hourlyRate * cfaPercentage / 100);

                              // Calcular el costo de impresión
                              const printingCost = hourlyRate * timeHours;

                              // Calcular el costo total de máquina
                              machineCost = cfaCost + printingCost;
                            } else if (part.sheet_calculation?.cfa_cost && part.sheet_calculation?.printing_cost) {
                              // Si no tenemos los datos para calcular, usar los valores del cálculo
                              machineCost = part.sheet_calculation.cfa_cost + part.sheet_calculation.printing_cost;
                            }

                            // Calcular el costo total de la parte
                            const partTotal = (part.total_cost || 0) - (part.machine_cost || 0) + machineCost;

                            return sum + partTotal;
                          }, 0);
                        }
                        if (budget.process_total_cost) {
                          totalCost += budget.process_total_cost;
                        }
                        // Incluir coste de envío en el cálculo pero no mostrarlo separadamente
                        if (budget.shipping_cost) {
                          totalCost += budget.shipping_cost;
                        }
                        const listPrice = totalCost * 1.3;
                        const discountAmount = listPrice * (client.discount_percentage / 100);
                        return discountAmount > 0 ? `-${discountAmount.toFixed(2)} €` : '0.00 €';
                      })()}
                    </Text>
                  </View>
                </View>

                <View style={styles.tableRow}>
                  <View style={[styles.tableCol, { width: '70%', backgroundColor: '#e6f7ff' }]}>
                    <Text style={{ fontWeight: 'bold', fontSize: 14 }}>PRECIO FINAL (PVP)</Text>
                  </View>
                  <View style={[styles.tableCol, { width: '30%', backgroundColor: '#e6f7ff' }]}>
                    <Text style={{ fontWeight: 'bold', fontSize: 14 }}>
                      {(() => {
                        let totalCost = 0;
                        if (budget.parts && budget.parts.length > 0) {
                          totalCost += budget.parts.reduce((sum, part) => {
                            // Calcular el costo de máquina correcto
                            let machineCost = part.machine_cost || 0;

                            // Si tenemos los datos necesarios, calcular el costo correcto
                            if (part.sheet_calculation?.total_time_hours && part.machine_data?.hourly_cost) {
                              const hourlyRate = part.machine_data.hourly_cost;
                              const timeHours = part.sheet_calculation.total_time_hours;

                              // Calcular el CFA (Costo Fijo de Arranque)
                              // Usar el valor exacto de cfa_percentage, incluso si es 0
                              const cfaPercentage = part.machine_data.cfa_percentage !== undefined ? part.machine_data.cfa_percentage : 0;
                              const cfaCost = (hourlyRate * cfaPercentage / 100);

                              // Calcular el costo de impresión
                              const printingCost = hourlyRate * timeHours;

                              // Calcular el costo total de máquina
                              machineCost = cfaCost + printingCost;
                            } else if (part.sheet_calculation?.cfa_cost && part.sheet_calculation?.printing_cost) {
                              // Si no tenemos los datos para calcular, usar los valores del cálculo
                              machineCost = part.sheet_calculation.cfa_cost + part.sheet_calculation.printing_cost;
                            }

                            // Calcular el costo total de la parte
                            const partTotal = (part.total_cost || 0) - (part.machine_cost || 0) + machineCost;

                            return sum + partTotal;
                          }, 0);
                        }
                        if (budget.process_total_cost) {
                          totalCost += budget.process_total_cost;
                        }
                        // Incluir coste de envío en el cálculo pero no mostrarlo separadamente
                        if (budget.shipping_cost) {
                          totalCost += budget.shipping_cost;
                        }
                        const listPrice = totalCost * 1.3;
                        const pvp = listPrice * (1 - (client.discount_percentage / 100));
                        return pvp > 0 ? `${pvp.toFixed(2)} €` : 'No calculado';
                      })()}
                    </Text>
                  </View>
                </View>
              </>
            )}
          </View>
        </View>

        {/* Pie de página */}
        <View style={styles.footer}>
          <Text>Este presupuesto tiene una validez de 30 días desde la fecha de emisión.</Text>
          <Text>TrikyPrinter - c/Costa Rica 11, 28016 Madrid - CIF: B12345678</Text>
        </View>
      </Page>
    </Document>
  );
};

BudgetPDFDocument.propTypes = {
  budget: PropTypes.object.isRequired,
  client: PropTypes.object,
  paper: PropTypes.object,
  machine: PropTypes.object
};

// Componente principal
const BudgetPDF = ({ budgetId, onClose }) => {
  const [budget, setBudget] = useState(null);
  const [client, setClient] = useState(null);
  const [paper, setPaper] = useState(null);
  const [machine, setMachine] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // Eliminamos variables no utilizadas

  // URL de la API importada desde config.js

  // Cargar datos del presupuesto
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Iniciando carga de datos para el PDF...');
        setLoading(true);

        // Obtener el token de autenticación del localStorage
        const token = localStorage.getItem('auth_token');

        if (!token) {
          throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
        }

        // Obtener el presupuesto
        const budgetResponse = await fetch(buildApiUrl(`/budgets/${budgetId}`), {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (!budgetResponse.ok) throw new Error('Error al obtener el presupuesto');
        const budgetData = await budgetResponse.json();
        console.log('Datos del presupuesto cargados:', budgetData);

        // Si el presupuesto tiene partes, cargar los datos de papel para cada parte
        if (budgetData.parts && budgetData.parts.length > 0) {
          console.log('Cargando datos de papel para cada parte...');

          // Crear una copia del presupuesto para modificarlo
          const budgetWithPaperData = { ...budgetData };

          // Para cada parte, cargar los datos del papel y la máquina
          for (let i = 0; i < budgetWithPaperData.parts.length; i++) {
            const part = budgetWithPaperData.parts[i];

            // Cargar datos del papel si tiene paper_id
            if (part.paper_id) {
              try {
                console.log(`Cargando datos del papel para la parte ${i+1} (${part.name}): ${part.paper_id}`);
                const paperResponse = await fetch(buildApiUrl(`/papers/${part.paper_id}`), {
                  headers: {
                    'Authorization': `Bearer ${token}`
                  }
                });

                if (paperResponse.ok) {
                  const paperData = await paperResponse.json();
                  console.log(`Datos del papel cargados para la parte ${i+1}:`, paperData);
                  // Añadir los datos del papel a la parte
                  budgetWithPaperData.parts[i].paper_data = paperData;
                  // También guardar el nombre descriptivo del papel para fácil acceso
                  budgetWithPaperData.parts[i].paper_name = paperData.descriptive_name;
                } else {
                  console.warn(`No se pudo cargar el papel para la parte ${i+1}`);
                }
              } catch (err) {
                console.error(`Error al cargar datos del papel para la parte ${i+1}:`, err);
              }
            }

            // Cargar datos de la máquina si tiene machine_id
            if (part.machine_id) {
              try {
                console.log(`Cargando datos de la máquina para la parte ${i+1} (${part.name}): ${part.machine_id}`);
                const machineResponse = await fetch(buildApiUrl(`/machines/${part.machine_id}`), {
                  headers: {
                    'Authorization': `Bearer ${token}`
                  }
                });

                if (machineResponse.ok) {
                  const machineData = await machineResponse.json();
                  console.log(`Datos de la máquina cargados para la parte ${i+1}:`, machineData);
                  // Añadir los datos de la máquina a la parte
                  budgetWithPaperData.parts[i].machine_data = machineData;
                } else {
                  console.warn(`No se pudo cargar la máquina para la parte ${i+1}`);
                }
              } catch (err) {
                console.error(`Error al cargar datos de la máquina para la parte ${i+1}:`, err);
              }
            }
          }

          // Actualizar el presupuesto con los datos de papel cargados
          setBudget(budgetWithPaperData);
        } else {
          setBudget(budgetData);
        }

        // Obtener el cliente
        if (budgetData.client_id) {
          console.log('Cargando datos del cliente...');
          const clientResponse = await fetch(buildApiUrl(`/clients/${budgetData.client_id}`), {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          if (clientResponse.ok) {
            const clientData = await clientResponse.json();
            console.log('Datos del cliente cargados:', clientData);
            setClient(clientData);
          } else {
            console.warn('No se pudo cargar el cliente');
          }
        }

        // Obtener el papel principal (para compatibilidad)
        if (budgetData.paper_id) {
          console.log('Cargando datos del papel principal...');
          const paperResponse = await fetch(buildApiUrl(`/papers/${budgetData.paper_id}`), {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          if (paperResponse.ok) {
            const paperData = await paperResponse.json();
            console.log('Datos del papel principal cargados:', paperData);
            setPaper(paperData);
          } else {
            console.warn('No se pudo cargar el papel principal');
          }
        }

        // Obtener la máquina
        if (budgetData.machine_id) {
          console.log('Cargando datos de la máquina...');
          const machineResponse = await fetch(buildApiUrl(`/machines/${budgetData.machine_id}`), {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          if (machineResponse.ok) {
            const machineData = await machineResponse.json();
            console.log('Datos de la máquina cargados:', machineData);
            setMachine(machineData);
          } else {
            console.warn('No se pudo cargar la máquina');
          }
        }

        console.log('Carga de datos completada.');
        setLoading(false);
      } catch (err) {
        console.error('Error al cargar datos:', err);
        setError(err.message);
        setLoading(false);
      }
    };

    if (budgetId) {
      fetchData();
    }
  }, [budgetId]);

  // Comentamos temporalmente el procesamiento de datos para solucionar el problema del PDF en blanco
  /*
  useEffect(() => {
    if (budget && machine) {
      console.log('Procesando datos del presupuesto...');
      // Resto del código comentado...
    }
  }, [budget, machine]);
  */

  if (loading) {
    return (
      <Dialog open={true} maxWidth="md" fullWidth>
        <DialogTitle>Generando PDF del Presupuesto</DialogTitle>
        <DialogContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={true} maxWidth="md" fullWidth>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          <Text>{error}</Text>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cerrar</Button>
        </DialogActions>
      </Dialog>
    );
  }

  if (!budget) {
    return (
      <Dialog open={true} maxWidth="md" fullWidth>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          <Text>No se encontró el presupuesto</Text>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cerrar</Button>
        </DialogActions>
      </Dialog>
    );
  }

  // Verificar que tenemos todos los datos necesarios antes de renderizar
  if (!budget) {
    return (
      <Dialog open={true} maxWidth="md" fullWidth>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          <Typography>No se encontró el presupuesto</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cerrar</Button>
        </DialogActions>
      </Dialog>
    );
  }

  console.log('Renderizando diálogo de PDF con datos:', { budget, client, paper, machine });

  // Log adicional para verificar los datos de papel en cada parte
  if (budget && budget.parts) {
    console.log('Datos de papel y máquina en las partes del presupuesto:');
    budget.parts.forEach((part, index) => {
      console.log(`Parte ${index + 1} (${part.name}):`, {
        paper_id: part.paper_id,
        paper_name: part.paper_name,
        paper_data: part.paper_data,
        machine_id: part.machine_id,
        machine_data: part.machine_data
      });
    });

    // Log para verificar los datos de pliegos
    console.log('Datos de pliegos en las partes del presupuesto:');
    let totalPliegos = 0;
    let totalPliegosImpresos = 0;
    let totalMaculatura = 0;

    budget.parts.forEach((part, index) => {
      if (part.sheet_calculation && part.sheet_calculation.mejor_combinacion && part.sheet_calculation.mejor_combinacion.esquemas_utilizados) {
        // Calcular pliegos impresos para esta parte
        let pliegosImpresosParte = 0;
        let maculaturaParte = 0;

        // Sumar pliegos totales
        const pliegosTotalesParte = part.sheet_calculation.mejor_combinacion.total_pliegos || 0;
        totalPliegos += pliegosTotalesParte;

        // Calcular pliegos impresos considerando tira-retira
        part.sheet_calculation.mejor_combinacion.esquemas_utilizados.forEach(esquema => {
          // Si es tira-retira, cada pliego físico produce 2 unidades (factor 0.5)
          const factor = esquema.es_tira_retira ? 0.5 : 1;
          const pliegosImpresos = esquema.numero_pliegos * factor * (budget.quantity || 0);
          pliegosImpresosParte += pliegosImpresos;
          totalPliegosImpresos += pliegosImpresos;
        });

        // Sumar maculatura si está disponible
        if (part.sheet_calculation.total_maculatura) {
          maculaturaParte = part.sheet_calculation.total_maculatura;
          totalMaculatura += maculaturaParte;
        } else if (part.machine_data?.type === 'Offset' && part.machine_data?.maculatura) {
          // Si no hay total_maculatura pero es una máquina offset, calcular la maculatura
          // usando el valor de maculatura de la máquina multiplicado por el número de pliegos
          maculaturaParte = (pliegosTotalesParte || 0) * (part.machine_data.maculatura || 0);
          totalMaculatura += maculaturaParte;
        }

        console.log(`Parte ${index + 1} (${part.name}):`, {
          pliegos_totales: pliegosTotalesParte,
          pliegos_impresos: Math.ceil(pliegosImpresosParte),
          maculatura: maculaturaParte,
          pliegos_fisicos: Math.ceil(pliegosImpresosParte) + maculaturaParte,
          esquemas: part.sheet_calculation.mejor_combinacion.esquemas_utilizados
        });
      }
    });

    console.log('Totales de pliegos:', {
      pliegos_totales: totalPliegos,
      pliegos_impresos: Math.ceil(totalPliegosImpresos),
      maculatura: totalMaculatura,
      pliegos_fisicos: Math.ceil(totalPliegosImpresos) + totalMaculatura
    });
  }

  // Usar el presupuesto original para evitar problemas
  // Vamos a simplificar para solucionar el problema del PDF en blanco
  return (
    <Dialog open={true} maxWidth="xl" fullWidth>
      <DialogTitle>
        Presupuesto {budget.budget_id || ''}
        <Button
          variant="contained"
          color="primary"
          style={{ position: 'absolute', right: '24px' }}
          onClick={() => {
            console.log('Intentando descargar PDF...');
          }}
        >
          <PDFDownloadLink
            document={<BudgetPDFDocument budget={budget} client={client} paper={paper} machine={machine} />}
            fileName={`Presupuesto_${budget.budget_id || 'sin_id'}.pdf`}
            style={{ textDecoration: 'none', color: 'white' }}
          >
            {({ loading, error }) => {
              if (error) {
                console.error('Error al generar PDF para descargar:', error);
                return 'Error';
              }
              return loading ? 'Generando...' : 'Descargar PDF';
            }}
          </PDFDownloadLink>
        </Button>
      </DialogTitle>
      <DialogContent sx={{ p: 1 }}>
        <Box sx={{ width: '100%', height: 'calc(100vh - 150px)' }}>
          <PDFViewer width="100%" height="100%" style={{ border: 'none' }}>
            <BudgetPDFDocument budget={budget} client={client} paper={paper} machine={machine} />
          </PDFViewer>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cerrar</Button>
      </DialogActions>
    </Dialog>
  );
};

BudgetPDF.propTypes = {
  budgetId: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired
};

export default BudgetPDF;
