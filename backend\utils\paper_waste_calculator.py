"""
Utilidades para calcular el desperdicio de papel en trabajos de impresión.
"""
from typing import List, Dict, Tuple, Optional

def calcular_desperdicio_papel(
    ancho_pliego: float,
    alto_pliego: float,
    ancho_pagina: float,
    alto_pagina: float,
    paginas_ancho: int,
    paginas_alto: int,
    sangrado: float = 3.0,
    pinzas: float = 10.0,
    margen_lateral: float = 5.0,
    margen_superior: float = 5.0,
    margen_inferior: float = 5.0,
    marcas_registro: bool = True,
    tiras_control: bool = True
) -> Dict:
    """
    Calcula el desperdicio de papel y el área mínima necesaria para imprimir.
    
    Args:
        ancho_pliego: Ancho del pliego en mm
        alto_pliego: Alto del pliego en mm
        ancho_pagina: Ancho de la página en mm
        alto_pagina: Alto de la página en mm
        paginas_ancho: Número de páginas a lo ancho del pliego
        paginas_alto: Número de páginas a lo alto del pliego
        sangrado: Sangrado en mm (por defecto 3mm)
        pinzas: Espacio para pinzas en mm (por defecto 10mm)
        margen_lateral: Margen lateral en mm (por defecto 5mm)
        margen_superior: Margen superior en mm (por defecto 5mm)
        margen_inferior: Margen inferior en mm (por defecto 5mm)
        marcas_registro: Si se incluyen marcas de registro (por defecto True)
        tiras_control: Si se incluyen tiras de control (por defecto True)
    
    Returns:
        Dict: Información detallada sobre el desperdicio de papel
    """
    # Área total del pliego
    area_pliego = ancho_pliego * alto_pliego
    
    # Área de una página con sangrado
    ancho_pagina_con_sangrado = ancho_pagina + (sangrado * 2)
    alto_pagina_con_sangrado = alto_pagina + (sangrado * 2)
    
    # Área ocupada por todas las páginas con sangrado
    area_paginas = (ancho_pagina_con_sangrado * alto_pagina_con_sangrado) * paginas_ancho * paginas_alto
    
    # Área ocupada por pinzas
    area_pinzas = ancho_pliego * pinzas
    
    # Área ocupada por márgenes
    area_margenes_laterales = alto_pliego * margen_lateral * 2  # Márgenes en ambos lados
    area_margen_superior = ancho_pliego * margen_superior
    area_margen_inferior = ancho_pliego * margen_inferior
    area_margenes = area_margenes_laterales + area_margen_superior + area_margen_inferior
    
    # Área ocupada por marcas de registro y tiras de control
    area_marcas = 0
    if marcas_registro:
        # Estimación del área ocupada por marcas de registro (4 esquinas)
        area_marcas += 4 * (20 * 20)  # 20mm x 20mm por marca
    
    if tiras_control:
        # Estimación del área ocupada por tiras de control
        area_marcas += ancho_pliego * 10  # 10mm de alto para tiras de control
    
    # Área total utilizada (páginas + pinzas + márgenes + marcas)
    area_utilizada = min(area_paginas + area_pinzas + area_margenes + area_marcas, area_pliego)
    
    # Área desperdiciada
    area_desperdiciada = area_pliego - area_utilizada
    
    # Porcentaje de desperdicio
    porcentaje_desperdicio = (area_desperdiciada / area_pliego) * 100
    
    # Porcentaje de aprovechamiento
    porcentaje_aprovechamiento = 100 - porcentaje_desperdicio
    
    # Calcular el área mínima necesaria
    # Ancho mínimo: ancho de las páginas + márgenes laterales
    ancho_minimo = (ancho_pagina_con_sangrado * paginas_ancho) + (margen_lateral * 2)
    
    # Alto mínimo: alto de las páginas + espacio para pinzas + márgenes superior e inferior
    alto_minimo = (alto_pagina_con_sangrado * paginas_alto) + pinzas + margen_superior + margen_inferior
    
    # Si hay marcas de registro o tiras de control, añadir espacio adicional
    if marcas_registro or tiras_control:
        ancho_minimo += 20  # Espacio adicional para marcas en los laterales
        alto_minimo += 20   # Espacio adicional para marcas en la parte superior e inferior
    
    # Área mínima necesaria
    area_minima = ancho_minimo * alto_minimo
    
    # Generar recomendaciones
    recomendaciones = []
    
    # Verificar si el pliego es óptimo
    pliego_optimo = area_minima <= area_pliego
    
    if not pliego_optimo:
        recomendaciones.append(f"El pliego actual ({ancho_pliego}x{alto_pliego}mm) es demasiado pequeño para la disposición solicitada. Se necesita un pliego de al menos {ancho_minimo:.2f}x{alto_minimo:.2f}mm.")
    
    # Verificar si hay un desperdicio significativo
    if porcentaje_desperdicio > 30:
        recomendaciones.append(f"Hay un desperdicio significativo de papel ({porcentaje_desperdicio:.2f}%). Considere utilizar un pliego más pequeño o aumentar el número de páginas por pliego.")
    
    # Verificar si se puede rotar para aprovechar mejor el papel
    if ancho_minimo > alto_minimo and ancho_pliego < alto_pliego:
        recomendaciones.append("Considere rotar el pliego para aprovechar mejor el papel.")
    elif alto_minimo > ancho_minimo and alto_pliego < ancho_pliego:
        recomendaciones.append("Considere rotar el pliego para aprovechar mejor el papel.")
    
    # Crear y devolver el resultado
    resultado = {
        "area_pliego_mm2": area_pliego,
        "area_utilizada_mm2": area_utilizada,
        "area_desperdiciada_mm2": area_desperdiciada,
        "porcentaje_desperdicio": round(porcentaje_desperdicio, 2),
        "porcentaje_aprovechamiento": round(porcentaje_aprovechamiento, 2),
        "ancho_minimo_mm": round(ancho_minimo, 2),
        "alto_minimo_mm": round(alto_minimo, 2),
        "area_minima_mm2": round(area_minima, 2),
        "pliego_optimo": pliego_optimo,
        "area_paginas_mm2": round(area_paginas, 2),
        "area_sangrado_mm2": round((sangrado * 2) * (ancho_pagina + alto_pagina) * paginas_ancho * paginas_alto, 2),
        "area_pinzas_mm2": round(area_pinzas, 2),
        "area_margenes_mm2": round(area_margenes, 2),
        "area_marcas_mm2": round(area_marcas, 2),
        "recomendaciones": recomendaciones
    }
    
    return resultado
