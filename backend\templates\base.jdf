<?xml version="1.0" encoding="UTF-8"?>
<JDF
    xmlns="http://www.CIP4.org/JDFSchema_1_1"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    ID="JDF-{{ job_id }}"
    Type="Product"
    JobID="{{ job_id }}"
    Status="Waiting"
    Version="1.3"
    xsi:type="Product"
    JobPartID="{{ job_id }}"
    Activation="Active"
    MaxVersion="1.3"
    ICSVersions="Base_L2-1.3 JMF_L2-1.3 MIS_L3-1.3 MISPRE_L2-1.3"
    DescriptiveName="{{ descriptive_name }}"
>
    <!-- 1) AuditPool -->
    <AuditPool>
        <Created
            ID="Created-001"
            Author="{{ author }}"
            AgentName="{{ agent_name }}"
            TimeStamp="{{ timestamp }}"
            AgentVersion="{{ agent_version }}"
        />
    </AuditPool>

    <!-- 2) Comment -->
    <Comment>{{ comment }}</Comment>

    <!-- 3) ResourcePool -->
    <ResourcePool>
        <CustomerInfo
            ID="CI-001"
            Class="Parameter"
            Status="Available"
            CustomerID="{{ customer_info.customer_id if customer_info else 'Customer-1' }}"
            BillingCode="{{ customer_info.billing_code if customer_info else 'AAA' }}"
            CustomerOrderID="{{ customer_info.order_id if customer_info else 'OrderID-1' }}"
        >
            <Contact ContactTypes="Customer">
                <Company OrganizationName="{{ customer_info.company_name if customer_info else 'x-Company' }}"/>
                <Address
                    Country="{{ customer_info.country if customer_info else 'a-Country' }}"
                    Region="{{ customer_info.region if customer_info else 'b-Region' }}"
                    City="{{ customer_info.city if customer_info else 'c-City' }}"
                    Street="{{ customer_info.street if customer_info else 'd-Avenue' }}"
                    PostalCode="{{ customer_info.postal_code if customer_info else '000' }}"
                />
                <Person
                    JobTitle="{{ customer_info.job_title if customer_info else 'Manager' }}"
                    FirstName="{{ customer_info.first_name if customer_info else 'John' }}"
                    FamilyName="{{ customer_info.family_name if customer_info else 'Smith' }}"
                >
                    <ComChannel Locator="{{ customer_info.phone if customer_info else '************' }}" ChannelType="Phone"/>
                    <ComChannel Locator="{{ customer_info.fax if customer_info else '************' }}" ChannelType="Fax"/>
                    <ComChannel Locator="{{ customer_info.email if customer_info else '<EMAIL>' }}" ChannelType="Email"/>
                </Person>
            </Contact>
        </CustomerInfo>

        <!-- Devices -->
        {% for signature in signatures %}
        {% if signature.press_name %}
        <Device
            ID="Press-{{ loop.index }}"
            Class="Implementation"
            Status="Available"
            DeviceID="Dev-PR"
            DescriptiveName="{{ signature.press_name }}"
        />
        {% if not signature.press_name.startswith('HPINDIGO') %}
        <Media
            ID="Plate-{{ '%03d' % loop.index }}"
            Class="Consumable"
            Status="Available"
            {% if signature.press_name.endswith('B1') %}
            Dimension="2917.7 2182.7"
            {% else %}
            Dimension="2267.72 1842.52"
            {% endif %}
            MediaType="Plate"
            ProductID="Pla-{{ loop.index }}"
            DescriptiveName="Plancha {{ signature.press_name.split('Prensa ')[1] if signature.press_name else 'B1' }}"
        />
        {% endif %}
        {% endif %}
        {% endfor %}
        {% if not signatures[0].press_name.startswith('HPINDIGO') %}
        <Device
            ID="PS-001"
            Class="Implementation"
            Status="Available"
            DeviceID="Dev-PS"
            DescriptiveName="PlateSetter-1"
        />
        {% endif %}

        <!-- Media (papel y plancha) -->
        {% for paper in paper_configs %}
        {% set width_points = paper.dimension_width * 2.834645669 %}
        {% set height_points = paper.dimension_height * 2.834645669 %}
        <Media
            ID="{{ paper.product_id }}"
            Class="Consumable"
            Status="Available"
            Weight="{{ '%.1f'|format(paper.weight) }}"
            Dimension="{{ '%0.1f %0.1f'|format(width_points|float, height_points|float) }}"
            MediaType="{{ paper.media_type }}"
            MediaUnit="Sheet"
            ProductID="{{ paper.product_id }}"
            Thickness="{{ '%d'|format(paper.thickness) }}"
            DescriptiveName="{{ paper.descriptive_name }}"
        />
        {% endfor %}
        <Component
            ID="Com-001"
            Class="Quantity"
            Status="Unavailable"
            ProductType="{{ product_type }}"
            ComponentType="PartialProduct"
            DescriptiveName="{{ descriptive_name }} (parcial)"
        />

        {% for sig in signatures %}
        {% set signature_index = loop.index %}
        {% for sheet in sig.stripping_params.sheets %}
        <BinderySignature
            ID="{{ sheet.bindery_signature_name }}"
            Class="Parameter"
            Status="Available"
            FoldCatalog="{{ sheet.fold_catalog }}"
            BinderySignatureType="Fold"
        />
        {% endfor %}
        <Assembly
            ID="ASM-{{ signature_index }}"
            Class="Parameter"
            Order="{{ sig.assembly_order }}"
            Status="Available"
            BindingSide="{{ binding_side }}"
        />
        {% endfor %}

        {% if runlists %}
        {% for runlist in runlists %}
        <RunList
            ID="RL-{{ loop.index }}d"
            Class="Parameter"
            NPage="{{ runlist.pages }}"
            Status="Available"
            Pages="{{ runlist.page_range }}"
        >
            <LayoutElement>
                <FileSpec
                    MimeType="application/pdf"
                    URL="{{ runlist.pdf_url }}"
                />
            </LayoutElement>
        </RunList>
        {% endfor %}
        {% endif %}

        {% for signature in signatures %}
        {% set signature_index = loop.index %}
        <StrippingParams
            ID="{{ signature.signature_ID }}"
            Class="Parameter"
            Status="Available"
            PartIDKeys="SignatureName SheetName BinderySignatureName"
        >
            <StrippingParams SignatureName="{{ signature.stripping_params.signature_name }}" >
                {% for sheet in signature.stripping_params.sheets %}
                <StrippingParams SheetName="{{ sheet.sheet_name }}" WorkStyle="{{ sheet.work_style }}">
                    <DeviceRef rRef="Press-{{ signature_index }}"/>
                    <MediaRef rRef="{{ sheet.paper_ref }}"/>
                    {% if not signatures[signature_index-1].press_name.startswith('HPINDIGO') %}
                    <MediaRef rRef="Plate-{{ '%03d' % signature_index }}"/>
                    {% endif %}
                    <StrippingParams BinderySignatureName="{{ sheet.bindery_signature_name }}">
                        {% set params = sheet.strip_cell_params %}
                        <StripCellParams
                            Spine="{{ '%.1f'|format(params.spine * 2.834645669) }}"
                            TrimFace="{{ '%.1f'|format(params.trim_face * 2.834645669) }}"
                            TrimFoot="{{ '%.1f'|format(params.trim_foot * 2.834645669) }}"
                            TrimHead="{{ '%.1f'|format(params.trim_head * 2.834645669) }}"
                            TrimSize="{{ '%.1f %.1f'|format(params.trim_size_width * 2.834645669, params.trim_size_height * 2.834645669) }}"
                            BleedFace="{{ '%.1f'|format(params.bleed_face * 2.834645669) }}"
                            BleedFoot="{{ '%.1f'|format(params.bleed_foot * 2.834645669) }}"
                            BleedHead="{{ '%.1f'|format(params.bleed_head * 2.834645669) }}"
                            BleedSpine="{{ '%.1f'|format(params.bleed_spine * 2.834645669) }}"
                            BackOverfold="{{ '%.2f'|format(params.back_overfold * 2.834645669) }}"
                            MillingDepth="{{ '%.1f'|format(params.milling_depth * 2.834645669) }}"
                            FrontOverfold="{{ '%.2f'|format(params.front_overfold * 2.834645669) }}"
                            Creep="{{ '%.1f %.1f'|format(params.creep_x * 2.834645669, params.creep_y * 2.834645669) }}"
                        />
                        <BinderySignatureRef rRef="{{ sheet.bindery_signature_name }}"/>
                        <Position
                            MarginTop="{{ '%.2f'|format(params.margin_top * 2.834645669) }}"
                            MarginLeft="{{ '%.2f'|format(params.margin_left * 2.834645669) }}"
                            MarginRight="{{ '%.2f'|format(params.margin_right * 2.834645669) }}"
                            MarginBottom="{{ '%.2f'|format(params.margin_bottom * 2.834645669) }}"
                            Orientation="{% if sheet.work_style == 'WorkAndTurn' %}Rotate90{% else %}{{ params.orientation }}{% endif %}"
                            RelativeBox="{% if sheet.work_style == 'WorkAndTurn' %}-0.5000 0.0000 0.5000 1.0000{% else %}{{ '%.4f %.4f %.4f %.4f'|format(params.relative_box_x1, params.relative_box_y1, params.relative_box_x2, params.relative_box_y2) }}{% endif %}"
                        />
                    </StrippingParams>
                </StrippingParams>
                {% endfor %}
            </StrippingParams>
        </StrippingParams>
        {% endfor %}
    </ResourcePool>

    <!-- 4) ResourceLinkPool -->
    <ResourceLinkPool>
        <CustomerInfoLink rRef="CI-001" Usage="Input"/>
    </ResourceLinkPool>

    <!-- 5) Sub-JDF principal (Producto) -->
    {% for signature in signatures %}
    <JDF
        ID="JDF-{{ signature.signature_ID }}"
        Type="Product"
        Status="Waiting"
        Version="1.3"
        xsi:type="Product"
        JobPartID="{{ signature.job_part_id_name }}"
    >
        <ResourceLinkPool>
            <ComponentLink rRef="Com-001" Usage="Output"/>
        </ResourceLinkPool>

        <!-- Sub-JDF de Prepress / PlateMaking -->
        <JDF
            ID="Pre-00{{ loop.index }}"
            Type="ProcessGroup"
            Status="Waiting"
            Category="PrePress"
            JobPartID="Pre-00{{ loop.index }}"
        >
            <JDF
                ID="PPPre-001"
                Type="ProcessGroup"
                Types="PrePressPreparation"
                Status="Waiting"
                Category="MISPRE.PrePressPreparation"
                JobPartID="PPPre-001"
            >
            {% if runlists %}
            <ResourceLinkPool>
                {% for runlist in runlists %}
                {% if runlist.signature_ref == signature.signature_ID %}
                <RunListLink rRef="{{ runlist.runlist_id }}" Usage="Input" ProcessUsage="Document"/>
                {% endif %}
                {% endfor %}
            </ResourceLinkPool>
            {% endif %}
            </JDF>

            <JDF
                ID="IPre-001"
                Type="ProcessGroup"
                Types="ImpositionPreparation"
                Status="Waiting"
                Category="MISPRE.ImpositionPreparation"
                JobPartID="IPre-001"
            >
                <ResourceLinkPool>
                    <StrippingParamsLink rRef="{{ signature.signature_ID }}" Usage="Input"/>
                    <AssemblyLink rRef="ASM-{{ loop.index }}" Usage="Input"/>
                </ResourceLinkPool>
            </JDF>

            {% if not signature.press_name.startswith('HPINDIGO') %}
            <JDF
                ID="PM-001"
                Type="ProcessGroup"
                Types="Imposition RIPing PreviewGeneration ImageSetting"
                Status="Waiting"
                Category="MISPRE.PlateMaking"
                JobPartID="PM-001"
            >
                <ResourceLinkPool>
                    <MediaLink rRef="Plate-{{ '%03d' % loop.index }}" Usage="Input" ProcessUsage="Plate"/>
                    <DeviceLink rRef="PS-001" Usage="Input"/>
                    {% if signature.press_name %}
                    <DeviceLink rRef="Press-{{ loop.index }}" Usage="Input"/>
                    {% endif %}
                </ResourceLinkPool>
            </JDF>
            {% endif %}

            <JDF 
                ID="CP-{{ signature.signature_ID }}" 
                Type="ProcessGroup" 
                Types="InkZoneCalculation ConventionalPrinting"
                Status="Waiting" 
                Category="MISCPS.Printing" 
                JobPartID="CP-{{ signature.signature_ID }}"
            >
                <ResourceLinkPool>
                    {% for sheet in signature.stripping_params.sheets %}
                    <MediaLink rRef="{{ sheet.paper_ref }}" Usage="Input" />
                    {% endfor %}
                    {% if signature.press_name %}
                    <DeviceLink rRef="Press-{{ loop.index }}" Usage="Input" />
                    {% endif %}
                </ResourceLinkPool>
            </JDF>
        </JDF>
    </JDF>
    {% endfor %}
</JDF>
