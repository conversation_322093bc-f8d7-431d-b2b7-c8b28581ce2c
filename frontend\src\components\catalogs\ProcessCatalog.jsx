import { useState, useEffect, useCallback, useRef } from 'react';
import { buildApiUrl } from '../../config';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Snackbar,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Tabs,
  Tab,
  OutlinedInput,
  ListItemText,
  Checkbox
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';

// Tipos de procesos
const PROCESS_TYPES = [
  'Todos',
  'Impresión',
  'Corte',
  'Plegado',
  'Encuadernación',
  'Acabado',
  'Troquelado',
  '<PERSON><PERSON><PERSON>o',
  'Laminado',
  'O<PERSON><PERSON>'
];

// Tipos de unidades
const UNIT_TYPES = [
  'Unidad',
  'Hora',
  'Pliego',
  'Metro',
  'Centímetro',
  'Kilogramo',
  'Litro'
];

// Colores para los tipos de procesos
const TYPE_COLORS = {
  'Impresión': 'primary',
  'Corte': 'secondary',
  'Plegado': 'success',
  'Encuadernación': 'info',
  'Acabado': 'warning',
  'Troquelado': 'error',
  'Barnizado': 'default',
  'Laminado': 'info',
  'Otros': 'default',
  'Todos': 'default'
};

// Tipos de máquinas
const MACHINE_TYPES = [
  '',
  'Offset',
  'Digital',
  'Plotter',
  'Encuadernadora',
  'Guillotina',
  'Plegadora',
  'CTP'
];

// Estilo para el menú de selección múltiple
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const ProcessCatalog = () => {
  // Estados
  const [processes, setProcesses] = useState([]);
  const [filteredProcesses, setFilteredProcesses] = useState([]);
  const [selectedType, setSelectedType] = useState('Todos');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentProcess, setCurrentProcess] = useState(null);
  const [machines, setMachines] = useState([]);
  const [formData, setFormData] = useState({
    name: '',
    type: 'Impresión',
    description: '',
    machine_type: '',
    unit_cost: '',
    unit_type: 'Unidad',
    notes: '',
    compatible_machines: []
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const fetchedRef = useRef(false);

  // Función para obtener todos los procesos
  const fetchProcesses = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/processes/'));
      if (!response.ok) throw new Error('Error al obtener el catálogo de procesos');
      const data = await response.json();
      setProcesses(data);

      // Aplicar filtros
      let filtered = data;
      if (selectedType !== 'Todos') {
        filtered = filtered.filter(process => process.type === selectedType);
      }
      setFilteredProcesses(filtered);
    } catch (err) {
      setSnackbar({ open: true, message: err.message, severity: 'error' });
    }
  }, [selectedType]);

  // Función para obtener todas las máquinas
  const fetchMachines = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/machines/'));
      if (!response.ok) throw new Error('Error al obtener el catálogo de máquinas');
      const data = await response.json();

      // Filtrar solo máquinas activas
      const activeMachines = data.filter(machine => machine.status === 'Activa');
      setMachines(activeMachines);
    } catch (err) {
      setSnackbar({ open: true, message: err.message, severity: 'error' });
    }
  }, []);

  // Cargar procesos y máquinas al montar el componente
  useEffect(() => {
    if (!fetchedRef.current) {
      fetchProcesses();
      fetchMachines();
      fetchedRef.current = true;
    }
  }, [fetchProcesses, fetchMachines]);

  // Actualizar procesos filtrados cuando cambia el tipo seleccionado
  useEffect(() => {
    if (processes.length > 0) {
      if (selectedType === 'Todos') {
        setFilteredProcesses(processes);
      } else {
        setFilteredProcesses(processes.filter(process => process.type === selectedType));
      }
    }
  }, [selectedType, processes]);

  // Manejadores de eventos
  const handleTypeChange = (event, newValue) => {
    setSelectedType(newValue);
  };

  const handleOpenDialog = (process = null) => {
    if (process) {
      setCurrentProcess(process);
      setFormData({
        name: process.name,
        type: process.type,
        description: process.description,
        machine_type: process.machine_type || '',
        unit_cost: process.unit_cost.toString(),
        unit_type: process.unit_type,
        notes: process.notes || '',
        compatible_machines: process.compatible_machines || []
      });
    } else {
      setCurrentProcess(null);
      setFormData({
        name: '',
        type: 'Impresión',
        description: '',
        machine_type: '',
        unit_cost: '',
        unit_type: 'Unidad',
        notes: '',
        compatible_machines: []
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Si cambia el tipo de máquina, filtrar las máquinas compatibles
    if (name === 'machine_type') {
      // Filtrar las máquinas compatibles que coincidan con el nuevo tipo
      const filteredMachines = formData.compatible_machines.filter(machineId => {
        const machine = machines.find(m => m.machine_id === machineId);
        return machine && machine.type === value;
      });

      setFormData(prev => ({
        ...prev,
        [name]: value,
        compatible_machines: filteredMachines
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async () => {
    try {
      // Validar campos requeridos
      if (!formData.name || !formData.type || !formData.description) {
        throw new Error('Por favor complete todos los campos requeridos');
      }

      // Validar que el costo sea un número
      const unitCost = parseFloat(formData.unit_cost);
      if (isNaN(unitCost) || unitCost < 0) {
        throw new Error('El costo por unidad debe ser un número válido');
      }

      // Preparar datos para enviar
      const processData = {
        ...formData,
        unit_cost: unitCost
      };

      // Si estamos editando, incluir el ID
      if (currentProcess) {
        processData.process_id = currentProcess.process_id;
      }

      // Determinar URL y método
      const url = currentProcess
        ? buildApiUrl(`/processes/${currentProcess.process_id}`)
        : buildApiUrl('/processes/');
      const method = currentProcess ? 'PUT' : 'POST';

      // Enviar solicitud
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(processData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al guardar el proceso');
      }

      // Actualizar UI
      setSnackbar({
        open: true,
        message: currentProcess ? 'Proceso actualizado correctamente' : 'Proceso creado correctamente',
        severity: 'success'
      });
      handleCloseDialog();
      fetchProcesses();
    } catch (err) {
      setSnackbar({
        open: true,
        message: err.message,
        severity: 'error'
      });
    }
  };

  const handleDeleteProcess = async (processId) => {
    if (!window.confirm('¿Está seguro de que desea eliminar este proceso?')) {
      return;
    }

    try {
      const response = await fetch(buildApiUrl(`/processes/${processId}`), {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al eliminar el proceso');
      }

      setSnackbar({
        open: true,
        message: 'Proceso eliminado correctamente',
        severity: 'success'
      });
      fetchProcesses();
    } catch (err) {
      setSnackbar({
        open: true,
        message: err.message,
        severity: 'error'
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Renderizado
  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">Catálogo de Procesos</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Añadir Proceso
        </Button>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={selectedType}
          onChange={handleTypeChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          {PROCESS_TYPES.map((type) => (
            <Tab
              key={type}
              label={type === 'Todos' ? type : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {type}
                  {type !== 'Todos' && (
                    <Chip
                      label={processes.filter(p => p.type === type).length}
                      size="small"
                      color={TYPE_COLORS[type]}
                      sx={{ minWidth: 28, height: 20 }}
                    />
                  )}
                </Box>
              )}
              value={type}
              sx={{
                '&.Mui-selected': {
                  color: type !== 'Todos' ? `${TYPE_COLORS[type]}.main` : 'primary.main',
                  fontWeight: 'bold',
                }
              }}
            />
          ))}
        </Tabs>
      </Paper>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Nombre</TableCell>
              <TableCell>Tipo</TableCell>
              <TableCell>Descripción</TableCell>
              <TableCell>Tipo de Máquina</TableCell>
              <TableCell>Máquinas Compatibles</TableCell>
              <TableCell>Costo por Unidad</TableCell>
              <TableCell>Tipo de Unidad</TableCell>
              <TableCell>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredProcesses.length > 0 ? (
              filteredProcesses.map((process) => (
                <TableRow key={process.process_id}>
                  <TableCell>{process.name}</TableCell>
                  <TableCell>
                    <Chip
                      label={process.type}
                      color={TYPE_COLORS[process.type]}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{process.description}</TableCell>
                  <TableCell>{process.machine_type || 'N/A'}</TableCell>
                  <TableCell>
                    {process.compatible_machines && process.compatible_machines.length > 0 ? (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {process.compatible_machines.map((machineId) => {
                          const machine = machines.find(m => m.machine_id === machineId);
                          return machine ? (
                            <Chip
                              key={machineId}
                              label={machine.name}
                              size="small"
                              sx={{ fontSize: '0.7rem' }}
                            />
                          ) : null;
                        })}
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No asignadas
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>{process.unit_cost.toFixed(2)} €</TableCell>
                  <TableCell>{process.unit_type}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleOpenDialog(process)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteProcess(process.process_id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No hay procesos disponibles
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Diálogo para añadir/editar proceso */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {currentProcess ? 'Editar Proceso' : 'Añadir Nuevo Proceso'}
          <IconButton
            aria-label="close"
            onClick={handleCloseDialog}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Box component="form" sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mt: 1 }}>
            <TextField
              label="Nombre"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
              margin="normal"
            />
            <FormControl fullWidth margin="normal" required>
              <InputLabel>Tipo de Proceso</InputLabel>
              <Select
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                label="Tipo de Proceso"
              >
                {PROCESS_TYPES.filter(type => type !== 'Todos').map((type) => (
                  <MenuItem key={type} value={type}>{type}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              label="Descripción"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              fullWidth
              required
              margin="normal"
              multiline
              rows={2}
              sx={{ gridColumn: '1 / span 2' }}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Tipo de Máquina (opcional)</InputLabel>
              <Select
                name="machine_type"
                value={formData.machine_type}
                onChange={handleInputChange}
                label="Tipo de Máquina (opcional)"
              >
                {MACHINE_TYPES.map((type) => (
                  <MenuItem key={type} value={type}>{type || 'Ninguno'}</MenuItem>
                ))}
              </Select>
            </FormControl>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                label="Costo por Unidad"
                name="unit_cost"
                type="number"
                value={formData.unit_cost}
                onChange={handleInputChange}
                fullWidth
                required
                margin="normal"
                InputProps={{
                  endAdornment: '€',
                  inputProps: { min: 0, step: 0.01 }
                }}
              />
              <FormControl fullWidth margin="normal" required>
                <InputLabel>Tipo de Unidad</InputLabel>
                <Select
                  name="unit_type"
                  value={formData.unit_type}
                  onChange={handleInputChange}
                  label="Tipo de Unidad"
                >
                  {UNIT_TYPES.map((type) => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            <TextField
              label="Notas (opcional)"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              multiline
              rows={3}
              sx={{ gridColumn: '1 / span 2' }}
            />

            {/* Selector de máquinas compatibles */}
            <FormControl fullWidth margin="normal" sx={{ gridColumn: '1 / span 2' }}>
              <InputLabel id="compatible-machines-label">Máquinas Compatibles</InputLabel>
              <Select
                labelId="compatible-machines-label"
                id="compatible-machines"
                multiple
                value={formData.compatible_machines || []}
                onChange={(e) => setFormData({ ...formData, compatible_machines: e.target.value })}
                input={<OutlinedInput label="Máquinas Compatibles" />}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((machineId) => {
                      const machine = machines.find(m => m.machine_id === machineId);
                      return machine ? (
                        <Chip key={machineId} label={machine.name} size="small" />
                      ) : null;
                    })}
                  </Box>
                )}
                MenuProps={MenuProps}
              >
                {machines
                  .filter(machine => !formData.machine_type || machine.type === formData.machine_type)
                  .map((machine) => (
                    <MenuItem key={machine.machine_id} value={machine.machine_id}>
                      <Checkbox checked={formData.compatible_machines.indexOf(machine.machine_id) > -1} />
                      <ListItemText
                        primary={machine.name}
                        secondary={`${machine.type} - ${machine.manufacturer} ${machine.model}`}
                      />
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancelar</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {currentProcess ? 'Actualizar' : 'Guardar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar para notificaciones */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default ProcessCatalog;
