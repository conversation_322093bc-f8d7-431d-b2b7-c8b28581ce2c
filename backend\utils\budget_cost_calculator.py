"""
Utilidades para calcular costes de presupuestos
"""
from utils.paper_cost_calculator import calculate_sheet_cost
from utils.catalog_manager import load_paper_catalog, load_machine_catalog, load_process_catalog

def calculate_budget_costs(budget_data):
    """
    Calcula los costes totales de un presupuesto

    Args:
        budget_data (dict): Datos del presupuesto

    Returns:
        dict: Presupuesto actualizado con los costes calculados
    """
    # Inicializar costes
    paper_cost = 0.0
    machine_cost = 0.0
    process_costs = []
    total_cost = 0.0

    # Obtener catálogos
    paper_catalog = load_paper_catalog()
    machine_catalog = load_machine_catalog()

    # Calcular coste del papel si hay un papel seleccionado y cálculo de pliegos
    if budget_data.get("paper_id") and budget_data.get("sheet_calculation"):
        # Buscar el papel en el catálogo
        paper = next((p for p in paper_catalog if p["product_id"] == budget_data["paper_id"]), None)

        if paper:
            # Obtener el número total de pliegos del cálculo
            total_sheets = budget_data["sheet_calculation"].get("mejor_combinacion", {}).get("total_pliegos", 0)

            # Calcular el coste por pliego
            sheet_cost = calculate_sheet_cost(paper)

            # Calcular el coste total del papel (pliegos * coste por pliego * cantidad)
            paper_cost = sheet_cost * total_sheets * budget_data["quantity"]

    # Calcular coste de la máquina si hay una máquina seleccionada
    if budget_data.get("machine_id"):
        # Buscar la máquina en el catálogo
        machine = next((m for m in machine_catalog if m["machine_id"] == budget_data["machine_id"]), None)

        if machine and machine.get("hourly_cost"):
            # Estimar tiempo de impresión (horas)
            # Aquí podríamos tener una fórmula más compleja basada en el tipo de máquina,
            # la cantidad de pliegos, etc. Por ahora usamos una estimación simple.
            estimated_hours = 0

            if budget_data.get("sheet_calculation"):
                total_sheets = budget_data["sheet_calculation"].get("mejor_combinacion", {}).get("total_pliegos", 0)
                # Estimación simple: 1 hora por cada 1000 pliegos
                estimated_hours = (total_sheets * budget_data["quantity"]) / 1000

                # Mínimo 1 hora
                estimated_hours = max(1, estimated_hours)

            # Calcular el Coste Fijo de Arranque (CFA)
            cfa_percentage = machine.get("cfa_percentage", 0.0)
            cfa_cost = (machine["hourly_cost"] * cfa_percentage / 100.0)

            # Calcular coste de la máquina (CFA + tiempo de uso)
            machine_usage_cost = machine["hourly_cost"] * estimated_hours
            machine_cost = cfa_cost + machine_usage_cost

            # Guardar el desglose para mostrarlo en el PDF
            budget_data["machine_cost_breakdown"] = {
                "cfa_percentage": cfa_percentage,
                "cfa_cost": round(cfa_cost, 2),
                "hourly_cost": machine["hourly_cost"],
                "estimated_hours": round(estimated_hours, 2),
                "usage_cost": round(machine_usage_cost, 2),
                "total_machine_cost": round(machine_cost, 2)
            }

    # Calcular coste total
    total_cost = paper_cost + machine_cost + sum(p.get("cost", 0) for p in process_costs)

    # Actualizar el presupuesto con los costes calculados
    budget_data["paper_cost"] = round(paper_cost, 2)
    budget_data["machine_cost"] = round(machine_cost, 2)
    budget_data["process_costs"] = process_costs
    budget_data["total_cost"] = round(total_cost, 2)

    return budget_data

def add_process_to_budget(budget_data, process_id, quantity=1):
    """
    Añade un proceso al presupuesto y recalcula los costes

    Args:
        budget_data (dict): Datos del presupuesto
        process_id (str): ID del proceso a añadir
        quantity (int): Cantidad del proceso

    Returns:
        dict: Presupuesto actualizado
    """
    # Obtener catálogo de procesos
    process_catalog = load_process_catalog()

    # Buscar el proceso en el catálogo
    process = next((p for p in process_catalog if p["process_id"] == process_id), None)

    if not process:
        return budget_data

    # Inicializar la lista de procesos si no existe
    if not budget_data.get("process_costs"):
        budget_data["process_costs"] = []

    # Calcular coste del proceso
    process_cost = process["unit_cost"] * quantity

    # Añadir el proceso a la lista
    budget_data["process_costs"].append({
        "process_id": process_id,
        "name": process["name"],
        "quantity": quantity,
        "unit_cost": process["unit_cost"],
        "cost": process_cost
    })

    # Recalcular el coste total
    total_cost = budget_data.get("paper_cost", 0) + budget_data.get("machine_cost", 0)
    total_cost += sum(p.get("cost", 0) for p in budget_data["process_costs"])

    budget_data["total_cost"] = round(total_cost, 2)

    return budget_data

def remove_process_from_budget(budget_data, process_index):
    """
    Elimina un proceso del presupuesto y recalcula los costes

    Args:
        budget_data (dict): Datos del presupuesto
        process_index (int): Índice del proceso a eliminar

    Returns:
        dict: Presupuesto actualizado
    """
    if not budget_data.get("process_costs") or process_index >= len(budget_data["process_costs"]):
        return budget_data

    # Eliminar el proceso
    budget_data["process_costs"].pop(process_index)

    # Recalcular el coste total
    total_cost = budget_data.get("paper_cost", 0) + budget_data.get("machine_cost", 0)
    total_cost += sum(p.get("cost", 0) for p in budget_data["process_costs"])

    budget_data["total_cost"] = round(total_cost, 2)

    return budget_data
