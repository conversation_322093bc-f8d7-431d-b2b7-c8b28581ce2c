from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from typing import Optional, List, Dict

# Tarifas de envío basadas en el peso (más graduales)
SHIPPING_RATES = [
    {"min_weight": 0, "max_weight": 1, "cost": 5.0},
    {"min_weight": 1, "max_weight": 5, "cost": 10.0},
    {"min_weight": 5, "max_weight": 10, "cost": 15.0},
    {"min_weight": 10, "max_weight": 20, "cost": 20.0},
    {"min_weight": 20, "max_weight": 50, "cost": 30.0},
    {"min_weight": 50, "max_weight": 100, "cost": 50.0},
    {"min_weight": 100, "max_weight": 200, "cost": 75.0},
    {"min_weight": 200, "max_weight": 500, "cost": 100.0},
    {"min_weight": 500, "max_weight": 1000, "cost": 150.0},
    {"min_weight": 1000, "max_weight": float('inf'), "cost": 200.0}
]

router = APIRouter(
    prefix="/shipping",
    tags=["shipping"],
    responses={400: {"description": "Error en los parámetros"}}
)

class ShippingCostRequest(BaseModel):
    weight_kg: float
    country: Optional[str] = None

class ShippingCostResponse(BaseModel):
    weight_kg: float
    country: str
    shipping_cost: float
    distance_factor: float

def get_distance_factor(country: Optional[str] = None) -> float:
    """
    Calcula el factor de distancia basado en el país de destino
    """
    # Factor por defecto (nacional)
    distance_factor = 1.0
    
    # Si el cliente es internacional, aumentar el factor
    # Considerar país vacío o None como nacional
    if country and country != "" and country != "España":
        distance_factor = 2.5  # Factor internacional
    
    return distance_factor

def calculate_shipping_cost_by_weight(weight_kg: float, country: Optional[str] = None) -> float:
    """
    Calcula el coste de envío basado en el peso y el país de destino
    """
    # Calcular el coste de envío basado en el peso total
    shipping_cost = 0.0
    
    # Para pesos muy grandes, usar un cálculo progresivo
    if weight_kg >= SHIPPING_RATES[-1]["min_weight"]:
        # Obtener la última tarifa como base
        base_rate = SHIPPING_RATES[-1]["cost"]
        # Añadir un costo adicional por cada 100kg extra
        extra_weight = weight_kg - SHIPPING_RATES[-1]["min_weight"]
        extra_cost = (extra_weight // 100) * 25.0  # 25€ por cada 100kg adicionales
        shipping_cost = base_rate + extra_cost
    else:
        # Usar la tabla de tarifas para pesos menores
        for rate in SHIPPING_RATES:
            if rate["min_weight"] <= weight_kg < rate["max_weight"]:
                shipping_cost = rate["cost"]
                break

    # Aplicar el factor de distancia
    distance_factor = get_distance_factor(country)
    shipping_cost *= distance_factor

    return shipping_cost

@router.post("/calculate", response_model=ShippingCostResponse)
async def calculate_shipping_cost(request: ShippingCostRequest):
    """
    Calcula el coste de envío basado en el peso y el país de destino
    """
    weight = request.weight_kg
    country = request.country

    # Validar el peso
    if weight <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="El peso debe ser mayor que 0"
        )

    # Calcular el coste de envío
    shipping_cost = calculate_shipping_cost_by_weight(weight, country)

    # Determinar el factor de distancia
    distance_factor = get_distance_factor(country)

    return ShippingCostResponse(
        weight_kg=weight,
        country=country,
        shipping_cost=shipping_cost,
        distance_factor=distance_factor
    )
