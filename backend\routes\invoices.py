from fastapi import APIRouter, HTTPException, status, Depends, Query, Body
from typing import List, Dict, Any, Optional
from datetime import datetime
import os
import json
from models.budget import Budget
from utils.invoice_manager import (
    get_billable_budgets,
    mark_budget_as_billed,
    get_next_invoice_number,
    get_invoice_directory
)
from utils.logger import log_info, log_error
from dependencies.auth import get_current_active_user
from utils.data_manager import read_data, write_data
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import mm

router = APIRouter(
    prefix="/invoices",
    tags=["invoices"],
    responses={404: {"description": "Factura no encontrada"}}
)

@router.get("/billable", response_model=List[Budget])
async def get_billable_budgets_endpoint(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Obtiene los presupuestos que pueden ser facturados (Completados o Enviados y no facturados)
    """
    log_info("Solicitud para obtener presupuestos facturables")
    billable = get_billable_budgets()
    return billable

@router.post("/{budget_id}/generate", response_model=Dict[str, Any])
async def generate_invoice(
    budget_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """
    Genera una factura para un presupuesto específico
    """
    log_info(f"Solicitud para generar factura para el presupuesto {budget_id}")

    # Obtener todos los presupuestos
    budgets = read_data("budgets")

    # Buscar el presupuesto específico
    budget = None
    for b in budgets:
        if b.get("budget_id") == budget_id:
            budget = b
            break

    if not budget:
        log_error(f"Presupuesto {budget_id} no encontrado")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Presupuesto con ID {budget_id} no encontrado"
        )

    # Verificar que el presupuesto esté en estado Completado o Enviado
    if budget.get("status") not in ["Completado", "Enviado"]:
        log_error(f"El presupuesto {budget_id} no está en estado Completado o Enviado")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"El presupuesto debe estar en estado Completado o Enviado para generar una factura"
        )

    # Verificar que el presupuesto no esté ya facturado
    if budget.get("facturado", False):
        log_error(f"El presupuesto {budget_id} ya está facturado")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"El presupuesto ya está facturado con número {budget.get('numero_factura')}"
        )

    # Obtener datos del cliente
    client_id = budget.get("client_id")
    clients = read_data("clients")
    client = None
    for c in clients:
        if c.get("client_id") == client_id:
            client = c
            break

    if not client:
        log_error(f"Cliente {client_id} no encontrado")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cliente con ID {client_id} no encontrado"
        )

    # Generar número de factura
    invoice_number = get_next_invoice_number()

    # Obtener directorio para guardar la factura
    invoice_date = datetime.now()
    invoice_dir = get_invoice_directory(invoice_date)

    # Generar nombre de archivo para la factura
    invoice_filename = f"{invoice_number}.pdf"
    invoice_path = os.path.join(invoice_dir, invoice_filename)

    try:
        # Crear la factura en PDF
        create_invoice_pdf(invoice_path, budget, client, invoice_number, invoice_date)

        # Marcar el presupuesto como facturado
        mark_budget_as_billed(budget_id, invoice_number)

        # Extraer solo el nombre del archivo para la respuesta
        invoice_filename_only = os.path.basename(invoice_path)

        # Construir una ruta relativa para el frontend
        year_month = invoice_number.split('-')[1]
        year = year_month[:4]
        month = year_month[4:6]
        relative_path = f"facturas/{year}/{month}/{invoice_filename_only}"

        return {
            "success": True,
            "message": f"Factura generada correctamente",
            "invoice_number": invoice_number,
            "invoice_path": relative_path,  # Usar ruta relativa
            "invoice_date": invoice_date.isoformat()
        }

    except Exception as e:
        log_error(f"Error al generar factura: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al generar factura: {str(e)}"
        )

@router.get("/list", response_model=List[Dict[str, Any]])
async def list_invoices(
    year: Optional[int] = Query(None, description="Año de las facturas"),
    month: Optional[int] = Query(None, description="Mes de las facturas"),
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """
    Lista las facturas generadas
    """
    log_info(f"Solicitud para listar facturas")

    # Si no se especifica año, usar el año actual
    if year is None:
        year = datetime.now().year

    # Construir la ruta al directorio de facturas
    if month is None:
        # Listar facturas de todo el año
        year_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "facturas", str(year))
        if not os.path.exists(year_dir):
            return []

        invoices = []
        for month_dir in sorted(os.listdir(year_dir)):
            if os.path.isdir(os.path.join(year_dir, month_dir)):
                month_path = os.path.join(year_dir, month_dir)
                for invoice_file in os.listdir(month_path):
                    if invoice_file.endswith(".pdf"):
                        invoice_number = os.path.splitext(invoice_file)[0]
                        invoice_path = os.path.join(month_path, invoice_file)
                        invoice_date = f"{year}-{month_dir}"

                        # Buscar el presupuesto asociado a esta factura
                        budget_info = get_budget_by_invoice_number(invoice_number)

                        # Construir una ruta relativa para el frontend
                        relative_path = f"facturas/{year}/{month_dir}/{invoice_file}"

                        invoices.append({
                            "invoice_number": invoice_number,
                            "invoice_path": relative_path,  # Usar ruta relativa
                            "invoice_date": invoice_date,
                            "budget_id": budget_info.get("budget_id") if budget_info else None,
                            "client_name": budget_info.get("client_name") if budget_info else None,
                            "amount": budget_info.get("amount") if budget_info else None
                        })

        return invoices
    else:
        # Listar facturas de un mes específico
        month_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "data",
            "facturas",
            str(year),
            f"{month:02d}"
        )

        if not os.path.exists(month_dir):
            return []

        invoices = []
        for invoice_file in os.listdir(month_dir):
            if invoice_file.endswith(".pdf"):
                invoice_number = os.path.splitext(invoice_file)[0]
                invoice_path = os.path.join(month_dir, invoice_file)
                invoice_date = f"{year}-{month:02d}"

                # Buscar el presupuesto asociado a esta factura
                budget_info = get_budget_by_invoice_number(invoice_number)

                # Construir una ruta relativa para el frontend
                relative_path = f"facturas/{year}/{month:02d}/{invoice_file}"

                invoices.append({
                    "invoice_number": invoice_number,
                    "invoice_path": relative_path,  # Usar ruta relativa
                    "invoice_date": invoice_date,
                    "budget_id": budget_info.get("budget_id") if budget_info else None,
                    "client_name": budget_info.get("client_name") if budget_info else None,
                    "amount": budget_info.get("amount") if budget_info else None
                })

        return invoices

def get_budget_by_invoice_number(invoice_number: str) -> Dict[str, Any]:
    """
    Busca el presupuesto asociado a un número de factura
    """
    try:
        budgets = read_data("budgets")
        for budget in budgets:
            if budget.get("numero_factura") == invoice_number:
                # Obtener el nombre del cliente
                client_name = "Cliente desconocido"
                client_id = budget.get("client_id")
                if client_id:
                    clients = read_data("clients")
                    for client in clients:
                        if client.get("client_id") == client_id:
                            client_name = client.get("company", {}).get("name", "Cliente desconocido")
                            break

                # Calcular el importe total
                total_amount = 0.0

                # Sumar costos de partes
                parts = budget.get("parts", [])
                for part in parts:
                    total_amount += part.get("total_cost", 0.0)

                # Sumar costos de procesos
                total_amount += budget.get("process_total_cost", 0.0)

                # Sumar costo de envío
                total_amount += budget.get("shipping_cost", 0.0)

                # Aplicar descuento del cliente si existe
                client_data = budget.get("client_data", {})
                discount_percentage = client_data.get("discount_percentage", 0.0)
                if discount_percentage > 0:
                    discount_amount = total_amount * (discount_percentage / 100)
                    total_amount -= discount_amount

                return {
                    "budget_id": budget.get("budget_id"),
                    "client_name": client_name,
                    "amount": round(total_amount, 2)
                }

        return {}

    except Exception as e:
        log_error(f"Error al buscar presupuesto por número de factura: {str(e)}")
        return {}

def create_invoice_pdf(invoice_path: str, budget: Dict[str, Any], client: Dict[str, Any], invoice_number: str, invoice_date: datetime):
    """
    Crea un archivo PDF con la factura
    """
    try:
        # Crear el documento PDF
        doc = SimpleDocTemplate(
            invoice_path,
            pagesize=A4,
            rightMargin=20*mm,
            leftMargin=20*mm,
            topMargin=20*mm,
            bottomMargin=20*mm
        )

        # Estilos
        styles = getSampleStyleSheet()
        title_style = styles['Heading1']
        subtitle_style = styles['Heading2']
        normal_style = styles['Normal']

        # Elementos del documento
        elements = []

        # Ruta al logo
        logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "images", "trikyprinter-logo.png")

        # Verificar si el logo existe
        if os.path.exists(logo_path):
            # Añadir logo
            logo = Image(logo_path, width=50*mm, height=20*mm)
            elements.append(logo)
        else:
            # Si no se encuentra el logo, usar texto
            elements.append(Paragraph("TrikyPrinter", title_style))

        elements.append(Spacer(1, 5*mm))

        # Título
        elements.append(Paragraph(f"FACTURA", title_style))
        elements.append(Spacer(1, 10*mm))

        # Información de la factura
        elements.append(Paragraph(f"Número de factura: {invoice_number}", subtitle_style))
        elements.append(Paragraph(f"Fecha: {invoice_date.strftime('%d/%m/%Y')}", normal_style))
        elements.append(Spacer(1, 5*mm))

        # Información de la empresa
        elements.append(Paragraph("DATOS DE LA EMPRESA:", subtitle_style))
        elements.append(Paragraph("TrikyPrinter", normal_style))
        elements.append(Paragraph("CIF: B12345678", normal_style))
        elements.append(Paragraph("Dirección: c/Costa Rica 11", normal_style))
        elements.append(Paragraph("Ciudad: 28016 Madrid", normal_style))
        elements.append(Paragraph("Teléfono: +34 91 000 00 00", normal_style))
        elements.append(Paragraph("Email: <EMAIL>", normal_style))
        elements.append(Spacer(1, 5*mm))

        # Información del cliente
        elements.append(Paragraph("CLIENTE:", subtitle_style))
        company = client.get("company", {})
        contact = client.get("contact", {})
        elements.append(Paragraph(f"Nombre: {company.get('name', 'N/A')}", normal_style))
        elements.append(Paragraph(f"CIF/NIF: {client.get('billing_code', 'N/A')}", normal_style))

        address = company.get("address", {})
        address_str = f"{address.get('street', '')}, {address.get('city', '')}, {address.get('postal_code', '')}, {address.get('region', '')}, {address.get('country', '')}"
        elements.append(Paragraph(f"Dirección: {address_str}", normal_style))

        elements.append(Paragraph(f"Contacto: {contact.get('first_name', '')} {contact.get('last_name', '')}", normal_style))
        elements.append(Paragraph(f"Email: {contact.get('email', 'N/A')}", normal_style))
        elements.append(Paragraph(f"Teléfono: {contact.get('phone', 'N/A')}", normal_style))
        elements.append(Spacer(1, 10*mm))

        # Información del presupuesto
        elements.append(Paragraph("DETALLES DEL TRABAJO:", subtitle_style))
        elements.append(Paragraph(f"Presupuesto: {budget.get('budget_id', 'N/A')}", normal_style))
        elements.append(Paragraph(f"OT: {budget.get('ot_number', 'N/A')}", normal_style))
        elements.append(Paragraph(f"Descripción: {budget.get('description', 'N/A')}", normal_style))
        elements.append(Paragraph(f"Tipo de trabajo: {budget.get('job_type', 'N/A')}", normal_style))
        elements.append(Paragraph(f"Cantidad: {budget.get('quantity', 0)}", normal_style))
        elements.append(Spacer(1, 10*mm))

        # Tabla de conceptos
        data = [
            ["Concepto", "Cantidad", "Precio unitario", "Total"]
        ]

        # Añadir partes del presupuesto
        parts = budget.get("parts", [])
        for part in parts:
            part_name = part.get("name", "Parte sin nombre")
            part_description = part.get("description", "")
            part_cost = part.get("total_cost", 0.0)

            data.append([
                f"{part_name} - {part_description}",
                "1",
                f"{part_cost:.2f} €",
                f"{part_cost:.2f} €"
            ])

        # Añadir procesos de acabado
        processes = budget.get("process_costs", [])
        for process in processes:
            process_name = process.get("name", "Proceso sin nombre")
            process_quantity = process.get("quantity", 1)
            process_unit_cost = process.get("unit_cost", 0.0)
            process_total_cost = process.get("total_cost", 0.0)

            data.append([
                process_name,
                str(process_quantity),
                f"{process_unit_cost:.2f} €",
                f"{process_total_cost:.2f} €"
            ])

        # Añadir costo de envío si existe
        shipping_cost = budget.get("shipping_cost", 0.0)
        if shipping_cost > 0:
            data.append([
                "Gastos de envío",
                "1",
                f"{shipping_cost:.2f} €",
                f"{shipping_cost:.2f} €"
            ])

        # Crear la tabla
        table = Table(data, colWidths=[250, 70, 100, 80])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        elements.append(table)
        elements.append(Spacer(1, 10*mm))

        # Calcular totales
        subtotal = 0.0

        # Sumar costos de partes
        for part in parts:
            subtotal += part.get("total_cost", 0.0)

        # Sumar costos de procesos
        subtotal += budget.get("process_total_cost", 0.0)

        # Sumar costo de envío
        subtotal += shipping_cost

        # Aplicar descuento del cliente si existe
        client_data = budget.get("client_data", {})
        discount_percentage = client_data.get("discount_percentage", 0.0)
        discount_amount = 0.0
        if discount_percentage > 0:
            discount_amount = subtotal * (discount_percentage / 100)

        # Calcular IVA (21%)
        iva_rate = 0.21
        iva_amount = (subtotal - discount_amount) * iva_rate

        # Calcular total
        total = subtotal - discount_amount + iva_amount

        # Tabla de totales
        totals_data = [
            ["Subtotal", f"{subtotal:.2f} €"],
            ["Descuento", f"{discount_amount:.2f} €"],
            ["Base imponible", f"{(subtotal - discount_amount):.2f} €"],
            ["IVA (21%)", f"{iva_amount:.2f} €"],
            ["TOTAL", f"{total:.2f} €"]
        ]

        totals_table = Table(totals_data, colWidths=[150, 100])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('LINEABOVE', (0, -1), (-1, -1), 1, colors.black),
            ('LINEBELOW', (0, -1), (-1, -1), 1, colors.black),
        ]))

        elements.append(totals_table)
        elements.append(Spacer(1, 15*mm))

        # Información de pago
        elements.append(Paragraph("FORMA DE PAGO:", subtitle_style))
        elements.append(Paragraph("Transferencia bancaria", normal_style))
        elements.append(Paragraph("IBAN: ES12 3456 7890 1234 5678 9012", normal_style))
        elements.append(Paragraph("Banco: Banco Ejemplo", normal_style))
        elements.append(Spacer(1, 5*mm))

        # Notas
        elements.append(Paragraph("NOTAS:", subtitle_style))
        elements.append(Paragraph("Plazo de pago: 30 días desde la fecha de emisión de la factura.", normal_style))
        elements.append(Paragraph("Para cualquier consulta relacionada con esta factura, por favor contacte con nuestro departamento de administración.", normal_style))

        # Pie de página
        elements.append(Spacer(1, 10*mm))
        elements.append(Paragraph(f"TrikyPrinter - c/Costa Rica 11, 28016 Madrid - CIF: B12345678", normal_style))
        elements.append(Paragraph(f"Tel: +34 91 000 00 00 - Email: <EMAIL> - www.trikyprinter.com", normal_style))
        elements.append(Paragraph(f"Documento generado el {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}", normal_style))

        # Construir el documento
        doc.build(elements)

        log_info(f"Factura generada correctamente: {invoice_path}")
        return True

    except Exception as e:
        log_error(f"Error al crear PDF de factura: {str(e)}")
        raise e
