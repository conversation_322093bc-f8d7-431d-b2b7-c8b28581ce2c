import { buildApiUrl } from '../config';

/**
 * Servicio para manejar la autenticación en el frontend
 */
class AuthService {
  /**
   * Iniciar sesión con nombre de usuario y contraseña
   * @param {Object} credentials - Credenciales de usuario
   * @param {string} credentials.username - Nombre de usuario
   * @param {string} credentials.password - Contraseña
   * @returns {Promise<Object>} - Datos del usuario y token
   */
  async login(credentials) {
    try {
      // Crear un objeto URLSearchParams para enviar las credenciales como application/x-www-form-urlencoded
      const formData = new URLSearchParams();
      formData.append('username', credentials.username);
      formData.append('password', credentials.password);

      // Realizar la solicitud de inicio de sesión
      const response = await fetch(buildApiUrl('/auth/login'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al iniciar sesión');
      }

      const data = await response.json();

      // Obtener información del usuario
      const userResponse = await fetch(buildApiUrl('/auth/me'), {
        headers: {
          'Authorization': `Bearer ${data.access_token}`
        }
      });

      if (!userResponse.ok) {
        throw new Error('Error al obtener información del usuario');
      }

      const userData = await userResponse.json();

      return {
        token: data.access_token,
        user: userData
      };
    } catch (error) {
      console.error('Error en login:', error);
      throw error;
    }
  }

  /**
   * Obtener información del usuario actual
   * @param {string} token - Token JWT
   * @returns {Promise<Object>} - Datos del usuario
   */
  async getCurrentUser(token) {
    try {
      const response = await fetch(buildApiUrl('/auth/me'), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al obtener información del usuario');
      }

      return await response.json();
    } catch (error) {
      console.error('Error al obtener usuario actual:', error);
      throw error;
    }
  }

  /**
   * Obtener todos los usuarios (solo para administradores)
   * @param {string} token - Token JWT
   * @returns {Promise<Array>} - Lista de usuarios
   */
  async getAllUsers(token) {
    try {
      const response = await fetch(buildApiUrl('/auth/users'), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al obtener usuarios');
      }

      return await response.json();
    } catch (error) {
      console.error('Error al obtener usuarios:', error);
      throw error;
    }
  }

  /**
   * Crear un nuevo usuario (solo para administradores)
   * @param {Object} userData - Datos del usuario
   * @param {string} token - Token JWT
   * @returns {Promise<Object>} - Datos del usuario creado
   */
  async createUser(userData, token) {
    try {
      const response = await fetch(buildApiUrl('/auth/users'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(userData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al crear usuario');
      }

      return await response.json();
    } catch (error) {
      console.error('Error al crear usuario:', error);
      throw error;
    }
  }

  /**
   * Actualizar un usuario existente (solo para administradores)
   * @param {string} userId - ID del usuario
   * @param {Object} userData - Datos del usuario
   * @param {string} token - Token JWT
   * @returns {Promise<Object>} - Datos del usuario actualizado
   */
  async updateUser(userId, userData, token) {
    try {
      const response = await fetch(buildApiUrl(`/auth/users/${userId}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(userData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al actualizar usuario');
      }

      return await response.json();
    } catch (error) {
      console.error('Error al actualizar usuario:', error);
      throw error;
    }
  }

  /**
   * Eliminar un usuario (solo para administradores)
   * @param {string} userId - ID del usuario
   * @param {string} token - Token JWT
   * @returns {Promise<Object>} - Mensaje de confirmación
   */
  async deleteUser(userId, token) {
    try {
      const response = await fetch(buildApiUrl(`/auth/users/${userId}`), {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al eliminar usuario');
      }

      return await response.json();
    } catch (error) {
      console.error('Error al eliminar usuario:', error);
      throw error;
    }
  }
}

export default new AuthService();
