import { buildApiUrl } from '../config';

/**
 * Servicio para manejar las operaciones relacionadas con el dashboard
 */
class DashboardService {
  /**
   * Obtiene las estadísticas generales para el dashboard
   *
   * @param {string} token - Token de autenticación
   * @returns {Promise<Object>} - Estadísticas del dashboard
   */
  static async getDashboardStats(token) {
    try {
      const response = await fetch(buildApiUrl('/dashboard/stats'), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al obtener estadísticas del dashboard');
      }

      return await response.json();
    } catch (error) {
      console.error('Error en DashboardService.getDashboardStats:', error);
      throw error;
    }
  }

  /**
   * Obtiene las estadísticas de costes y beneficios para el dashboard
   *
   * @param {string} token - Token de autenticación
   * @returns {Promise<Object>} - Estadísticas de costes y beneficios
   */
  static async getCostProfitStats(token) {
    try {
      const response = await fetch(buildApiUrl('/dashboard/cost-profit'), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al obtener estadísticas de costes y beneficios');
      }

      return await response.json();
    } catch (error) {
      console.error('Error en DashboardService.getCostProfitStats:', error);
      throw error;
    }
  }
}

export default DashboardService;
