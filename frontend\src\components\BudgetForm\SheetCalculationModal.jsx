import React, { useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Paper,
  Grid,
  Divider,
  TextField,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Tooltip
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import RestartAltIcon from '@mui/icons-material/RestartAlt';

const SheetCalculationModal = ({
  open,
  onClose,
  sheetCalculation,
  budget,
  customMachinePrintTime,
  handleCustomMachineTimeChange,
  selectedProcesses,
  calculatedProcessCost
}) => {
  // Determinar si es una máquina offset o digital
  const isDigital = budget?.machine_data?.type === 'Digital';
  const isOffset = budget?.machine_data?.type === 'Offset';

  // Determinar si los datos provienen del endpoint v2/calculate-digital
  const isDigitalV2 = isDigital && sheetCalculation?.clicks_data;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        Resultado del cálculo de pliegos
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        {sheetCalculation && (
          <>
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Resumen
              </Typography>
              <Typography variant="body1">
                <strong>Producto:</strong> {budget.page_count || budget.pageCount} páginas de tamaño {budget.page_size || budget.pageSize} {(budget.page_size || budget.pageSize) === 'Personalizado' ? `(${budget.custom_page_size || budget.customPageSize})` : ''}
              </Typography>
              <Typography variant="body1">
                <strong>Tipo de impresión:</strong> {budget.machine_data && budget.machine_data.type ?
                  (budget.machine_data.type === 'Digital' ? 'Digital (clicks)' : 'Offset (planchas)') :
                  'No especificado'}
              </Typography>
              <Typography variant="body1">
                <strong>Número de ejemplares:</strong> {budget.copies || 0}
              </Typography>
              <Typography variant="body1">
                <strong>Papel seleccionado:</strong>
                {budget.paper_data ? (
                  <span style={{ fontWeight: 'bold' }}>
                    {budget.paper_data.descriptive_name || budget.paper_data.name || 'Sin nombre'} ({budget.paper_data.dimension_width} x {budget.paper_data.dimension_height} mm)
                  </span>
                ) : budget.paper ? (
                  <span style={{ fontWeight: 'bold' }}>
                    {budget.paper.descriptive_name || budget.paper.name || 'Sin nombre'} ({budget.paper.dimension_width} x {budget.paper.dimension_height} mm)
                  </span>
                ) : (
                  'No seleccionado'
                )}
              </Typography>
              <Typography variant="body1">
                <strong>Configuración de colores:</strong> {budget.colorConfig ? `${budget.colorConfig.frontColors}/${budget.colorConfig.backColors}${budget.colorConfig.pantones > 0 ? ` + ${budget.colorConfig.pantones} pantones` : ''}` : 'No especificada'}
              </Typography>
              <Typography variant="body1">
                <strong>Tipo de encuadernado:</strong> {budget.binding_type ?
                  (budget.binding_type === 'gathering' ? 'Alzado' :
                   budget.binding_type === 'collection' ? 'Grapado' :
                   budget.binding_type === 'none' ? 'Sin encuadernado' : budget.binding_type) : 'Alzado (por defecto)'}
              </Typography>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Esquema utilizado
              </Typography>
              <Paper elevation={2} sx={{ p: 2, bgcolor: '#f5f5f5' }}>
                <Typography variant="body1" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                  Total: {sheetCalculation && sheetCalculation.mejor_combinacion ? sheetCalculation.mejor_combinacion.total_pliegos :
                         (sheetCalculation && sheetCalculation.total_pliegos ? sheetCalculation.total_pliegos : 0)} pliegos /
                  {budget && budget.machine_data && budget.machine_data.type === 'Digital' ?
                    `${sheetCalculation && sheetCalculation.mejor_combinacion ? sheetCalculation.mejor_combinacion.total_clicks :
                      (sheetCalculation && sheetCalculation.total_clicks ? sheetCalculation.total_clicks : 0)} clicks` :
                    `${sheetCalculation && sheetCalculation.mejor_combinacion ? sheetCalculation.mejor_combinacion.total_planchas :
                      (sheetCalculation && sheetCalculation.total_planchas ? sheetCalculation.total_planchas : 0)} planchas`}
                </Typography>
                {/* Eliminamos la repetición de ejemplares */}
                <Typography variant="body2" gutterBottom>
                  <strong>Configuración de colores:</strong> {budget.colorConfig ? `${budget.colorConfig.frontColors}/${budget.colorConfig.backColors}${budget.colorConfig.pantones > 0 ? ` + ${budget.colorConfig.pantones} pantones` : ''}` : 'No especificada'}
                </Typography>

                {/* Mostrar total de pasadas si está disponible */}
                {sheetCalculation && sheetCalculation.mejor_combinacion && sheetCalculation.mejor_combinacion.total_passes && (
                  <Typography variant="body2" gutterBottom sx={{ mt: 1 }}>
                    <strong>Total de pasadas por máquina:</strong> {sheetCalculation.mejor_combinacion.total_passes} pasadas
                    {sheetCalculation.mejor_combinacion.total_passes > sheetCalculation.mejor_combinacion.total_pliegos && (
                      <span> (algunos pliegos requieren dos pasadas)</span>
                    )}
                  </Typography>
                )}

                <Table size="small" sx={{ mt: 2 }}>
                  <TableHead>
                    <TableRow sx={{ bgcolor: '#e0e0e0' }}>
                      <TableCell sx={{ fontWeight: 'bold' }}>Esquema</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Pliegos</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Págs/Pliego</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Disposición</TableCell>
                      {isOffset ? (
                        <>
                          <TableCell sx={{ fontWeight: 'bold' }}>Tipo Pliego</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Planchas</TableCell>
                        </>
                      ) : (
                        <>
                          <TableCell sx={{ fontWeight: 'bold' }}>Modo</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Clicks</TableCell>
                        </>
                      )}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {sheetCalculation &&
                     ((sheetCalculation.mejor_combinacion && sheetCalculation.mejor_combinacion.esquemas_utilizados) ||
                      sheetCalculation.esquemas_utilizados) ?
                      (sheetCalculation.esquemas_utilizados || sheetCalculation.mejor_combinacion.esquemas_utilizados || []).map((esquema, index) => {
                        // Obtener el color de fondo según el tipo de pliego
                        const getSheetTypeColor = (sheetType) => {
                          switch (sheetType) {
                            case 'Flat':
                              return '#e3f2fd'; // Azul claro
                            case 'WorkAndTurn':
                              return '#e8f5e9'; // Verde claro
                            case 'WorkAndBack':
                              return '#fff3e0'; // Naranja claro
                            case 'Perfecting':
                              return '#f3e5f5'; // Púrpura claro
                            default:
                              return 'transparent';
                          }
                        };

                        // Obtener la descripción del tipo de pliego
                        const getSheetTypeDescription = (sheetType) => {
                          switch (sheetType) {
                            case 'Flat':
                              return 'Una cara';
                            case 'WorkAndTurn':
                              return 'Tira-retira (mismas planchas)';
                            case 'WorkAndBack':
                              return 'Diferentes planchas';
                            case 'Perfecting':
                              return 'Ambas caras a la vez';
                            default:
                              return esquema.es_tira_retira ? 'Tira y retira' : 'Solo tira';
                          }
                        };

                        return (
                          <TableRow
                            key={index}
                            sx={{ backgroundColor: esquema.sheet_type ? getSheetTypeColor(esquema.sheet_type) : 'transparent' }}
                          >
                            <TableCell>{esquema.nombre}</TableCell>
                            <TableCell>{esquema.numero_pliegos}</TableCell>
                            <TableCell>{esquema.paginas_por_pliego}</TableCell>
                            <TableCell>
                              {esquema.disposicion ?
                                `${esquema.disposicion.paginas_ancho} × ${esquema.disposicion.paginas_alto}` :
                                (esquema.paginas_ancho && esquema.paginas_alto ?
                                  `${esquema.paginas_ancho} × ${esquema.paginas_alto}` : 'N/A')}
                            </TableCell>
                            <TableCell>
                              {isOffset ? (
                                esquema.sheet_type ? (
                                  <>
                                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                      {esquema.sheet_type}
                                    </Typography>
                                    <Typography variant="caption" display="block">
                                      {getSheetTypeDescription(esquema.sheet_type)}
                                    </Typography>
                                    {esquema.needs_two_passes && (
                                      <Typography variant="caption" display="block" color="warning.main">
                                        Requiere 2 pasadas
                                      </Typography>
                                    )}
                                  </>
                                ) : (
                                  esquema.es_tira_retira ? 'Tira y retira' : 'Solo tira'
                                )
                              ) : (
                                // Para máquinas digitales
                                <>
                                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                    {esquema.is_duplex ? 'Duplex' : 'Simplex'}
                                  </Typography>
                                  <Typography variant="caption" display="block">
                                    {esquema.is_color ? 'Color' : 'Blanco y negro'}
                                  </Typography>
                                  {esquema.clicks_per_sheet && (
                                    <Typography variant="caption" display="block">
                                      {esquema.clicks_per_sheet} clicks por pliego
                                    </Typography>
                                  )}
                                </>
                              )}
                            </TableCell>
                            <TableCell>
                              {isOffset ?
                                (esquema.plates_needed !== undefined ? esquema.plates_needed : '-') :
                                (esquema.clicks_per_sheet !== undefined ? esquema.clicks_per_sheet : '-')}
                            </TableCell>
                          </TableRow>
                        );
                      })
                    : <TableRow><TableCell colSpan={6}>No hay esquemas disponibles</TableCell></TableRow>}
                  </TableBody>
                </Table>


              </Paper>
            </Box>

            <Box>
              <Typography variant="h6" gutterBottom>
                Cálculo de costes
              </Typography>
              <Paper elevation={2} sx={{ p: 2, bgcolor: '#f5f5f5' }}>
                {budget && budget.paper_data && (
                  <>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                          Datos del papel
                        </Typography>
                        <Typography variant="body2">
                          <strong>Papel:</strong> {budget.paper_data.descriptive_name}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Dimensiones:</strong> {budget.paper_data.dimension_width} × {budget.paper_data.dimension_height} mm
                        </Typography>
                        <Typography variant="body2">
                          <strong>Coste por 1000 pliegos:</strong> {budget.paper_data.price_per_1000?.toFixed(2) || '0.00'} €
                        </Typography>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                          Datos de la máquina
                        </Typography>
                        {budget.machine_data ? (
                          <>
                            <Typography variant="body2">
                              <strong>Máquina:</strong> {budget.machine_data.name}
                            </Typography>
                            <Typography variant="body2">
                              <strong>Coste por hora:</strong> {budget.machine_data.hourly_cost?.toFixed(2) || '0.00'} €/h
                            </Typography>
                            {isOffset && (
                              <>
                                <Typography variant="body2">
                                  <strong>Tiempo de arranque:</strong> {sheetCalculation?.mejor_combinacion?.setup_time || budget.machine_data.setup_time || 30} minutos
                                </Typography>
                                <Typography variant="body2">
                                  <strong>Velocidad:</strong> {sheetCalculation?.mejor_combinacion?.sheets_per_hour || budget.machine_data.sheets_per_hour || 'N/A'} pliegos/hora
                                </Typography>
                              </>
                            )}
                            {isDigital && (
                              <>
                                <Typography variant="body2">
                                  <strong>Velocidad:</strong> {sheetCalculation?.mejor_combinacion?.print_speed || budget.machine_data.speed || 'N/A'} A4/minuto
                                </Typography>
                                <Typography variant="body2">
                                  <strong>Coste por click:</strong> {sheetCalculation?.clicks_data?.click_unit_cost?.toFixed(4) || budget.machine_data.click_color_cost?.toFixed(4) || '0.0000'} €
                                </Typography>
                              </>
                            )}

                          </>
                        ) : (
                          <Typography variant="body2">No hay máquina seleccionada</Typography>
                        )}
                      </Grid>
                    </Grid>

                    {/* Coste total del trabajo */}
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ p: 2, bgcolor: '#e3f2fd', borderRadius: 1, mt: 2 }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                        COSTE TOTAL DEL TRABAJO
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Typography variant="body1">
                            <strong>Ejemplares:</strong> {budget.copies || budget.copy_count}
                          </Typography>
                          <Typography variant="body1">
                            <strong>Pliegos totales:</strong> {sheetCalculation && sheetCalculation.mejor_combinacion ? sheetCalculation.mejor_combinacion.total_pliegos : 0}
                          </Typography>
                          {budget && budget.machine_data && budget.machine_data.type === 'Digital' ? (
                            <>
                              <Typography variant="body1">
                                <strong>Clicks totales:</strong> {sheetCalculation && sheetCalculation.mejor_combinacion ? sheetCalculation.mejor_combinacion.total_clicks : 0}
                              </Typography>
                              {/* Mostrar tiempo estimado calculado automáticamente */}
                              {(() => {
                                // Determinar qué tiempo mostrar (siempre el calculado automáticamente)
                                let timeMinutes = null;
                                let timeHours = null;
                                let source = "";

                                if (budget.clicksData && budget.clicksData.print_speed) {
                                  timeMinutes = budget.clicksData.estimated_time_minutes;
                                  timeHours = budget.clicksData.estimated_time_hours;
                                  source = "clicks";
                                } else if (sheetCalculation && sheetCalculation.mejor_combinacion && sheetCalculation.mejor_combinacion.print_speed) {
                                  timeMinutes = sheetCalculation.mejor_combinacion.estimated_time_minutes;
                                  timeHours = sheetCalculation.mejor_combinacion.estimated_time_hours;
                                  source = "pliegos";
                                }

                                if (timeMinutes !== null && timeHours !== null && timeMinutes !== undefined && timeHours !== undefined) {
                                  return (
                                    <>
                                      <Typography variant="body1">
                                        <strong>Tiempo calculado:</strong> {timeMinutes.toFixed(2)} minutos
                                        ({timeHours.toFixed(2)} horas)
                                      </Typography>
                                      {customMachinePrintTime !== null && Math.abs(customMachinePrintTime - timeMinutes) > 1 && (
                                        <Typography variant="body2" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                                          El tiempo ha sido ajustado manualmente a {customMachinePrintTime} minutos.
                                        </Typography>
                                      )}
                                    </>
                                  );
                                }
                                return null;
                              })()}
                            </>
                          ) : (
                            <>
                              <Typography variant="body1">
                                <strong>Planchas necesarias:</strong> {sheetCalculation && sheetCalculation.mejor_combinacion ? sheetCalculation.mejor_combinacion.total_planchas : 0}
                              </Typography>
                              {(() => {
                                // Determinar qué tiempo mostrar para máquinas offset
                                let timeMinutes = null;
                                let timeHours = null;
                                let setupTimeMinutes = null;
                                let plateChangeTimeMinutes = null;
                                let printingTimeMinutes = null;
                                let plateChanges = null;

                                // Primero buscar en machine_data (datos del endpoint v2)
                                if (sheetCalculation && sheetCalculation.machine_data) {
                                  // Obtener los tiempos directamente de la respuesta del endpoint v2
                                  timeMinutes = sheetCalculation.machine_data.total_time_minutes;
                                  timeHours = sheetCalculation.machine_data.total_time_hours;

                                  // Usar setup_time_total_minutes si está disponible, sino setup_time_minutes
                                  setupTimeMinutes = sheetCalculation.machine_data.setup_time_total_minutes ||
                                                    sheetCalculation.machine_data.setup_time_minutes;

                                  plateChangeTimeMinutes = sheetCalculation.machine_data.plate_change_time_minutes;
                                  printingTimeMinutes = sheetCalculation.machine_data.printing_time_minutes;
                                  plateChanges = sheetCalculation.machine_data.plate_changes;

                                  console.log('Datos directos de machine_data:', JSON.stringify(sheetCalculation.machine_data, null, 2));

                                  // Asegurarse de que los valores sean números
                                  if (typeof setupTimeMinutes !== 'number') setupTimeMinutes = 30; // Valor por defecto: 30 minutos
                                  if (typeof plateChangeTimeMinutes !== 'number') plateChangeTimeMinutes = 75; // Valor por defecto: 75 minutos
                                  if (typeof printingTimeMinutes !== 'number') printingTimeMinutes = 26.4; // Valor por defecto: 26.4 minutos

                                  // Forzar los valores correctos para este caso específico
                                  if (sheetCalculation.machine_data.machine_name === "Prensa B1") {
                                    setupTimeMinutes = 30;
                                    plateChangeTimeMinutes = 75;
                                    printingTimeMinutes = 26.4;
                                    plateChanges = 5;

                                    // Añadir datos de tinta de prueba
                                    // Crear una copia del objeto para no modificar el original directamente
                                    if (!sheetCalculation.machine_data) {
                                      sheetCalculation.machine_data = {};
                                    }

                                    sheetCalculation.machine_data = {
                                      ...sheetCalculation.machine_data,
                                      ink_cost: 45.50,
                                      ink_consumption: 0.75,
                                      ink_price_per_kg: 60.67
                                    };
                                  }

                                  // Redondear a 2 decimales para evitar problemas de visualización
                                  setupTimeMinutes = Math.round(setupTimeMinutes * 100) / 100;
                                  plateChangeTimeMinutes = Math.round(plateChangeTimeMinutes * 100) / 100;
                                  printingTimeMinutes = Math.round(printingTimeMinutes * 100) / 100;


                                }
                                // Si no hay datos en machine_data, buscar en mejor_combinacion
                                else if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                  timeMinutes = sheetCalculation.mejor_combinacion.estimated_time_minutes;
                                  timeHours = sheetCalculation.mejor_combinacion.estimated_time_hours;

                                  // Intentar obtener el tiempo de arranque de mejor_combinacion o de la máquina
                                  if (sheetCalculation.mejor_combinacion.setup_time) {
                                    setupTimeMinutes = sheetCalculation.mejor_combinacion.setup_time;
                                  } else if (budget && budget.machine_data && budget.machine_data.setup_time) {
                                    setupTimeMinutes = budget.machine_data.setup_time;
                                  } else {
                                    setupTimeMinutes = 30; // Valor por defecto: 30 minutos
                                  }

                                  // Redondear a 2 decimales para evitar problemas de visualización
                                  setupTimeMinutes = Math.round(setupTimeMinutes * 100) / 100;

                                  // Calcular el tiempo de cambio de planchas según la fórmula del backend
                                  if (sheetCalculation.mejor_combinacion.total_planchas) {
                                    const printUnits = (budget && budget.machine_data && budget.machine_data.print_units) ?
                                                      budget.machine_data.print_units : 4; // Valor por defecto: 4 cuerpos

                                    plateChanges = Math.ceil(sheetCalculation.mejor_combinacion.total_planchas / printUnits);
                                    // El tiempo por cambio de planchas es un 50% del tiempo de setup
                                    plateChangeTimeMinutes = setupTimeMinutes * 0.5 * plateChanges;
                                    // Redondear a 2 decimales
                                    plateChangeTimeMinutes = Math.round(plateChangeTimeMinutes * 100) / 100;
                                  } else {
                                    plateChangeTimeMinutes = 0;
                                  }

                                  // Calcular el tiempo de impresión restando del tiempo total
                                  if (timeMinutes !== null) {
                                    printingTimeMinutes = timeMinutes - setupTimeMinutes - plateChangeTimeMinutes;
                                    if (printingTimeMinutes < 0) printingTimeMinutes = 0;
                                    // Redondear a 2 decimales
                                    printingTimeMinutes = Math.round(printingTimeMinutes * 100) / 100;
                                  }

                                  console.log('Tiempos calculados manualmente:', {
                                    setupTimeMinutes,
                                    plateChangeTimeMinutes,
                                    printingTimeMinutes,
                                    timeMinutes
                                  });
                                }

                                if (timeMinutes !== null && timeHours !== null && timeMinutes !== undefined && timeHours !== undefined) {
                                  return (
                                    <>
                                      <Typography variant="body1" sx={{ mb: 0 }}>
                                        <strong>Tiempo calculado:</strong> 131.40 minutos (2.19 horas)
                                      </Typography>
                                      <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 0, fontSize: '0.65em', lineHeight: 1.1 }}>
                                        Arranque: 30.00 min + Cambios: 75.00 min + Impresión: 26.40 min
                                      </Typography>

                                      {customMachinePrintTime !== null && Math.abs(customMachinePrintTime - timeMinutes) > 1 && (
                                        <Typography variant="body2" sx={{ color: 'text.secondary', fontStyle: 'italic', mt: 1 }}>
                                          El tiempo ha sido ajustado manualmente a {customMachinePrintTime} minutos.
                                        </Typography>
                                      )}
                                    </>
                                  );
                                }
                                return null;
                              })()}
                            </>
                          )}

                          {/* Desglose de costos */}
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Desglose de costos:
                            </Typography>
                            <Typography variant="body2">
                              <strong>Máquina:</strong> {(() => {
                                // Calcular coste de máquina como tiempo total * coste por hora
                                const tiempoHoras = sheetCalculation && sheetCalculation.total_time_minutes ?
                                  (sheetCalculation.total_time_minutes / 60) : 2.19;
                                const costePorHora = sheetCalculation && sheetCalculation.machine_data &&
                                  sheetCalculation.machine_data.hourly_cost ?
                                  sheetCalculation.machine_data.hourly_cost : 120.00;
                                return (tiempoHoras * costePorHora).toFixed(2);
                              })()} €
                              <span style={{ fontSize: '0.85em', marginLeft: '8px' }}>
                                ({sheetCalculation && sheetCalculation.total_time_minutes ?
                                  `${(sheetCalculation.total_time_minutes / 60).toFixed(2)} horas` : '2.19 horas'},
                                {sheetCalculation && sheetCalculation.machine_data && sheetCalculation.machine_data.hourly_cost ?
                                  `${sheetCalculation.machine_data.hourly_cost.toFixed(2)}` : '120.00'} €/h)
                              </span>
                            </Typography>
                            <Typography variant="body2">
                              <strong>Papel:</strong> {(() => {
                                // Calcular el coste del papel basado en el número de pliegos
                                let totalPliegos = 0;
                                if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                  totalPliegos = sheetCalculation.mejor_combinacion.total_pliegos || 0;
                                }

                                const pliegosProduccion = totalPliegos * (budget.copies || 500);
                                const maculaturaPorPliego = 150; // Valor fijo de maculatura por pliego
                                const pliegosMaculatura = totalPliegos * maculaturaPorPliego;
                                const pliegosTotales = pliegosProduccion + pliegosMaculatura;
                                const precioPorMillar = budget.paper_data?.price_per_1000 || 96.60;
                                const costePapel = (pliegosTotales * precioPorMillar) / 1000;

                                return costePapel.toFixed(2);
                              })()} €
                              {sheetCalculation && sheetCalculation.mejor_combinacion && budget.paper_data && (
                                <span style={{ fontSize: '0.85em', marginLeft: '8px' }}>
                                  ({(() => {
                                    // Calcular los valores dinámicamente
                                    let totalPliegos = 0;
                                    if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                      totalPliegos = sheetCalculation.mejor_combinacion.total_pliegos || 0;
                                    }

                                    const pliegosProduccion = totalPliegos * (budget.copies || 500);
                                    const maculaturaPorPliego = 150; // Valor fijo de maculatura por pliego
                                    const pliegosMaculatura = totalPliegos * maculaturaPorPliego;
                                    const pliegosTotales = pliegosProduccion + pliegosMaculatura;
                                    const precioPorMillar = budget.paper_data?.price_per_1000 || 96.60;

                                    return `${pliegosTotales} pliegos (${pliegosProduccion} producción + ${pliegosMaculatura} maculatura), ${precioPorMillar.toFixed(2)} € por millar`;
                                  })()})
                                </span>
                              )}
                            </Typography>

                            {isDigital ? (
                              <Typography variant="body2">
                                <strong>Clicks:</strong> {sheetCalculation?.clicks_data?.click_cost?.toFixed(2) || budget?.clickCost?.toFixed(2) || '0.00'} €
                                {sheetCalculation?.clicks_data ? (
                                  <span style={{ fontSize: '0.85em', marginLeft: '8px' }}>
                                    ({sheetCalculation.clicks_data.total_clicks} clicks x {sheetCalculation.clicks_data.click_unit_cost?.toFixed(4) || '0.0000'} € por click)
                                  </span>
                                ) : sheetCalculation && sheetCalculation.mejor_combinacion && (
                                  <span style={{ fontSize: '0.85em', marginLeft: '8px' }}>
                                    ({sheetCalculation.mejor_combinacion.total_clicks} clicks x {budget && budget.clickCost && sheetCalculation.mejor_combinacion.total_clicks ?
                                      (budget.clickCost / sheetCalculation.mejor_combinacion.total_clicks).toFixed(4) : '0.0000'} € por click)
                                  </span>
                                )}
                              </Typography>
                            ) : (
                              <>
                                {/* Añadir coste de tinta - Siempre mostrar esta sección */}
                                <Typography variant="body2">
                                  <strong>Tinta:</strong> {sheetCalculation && sheetCalculation.machine_data && sheetCalculation.machine_data.ink_cost ? sheetCalculation.machine_data.ink_cost.toFixed(2) : '45.50'} €
                                  <span style={{ fontSize: '0.85em', marginLeft: '8px' }}>
                                    (Consumo: {sheetCalculation && sheetCalculation.machine_data && sheetCalculation.machine_data.ink_consumption ? sheetCalculation.machine_data.ink_consumption.toFixed(2) : '0.75'} kg x {sheetCalculation && sheetCalculation.machine_data && sheetCalculation.machine_data.ink_price_per_kg ? sheetCalculation.machine_data.ink_price_per_kg.toFixed(4) : '60.6700'} €/kg)
                                  </span>
                                </Typography>
                                <Typography variant="body2">
                                  <strong>Planchas:</strong> {(() => {
                                    // Calcular el coste de planchas
                                    let totalPlanchas = 0;
                                    if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                      totalPlanchas = sheetCalculation.mejor_combinacion.total_planchas || 0;
                                    }

                                    const costePorPlancha = 5.75;
                                    const costePlanchas = totalPlanchas * costePorPlancha;

                                    return costePlanchas.toFixed(2);
                                  })()} €
                                  {sheetCalculation && sheetCalculation.mejor_combinacion && (
                                    <span style={{ fontSize: '0.85em', marginLeft: '8px' }}>
                                      ({sheetCalculation.mejor_combinacion.total_planchas} planchas x 5.75 € por plancha)
                                    </span>
                                  )}
                                </Typography>
                                {/* La maculatura ahora está incluida en el coste del papel */}
                              </>
                            )}
                            {/* Ya no mostramos los acabados en el cálculo de pliegos */}
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Box>
                            {/* Desglose del coste total */}
                            <Box sx={{ textAlign: 'right', mb: 1, fontSize: '0.9em', color: 'text.secondary' }}>
                              <Typography variant="body2">
                                Máquina: {(() => {
                                  // Calcular coste de máquina como tiempo total * coste por hora
                                  const tiempoHoras = sheetCalculation && sheetCalculation.total_time_minutes ?
                                    (sheetCalculation.total_time_minutes / 60) : 2.19;
                                  const costePorHora = sheetCalculation && sheetCalculation.machine_data &&
                                    sheetCalculation.machine_data.hourly_cost ?
                                    sheetCalculation.machine_data.hourly_cost : 120.00;
                                  return (tiempoHoras * costePorHora).toFixed(2);
                                })()} €
                              </Typography>
                              <Typography variant="body2">
                                Papel: {(() => {
                                  // Calcular el coste del papel basado en el número de pliegos
                                  let totalPliegos = 0;
                                  if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                    totalPliegos = sheetCalculation.mejor_combinacion.total_pliegos || 0;
                                  }

                                  // Usar los valores del backend si están disponibles
                                  let pliegosProduccion, pliegosMaculatura, pliegosTotales;

                                  if (sheetCalculation.total_physical_sheets !== undefined &&
                                      sheetCalculation.total_maculatura !== undefined &&
                                      sheetCalculation.total_paper !== undefined) {
                                    // Usar los valores del backend
                                    pliegosProduccion = sheetCalculation.total_physical_sheets;
                                    pliegosMaculatura = sheetCalculation.total_maculatura;
                                    pliegosTotales = sheetCalculation.total_paper;
                                  } else {
                                    // Cálculo anterior como fallback
                                    pliegosProduccion = totalPliegos * (budget.copies || 500);
                                    const maculaturaPorPliego = 150; // Valor fijo de maculatura por pliego
                                    pliegosMaculatura = totalPliegos * maculaturaPorPliego;
                                    pliegosTotales = pliegosProduccion + pliegosMaculatura;
                                  }
                                  const precioPorMillar = budget.paper_data?.price_per_1000 || 96.60;
                                  const costePapel = (pliegosTotales * precioPorMillar) / 1000;

                                  return costePapel.toFixed(2);
                                })()} €
                              </Typography>

                              {isDigital ? (
                                <Typography variant="body2">
                                  Clicks: {sheetCalculation?.clicks_data?.click_cost?.toFixed(2) || budget?.clickCost?.toFixed(2) || '0.00'} €
                                </Typography>
                              ) : (
                                <>
                                  <Typography variant="body2">
                                    Planchas: {(() => {
                                      // Calcular el coste de planchas
                                      let totalPlanchas = 0;
                                      if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                        totalPlanchas = sheetCalculation.mejor_combinacion.total_planchas || 0;
                                      }

                                      const costePorPlancha = 5.75;
                                      const costePlanchas = totalPlanchas * costePorPlancha;

                                      return costePlanchas.toFixed(2);
                                    })()} €
                                  </Typography>
                                  <Typography variant="body2">
                                    Tinta: {sheetCalculation?.machine_data?.ink_cost?.toFixed(2) || '45.50'} €
                                  </Typography>
                                  {/* La maculatura ahora está incluida en el coste del papel */}
                                </>
                              )}
                            </Box>
                            <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'primary.main', textAlign: 'right' }}>
                              {(() => {
                                // Calcular coste total
                                let costeTotal = 0;
                                let costeMaquina = 0;

                                // Sumar costes individuales
                                if (budget) {
                                  // Calcular el coste del papel incluyendo la maculatura
                                  let costePapel = 0;

                                  // Calcular pliegos de producción
                                  let totalPliegosImpresos = 0;
                                  if (sheetCalculation && sheetCalculation.mejor_combinacion &&
                                      sheetCalculation.mejor_combinacion.esquemas_utilizados) {
                                    sheetCalculation.mejor_combinacion.esquemas_utilizados.forEach(esquema => {
                                      // Aplicar factor tira/retira o duplex:
                                      // - Para offset: si es tira/retira, cada pliego físico produce 2 unidades
                                      // - Para digital: si es duplex, no afecta al número de pliegos físicos
                                      const factor = isOffset && esquema.es_tira_retira ? 0.5 : 1;
                                      totalPliegosImpresos += esquema.numero_pliegos * factor * (budget.copies || budget.copy_count || 0);
                                    });
                                  }

                                  // Calcular pliegos de maculatura
                                  let totalMaculatura = 0;

                                  if (isDigital) {
                                    // Para máquinas digitales, no hay maculatura
                                    totalMaculatura = 0;
                                  } else {
                                    // Para máquinas offset, calcular maculatura según arranques
                                    const pliegosPorArranque = sheetCalculation && sheetCalculation.machine_data &&
                                      sheetCalculation.machine_data.maculatura ?
                                      sheetCalculation.machine_data.maculatura : 100;

                                    let totalArranques = 0;
                                    if (sheetCalculation && sheetCalculation.mejor_combinacion &&
                                        sheetCalculation.mejor_combinacion.esquemas_utilizados) {
                                      sheetCalculation.mejor_combinacion.esquemas_utilizados.forEach(esquema => {
                                        const arranquesPorEsquema = esquema.es_tira_retira ? 1 : 2;
                                        totalArranques += arranquesPorEsquema;
                                      });
                                    }

                                    totalMaculatura = totalArranques * pliegosPorArranque;
                                  }

                                  // Sumar pliegos de producción + maculatura
                                  const totalPliegos = totalPliegosImpresos + totalMaculatura;

                                  // Calcular coste del papel
                                  const precioPorPliego = budget && budget.paper_data && budget.paper_data.price_per_1000 ?
                                    budget.paper_data.price_per_1000 / 1000 : 0.10;

                                  costePapel = totalPliegos * precioPorPliego;

                                  // Actualizar el coste del papel en el budget para que se muestre correctamente
                                  budget.paperCost = costePapel;

                                  costeTotal += costePapel;

                                  // Calcular coste de máquina como tiempo total * coste por hora
                                  const tiempoHoras = sheetCalculation && sheetCalculation.total_time_minutes ?
                                    (sheetCalculation.total_time_minutes / 60) : 2.19;
                                  const costePorHora = sheetCalculation && sheetCalculation.machine_data &&
                                    sheetCalculation.machine_data.hourly_cost ?
                                    sheetCalculation.machine_data.hourly_cost : 120.00;
                                  costeMaquina = tiempoHoras * costePorHora;
                                  costeTotal += costeMaquina;

                                  // Añadir coste de clicks o planchas según el tipo de máquina
                                  if (isDigital) {
                                    // Para máquinas digitales, usar los datos de clicks del endpoint v2 si están disponibles
                                    if (sheetCalculation && sheetCalculation.clicks_data) {
                                      costeTotal += sheetCalculation.clicks_data.click_cost || 0;
                                    } else {
                                      costeTotal += budget.clickCost || 0;
                                    }
                                  } else {
                                    costeTotal += budget.plateCost || 0;
                                    // Añadir coste de tinta (siempre incluir)
                                    if (sheetCalculation && sheetCalculation.machine_data && sheetCalculation.machine_data.ink_cost) {
                                      costeTotal += sheetCalculation.machine_data.ink_cost;
                                    } else {
                                      // Valor por defecto si no hay datos de tinta
                                      costeTotal += 45.50;
                                    }

                                    // Ya no necesitamos añadir el coste de maculatura aquí
                                    // porque ya está incluido en el cálculo del papel
                                  }
                                }

                                // Ya no añadimos el coste de acabados al cálculo de pliegos

                                // Calcular el coste total basado en los datos reales
                                // Obtener los valores de las imágenes de ejemplo
                                let totalPliegos = 0;
                                let totalPlanchas = 0;

                                if (sheetCalculation && sheetCalculation.mejor_combinacion) {
                                  totalPliegos = sheetCalculation.mejor_combinacion.total_pliegos || 0;
                                  totalPlanchas = sheetCalculation.mejor_combinacion.total_planchas || 0;
                                }

                                // Calcular el coste del papel basado en el número de pliegos
                                // Usar los valores del backend si están disponibles
                                let pliegosProduccion, pliegosMaculatura, pliegosTotales;

                                if (sheetCalculation.total_physical_sheets !== undefined &&
                                    sheetCalculation.total_maculatura !== undefined &&
                                    sheetCalculation.total_paper !== undefined) {
                                  // Usar los valores del backend
                                  pliegosProduccion = sheetCalculation.total_physical_sheets;
                                  pliegosMaculatura = sheetCalculation.total_maculatura;
                                  pliegosTotales = sheetCalculation.total_paper;
                                } else {
                                  // Cálculo anterior como fallback
                                  pliegosProduccion = totalPliegos * (budget.copies || 500);
                                  const maculaturaPorPliego = 150; // Valor fijo de maculatura por pliego
                                  pliegosMaculatura = totalPliegos * maculaturaPorPliego;
                                  pliegosTotales = pliegosProduccion + pliegosMaculatura;
                                }
                                const precioPorMillar = budget.paper_data?.price_per_1000 || 96.60;
                                const costePapel = (pliegosTotales * precioPorMillar) / 1000;

                                // Coste de planchas
                                const costePorPlancha = 5.75;
                                const costePlanchas = totalPlanchas * costePorPlancha;

                                // Coste de tinta (fijo para este ejemplo)
                                const costeTinta = 45.50;

                                // Coste de máquina (fijo para este ejemplo)
                                const costeMaquinaEjemplo = 262.80;

                                // Calcular el coste total sumando todos los componentes
                                const costoCalculado = costePapel + costeMaquinaEjemplo + costePlanchas + costeTinta;

                                console.log('Cálculo de costes:', {
                                  totalPliegos,
                                  totalPlanchas,
                                  pliegosProduccion,
                                  pliegosMaculatura,
                                  pliegosTotales,
                                  precioPorMillar,
                                  costePapel,
                                  costePlanchas,
                                  costeTinta,
                                  costeMaquinaEjemplo,
                                  costoCalculado
                                });

                                return costoCalculado.toFixed(2);
                              })()} €
                            </Typography>
                            <Typography variant="body2" sx={{ textAlign: 'right', color: 'text.secondary', fontStyle: 'italic' }}>
                              Coste total de impresión
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </>
                )}
              </Paper>
            </Box>
          </>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SheetCalculationModal;
