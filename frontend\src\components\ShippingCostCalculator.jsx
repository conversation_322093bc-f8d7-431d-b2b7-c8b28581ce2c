import { useState, useEffect, useCallback } from 'react';
import { buildApiUrl } from '../config';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  Alert,
  Snackbar,
  Card,
  CardContent,
  InputAdornment
} from '@mui/material';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';

const ShippingCostCalculator = () => {
  // Estados
  const [clients, setClients] = useState([]);
  const [formData, setFormData] = useState({
    weight_kg: 10,
    country: 'España',
    client_id: ''
  });
  const [result, setResult] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [useClientAddress, setUseClientAddress] = useState(false);

  // Cargar la lista de clientes
  const fetchClients = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/clients/'));
      if (!response.ok) throw new Error('Error al obtener el catálogo de clientes');
      const data = await response.json();
      setClients(data);
    } catch (err) {
      setSnackbar({
        open: true,
        message: err.message,
        severity: 'error'
      });
    }
  }, []);

  useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  // Manejar cambios en el formulario
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Manejar cambio de modo (dirección personalizada o del cliente)
  const handleModeChange = (e) => {
    setUseClientAddress(e.target.value === 'client');
    // Si cambiamos a modo cliente, limpiar el país
    if (e.target.value === 'client') {
      setFormData(prev => ({
        ...prev,
        country: 'España'
      }));
    } else {
      // Si cambiamos a modo personalizado, limpiar el client_id
      setFormData(prev => ({
        ...prev,
        client_id: ''
      }));
    }
  };

  // Calcular coste de envío
  const handleCalculate = async () => {
    try {
      // Validar datos
      if (useClientAddress && !formData.client_id) {
        throw new Error('Debe seleccionar un cliente');
      }
      if (formData.weight_kg <= 0) {
        throw new Error('El peso debe ser mayor que 0');
      }

      // Preparar datos para la solicitud
      const requestData = {
        weight_kg: parseFloat(formData.weight_kg),
        country: formData.country
      };

      // Añadir client_id si estamos usando la dirección del cliente
      if (useClientAddress) {
        requestData.client_id = formData.client_id;
      }

      // Enviar solicitud
      const response = await fetch(buildApiUrl('/calculations/shipping-cost'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al calcular el coste de envío');
      }

      // Procesar resultado
      const data = await response.json();
      setResult(data);
      setSnackbar({
        open: true,
        message: 'Cálculo realizado correctamente',
        severity: 'success'
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: err.message,
        severity: 'error'
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Obtener el cliente seleccionado
  const selectedClient = clients.find(c => c.client_id === formData.client_id);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Calculadora de Coste de Envío
      </Typography>
      <Typography variant="body1" paragraph>
        Esta herramienta calcula el coste de envío basado en el peso del paquete y el país de destino.
      </Typography>

      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Modo de cálculo</InputLabel>
              <Select
                value={useClientAddress ? 'client' : 'custom'}
                onChange={handleModeChange}
                label="Modo de cálculo"
              >
                <MenuItem value="custom">Especificar país manualmente</MenuItem>
                <MenuItem value="client">Usar dirección del cliente</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {useClientAddress ? (
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Cliente</InputLabel>
                <Select
                  name="client_id"
                  value={formData.client_id}
                  onChange={handleInputChange}
                  label="Cliente"
                >
                  {clients.map(client => (
                    <MenuItem key={client.client_id} value={client.client_id}>
                      {client.name} ({client.country})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          ) : (
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>País</InputLabel>
                <Select
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  label="País"
                >
                  <MenuItem value="España">España</MenuItem>
                  <MenuItem value="Portugal">Portugal</MenuItem>
                  <MenuItem value="Francia">Francia</MenuItem>
                  <MenuItem value="Alemania">Alemania</MenuItem>
                  <MenuItem value="Italia">Italia</MenuItem>
                  <MenuItem value="Reino Unido">Reino Unido</MenuItem>
                  <MenuItem value="Otro">Otro país europeo</MenuItem>
                  <MenuItem value="Internacional">Internacional (fuera de Europa)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          )}

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Peso (kg)"
              name="weight_kg"
              type="number"
              value={formData.weight_kg}
              onChange={handleInputChange}
              required
              InputProps={{
                inputProps: { min: 0.1, step: 0.1 },
                endAdornment: <InputAdornment position="end">kg</InputAdornment>
              }}
              helperText="Peso total del envío en kilogramos"
            />
          </Grid>

          <Grid item xs={12}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<LocalShippingIcon />}
              onClick={handleCalculate}
              fullWidth
              size="large"
              sx={{ mt: 2 }}
            >
              Calcular Coste de Envío
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {selectedClient && useClientAddress && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Información del cliente seleccionado
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2">
                  <strong>Nombre:</strong> {selectedClient.name}
                </Typography>
                <Typography variant="body2">
                  <strong>País:</strong> {selectedClient.country}
                </Typography>
                <Typography variant="body2">
                  <strong>Ciudad:</strong> {selectedClient.city}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2">
                  <strong>Dirección:</strong> {selectedClient.address}
                </Typography>
                <Typography variant="body2">
                  <strong>Código Postal:</strong> {selectedClient.postal_code}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {result && (
        <Paper elevation={3} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Resultado del cálculo
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="body1">
                <strong>Peso del envío:</strong> {result.weight_kg.toFixed(2)} kg
              </Typography>
              <Typography variant="body1">
                <strong>País de destino:</strong> {result.country}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body1">
                <strong>Factor de distancia:</strong> {result.distance_factor.toFixed(1)}x
              </Typography>
              <Typography variant="h6" color="primary" sx={{ mt: 1 }}>
                <strong>Coste de envío:</strong> {result.shipping_cost.toFixed(2)} €
              </Typography>
            </Grid>
          </Grid>
          
          <Alert severity="info" sx={{ mt: 2 }}>
            El envío de {result.weight_kg.toFixed(2)} kg a {result.country} tiene un coste de <strong>{result.shipping_cost.toFixed(2)} €</strong>.
            {result.distance_factor > 1 && (
              <span> Se ha aplicado un factor de distancia de {result.distance_factor.toFixed(1)}x por ser un envío {result.country !== 'España' ? 'internacional' : 'nacional'}.</span>
            )}
          </Alert>
        </Paper>
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ShippingCostCalculator;
