import React from 'react';
import { Box, Typography } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import PropTypes from 'prop-types';

// Colores para los diferentes tipos de máquinas
export const TYPE_COLORS = {
  'Offset': 'primary',
  'Digital': 'secondary',
  'Plotter': 'info',
  'CTP': 'warning',
  'Encuadernadora': 'success',
  'Guillotina': 'error',
  'Plegadora': 'info'
};

const MachineCard = ({ machine, isSelected, onClick, colorCategory }) => {
  return (
    <Box 
      key={machine.machine_id} 
      sx={{ 
        width: '100px',
        height: '50px',
        mr: 0.5,
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: isSelected ? 'white' : 'grey.100',
        borderRadius: '6px',
        boxShadow: isSelected ? 3 : 1,
        cursor: 'pointer',
        overflow: 'hidden',
        border: isSelected ? `2px solid ${colorCategory === 'primary' ? '#1976d2' : '#4caf50'}` : 'none',
        transition: 'all 0.2s ease',
        '&:hover': {
          boxShadow: 2,
          bgcolor: colorCategory === 'primary' 
            ? 'rgba(25, 118, 210, 0.08)' 
            : 'rgba(76, 175, 80, 0.08)'
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: '5px',
          bgcolor: `${colorCategory}.main`,
        }
      }}
      onClick={onClick}
    >
      <Box sx={{ 
        bgcolor: TYPE_COLORS[machine.type] ? `${TYPE_COLORS[machine.type]}.main` : `${colorCategory}.main`,
        color: 'white',
        py: 0.1,
        px: 0.5,
        ml: '5px',
        fontSize: '0.6rem',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}>
        {machine.type}
      </Box>
      <Box sx={{ 
        p: 0.3, 
        pl: 0.5,
        ml: '5px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        flexGrow: 1
      }}>
        <Typography 
          variant="caption" 
          fontWeight="bold" 
          sx={{ 
            display: 'block', 
            lineHeight: 1,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            fontSize: '0.65rem'
          }}
        >
          {machine.name}
        </Typography>
        <Typography 
          variant="caption" 
          color="text.secondary" 
          sx={{ 
            fontSize: '0.55rem', 
            display: 'block', 
            lineHeight: 1,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }}
        >
          {machine.model}
        </Typography>
      </Box>
      {isSelected && (
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          bgcolor: `${colorCategory}.main`,
          width: '16px',
          height: '16px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: '0 0 0 4px'
        }}>
          <CheckIcon sx={{ color: 'white', fontSize: '12px' }} />
        </Box>
      )}
    </Box>
  );
};

MachineCard.propTypes = {
  machine: PropTypes.object.isRequired,
  isSelected: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  colorCategory: PropTypes.oneOf(['primary', 'success']).isRequired
};

export default MachineCard;
