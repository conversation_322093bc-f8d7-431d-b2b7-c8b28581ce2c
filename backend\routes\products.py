from fastapi import APIRouter, HTTPException
from typing import List
from models.models import Product, ProductCreate, ProductUpdate
from utils.catalog_manager import load_product_catalog, save_product_catalog

router = APIRouter(
    prefix="/products",
    tags=["products"],
    responses={404: {"description": "Producto no encontrado"}}
)

# Variable global para el catálogo
product_catalog = load_product_catalog()

@router.get("/")
def get_all_products():
    """
    Obtiene todos los productos del catálogo
    """
    global product_catalog
    product_catalog = load_product_catalog()
    print(f"Cargados {len(product_catalog)} productos del catálogo")
    return product_catalog

@router.get("/{product_id}")
def get_product_by_id(product_id: str):
    """
    Obtiene un producto específico por su ID
    """
    global product_catalog

    # Recargar el catálogo para asegurarnos de tener la versión más reciente
    product_catalog = load_product_catalog()

    product = next((p for p in product_catalog if p["product_id"] == product_id), None)
    if not product:
        raise HTTPException(status_code=404, detail="Producto no encontrado")

    print(f"Producto obtenido por ID: {product_id} - {product.get('name', 'Desconocido')}")
    return product

@router.post("/", response_model=Product)
def create_product(product: ProductCreate):
    """
    Crea un nuevo producto en el catálogo
    """
    global product_catalog

    # Recargar el catálogo para asegurarnos de tener la versión más reciente
    product_catalog = load_product_catalog()

    if any(p["product_id"] == product.product_id for p in product_catalog):
        raise HTTPException(status_code=400, detail="Ya existe un producto con ese ID")

    # Convertir el modelo a un diccionario
    product_dict = product.model_dump()

    # Asegurarse de que los campos opcionales tengan valores predeterminados
    if "parts" not in product_dict or product_dict["parts"] is None:
        product_dict["parts"] = []
    if "default_finishing" not in product_dict or product_dict["default_finishing"] is None:
        product_dict["default_finishing"] = []
    if "finishing_processes" not in product_dict or product_dict["finishing_processes"] is None:
        product_dict["finishing_processes"] = []

    # Registrar el producto que se va a guardar
    print(f"Guardando producto: {product_dict}")

    # Añadir el producto al catálogo
    product_catalog.append(product_dict)

    # Guardar el catálogo actualizado
    try:
        save_product_catalog(product_catalog)
        print(f"Catálogo guardado correctamente con {len(product_catalog)} productos")
    except Exception as e:
        print(f"Error al guardar el catálogo: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error al guardar el catálogo: {str(e)}")

    # Registrar la creación del producto
    print(f"Producto creado: {product_dict['product_id']} - {product_dict['name']}")

    return product_dict

@router.put("/{product_id}", response_model=Product)
def update_product(product_id: str, product_update: ProductUpdate):
    """
    Actualiza un producto existente en el catálogo
    """
    global product_catalog

    # Recargar el catálogo para asegurarnos de tener la versión más reciente
    product_catalog = load_product_catalog()

    product_idx = next((i for i, p in enumerate(product_catalog) if p["product_id"] == product_id), None)
    if product_idx is None:
        raise HTTPException(status_code=404, detail="Producto no encontrado")

    current_product = product_catalog[product_idx]
    update_data = product_update.model_dump(exclude_unset=True)

    # Registrar los datos de actualización
    print(f"Datos de actualización: {update_data}")

    # Asegurarse de que los campos opcionales tengan valores predeterminados
    if "parts" in update_data and update_data["parts"] is None:
        update_data["parts"] = []
    if "default_finishing" in update_data and update_data["default_finishing"] is None:
        update_data["default_finishing"] = []
    if "finishing_processes" in update_data and update_data["finishing_processes"] is None:
        update_data["finishing_processes"] = []

    updated_product = {**current_product, **update_data}

    # Registrar el producto actualizado
    print(f"Producto actualizado: {updated_product}")

    product_catalog[product_idx] = updated_product
    save_product_catalog(product_catalog)

    # Registrar la actualización del producto
    print(f"Producto actualizado: {product_id} - {updated_product['name']}")

    return updated_product

@router.delete("/{product_id}")
def delete_product(product_id: str):
    """
    Elimina un producto del catálogo
    """
    global product_catalog

    # Recargar el catálogo para asegurarnos de tener la versión más reciente
    product_catalog = load_product_catalog()

    product_idx = next((i for i, p in enumerate(product_catalog) if p["product_id"] == product_id), None)
    if product_idx is None:
        raise HTTPException(status_code=404, detail="Producto no encontrado")

    # Guardar el nombre del producto antes de eliminarlo para el registro
    product_name = product_catalog[product_idx].get("name", "Desconocido")

    product_catalog.pop(product_idx)
    save_product_catalog(product_catalog)

    # Registrar la eliminación del producto
    print(f"Producto eliminado: {product_id} - {product_name}")

    return {"message": "Producto eliminado correctamente"}
