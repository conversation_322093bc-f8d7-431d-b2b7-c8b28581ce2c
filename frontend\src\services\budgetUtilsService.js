/**
 * Servicio de utilidades para el BudgetForm
 * Extrae funciones auxiliares y de utilidad del componente principal
 */

/**
 * Crea una parte vacía del presupuesto con valores por defecto
 * @param {number} index - Índice de la parte
 * @param {string} name - Nombre de la parte
 * @returns {Object} - Parte del presupuesto inicializada
 */
export const createEmptyPart = (index = 0, name = 'General') => ({
  part_id: `part-${Date.now()}-${index}`,
  name: name,
  description: '',
  assembly_order: 'None', // Tipo de encuadernado: None, Gathering (Alzado), Collecting (Grapado)
  pageSize: 'A4',
  customPageSize: '',
  pageCount: '',
  paper: null,
  machine: null,
  sheetCalculation: null,
  paperCost: 0,
  machineCost: 0,
  plateCost: 0,
  processCosts: [],
  totalCost: 0,
  customPrintTime: null,
  colorConfig: {
    frontColors: 4,
    backColors: 4,
    pantones: 0
  }
});

/**
 * Determina el tamaño de página a partir de las dimensiones
 * @param {Object} dimensions - Dimensiones con width y height
 * @returns {string} - Tamaño de página estándar o 'Personalizado'
 */
export const getPageSizeFromDimensions = (dimensions) => {
  if (!dimensions || !dimensions.width || !dimensions.height) {
    return 'A4';
  }

  const width = dimensions.width;
  const height = dimensions.height;

  // Verificar si es un tamaño estándar
  if (width === 210 && height === 297) {
    return 'A4';
  } else if (width === 148 && height === 210) {
    return 'A5';
  } else if (width === 297 && height === 420) {
    return 'A3';
  } else if (width === 216 && height === 279) {
    return 'Carta';
  } else {
    // Si no es un tamaño estándar, es personalizado
    return 'Personalizado';
  }
};

/**
 * Genera un número de OT automático basado en la fecha actual
 * @returns {string} - Número de OT generado
 */
export const generateOTNumber = () => {
  const date = new Date();
  const year = date.getFullYear().toString().slice(2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const randomNum = Math.floor(1000 + Math.random() * 9000);
  
  return `OT-${year}${month}${randomNum}`;
};

/**
 * Crea un presupuesto vacío con valores por defecto
 * @returns {Object} - Presupuesto inicializado
 */
export const createEmptyBudget = () => ({
  otNumber: generateOTNumber(),
  description: '',
  client: null,
  jobType: '',
  copies: '',
  parts: [],
  shipping_cost: 0,
  total_paper_weight_kg: 0
});

/**
 * Calcula el total de pliegos físicos basado en los cálculos del backend
 * @param {number} selectedPartIndex - Índice de la parte seleccionada
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {Object} sheetCalculation - Cálculo general de pliegos
 * @returns {number} - Total de pliegos calculados
 */
export const calculateTotalSheets = (selectedPartIndex, budgetParts, sheetCalculation) => {
  // Si hay una parte seleccionada, usar sus datos calculados por el backend
  if (selectedPartIndex !== null && budgetParts[selectedPartIndex]?.sheetCalculation) {
    const part = budgetParts[selectedPartIndex];
    // Usar el total calculado por el backend en lugar de calcular localmente
    return part.sheetCalculation.total_sheets ||
           part.sheetCalculation.total_pliegos ||
           part.sheetCalculation.mejor_combinacion?.total_pliegos || 0;
  } else if (sheetCalculation) {
    // Usar el cálculo general si no hay parte seleccionada
    return sheetCalculation.total_sheets ||
           sheetCalculation.total_pliegos ||
           sheetCalculation.mejor_combinacion?.total_pliegos || 0;
  }

  return 0;
};

/**
 * Actualiza las cantidades de procesos basado en el número de copias
 * @param {Array} selectedProcesses - Procesos seleccionados
 * @param {number} copies - Número de copias
 * @returns {Array} - Procesos actualizados
 */
export const updateProcessQuantities = (selectedProcesses, copies) => {
  if (!selectedProcesses || selectedProcesses.length === 0 || !copies) {
    return selectedProcesses;
  }

  return selectedProcesses.map(process => {
    // Solo actualizar si la cantidad no ha sido modificada manualmente
    // y si el tipo de unidad no es 'Hora' (los procesos por hora no dependen del número de ejemplares)
    if (!process.quantityModified && (!process.unit_type || process.unit_type !== 'Hora')) {
      return { ...process, quantity: parseInt(copies) || 1 };
    }
    return process;
  });
};

/**
 * Maneja los cambios en la información del PDF
 * @param {Object} info - Información del PDF
 * @param {Function} setPdfInfo - Setter para la información del PDF
 * @param {Function} setBudget - Setter para el presupuesto
 * @returns {void}
 */
export const handlePdfInfoChange = (info, setPdfInfo, setBudget) => {
  setPdfInfo(info);

  // Actualizar el nombre del archivo PDF en el presupuesto
  if (info && info.filename) {
    setBudget(prev => ({
      ...prev,
      pdf_filename: info.filename
    }));
  } else {
    // Si no hay información, eliminar el nombre del archivo PDF
    setBudget(prev => ({
      ...prev,
      pdf_filename: null
    }));
  }
};

/**
 * Actualiza el número de páginas en la primera parte del presupuesto
 * @param {number} count - Número de páginas
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {Function} setBudgetParts - Setter para las partes
 * @returns {void}
 */
export const handlePageCountChange = (count, budgetParts, setBudgetParts) => {
  if (budgetParts.length > 0) {
    const updatedParts = [...budgetParts];
    updatedParts[0] = {
      ...updatedParts[0],
      pageCount: count
    };
    setBudgetParts(updatedParts);
  }
};

/**
 * Actualiza el tamaño de página en la primera parte del presupuesto
 * @param {string} pageSize - Tamaño de página
 * @param {string} customPageSize - Tamaño personalizado
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {Function} setBudgetParts - Setter para las partes
 * @returns {void}
 */
export const handlePageSizeChange = (pageSize, customPageSize, budgetParts, setBudgetParts) => {
  if (budgetParts.length > 0) {
    const updatedParts = [...budgetParts];
    updatedParts[0] = {
      ...updatedParts[0],
      pageSize,
      customPageSize
    };
    setBudgetParts(updatedParts);
  }
};

/**
 * Maneja el clic en el botón de información de la máquina para una parte específica
 * @param {Object} machine - Máquina seleccionada
 * @param {number} partIndex - Índice de la parte
 * @param {Function} setSelectedMachine - Setter para la máquina seleccionada
 * @param {Function} setSelectedPartIndex - Setter para el índice de la parte
 * @param {Function} setMachineInfoDialog - Setter para el modal de información
 * @returns {void}
 */
export const handleMachineInfoClick = (
  machine, 
  partIndex, 
  setSelectedMachine, 
  setSelectedPartIndex, 
  setMachineInfoDialog
) => {
  if (!machine) return;

  // Guardar la máquina y el índice de la parte seleccionada
  setSelectedMachine(machine);
  setSelectedPartIndex(partIndex);

  // Mostrar el diálogo de información de la máquina
  setMachineInfoDialog(true);
};

/**
 * Maneja la apertura del modal de información del cliente
 * @param {Object} client - Cliente seleccionado
 * @param {Function} setClientInfoModal - Setter para el modal
 * @param {Function} showSnackbar - Función para mostrar mensajes
 * @returns {void}
 */
export const handleClientInfoClick = (client, setClientInfoModal, showSnackbar) => {
  if (client) {
    setClientInfoModal(true);
  } else {
    showSnackbar('Selecciona un cliente primero', 'warning');
  }
};

/**
 * Maneja el cierre del modal de información del cliente
 * @param {Function} setClientInfoModal - Setter para el modal
 * @returns {void}
 */
export const handleCloseClientInfo = (setClientInfoModal) => {
  setClientInfoModal(false);
};

/**
 * Maneja el cierre del diálogo de PDF
 * @param {Function} setPdfDialogOpen - Setter para el diálogo
 * @returns {void}
 */
export const handleClosePdfDialog = (setPdfDialogOpen) => {
  setPdfDialogOpen(false);
};

/**
 * Maneja los cambios en selecciones de autocomplete
 * @param {string} name - Nombre del campo
 * @param {any} value - Valor seleccionado
 * @param {Function} setBudget - Setter para el presupuesto
 * @returns {void}
 */
export const handleAutocompleteChange = (name, value, setBudget) => {
  setBudget(prev => ({
    ...prev,
    [name]: value
  }));
};

/**
 * Maneja la navegación para cerrar el componente
 * @returns {void}
 */
export const handleCloseNavigation = () => {
  window.dispatchEvent(new CustomEvent('navigate-to-budget-list'));
};

/**
 * Crea la configuración de colores para compatibilidad
 * @param {Object} productConfig - Configuración del producto
 * @returns {Object} - Configuración de colores
 */
export const createColorConfig = (productConfig) => ({
  frontColors: productConfig.frontColors,
  backColors: productConfig.backColors,
  pantones: productConfig.pantones
});

/**
 * Actualiza la configuración de colores manteniendo compatibilidad
 * @param {Object} newColorConfig - Nueva configuración de colores
 * @param {Function} setProductConfig - Setter para la configuración del producto
 * @returns {void}
 */
export const updateColorConfig = (newColorConfig, setProductConfig) => {
  setProductConfig(prev => ({
    ...prev,
    frontColors: newColorConfig.frontColors,
    backColors: newColorConfig.backColors,
    pantones: newColorConfig.pantones
  }));
};

/**
 * Crea la configuración inicial del producto
 * @returns {Object} - Configuración del producto inicializada
 */
export const createInitialProductConfig = () => ({
  // Configuración de colores
  frontColors: '4', // CMYK por defecto
  backColors: '4',  // CMYK en reverso por defecto
  pantones: 0,      // Sin pantones adicionales por defecto

  // Configuración de partes del producto
  selectedParts: [], // Partes seleccionadas del producto

  // Configuración de estilo de trabajo
  workStyle: 'Flat', // Estilo de trabajo por defecto

  // Configuración adicional
  customOptions: {}
});

/**
 * Valida si una parte tiene los datos básicos necesarios
 * @param {Object} part - Parte del presupuesto
 * @returns {boolean} - true si tiene los datos básicos
 */
export const hasBasicPartData = (part) => {
  return !!(part && part.paper && part.machine && part.pageCount);
};

/**
 * Valida si una parte necesita recálculo
 * @param {Object} part - Parte del presupuesto
 * @returns {boolean} - true si necesita recálculo
 */
export const partNeedsRecalculation = (part) => {
  if (!hasBasicPartData(part)) {
    return false;
  }
  
  return !part.sheetCalculation || part.sheetCalculation.outdated === true;
};

export default {
  createEmptyPart,
  getPageSizeFromDimensions,
  generateOTNumber,
  createEmptyBudget,
  calculateTotalSheets,
  updateProcessQuantities,
  handlePdfInfoChange,
  handlePageCountChange,
  handlePageSizeChange,
  handleMachineInfoClick,
  handleClientInfoClick,
  handleCloseClientInfo,
  handleClosePdfDialog,
  handleAutocompleteChange,
  handleCloseNavigation,
  createColorConfig,
  updateColorConfig,
  createInitialProductConfig,
  hasBasicPartData,
  partNeedsRecalculation
};
