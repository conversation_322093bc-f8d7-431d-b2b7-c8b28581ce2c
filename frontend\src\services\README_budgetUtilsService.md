# Budget Utils Service

## Descripción

El `budgetUtilsService` es un servicio especializado que centraliza todas las funciones auxiliares y de utilidad del componente `BudgetForm`. Este servicio extrae funciones de creación, validación, manejo de eventos y utilidades generales, proporcionando una biblioteca reutilizable y bien organizada.

## Motivación

El `BudgetForm` original contenía múltiples funciones auxiliares dispersas a lo largo del componente:
- Funciones de creación de objetos (partes, presupuestos, configuraciones)
- Funciones de validación y cálculo
- Manejadores de eventos de UI
- Funciones de transformación de datos
- Utilidades de configuración

Al extraer estas funciones a un servicio especializado, conseguimos:
- **Centralización**: Todas las utilidades en un solo lugar
- **Reutilización**: Funciones disponibles para otros componentes
- **Testabilidad**: Funciones puras fáciles de probar
- **Mantenibilidad**: Código más organizado y legible
- **Consistencia**: Comportamiento uniforme en toda la aplicación

## Estructura del Servicio

### Funciones de Creación

#### `createEmptyPart(index, name)`
Crea una parte vacía del presupuesto con valores por defecto.

```javascript
const part = budgetUtilsService.createEmptyPart(0, 'General');
// {
//   part_id: "part-1234567890-0",
//   name: "General",
//   description: "",
//   assembly_order: "None",
//   pageSize: "A4",
//   customPageSize: "",
//   pageCount: "",
//   paper: null,
//   machine: null,
//   sheetCalculation: null,
//   paperCost: 0,
//   machineCost: 0,
//   plateCost: 0,
//   processCosts: [],
//   totalCost: 0,
//   customPrintTime: null,
//   colorConfig: {
//     frontColors: 4,
//     backColors: 4,
//     pantones: 0
//   }
// }
```

#### `createEmptyBudget()`
Crea un presupuesto vacío con valores por defecto.

#### `generateOTNumber()`
Genera un número de OT automático basado en la fecha actual.

#### `createInitialProductConfig()`
Crea la configuración inicial del producto.

### Funciones de Validación

#### `getPageSizeFromDimensions(dimensions)`
Determina el tamaño de página estándar a partir de dimensiones.

```javascript
const pageSize = budgetUtilsService.getPageSizeFromDimensions({ width: 210, height: 297 });
// "A4"

const customSize = budgetUtilsService.getPageSizeFromDimensions({ width: 100, height: 150 });
// "Personalizado"
```

#### `hasBasicPartData(part)`
Valida si una parte tiene los datos básicos necesarios.

#### `partNeedsRecalculation(part)`
Determina si una parte necesita recálculo.

### Funciones de Cálculo

#### `calculateTotalSheets(selectedPartIndex, budgetParts, sheetCalculation)`
Calcula el total de pliegos físicos basado en los cálculos del backend.

#### `updateProcessQuantities(selectedProcesses, copies)`
Actualiza las cantidades de procesos basado en el número de copias.

### Manejadores de Eventos

#### `handleMachineInfoClick(machine, partIndex, setters...)`
Maneja el clic en información de máquina.

#### `handleClientInfoClick(client, setClientInfoModal, showSnackbar)`
Maneja la apertura del modal de información del cliente.

#### `handleAutocompleteChange(name, value, setBudget)`
Maneja cambios en selecciones de autocomplete.

#### `handleCloseNavigation()`
Maneja la navegación para cerrar el componente.

### Funciones de Configuración

#### `createColorConfig(productConfig)`
Crea configuración de colores para compatibilidad.

#### `updateColorConfig(newColorConfig, setProductConfig)`
Actualiza la configuración de colores.

### Funciones de Manejo de PDF

#### `handlePdfInfoChange(info, setPdfInfo, setBudget)`
Maneja cambios en la información del PDF.

#### `handlePageCountChange(count, budgetParts, setBudgetParts)`
Actualiza el número de páginas en la primera parte.

#### `handlePageSizeChange(pageSize, customPageSize, budgetParts, setBudgetParts)`
Actualiza el tamaño de página en la primera parte.

## Uso en BudgetForm

### Antes (Funciones Dispersas)
```javascript
// 200+ líneas de funciones auxiliares dispersas
const createEmptyPart = (index = 0, name = 'General') => ({
  part_id: `part-${Date.now()}-${index}`,
  name: name,
  description: '',
  assembly_order: 'None',
  pageSize: 'A4',
  customPageSize: '',
  pageCount: '',
  paper: null,
  machine: null,
  sheetCalculation: null,
  paperCost: 0,
  machineCost: 0,
  plateCost: 0,
  processCosts: [],
  totalCost: 0,
  customPrintTime: null,
  colorConfig: {
    frontColors: 4,
    backColors: 4,
    pantones: 0
  }
});

const getPageSizeFromDimensions = (dimensions) => {
  if (!dimensions || !dimensions.width || !dimensions.height) {
    return 'A4';
  }
  // ... 20+ líneas más
};

const resetForm = useCallback(() => {
  const date = new Date();
  const year = date.getFullYear().toString().slice(2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const randomNum = Math.floor(1000 + Math.random() * 9000);
  // ... 20+ líneas más
}, []);

// ... muchas más funciones auxiliares
```

### Después (Con Servicio)
```javascript
// 10 líneas delegando al servicio
const createEmptyPart = budgetUtilsService.createEmptyPart;
const getPageSizeFromDimensions = budgetUtilsService.getPageSizeFromDimensions;

const resetForm = useCallback(() => {
  setBudget(budgetUtilsService.createEmptyBudget());
  setBudgetParts([budgetUtilsService.createEmptyPart(0, 'General')]);
  setSelectedPdf(null);
  setSelectedProcesses([]);
  setCalculatedProcessCost(0);
}, []);

const handleClientInfoClick = () => {
  budgetUtilsService.handleClientInfoClick(budget.client, setClientInfoModal, showSnackbar);
};

// ... funciones más simples y claras
```

## Beneficios Obtenidos

### 1. **Centralización de Utilidades**
- Todas las funciones auxiliares en un solo lugar
- Fácil localización y mantenimiento
- Comportamiento consistente

### 2. **Reutilización Mejorada**
- Funciones disponibles para otros componentes
- Evita duplicación de código
- Estándares unificados

### 3. **Testabilidad Excelente**
- Funciones puras fáciles de probar
- Tests unitarios comprehensivos
- Cobertura completa de casos edge

### 4. **Mantenibilidad Mejorada**
- Código más organizado y legible
- Cambios centralizados
- Documentación clara

## Funciones por Categoría

### Creación de Objetos
```javascript
// Crear parte vacía
const part = budgetUtilsService.createEmptyPart(1, 'Portada');

// Crear presupuesto vacío
const budget = budgetUtilsService.createEmptyBudget();

// Generar número OT
const otNumber = budgetUtilsService.generateOTNumber();
// "***********"
```

### Validación y Cálculo
```javascript
// Validar datos básicos
const isValid = budgetUtilsService.hasBasicPartData(part);

// Verificar necesidad de recálculo
const needsRecalc = budgetUtilsService.partNeedsRecalculation(part);

// Calcular total de pliegos
const totalSheets = budgetUtilsService.calculateTotalSheets(0, budgetParts, sheetCalculation);
```

### Manejo de Eventos
```javascript
// Manejar clic en máquina
const handleMachineClick = (machine, index) => {
  budgetUtilsService.handleMachineInfoClick(
    machine, index, setSelectedMachine, setSelectedPartIndex, setMachineInfoDialog
  );
};

// Manejar cambios de autocomplete
const handleChange = (name, value) => {
  budgetUtilsService.handleAutocompleteChange(name, value, setBudget);
};
```

### Configuración
```javascript
// Crear configuración de colores
const colorConfig = budgetUtilsService.createColorConfig(productConfig);

// Actualizar configuración
budgetUtilsService.updateColorConfig(newColorConfig, setProductConfig);
```

## Testing

El servicio incluye tests comprehensivos:

```javascript
describe('budgetUtilsService', () => {
  test('should create empty part with correct structure', () => { ... });
  test('should detect standard page sizes', () => { ... });
  test('should generate unique OT numbers', () => { ... });
  test('should validate part data correctly', () => { ... });
  test('should handle PDF info changes', () => { ... });
  // ... 25+ tests más
});
```

## Consideraciones de Implementación

### Funciones Puras
- La mayoría de funciones son puras (sin efectos secundarios)
- Fáciles de probar y predecir
- Reutilizables en diferentes contextos

### Manejo de Estados
- Funciones que manejan estados reciben setters como parámetros
- No dependen de contextos específicos
- Flexibles para diferentes implementaciones

### Validación Robusta
- Manejo de valores null/undefined
- Validación de tipos y estructuras
- Valores por defecto seguros

### Performance
- Funciones optimizadas para operaciones frecuentes
- Evita cálculos innecesarios
- Estructuras de datos eficientes

## Patrones de Uso

### Patrón de Delegación
```javascript
// En lugar de implementar lógica compleja en el componente
const handleSomething = () => {
  budgetUtilsService.handleSomething(params);
};
```

### Patrón de Configuración
```javascript
// Usar configuraciones predefinidas
const [productConfig, setProductConfig] = useState(
  budgetUtilsService.createInitialProductConfig()
);
```

### Patrón de Validación
```javascript
// Validar antes de procesar
if (budgetUtilsService.hasBasicPartData(part)) {
  // Procesar parte
}
```

## Próximos Pasos

1. **Añadir más utilidades**:
   - Funciones de formateo de datos
   - Utilidades de conversión
   - Helpers de validación avanzada

2. **Optimizaciones**:
   - Memoización de cálculos complejos
   - Cache de validaciones
   - Lazy loading de configuraciones

3. **Extensiones**:
   - Soporte para más tipos de página
   - Configuraciones personalizables
   - Integración con otros servicios

4. **Mejoras de DX**:
   - TypeScript para mejor tipado
   - JSDoc más detallado
   - Ejemplos de uso interactivos

La refactorización ha sido exitosa, centralizando todas las utilidades del BudgetForm en un servicio bien organizado, testeable y reutilizable que mejora significativamente la mantenibilidad y consistencia del código.
