/**
 * Tests para el servicio de cálculo silencioso de pliegos
 */
import {
  validatePartForCalculation,
  updatePartWithResults,
  calculatePartPaperWeight,
  calculateTotalPaperWeight
} from '../silentSheetCalculationService';

describe('Silent Sheet Calculation Service', () => {
  
  describe('validatePartForCalculation', () => {
    test('should return valid for complete part', () => {
      const part = {
        machine: { id: 1, name: 'Test Machine' },
        paper: { id: 1, name: 'Test Paper' },
        pageCount: 4
      };
      const result = validatePartForCalculation(part, 500);
      
      expect(result.isValid).toBe(true);
      expect(result.error).toBeNull();
    });

    test('should return invalid for missing machine', () => {
      const part = {
        paper: { id: 1, name: 'Test Paper' },
        pageCount: 4
      };
      const result = validatePartForCalculation(part, 500);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Selecciona una máquina para calcular pliegos');
    });

    test('should return invalid for missing paper', () => {
      const part = {
        machine: { id: 1, name: 'Test Machine' },
        pageCount: 4
      };
      const result = validatePartForCalculation(part, 500);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Selecciona un papel para calcular pliegos');
    });

    test('should return invalid for invalid page count', () => {
      const part = {
        machine: { id: 1, name: 'Test Machine' },
        paper: { id: 1, name: 'Test Paper' },
        pageCount: 0
      };
      const result = validatePartForCalculation(part, 500);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Ingresa un número válido de páginas');
    });

    test('should return invalid for invalid copies', () => {
      const part = {
        machine: { id: 1, name: 'Test Machine' },
        paper: { id: 1, name: 'Test Paper' },
        pageCount: 4
      };
      const result = validatePartForCalculation(part, 0);
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Ingresa una cantidad válida de copias');
    });
  });

  describe('updatePartWithResults', () => {
    test('should update part with calculation results', () => {
      const part = {
        id: 1,
        name: 'Test Part',
        machine: { id: 1 },
        paper: { id: 1 }
      };

      const results = {
        costos: {
          costo_papel: 100,
          costo_maquina: 200,
          costo_planchas: 50,
          costo_click: 30,
          costo_tinta: 25,
          costo_total: 405
        },
        mejor_combinacion: {
          total_pliegos: 10
        }
      };

      const updatedPart = updatePartWithResults(part, results);

      expect(updatedPart.sheetCalculation).toBe(results);
      expect(updatedPart.paperCost).toBe(100);
      expect(updatedPart.machineCost).toBe(200);
      expect(updatedPart.plateCost).toBe(50);
      expect(updatedPart.clickCost).toBe(30);
      expect(updatedPart.inkCost).toBe(25);
      expect(updatedPart.totalCost).toBe(405);
    });

    test('should handle missing costs gracefully', () => {
      const part = { id: 1, name: 'Test Part' };
      const results = { some_data: 'test' };

      const updatedPart = updatePartWithResults(part, results);

      expect(updatedPart.sheetCalculation).toBe(results);
      expect(updatedPart.paperCost).toBe(0);
      expect(updatedPart.machineCost).toBe(0);
      expect(updatedPart.plateCost).toBe(0);
      expect(updatedPart.clickCost).toBe(0);
      expect(updatedPart.inkCost).toBe(0);
      expect(updatedPart.totalCost).toBe(0);
    });
  });

  describe('calculatePartPaperWeight', () => {
    test('should return pre-calculated weight if available', () => {
      const part = {
        sheetCalculation: {
          paper_weight_kg: 15.5
        },
        paper: { weight: 80 }
      };

      const weight = calculatePartPaperWeight(part, 500);
      expect(weight).toBe(15.5);
    });

    test('should calculate weight manually when needed', () => {
      const part = {
        sheetCalculation: {
          mejor_combinacion: {
            total_pliegos: 10
          }
        },
        paper: {
          weight: 80,
          dimension_width: 700,
          dimension_height: 1000
        },
        machine: {
          type: 'Offset',
          maculatura_por_pliego: 150
        }
      };

      const weight = calculatePartPaperWeight(part, 500);
      expect(weight).toBeGreaterThan(0);
      expect(typeof weight).toBe('number');
    });

    test('should return 0 for digital machines without maculatura', () => {
      const part = {
        sheetCalculation: {
          mejor_combinacion: {
            total_pliegos: 10
          }
        },
        paper: {
          weight: 80,
          dimension_width: 700,
          dimension_height: 1000
        },
        machine: {
          type: 'Digital'
        }
      };

      const weight = calculatePartPaperWeight(part, 500);
      expect(weight).toBeGreaterThan(0);
    });

    test('should return 0 for missing data', () => {
      const part = {};
      const weight = calculatePartPaperWeight(part, 500);
      expect(weight).toBe(0);
    });
  });

  describe('calculateTotalPaperWeight', () => {
    test('should calculate total weight for multiple parts', () => {
      const parts = [
        {
          sheetCalculation: { paper_weight_kg: 10.5 },
          paper: { weight: 80 }
        },
        {
          sheetCalculation: { paper_weight_kg: 5.3 },
          paper: { weight: 90 }
        }
      ];

      const totalWeight = calculateTotalPaperWeight(parts, 500);
      expect(totalWeight).toBe(15.8); // 10.5 + 5.3 rounded to 2 decimals
    });

    test('should handle empty parts array', () => {
      const totalWeight = calculateTotalPaperWeight([], 500);
      expect(totalWeight).toBe(0);
    });

    test('should round to 2 decimal places', () => {
      const parts = [
        {
          sheetCalculation: { paper_weight_kg: 10.555 },
          paper: { weight: 80 }
        }
      ];

      const totalWeight = calculateTotalPaperWeight(parts, 500);
      expect(totalWeight).toBe(10.56); // Rounded to 2 decimals
    });
  });
});
