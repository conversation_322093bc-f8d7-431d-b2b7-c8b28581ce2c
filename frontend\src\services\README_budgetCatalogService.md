# Budget Catalog Service

## Descripción

El `budgetCatalogService` es un servicio especializado que extrae las funciones de obtención de catálogos del componente `BudgetForm`. Este servicio centraliza toda la lógica de fetch, filtrado y gestión de catálogos (clientes, papeles, máquinas y procesos) proporcionando una interfaz unificada y reutilizable.

## Motivación

Las funciones de fetch originales en `BudgetForm` tenían más de 67 líneas de código con mucha duplicación:
- Cuatro funciones similares con la misma estructura
- Lógica de filtrado repetida
- Manejo de errores duplicado
- Headers de autenticación repetidos
- Configuración de endpoints dispersa

Al extraer esta lógica a un servicio especializado, conseguimos:
- **Eliminación de duplicación**: Una función genérica para todos los catálogos
- **Configuración centralizada**: Endpoints y filtros en un solo lugar
- **Reutilización**: Funciones disponibles para otros componentes
- **Testabilidad**: Funciones más pequeñas y fáciles de probar
- **Mantenibilidad**: Cambios en un solo lugar afectan todos los catálogos

## Estructura del Servicio

### Funciones Principales

#### `fetchCatalogData(endpoint, filterFn, errorMessage, buildApiUrl)`
Función genérica que maneja todas las operaciones de fetch de catálogos.

**Parámetros:**
```javascript
{
  endpoint: string,        // Endpoint de la API (/clients/, /papers/, etc.)
  filterFn: Function,      // Función de filtrado específica
  errorMessage: string,    // Mensaje de error personalizado
  buildApiUrl: Function    // Función para construir URLs
}
```

#### Funciones Específicas de Catálogos

- **`fetchClients(buildApiUrl)`** - Obtiene clientes activos
- **`fetchPapers(buildApiUrl)`** - Obtiene papeles en stock
- **`fetchMachines(buildApiUrl)`** - Obtiene máquinas activas (Offset/Digital)
- **`fetchProcesses(buildApiUrl)`** - Obtiene procesos de acabado

### Configuración Centralizada

#### `catalogConfig`
Configuración unificada para todos los catálogos:

```javascript
export const catalogConfig = {
  clients: {
    endpoint: '/clients/',
    filter: catalogFilters.clients,
    errorMessage: 'Error al obtener los clientes'
  },
  papers: {
    endpoint: '/papers/',
    filter: catalogFilters.papers,
    errorMessage: 'Error al obtener los papeles'
  },
  // ... más configuraciones
};
```

#### `catalogFilters`
Filtros predefinidos para cada tipo de catálogo:

```javascript
export const catalogFilters = {
  clients: (client) => client.active,
  papers: (paper) => paper.inStock,
  machines: (machine) => 
    machine.status === 'Activa' && 
    (machine.type === 'Offset' || machine.type === 'Digital'),
  processes: (process) => 
    process.type && 
    ['Corte', 'Plegado', 'Encuadernación', 'Acabado', 'Barnizado', 'Laminado', 'Troquelado'].includes(process.type)
};
```

### Funciones Avanzadas

#### `loadAllCatalogs(buildApiUrl, showSnackbar)`
Carga todos los catálogos en paralelo con manejo de errores individual.

#### `createCatalogManager(params)`
Crea un gestor de catálogos con funciones para carga condicional y gestión de referencias.

#### `getCatalogStats(catalogs)`
Proporciona estadísticas detalladas de los catálogos cargados.

## Uso en BudgetForm

### Antes (Código Original)
```javascript
// 67 líneas de código duplicado
const fetchClients = useCallback(async () => {
  try {
    const apiUrl = buildApiUrl('/clients/');
    const response = await fetch(apiUrl);
    if (!response.ok) throw new Error('Error al obtener los clientes');
    const data = await response.json();
    const filteredClients = data.filter(client => client.active);
    setClients(filteredClients);
  } catch (err) {
    console.error('Error al obtener clientes:', err);
    showSnackbar(err.message, 'error');
  }
}, []);

// Funciones similares para papers, machines, processes...
```

### Después (Con Servicio)
```javascript
// 32 líneas delegando al servicio
const fetchClients = useCallback(async () => {
  try {
    const data = await budgetCatalogService.fetchClients(buildApiUrl);
    setClients(data);
  } catch (err) {
    console.error('Error al obtener clientes:', err);
    showSnackbar(err.message, 'error');
  }
}, []);

// Funciones similares pero más simples para otros catálogos...
```

## Beneficios Obtenidos

### 1. **Eliminación de Duplicación**
- **Antes**: 4 funciones con ~17 líneas cada una
- **Después**: 4 funciones con ~8 líneas cada una
- **Reducción**: ~52% menos código

### 2. **Configuración Centralizada**
- Endpoints en un solo lugar
- Filtros reutilizables
- Mensajes de error consistentes

### 3. **Autenticación Automática**
- Headers de autenticación incluidos automáticamente
- Manejo consistente de tokens

### 4. **Mejor Manejo de Errores**
- Errores específicos por catálogo
- Logging centralizado
- Fallbacks consistentes

## Funciones Avanzadas

### Carga Paralela de Catálogos
```javascript
const catalogs = await budgetCatalogService.loadAllCatalogs(buildApiUrl, showSnackbar);
// { clients: [...], papers: [...], machines: [...], processes: [...] }
```

### Gestor de Catálogos con Referencias
```javascript
const manager = budgetCatalogService.createCatalogManager({
  buildApiUrl,
  showSnackbar,
  setClients,
  setPapers,
  setMachines,
  setProcesses,
  fetchedClientsRef,
  fetchedPapersRef,
  fetchedMachinesRef,
  fetchedProcessesRef
});

// Carga solo los catálogos que no han sido cargados
await manager.loadInitialData();

// Recarga un catálogo específico
await manager.reloadCatalog('clients');
```

### Estadísticas de Catálogos
```javascript
const stats = budgetCatalogService.getCatalogStats(catalogs);
/*
{
  clients: { total: 150, active: 120 },
  papers: { total: 80, inStock: 65 },
  machines: { total: 12, active: 10, offset: 6, digital: 4 },
  processes: { total: 25, finishing: 18 }
}
*/
```

## Ejemplo de Uso Independiente

```javascript
import budgetCatalogService from '../services/budgetCatalogService';

// Obtener solo clientes activos
const activeClients = await budgetCatalogService.fetchClients(buildApiUrl);

// Usar filtros independientemente
const activeItems = allItems.filter(budgetCatalogService.catalogFilters.clients);

// Obtener configuración de un catálogo
const clientsConfig = budgetCatalogService.catalogConfig.clients;

// Cargar todos los catálogos de una vez
const allCatalogs = await budgetCatalogService.loadAllCatalogs(buildApiUrl, showSnackbar);
```

## Testing

El servicio incluye tests comprehensivos:

```javascript
describe('budgetCatalogService', () => {
  test('should filter catalogs correctly', () => { ... });
  test('should fetch catalog data with authentication', () => { ... });
  test('should handle API errors gracefully', () => { ... });
  test('should load all catalogs in parallel', () => { ... });
  test('should calculate statistics correctly', () => { ... });
  // ... 20+ tests más
});
```

## Consideraciones de Implementación

### Autenticación
- Incluye automáticamente el token de autenticación
- Headers consistentes en todas las llamadas

### Filtrado
- Filtros específicos para cada tipo de catálogo
- Lógica de filtrado centralizada y reutilizable

### Performance
- Carga paralela de múltiples catálogos
- Carga condicional para evitar requests innecesarios
- Gestión eficiente de referencias

### Robustez
- Manejo individual de errores por catálogo
- Fallbacks para catálogos que fallan
- Validación de datos de entrada

## Configuración de Filtros

### Clientes
```javascript
clients: (client) => client.active
```
Solo clientes con estado activo.

### Papeles
```javascript
papers: (paper) => paper.inStock
```
Solo papeles disponibles en stock.

### Máquinas
```javascript
machines: (machine) => 
  machine.status === 'Activa' && 
  (machine.type === 'Offset' || machine.type === 'Digital')
```
Solo máquinas activas de tipo Offset o Digital.

### Procesos
```javascript
processes: (process) => 
  process.type && 
  ['Corte', 'Plegado', 'Encuadernación', 'Acabado', 'Barnizado', 'Laminado', 'Troquelado'].includes(process.type)
```
Solo procesos de acabado específicos.

## Próximos Pasos

1. **Añadir cache** para evitar requests repetidos
2. **Implementar invalidación** de cache inteligente
3. **Añadir paginación** para catálogos grandes
4. **Crear hooks personalizados** para uso en React
5. **Añadir métricas** de uso y performance
6. **Implementar sincronización** en tiempo real

La refactorización ha sido exitosa, eliminando duplicación de código, centralizando configuración y mejorando significativamente la mantenibilidad mientras mantiene toda la funcionalidad original.
