/**
 * Tests para printTimeUpdateService
 */
import printTimeUpdateService, {
  prepareDigitalRequestData,
  prepareOffsetRequestData,
  validatePartForRecalculation,
  updatePartWithCalculation,
  getDefaultTimeFromPart,
  handleCustomMachineTimeReset
} from '../printTimeUpdateService';

// Mock de fetch global
global.fetch = jest.fn();

// Mock de localStorage
const mockLocalStorage = {
  getItem: jest.fn(() => 'mock-token'),
  setItem: jest.fn(),
  removeItem: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('printTimeUpdateService', () => {
  beforeEach(() => {
    fetch.mockClear();
    mockLocalStorage.getItem.mockClear();
  });

  describe('validatePartForRecalculation', () => {
    test('should return valid for complete part', () => {
      const part = {
        machine: { type: 'Digital' },
        paper: { paper_id: 1 },
        pageCount: 10
      };

      const result = validatePartForRecalculation(part);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeNull();
    });

    test('should return invalid when machine is missing', () => {
      const part = {
        paper: { paper_id: 1 },
        pageCount: 10
      };

      const result = validatePartForRecalculation(part);

      expect(result.isValid).toBe(false);
      expect(result.error).toContain('máquina');
    });

    test('should return invalid when paper is missing', () => {
      const part = {
        machine: { type: 'Digital' },
        pageCount: 10
      };

      const result = validatePartForRecalculation(part);

      expect(result.isValid).toBe(false);
      expect(result.error).toContain('papel');
    });

    test('should return invalid when pageCount is missing', () => {
      const part = {
        machine: { type: 'Digital' },
        paper: { paper_id: 1 }
      };

      const result = validatePartForRecalculation(part);

      expect(result.isValid).toBe(false);
      expect(result.error).toContain('páginas');
    });
  });

  describe('prepareDigitalRequestData', () => {
    test('should prepare correct data for digital machine', () => {
      const part = {
        machine: { machine_id: 'digital-1' },
        paper: { paper_id: 'paper-1' },
        pageCount: '10',
        isDuplex: true,
        isColor: false
      };
      const budget = { copies: '500' };
      const time = 2.5;

      const result = prepareDigitalRequestData(part, budget, time);

      expect(result).toEqual({
        machine_id: 'digital-1',
        copies: 500,
        paper_id: 'paper-1',
        is_duplex: true,
        is_color: false,
        num_paginas: 10,
        custom_print_time: 2.5
      });
    });

    test('should use default values when properties are undefined', () => {
      const part = {
        machine: { product_id: 'digital-2' },
        paper: { product_id: 'paper-2' },
        pageCount: '5'
      };
      const budget = { copies: '1000' };
      const time = 1;

      const result = prepareDigitalRequestData(part, budget, time);

      expect(result).toEqual({
        machine_id: 'digital-2',
        copies: 1000,
        paper_id: 'paper-2',
        is_duplex: true, // default
        is_color: true,  // default
        num_paginas: 5,
        custom_print_time: 1
      });
    });
  });

  describe('prepareOffsetRequestData', () => {
    test('should prepare correct data for offset machine', () => {
      const part = {
        machine: { machine_id: 'offset-1' },
        paper: { paper_id: 'paper-1' },
        pageCount: '8',
        customPageSize: { width: 200, height: 280 },
        colorConfig: { frontColors: 2, backColors: 1 }
      };
      const budget = { copies: '1000' };
      const time = 3;

      const result = prepareOffsetRequestData(part, budget, time);

      expect(result).toEqual({
        machine_id: 'offset-1',
        copies: 1000,
        paper_id: 'paper-1',
        num_paginas: 8,
        ancho_pagina: 200,
        alto_pagina: 280,
        colors_front: 2,
        colors_back: 1,
        custom_print_time: 3
      });
    });

    test('should use default values when properties are undefined', () => {
      const part = {
        machine: { product_id: 'offset-2' },
        paper: { product_id: 'paper-2' },
        pageCount: '4'
      };
      const budget = {};
      const time = 1.5;

      const result = prepareOffsetRequestData(part, budget, time);

      expect(result).toEqual({
        machine_id: 'offset-2',
        copies: 500, // default
        paper_id: 'paper-2',
        num_paginas: 4,
        ancho_pagina: 210, // default A4
        alto_pagina: 297,  // default A4
        colors_front: 4,   // default CMYK
        colors_back: 0,    // default
        custom_print_time: 1.5
      });
    });
  });

  describe('updatePartWithCalculation', () => {
    test('should update part with calculation data', () => {
      const part = {
        name: 'Test Part',
        machine: { type: 'Digital' },
        existingProperty: 'should be preserved'
      };

      const calculationData = {
        paper_cost: 100,
        machine_cost: 50,
        plates_cost: 25,
        click_cost: 30,
        ink_cost: 15,
        total_cost: 220
      };

      const time = 2;

      const result = updatePartWithCalculation(part, calculationData, time);

      expect(result).toEqual({
        name: 'Test Part',
        machine: { type: 'Digital' },
        existingProperty: 'should be preserved',
        customPrintTime: 2,
        sheetCalculation: calculationData,
        paperCost: 100,
        machineCost: 50,
        plateCost: 25,
        clickCost: 30,
        inkCost: 15,
        totalCost: 220
      });
    });

    test('should use 0 as default for missing cost values', () => {
      const part = { name: 'Test Part' };
      const calculationData = {}; // Empty calculation data
      const time = 1;

      const result = updatePartWithCalculation(part, calculationData, time);

      expect(result.paperCost).toBe(0);
      expect(result.machineCost).toBe(0);
      expect(result.plateCost).toBe(0);
      expect(result.clickCost).toBe(0);
      expect(result.inkCost).toBe(0);
      expect(result.totalCost).toBe(0);
    });
  });

  describe('integration tests', () => {
    test('should handle successful API call', async () => {
      const mockCalculationData = {
        paper_cost: 100,
        machine_cost: 50,
        total_cost: 150
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockCalculationData
      });

      const mockSetBudgetParts = jest.fn();
      const mockSetCurrentCalculatedPart = jest.fn();
      const mockShowSnackbar = jest.fn();
      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);

      const params = {
        time: 2,
        selectedPartIndex: 0,
        budgetParts: [{
          machine: { type: 'Digital', machine_id: 'digital-1' },
          paper: { paper_id: 'paper-1' },
          pageCount: '10'
        }],
        budget: { copies: '500' },
        buildApiUrl: mockBuildApiUrl,
        setBudgetParts: mockSetBudgetParts,
        setCurrentCalculatedPart: mockSetCurrentCalculatedPart,
        setCustomMachinePrintTime: jest.fn(),
        setMachineInfoDialog: jest.fn(),
        showSnackbar: mockShowSnackbar
      };

      await printTimeUpdateService.handlePrintTimeUpdate(params);

      expect(fetch).toHaveBeenCalledWith(
        'http://api.test/v2/calculate-digital',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          })
        })
      );

      expect(mockSetBudgetParts).toHaveBeenCalled();
      expect(mockSetCurrentCalculatedPart).toHaveBeenCalled();
      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('Tiempo actualizado'),
        'success'
      );
    });

    test('should handle API error', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Internal Server Error'
      });

      const mockShowSnackbar = jest.fn();
      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);

      const params = {
        time: 2,
        selectedPartIndex: 0,
        budgetParts: [{
          machine: { type: 'Digital', machine_id: 'digital-1' },
          paper: { paper_id: 'paper-1' },
          pageCount: '10'
        }],
        budget: { copies: '500' },
        buildApiUrl: mockBuildApiUrl,
        setBudgetParts: jest.fn(),
        setCurrentCalculatedPart: jest.fn(),
        setCustomMachinePrintTime: jest.fn(),
        setMachineInfoDialog: jest.fn(),
        showSnackbar: mockShowSnackbar
      };

      await expect(printTimeUpdateService.handlePrintTimeUpdate(params))
        .rejects.toThrow();

      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('Error al recalcular'),
        'error'
      );
    });
  });

  describe('getDefaultTimeFromPart', () => {
    test('should return estimated_time_hours from sheetCalculation', () => {
      const currentCalculatedPart = {
        sheetCalculation: {
          estimated_time_hours: 2.5
        }
      };

      const result = getDefaultTimeFromPart(currentCalculatedPart);
      expect(result).toBe(2.5);
    });

    test('should return estimated_time_hours from clicksData if sheetCalculation not available', () => {
      const currentCalculatedPart = {
        clicksData: {
          estimated_time_hours: 1.8
        }
      };

      const result = getDefaultTimeFromPart(currentCalculatedPart);
      expect(result).toBe(1.8);
    });

    test('should return default value of 1 when no time data available', () => {
      const currentCalculatedPart = {};
      const result = getDefaultTimeFromPart(currentCalculatedPart);
      expect(result).toBe(1);
    });

    test('should return default value of 1 when currentCalculatedPart is null', () => {
      const result = getDefaultTimeFromPart(null);
      expect(result).toBe(1);
    });
  });

  describe('handleCustomMachineTimeReset', () => {
    test('should reset custom machine time and update with default time', async () => {
      const mockSetCustomMachinePrintTime = jest.fn();
      const mockHandleUpdatePrintTime = jest.fn().mockResolvedValue();
      const mockShowSnackbar = jest.fn();

      const currentCalculatedPart = {
        sheetCalculation: {
          estimated_time_hours: 3.0
        }
      };

      const params = {
        currentCalculatedPart,
        selectedPartIndex: 0,
        setCustomMachinePrintTime: mockSetCustomMachinePrintTime,
        handleUpdatePrintTime: mockHandleUpdatePrintTime,
        showSnackbar: mockShowSnackbar
      };

      await handleCustomMachineTimeReset(params);

      expect(mockSetCustomMachinePrintTime).toHaveBeenCalledWith(null);
      expect(mockHandleUpdatePrintTime).toHaveBeenCalledWith(3.0);
    });

    test('should only reset custom time when no current calculated part', async () => {
      const mockSetCustomMachinePrintTime = jest.fn();
      const mockHandleUpdatePrintTime = jest.fn();
      const mockShowSnackbar = jest.fn();

      const params = {
        currentCalculatedPart: null,
        selectedPartIndex: null,
        setCustomMachinePrintTime: mockSetCustomMachinePrintTime,
        handleUpdatePrintTime: mockHandleUpdatePrintTime,
        showSnackbar: mockShowSnackbar
      };

      await handleCustomMachineTimeReset(params);

      expect(mockSetCustomMachinePrintTime).toHaveBeenCalledWith(null);
      expect(mockHandleUpdatePrintTime).not.toHaveBeenCalled();
    });

    test('should handle errors in handleUpdatePrintTime', async () => {
      const mockSetCustomMachinePrintTime = jest.fn();
      const mockHandleUpdatePrintTime = jest.fn().mockRejectedValue(new Error('Update failed'));
      const mockShowSnackbar = jest.fn();

      const currentCalculatedPart = {
        sheetCalculation: {
          estimated_time_hours: 2.0
        }
      };

      const params = {
        currentCalculatedPart,
        selectedPartIndex: 0,
        setCustomMachinePrintTime: mockSetCustomMachinePrintTime,
        handleUpdatePrintTime: mockHandleUpdatePrintTime,
        showSnackbar: mockShowSnackbar
      };

      await expect(handleCustomMachineTimeReset(params)).rejects.toThrow('Error al resetear tiempo personalizado');

      expect(mockSetCustomMachinePrintTime).toHaveBeenCalledWith(null);
      expect(mockHandleUpdatePrintTime).toHaveBeenCalledWith(2.0);
      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('Error al resetear tiempo personalizado'),
        'error'
      );
    });
  });
});
