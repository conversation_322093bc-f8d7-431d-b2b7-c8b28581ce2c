import { useState, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Description as DescriptionIcon,
  GetApp as GetAppIcon
} from '@mui/icons-material';
import { JDFService, LogService } from '../services/simplifiedServices';

const JDFGenerator = ({ budget, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [jdf, setJdf] = useState(null);
  const [useCompleteSchema, setUseCompleteSchema] = useState(false);

  // Función para obtener el nombre del cliente
  const getClientName = useCallback((budget) => {
    if (!budget) return 'No especificado';

    // Intentar obtener el nombre del cliente de diferentes fuentes
    if (budget.client_data && budget.client_data.company && budget.client_data.company.name) {
      return budget.client_data.company.name;
    }

    if (budget.client_name) {
      return budget.client_name;
    }

    if (budget.client) {
      if (typeof budget.client === 'object') {
        return budget.client.name || budget.client.company?.name || 'No especificado';
      }
      return budget.client;
    }

    return 'No especificado';
  }, []);



  // Función para generar el JDF
  const handleGenerateJDF = async () => {
    if (!budget?.budget_id) {
      setError('No hay un presupuesto seleccionado');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // El estilo de trabajo se determina automáticamente en el backend

      // Usar el endpoint de esquema completo si está seleccionado
      let jdfData;
      if (useCompleteSchema) {
        jdfData = await JDFService.getJDFSchemaByBudgetId(budget.budget_id);
        LogService.logUserAction('generate_jdf_schema_success', {
          budget_id: budget.budget_id,
          ot_number: budget.ot_number
        });
      } else {
        jdfData = await JDFService.generateJDF(budget.budget_id);
        LogService.logUserAction('generate_jdf_success', {
          budget_id: budget.budget_id,
          ot_number: budget.ot_number
        });
      }

      setJdf(jdfData);
    } catch (err) {
      console.error('Error al generar JDF:', err);
      setError(`Error al generar JDF: ${err.message}`);

      LogService.logError('generate_jdf_error', {
        budget_id: budget.budget_id,
        error: err.message
      });
    } finally {
      setLoading(false);
    }
  };

  // Función para descargar el JDF como archivo JSON
  const handleDownloadJDF = () => {
    if (!jdf) return;

    const dataStr = JSON.stringify(jdf, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `JDF_${budget.ot_number || budget.budget_id}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    LogService.logUserAction('download_jdf', {
      budget_id: budget.budget_id,
      ot_number: budget.ot_number
    });
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h5" gutterBottom>
        Generador de JDF
      </Typography>

      <Divider sx={{ my: 2 }} />

      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Typography variant="subtitle1">
            Presupuesto: {budget?.ot_number || 'No seleccionado'}
          </Typography>
          <Typography variant="body2">
            Cliente: {getClientName(budget)}
          </Typography>
          <Typography variant="body2">
            Tipo de trabajo: {budget?.job_type || 'No especificado'}
          </Typography>
          <Typography variant="body2">
            Cantidad: {budget?.quantity || 'No especificado'}
          </Typography>
          {budget?.machine && (
            <Typography variant="body2">
              Máquina: {budget?.machine}
            </Typography>
          )}
        </Grid>

        <Grid item xs={12} sm={6}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControl component="fieldset">
              <Typography variant="subtitle2" gutterBottom>
                Formato de JDF
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Button
                  variant={useCompleteSchema ? "outlined" : "contained"}
                  color="primary"
                  size="small"
                  onClick={() => setUseCompleteSchema(false)}
                >
                  Básico
                </Button>
                <Button
                  variant={useCompleteSchema ? "contained" : "outlined"}
                  color="primary"
                  size="small"
                  onClick={() => setUseCompleteSchema(true)}
                >
                  Esquema Completo
                </Button>
              </Box>
            </FormControl>
          </Box>
        </Grid>
      </Grid>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<DescriptionIcon />}
          onClick={handleGenerateJDF}
          disabled={loading || !budget?.budget_id}
        >
          {loading ? <CircularProgress size={24} /> : 'Generar JDF'}
        </Button>

        {jdf && (
          <Button
            variant="outlined"
            color="primary"
            startIcon={<GetAppIcon />}
            onClick={handleDownloadJDF}
          >
            Descargar JDF
          </Button>
        )}
      </Box>

      {jdf && (
        <Paper sx={{ p: 2, maxHeight: '400px', overflow: 'auto' }}>
          <Typography variant="subtitle1" gutterBottom>
            Vista previa del JDF:
          </Typography>
          <pre style={{ whiteSpace: 'pre-wrap', fontSize: '0.875rem' }}>
            {JSON.stringify(jdf, null, 2)}
          </pre>
        </Paper>
      )}
    </Box>
  );
};

export default JDFGenerator;
