from typing import Dict, Any, List, Optional
import os
import json
from utils.logger import log_info, log_error
from utils.data_manager import read_data, write_data
from utils.catalog_manager import load_paper_catalog as load_catalog

def load_paper_catalog() -> List[Dict[str, Any]]:
    """
    Carga el catálogo de papeles
    """
    # Intentar cargar desde catalog_manager primero
    catalog = load_catalog()
    if catalog:
        return catalog
    # Si no hay datos, intentar desde data_manager
    return read_data("papers")

def save_paper_catalog(catalog: List[Dict[str, Any]]) -> bool:
    """
    Guarda el catálogo de papeles
    """
    return write_data("papers", catalog)

def get_paper_by_id(paper_id: str) -> Optional[Dict[str, Any]]:
    """
    Obtiene un papel por su ID (paper_id o product_id)
    """
    paper_catalog = load_paper_catalog()
    for paper in paper_catalog:
        if paper.get("product_id") == paper_id or paper.get("paper_id") == paper_id:
            return paper
    return None

def calculate_paper_weight(paper: Dict[str, Any], total_sheets: int) -> float:
    """
    Calcula el peso total del papel en kg
    """
    if not paper:
        return 0.0

    # Obtener el gramaje del papel
    grammage = paper.get("weight", 0)
    if grammage == 0:
        grammage = paper.get("grammage", 0)

    # Obtener dimensiones del papel
    sheet_width_mm = paper.get("dimension_width", 0)
    sheet_height_mm = paper.get("dimension_height", 0)

    # Calcular el área del pliego en m²
    sheet_area_m2 = (sheet_width_mm / 1000) * (sheet_height_mm / 1000)

    # Calcular el peso de un pliego en kg
    sheet_weight_kg = (grammage * sheet_area_m2) / 1000

    # Calcular el peso total en kg
    total_weight_kg = sheet_weight_kg * total_sheets

    return total_weight_kg

def calculate_paper_weight_from_dimensions(width_mm: float, height_mm: float, weight_gsm: float, sheets: int) -> float:
    """
    Calcula el peso total del papel en kg a partir de dimensiones y gramaje

    Args:
        width_mm (float): Ancho del pliego en mm
        height_mm (float): Alto del pliego en mm
        weight_gsm (float): Gramaje del papel en g/m²
        sheets (int): Número de pliegos

    Returns:
        float: Peso total en kg
    """
    # Calcular el área del pliego en m²
    sheet_area_m2 = (width_mm / 1000) * (height_mm / 1000)

    # Calcular el peso de un pliego en kg
    sheet_weight_kg = (weight_gsm * sheet_area_m2) / 1000

    # Calcular el peso total en kg
    total_weight_kg = sheet_weight_kg * sheets

    return total_weight_kg
