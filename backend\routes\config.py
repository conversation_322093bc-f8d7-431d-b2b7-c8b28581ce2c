from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from typing import Dict, Any, Optional
import json
import os
from utils.logger import log_info, log_error

router = APIRouter(
    prefix="/config",
    tags=["config"],
    responses={404: {"description": "Configuración no encontrada"}}
)

# Ruta al archivo de configuración
CONFIG_FILE = "config/app_config.json"

# Modelo para actualizar la configuración
class ConfigUpdate(BaseModel):
    profit_percentage: Optional[float] = None
    default_waste_percentage: Optional[float] = None
    default_vat_percentage: Optional[float] = None
    default_currency: Optional[str] = None
    default_country: Optional[str] = None
    default_language: Optional[str] = None
    default_date_format: Optional[str] = None
    default_time_format: Optional[str] = None
    default_timezone: Optional[str] = None
    company_info: Optional[Dict[str, Any]] = None
    budget_settings: Optional[Dict[str, Any]] = None
    production_settings: Optional[Dict[str, Any]] = None

# Función para cargar la configuración
def load_config():
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r", encoding="utf-8") as file:
                return json.load(file)
        else:
            log_error(f"Archivo de configuración no encontrado: {CONFIG_FILE}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Archivo de configuración no encontrado"
            )
    except Exception as e:
        log_error(f"Error al cargar la configuración: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al cargar la configuración: {e}"
        )

# Función para guardar la configuración
def save_config(config_data):
    try:
        # Asegurarse de que el directorio existe
        os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
        
        with open(CONFIG_FILE, "w", encoding="utf-8") as file:
            json.dump(config_data, file, ensure_ascii=False, indent=2)
        
        log_info("Configuración guardada correctamente")
        return True
    except Exception as e:
        log_error(f"Error al guardar la configuración: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al guardar la configuración: {e}"
        )

# Obtener toda la configuración
@router.get("/")
async def get_config():
    """
    Obtiene toda la configuración de la aplicación
    """
    return load_config()

# Obtener un valor específico de la configuración
@router.get("/{key}")
async def get_config_value(key: str):
    """
    Obtiene un valor específico de la configuración
    """
    config = load_config()
    
    if key in config:
        return {key: config[key]}
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Clave de configuración '{key}' no encontrada"
        )

# Actualizar la configuración
@router.put("/")
async def update_config(config_update: ConfigUpdate):
    """
    Actualiza la configuración de la aplicación
    """
    current_config = load_config()
    
    # Actualizar solo los campos proporcionados
    update_data = config_update.dict(exclude_unset=True)
    
    for key, value in update_data.items():
        if isinstance(value, dict) and key in current_config and isinstance(current_config[key], dict):
            # Si es un diccionario, actualizar solo los campos proporcionados
            current_config[key].update(value)
        else:
            # Si no es un diccionario o no existe, reemplazar/crear el valor
            current_config[key] = value
    
    # Guardar la configuración actualizada
    save_config(current_config)
    
    return {"message": "Configuración actualizada correctamente", "config": current_config}

# Actualizar un valor específico de la configuración
@router.put("/{key}")
async def update_config_value(key: str, value: Dict[str, Any]):
    """
    Actualiza un valor específico de la configuración
    """
    current_config = load_config()
    
    if key in current_config:
        if isinstance(value, dict) and isinstance(current_config[key], dict):
            # Si es un diccionario, actualizar solo los campos proporcionados
            current_config[key].update(value)
        else:
            # Si no es un diccionario, reemplazar el valor
            current_config[key] = value.get(key, value)
        
        # Guardar la configuración actualizada
        save_config(current_config)
        
        return {"message": f"Configuración '{key}' actualizada correctamente", "config": current_config}
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Clave de configuración '{key}' no encontrada"
        )
