import { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  Grid,
  Divider,
  Snackbar,
  Alert,
  InputAdornment,
  Card,
  CardContent,
  CardHeader,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SaveIcon from '@mui/icons-material/Save';
import SettingsIcon from '@mui/icons-material/Settings';
import BusinessIcon from '@mui/icons-material/Business';
import DescriptionIcon from '@mui/icons-material/Description';
import FactoryIcon from '@mui/icons-material/Factory';
import FolderIcon from '@mui/icons-material/Folder';
import ConfigService from '../services/ConfigService';
import FoldingSchemesViewer from './FoldingSchemesViewer';

const AppConfig = () => {
  // Estado para la configuración
  const [config, setConfig] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  
  // Estado para el snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Cargar la configuración al montar el componente
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const data = await ConfigService.getConfig();
        setConfig(data);
        setError(null);
      } catch (err) {
        console.error('Error al cargar la configuración:', err);
        setError('Error al cargar la configuración. Por favor, inténtelo de nuevo.');
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  // Función para manejar cambios en los campos de configuración general
  const handleConfigChange = (key, value) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      [key]: value
    }));
  };

  // Función para manejar cambios en los campos de configuración anidados
  const handleNestedConfigChange = (section, key, value) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      [section]: {
        ...prevConfig[section],
        [key]: value
      }
    }));
  };

  // Función para guardar la configuración
  const handleSaveConfig = async () => {
    try {
      setSaving(true);
      await ConfigService.updateConfig(config);
      
      // Mostrar mensaje de éxito
      setSnackbar({
        open: true,
        message: 'Configuración guardada correctamente',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error al guardar la configuración:', err);
      
      // Mostrar mensaje de error
      setSnackbar({
        open: true,
        message: 'Error al guardar la configuración',
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  // Función para cerrar el snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Si está cargando, mostrar indicador de carga
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Si hay un error, mostrar mensaje de error
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  // Si no hay configuración, mostrar mensaje
  if (!config) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">No se ha podido cargar la configuración</Alert>
      </Box>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ display: 'flex', alignItems: 'center' }}>
          <SettingsIcon sx={{ mr: 1 }} />
          Configuración del Sistema
        </Typography>
        
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSaveConfig}
          disabled={saving}
        >
          {saving ? 'Guardando...' : 'Guardar Cambios'}
        </Button>
      </Box>

      <Divider sx={{ mb: 3 }} />

      {/* Configuración General */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Configuración General</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Porcentaje de Beneficio"
                type="number"
                value={config.profit_percentage}
                onChange={(e) => handleConfigChange('profit_percentage', parseFloat(e.target.value))}
                InputProps={{
                  endAdornment: <InputAdornment position="end">%</InputAdornment>,
                }}
                helperText="Porcentaje que se añade al coste total para calcular el precio de venta"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Porcentaje de Merma por Defecto"
                type="number"
                value={config.default_waste_percentage}
                onChange={(e) => handleConfigChange('default_waste_percentage', parseFloat(e.target.value))}
                InputProps={{
                  endAdornment: <InputAdornment position="end">%</InputAdornment>,
                }}
                helperText="Porcentaje de merma que se aplica por defecto en los cálculos"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="IVA por Defecto"
                type="number"
                value={config.default_vat_percentage}
                onChange={(e) => handleConfigChange('default_vat_percentage', parseFloat(e.target.value))}
                InputProps={{
                  endAdornment: <InputAdornment position="end">%</InputAdornment>,
                }}
                helperText="Porcentaje de IVA que se aplica por defecto"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Moneda por Defecto"
                value={config.default_currency}
                onChange={(e) => handleConfigChange('default_currency', e.target.value)}
                helperText="Símbolo de moneda que se muestra en los precios"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="País por Defecto"
                value={config.default_country}
                onChange={(e) => handleConfigChange('default_country', e.target.value)}
                helperText="País que se usa por defecto en los cálculos de envío"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Idioma por Defecto"
                value={config.default_language}
                onChange={(e) => handleConfigChange('default_language', e.target.value)}
                helperText="Código de idioma (ej: es-ES, en-US)"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Formato de Fecha"
                value={config.default_date_format}
                onChange={(e) => handleConfigChange('default_date_format', e.target.value)}
                helperText="Formato de fecha (ej: DD/MM/YYYY)"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Formato de Hora"
                value={config.default_time_format}
                onChange={(e) => handleConfigChange('default_time_format', e.target.value)}
                helperText="Formato de hora (ej: HH:mm)"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Zona Horaria"
                value={config.default_timezone}
                onChange={(e) => handleConfigChange('default_timezone', e.target.value)}
                helperText="Zona horaria (ej: Europe/Madrid)"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Información de la Empresa */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
            <BusinessIcon sx={{ mr: 1 }} />
            Información de la Empresa
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Nombre de la Empresa"
                value={config.company_info.name}
                onChange={(e) => handleNestedConfigChange('company_info', 'name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Dirección"
                value={config.company_info.address}
                onChange={(e) => handleNestedConfigChange('company_info', 'address', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Ciudad"
                value={config.company_info.city}
                onChange={(e) => handleNestedConfigChange('company_info', 'city', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Código Postal"
                value={config.company_info.postal_code}
                onChange={(e) => handleNestedConfigChange('company_info', 'postal_code', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                fullWidth
                label="Teléfono"
                value={config.company_info.phone}
                onChange={(e) => handleNestedConfigChange('company_info', 'phone', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                value={config.company_info.email}
                onChange={(e) => handleNestedConfigChange('company_info', 'email', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Sitio Web"
                value={config.company_info.website}
                onChange={(e) => handleNestedConfigChange('company_info', 'website', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="CIF/NIF"
                value={config.company_info.tax_id}
                onChange={(e) => handleNestedConfigChange('company_info', 'tax_id', e.target.value)}
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Configuración de Presupuestos */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
            <DescriptionIcon sx={{ mr: 1 }} />
            Configuración de Presupuestos
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Estado por Defecto"
                value={config.budget_settings.default_status}
                onChange={(e) => handleNestedConfigChange('budget_settings', 'default_status', e.target.value)}
                helperText="Estado que se asigna a los nuevos presupuestos"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Días de Validez"
                type="number"
                value={config.budget_settings.expiration_days}
                onChange={(e) => handleNestedConfigChange('budget_settings', 'expiration_days', parseInt(e.target.value))}
                helperText="Número de días que un presupuesto es válido"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Configuración de Producción */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
            <FactoryIcon sx={{ mr: 1 }} />
            Configuración de Producción
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Intervalo de Tiempo por Defecto (minutos)"
                type="number"
                value={config.production_settings.default_time_slot}
                onChange={(e) => handleNestedConfigChange('production_settings', 'default_time_slot', parseInt(e.target.value))}
                helperText="Duración en minutos de cada intervalo en el planificador"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Hora de Inicio"
                value={config.production_settings.working_hours.start}
                onChange={(e) => handleNestedConfigChange('production_settings', 'working_hours', {
                  ...config.production_settings.working_hours,
                  start: e.target.value
                })}
                helperText="Hora de inicio de la jornada laboral (HH:MM)"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Hora de Fin"
                value={config.production_settings.working_hours.end}
                onChange={(e) => handleNestedConfigChange('production_settings', 'working_hours', {
                  ...config.production_settings.working_hours,
                  end: e.target.value
                })}
                helperText="Hora de fin de la jornada laboral (HH:MM)"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Esquemas de Plegado */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
            <FolderIcon sx={{ mr: 1 }} />
            Esquemas de Plegado
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <div style={{ padding: '20px', backgroundColor: '#f0f0f0', borderRadius: '8px' }}>
            <h3>🔧 Componente de Esquemas de Plegado</h3>
            <p>Esta sección mostrará todos los esquemas de plegado disponibles.</p>
            <p><strong>Estado:</strong> Componente cargado correctamente</p>
            <div style={{ border: '2px solid red', padding: '10px', margin: '10px 0' }}>
              <p><strong>Intentando cargar FoldingSchemesViewer...</strong></p>
              <FoldingSchemesViewer />
            </div>
          </div>
        </AccordionDetails>
      </Accordion>

      {/* Snackbar para mostrar mensajes */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default AppConfig;
