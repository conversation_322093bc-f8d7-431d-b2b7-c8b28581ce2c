"""
Módulo para gestionar el logging de la aplicación
"""
import logging
import os
from datetime import datetime
from pathlib import Path

# Configuración de directorios
LOG_DIR = Path("logs")
LOG_DIR.mkdir(exist_ok=True)

# Nombre del archivo de log basado en la fecha actual
current_date = datetime.now().strftime("%Y-%m-%d")
LOG_FILE = LOG_DIR / f"imprenta_{current_date}.log"

# Configurar el logger
logger = logging.getLogger("imprenta")
logger.setLevel(logging.INFO)

# Crear un manejador de archivo
file_handler = logging.FileHandler(LOG_FILE, encoding="utf-8")
file_handler.setLevel(logging.INFO)

# Crear un manejador de consola
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Crear un formato para los logs
log_format = logging.Formatter(
    "%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

# Aplicar el formato a los manejadores
file_handler.setFormatter(log_format)
console_handler.setFormatter(log_format)

# Añadir los manejadores al logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

def log_info(message):
    """Registra un mensaje informativo en el log"""
    logger.info(message)

def log_error(message):
    """Registra un mensaje de error en el log"""
    logger.error(message)

def log_request(request, status_code=None):
    """Registra una solicitud HTTP en el log"""
    if status_code:
        logger.info(f"Respuesta: {request.method} {request.url.path} - {status_code}")
    else:
        logger.info(f"Solicitud: {request.method} {request.url.path}")

def log_warning(message):
    """Registra un mensaje de advertencia en el log"""
    logger.warning(message)

def log_debug(message):
    """Registra un mensaje de depuración en el log"""
    logger.debug(message)

def log_request(request, response_status=None):
    """
    Registra una solicitud HTTP en el log

    Args:
        request: Objeto de solicitud FastAPI
        response_status: Código de estado de la respuesta (opcional)
    """
    client_host = request.client.host if request.client else "unknown"
    method = request.method
    url = request.url.path
    query_params = str(request.query_params) if request.query_params else ""

    status_info = f" - Status: {response_status}" if response_status else ""

    log_message = f"Request: {client_host} - {method} {url}{query_params}{status_info}"
    log_info(log_message)

def log_frontend(action, details=None, user=None):
    """
    Registra una acción del frontend en el log

    Args:
        action: Acción realizada
        details: Detalles adicionales (opcional)
        user: Usuario que realizó la acción (opcional)
    """
    user_info = f" - User: {user}" if user else ""
    details_info = f" - Details: {details}" if details else ""

    log_message = f"Frontend: {action}{user_info}{details_info}"
    log_info(log_message)
