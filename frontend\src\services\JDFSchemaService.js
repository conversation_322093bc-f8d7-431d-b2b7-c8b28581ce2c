import { buildApiUrl } from '../config';
import { LogService } from './simplifiedServices';

class JDFSchemaService {
  /**
   * Genera un JSON JDF con esquema completo a partir de un presupuesto
   * @param {string} budgetId - ID del presupuesto
   * @returns {Promise<Object>} - Objeto JDF con esquema completo
   */
  static async generateJDFSchema(budgetId) {
    try {
      // Obtener el presupuesto
      const budgetResponse = await fetch(buildApiUrl(`/budgets/${budgetId}`));
      if (!budgetResponse.ok) {
        throw new Error('Error al obtener el presupuesto');
      }
      const budget = await budgetResponse.json();

      // Generar el JDF con esquema completo
      const jdf = this.createJDFSchema(budget);

      // Registrar la acción
      LogService.logUserAction('generate_jdf_schema', {
        budget_id: budgetId,
        success: true
      });

      return jdf;
    } catch (error) {
      console.error('Error al generar JDF con esquema completo:', error);

      LogService.logError('Error al generar JDF con esquema completo', {
        budget_id: budgetId,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Crea un objeto JDF con esquema completo a partir de un presupuesto
   * @param {Object} budget - Objeto presupuesto
   * @returns {Object} - Objeto JDF con esquema completo
   */
  static createJDFSchema(budget) {
    try {
      // Inicializar el JDF según el esquema requerido
      const jdf = {
        job_id: budget.ot_number?.replace("OT-", "") || "",
        descriptive_name: budget.description || "",
        author: "JDFast",
        agent_name: "JDFast",
        agent_version: "3.1",
        comment: `Generado a partir del presupuesto ${budget.budget_id}`,
        product_type: budget.job_type || "",
        binding_side: "Left",
        signatures: [],
        paper_configs: []
      };

      // Mapeo de estilos de trabajo
      const workStyleMap = {
        "tira_retira": "WorkAndBack",
        "tira y retira": "WorkAndTurn",
        "perfecting": "Perfecting",
        "flat": "Flat"
      };

      // Procesar las partes del presupuesto como signaturas
      if (budget.parts && budget.parts.length > 0) {
        budget.parts.forEach((part, idx) => {
          // Determinar el estilo de trabajo
          let workStyle = "Flat"; // Valor por defecto

          // Verificar si hay información de tira/retira en el cálculo de pliegos
          if (part.sheet_calculation && part.sheet_calculation.mejor_combinacion) {
            const mejorCombinacion = part.sheet_calculation.mejor_combinacion;
            if (mejorCombinacion.esquemas_utilizados && mejorCombinacion.esquemas_utilizados.length > 0) {
              // Verificar si alguno de los esquemas es tira/retira
              for (const esquema of mejorCombinacion.esquemas_utilizados) {
                if (esquema.es_tira_retira) {
                  workStyle = "tira_retira";
                  break;
                }
              }
            }
          }

          // Si hay configuración explícita de estilo de trabajo, usarla
          if (budget.product_config && budget.product_config.work_style) {
            workStyle = budget.product_config.work_style;
          } else if (part.work_style) {
            workStyle = part.work_style;
          } else if (part.color_config) {
            // Si hay configuración de colores, verificar si es tira/retira
            const colorConfig = part.color_config;
            if (colorConfig.frontColors > 0 && colorConfig.backColors > 0) {
              workStyle = "tira_retira";
            }
          }

          // Mapear el estilo de trabajo a los valores permitidos
          const mappedWorkStyle = workStyleMap[workStyle.toLowerCase()] || "Flat";

          // Crear la estructura de la signatura
          const signature = {
            signature_ID: `Str-${idx+1}`,
            job_part_id_name: part.name || `Parte ${idx+1}`,
            press_name: part.machine_data?.name || "Prensa B1",
            assembly_order: "Gathering",
            stripping_params: {
              signature_name: `Sig-${idx+1}`,
              sheets: []
            }
          };

          // Añadir hojas a la signatura
          const sheet = {
            sheet_name: `Sheet-${idx+1}-1`,
            bindery_signature_name: `BSN-${idx+1}-1`,
            paper_ref: `Pap-${part.paper_data?.product_id || '001'}`,
            fold_catalog: "F16-7", // Valor por defecto
            work_style: mappedWorkStyle
          };

          // Añadir parámetros de imposición si están disponibles
          if (part.page_size) {
            sheet.strip_cell_params = {
              trim_size_width: part.page_size.width || 0,
              trim_size_height: part.page_size.height || 0,
              bleed_face: 3, // Valores por defecto
              bleed_foot: 3,
              bleed_head: 3,
              bleed_spine: 3,
              orientation: "Rotate0",
              relative_box_x1: 0,
              relative_box_y1: 0,
              relative_box_x2: 1,
              relative_box_y2: 1
            };
          }

          signature.stripping_params.sheets.push(sheet);
          jdf.signatures.push(signature);

          // Añadir configuración de papel
          if (part.paper_data && !jdf.paper_configs.some(p => p.product_id === part.paper_data.product_id)) {
            jdf.paper_configs.push({
              weight: part.paper_data.weight || 0,
              dimension_width: part.paper_data.dimension_width || 0,
              dimension_height: part.paper_data.dimension_height || 0,
              media_type: "Paper",
              product_id: part.paper_data.product_id || "",
              thickness: part.paper_data.thickness || 0,
              descriptive_name: part.paper_data.descriptive_name || ""
            });
          }
        });
      } else {
        // Si no hay partes, crear una signatura por defecto
        const signature = {
          signature_ID: "Str-1",
          job_part_id_name: "Principal",
          press_name: "Prensa B1",
          assembly_order: "Gathering",
          stripping_params: {
            signature_name: "Sig-1",
            sheets: [{
              sheet_name: "Sheet-1-1",
              bindery_signature_name: "BSN-1-1",
              paper_ref: "Pap-001",
              fold_catalog: "F16-7",
              work_style: "Flat",
              strip_cell_params: {
                trim_size_width: budget.page_size?.width || 210,
                trim_size_height: budget.page_size?.height || 297,
                bleed_face: 3,
                bleed_foot: 3,
                bleed_head: 3,
                bleed_spine: 3,
                orientation: "Rotate0",
                relative_box_x1: 0,
                relative_box_y1: 0,
                relative_box_x2: 1,
                relative_box_y2: 1
              }
            }]
          }
        };
        jdf.signatures.push(signature);
      }

      // Añadir información del cliente
      if (budget.client_data) {
        jdf.customer_info = {
          customer_id: budget.client_data.client_id || "",
          company_name: budget.client_data.company?.name || "",
          country: budget.client_data.address?.country || "",
          region: budget.client_data.address?.region || "",
          city: budget.client_data.address?.city || "",
          street: budget.client_data.address?.street || "",
          postal_code: budget.client_data.address?.postal_code || "",
          first_name: budget.client_data.contact?.first_name || "",
          family_name: budget.client_data.contact?.last_name || "",
          phone: budget.client_data.contact?.phone || "",
          email: budget.client_data.contact?.email || ""
        };
      }

      // Añadir runlists si hay un PDF asociado
      if (budget.pdf_filename) {
        const pageCount = budget.page_count ||
                         (budget.parts && budget.parts.reduce((sum, part) => sum + (part.page_count || 0), 0)) ||
                         1;

        jdf.runlists = [{
          runlist_id: "RL-1d",
          pages: pageCount,
          page_range: `0 ~ ${pageCount-1}`,
          signature_ref: "Str-1", // Referencia a la primera signatura
          pdf_url: `http://localhost:3005/uploads/${budget.pdf_filename}`
        }];
      }

      return jdf;
    } catch (error) {
      console.error('Error al crear JDF con esquema completo:', error);
      throw error;
    }
  }

  /**
   * Descarga un JSON JDF con esquema completo
   * @param {Object} jdf - Objeto JDF con esquema completo
   * @param {string} filename - Nombre del archivo a descargar
   */
  static downloadJDFSchema(jdf, filename) {
    try {
      // Convertir a string con formato
      const dataStr = JSON.stringify(jdf, null, 2);

      // Crear un objeto Blob con el JSON
      const blob = new Blob([dataStr], { type: 'application/json' });

      // Crear URL para el blob
      const url = URL.createObjectURL(blob);

      // Crear un elemento <a> para descargar el archivo
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      // Añadir el enlace al documento y hacer clic en él
      document.body.appendChild(link);
      link.click();

      // Limpiar
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Registrar la acción
      LogService.logUserAction('download_jdf_schema', {
        filename,
        success: true
      });
    } catch (error) {
      console.error('Error al descargar JDF con esquema completo:', error);

      LogService.logError('Error al descargar JDF con esquema completo', {
        error: error.message
      });

      throw error;
    }
  }
}

export default JDFSchemaService;
