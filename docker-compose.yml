version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3005:3005"
    container_name: backend_imprenta
    environment:
      - AUTH_API_URL=https://auth.triky.app/token
      - AUTH_USERNAME=<EMAIL>
      - AUTH_PASSWORD=Masketu.123$
      #- JDF_API_URL=https://jdf-maker-back.triky.app/api/generate-and-send
      - JDF_API_URL=http://backend/api/generate-and-send
      - JDF_TEST_MODE=false
    networks:
      - app-network  # Conectar el backend a la red

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "4005:4005"
    container_name: frontend_imprenta
    depends_on:
      - backend
    networks:
      - app-network  # Conectar el frontend a la red

networks:
  app-network:
    driver: bridge  # Crea una red Docker compartida
