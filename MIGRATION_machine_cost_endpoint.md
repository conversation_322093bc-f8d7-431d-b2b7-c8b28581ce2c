# Migración: Eliminación del endpoint /calculations/machine-cost

## 📋 Resumen

Se ha eliminado completamente el endpoint `/calculations/machine-cost` del sistema debido a duplicación de funcionalidad y limitaciones técnicas. Este endpoint ha sido reemplazado por los endpoints V2 más avanzados.

## ❌ Endpoint Eliminado

### `/calculations/machine-cost` (ELIMINADO)
- **Ubicación**: `backend/routes/calculations.py`
- **Función**: `calculate_machine_cost()`
- **Modelo**: `MachineCostRequest`

**Limitaciones que motivaron la eliminación**:
- ❌ Cálculos simplificados y asunciones incorrectas
- ❌ No calculaba pliegos automáticamente
- ❌ Asumía todo a color por defecto
- ❌ No consideraba esquemas de plegado
- ❌ No incluía cálculo de papel integrado
- ❌ Manejo incorrecto de maculatura

## ✅ Endpoints de Reemplazo

### Para Máquinas Offset
**Usar**: `/v2/calculate-offset`
- **Ubicación**: `backend/routes/offset_calculator.py`
- **Función**: `calculate_offset_endpoint()`
- **Ventajas**:
  - ✅ Cálculo automático de pliegos desde páginas
  - ✅ Esquemas de plegado reales
  - ✅ Cálculo integrado de papel y maculatura
  - ✅ Manejo correcto de planchas y colores
  - ✅ Tiempos de impresión precisos

### Para Máquinas Digitales
**Usar**: `/v2/calculate-digital`
- **Ubicación**: `backend/routes/digital_calculator.py`
- **Función**: `calculate_digital_endpoint()`
- **Ventajas**:
  - ✅ Cálculo real de páginas a pliegos
  - ✅ Manejo correcto de duplex/simplex
  - ✅ Cálculo preciso de clicks por color/B&N
  - ✅ Integración de papel y tiempos

## 🔧 Cambios Realizados

### Backend
1. **Eliminado**: Función `calculate_machine_cost()` de `calculations.py`
2. **Eliminado**: Modelo `MachineCostRequest`
3. **Actualizado**: Endpoint `/calculations/part-cost` usa placeholder
4. **Limpiado**: Imports no utilizados (`math`, `Paper`, `Machine`, `Process`)

### Frontend
1. **BudgetForm.jsx**:
   - Eliminadas llamadas a `/calculations/machine-cost`
   - Implementado cálculo local de respaldo
   - Añadidas notas para migrar a endpoints V2

2. **sheetCalculationService.js**:
   - Eliminada llamada a `/calculations/machine-cost`
   - Implementado cálculo local de respaldo
   - Añadidas notas de migración

3. **calculationService.js**:
   - Función `calculateMachineCost()` marcada como DEPRECATED
   - Lanza error con mensaje de migración

4. **silentSheetCalculationService.js**:
   - No afectado (ya usa endpoints V2)

## 📊 Comparación de Funcionalidad

| Aspecto | `/calculations/machine-cost` (❌ Eliminado) | `/v2/calculate-offset` (✅) | `/v2/calculate-digital` (✅) |
|---------|---------------------------------------------|----------------------------|------------------------------|
| **Cálculo de pliegos** | Manual/externo | ✅ Automático | ✅ Automático |
| **Esquemas de plegado** | ❌ No soportado | ✅ Completo | ✅ Completo |
| **Cálculo de papel** | ❌ Separado | ✅ Integrado | ✅ Integrado |
| **Manejo de colores** | ❌ Asunciones | ✅ Preciso | ✅ Preciso |
| **Maculatura** | ❌ Básico | ✅ Avanzado | ✅ No aplica |
| **Tiempos** | ❌ Estimado | ✅ Preciso | ✅ Preciso |

## 🚀 Guía de Migración

### Para Desarrolladores

#### Antes (❌ Eliminado)
```javascript
// ESTO YA NO FUNCIONA
const response = await fetch('/calculations/machine-cost', {
  method: 'POST',
  body: JSON.stringify({
    machine_id: 'MACHINE_001',
    total_sheets: 1000,
    custom_print_time: 2.5,
    color_sides: 2000,
    bw_sides: 0
  })
});
```

#### Después (✅ Recomendado)
```javascript
// Para máquinas offset
const response = await fetch('/v2/calculate-offset', {
  method: 'POST',
  body: JSON.stringify({
    machine_id: 'OFFSET_001',
    copies: 500,
    paper_id: 'PAPER_001',
    num_paginas: 4,
    ancho_pagina: 210,
    alto_pagina: 297,
    colors_front: 4,
    colors_back: 0,
    custom_print_time: 2.5
  })
});

// Para máquinas digitales
const response = await fetch('/v2/calculate-digital', {
  method: 'POST',
  body: JSON.stringify({
    machine_id: 'DIGITAL_001',
    copies: 500,
    paper_id: 'PAPER_001',
    is_duplex: true,
    is_color: true,
    num_paginas: 4,
    custom_print_time: 2.5
  })
});
```

### Cálculo Local de Respaldo
Si necesitas un cálculo temporal mientras migras:

```javascript
// Cálculo local básico (solo para emergencias)
const calculateMachineCostLocal = (machine, totalSheets, customTime) => {
  const hourlyRate = machine.hourly_cost || 0;
  const cfaPercentage = machine.cfa_percentage || 0;
  const printTime = customTime || Math.max(1, totalSheets / 1000);
  
  const cfaCost = (hourlyRate * cfaPercentage / 100);
  const usageCost = hourlyRate * printTime;
  
  return cfaCost + usageCost;
};
```

## ⚠️ Notas Importantes

1. **Compatibilidad**: El endpoint eliminado NO es compatible hacia atrás
2. **Migración obligatoria**: Cualquier código que use `/calculations/machine-cost` debe migrar
3. **Mejores resultados**: Los endpoints V2 proporcionan cálculos más precisos
4. **Integración completa**: Los endpoints V2 incluyen papel, máquina y otros costos

## 🧪 Testing

Asegúrate de probar:
1. ✅ Cálculos de máquinas offset con `/v2/calculate-offset`
2. ✅ Cálculos de máquinas digitales con `/v2/calculate-digital`
3. ✅ Manejo de errores cuando se intenta usar el endpoint eliminado
4. ✅ Cálculos locales de respaldo funcionan correctamente

## 📈 Beneficios de la Migración

- **🎯 Precisión**: Cálculos más exactos y realistas
- **🔧 Simplicidad**: Un solo endpoint por tipo de máquina
- **⚡ Performance**: Menos llamadas de API necesarias
- **🛠️ Mantenimiento**: Código más limpio y organizado
- **📊 Funcionalidad**: Capacidades avanzadas integradas

## 🔗 Referencias

- **Endpoints V2**: `backend/routes/offset_calculator.py`, `backend/routes/digital_calculator.py`
- **Servicio silencioso**: `frontend/src/services/silentSheetCalculationService.js`
- **Documentación**: `frontend/src/services/README_silentSheetCalculation.md`
