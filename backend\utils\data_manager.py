import json
import os
from typing import Dict, List, Any, Optional
from utils.logger import log_info, log_error

# Directorio de datos
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")

# Mapeo de nombres de archivos
FILE_MAPPING = {
    "budgets": "budgets.json",
    "clients": "../config/client_catalog.json",  # Los clientes se cargan desde config/client_catalog.json
    "machines": "maquinas.json",
    "papers": "papeles.json",
    "production": "produccion.json",
    "products": "productos.json"
}

def read_data(data_type: str) -> List[Dict[str, Any]]:
    """
    Lee datos de un archivo JSON

    Args:
        data_type: Tipo de datos a leer (budgets, clients, machines, papers, production, products)

    Returns:
        List[Dict[str, Any]]: Datos leídos del archivo
    """
    try:
        # Obtener el nombre del archivo
        file_name = FILE_MAPPING.get(data_type)
        if not file_name:
            log_error(f"Tipo de datos no válido: {data_type}")
            return []

        # Caso especial para clientes que están en config/
        if data_type == "clients":
            # Usar la ruta relativa desde la raíz del proyecto
            file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "client_catalog.json")
            log_info(f"Cargando clientes desde: {file_path}")
        else:
            # Construir la ruta completa al archivo
            file_path = os.path.join(DATA_DIR, file_name)

        # Verificar si el archivo existe
        if not os.path.exists(file_path):
            log_info(f"Archivo no encontrado: {file_path}")
            return []

        # Leer el archivo
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)
            return data

    except Exception as e:
        log_error(f"Error al leer datos de {data_type}: {str(e)}")
        return []

def write_data(data_type: str, data: List[Dict[str, Any]]) -> bool:
    """
    Escribe datos en un archivo JSON

    Args:
        data_type: Tipo de datos a escribir (budgets, clients, machines, papers, production, products)
        data: Datos a escribir

    Returns:
        bool: True si la operación fue exitosa, False en caso contrario
    """
    try:
        # Obtener el nombre del archivo
        file_name = FILE_MAPPING.get(data_type)
        if not file_name:
            log_error(f"Tipo de datos no válido: {data_type}")
            return False

        # Caso especial para clientes que están en config/
        if data_type == "clients":
            # Usar la ruta relativa desde la raíz del proyecto
            file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "client_catalog.json")
            log_info(f"Guardando clientes en: {file_path}")
        else:
            # Construir la ruta completa al archivo
            file_path = os.path.join(DATA_DIR, file_name)

        # Crear el directorio si no existe
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # Escribir el archivo
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump(data, file, indent=4, ensure_ascii=False)
            return True

    except Exception as e:
        log_error(f"Error al escribir datos de {data_type}: {str(e)}")
        return False
