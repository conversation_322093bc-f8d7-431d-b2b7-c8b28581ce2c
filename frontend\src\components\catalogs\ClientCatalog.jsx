import { useState, useEffect, useCallback, useRef } from 'react';
import { API_URL, buildApiUrl } from '../../config';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Alert,
  Snackbar,
  Grid,
  Chip,
  Switch,
  FormControlLabel,
  InputAdornment
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';

// URL de la API importada desde config.js

const ClientCatalog = () => {
  // Mantenemos el estado clients porque lo necesitamos para actualizar filteredClients
  const [clients, setClients] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  const [showOnlyActive, setShowOnlyActive] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingClient, setEditingClient] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [formData, setFormData] = useState({
    client_id: '',
    billing_code: '',
    order_id: '',
    company: {
      name: '',
      address: {
        country: '',
        region: '',
        city: '',
        street: '',
        postal_code: ''
      }
    },
    contact: {
      position: '',
      first_name: '',
      last_name: '',
      phone: '',
      fax: '',
      email: ''
    },
    active: true,
    discount_percentage: 10.0,
    notes: ''
  });
  const fetchedRef = useRef(false);

  const fetchClients = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/clients/'));
      if (!response.ok) throw new Error('Error al obtener el catálogo de clientes');
      const data = await response.json();
      setClients(data);
      setFilteredClients(showOnlyActive ? data.filter(client => client.active) : data);
    } catch (err) {
      showSnackbar(err.message, 'error');
    }
  }, [showOnlyActive]);

  useEffect(() => {
    if (!fetchedRef.current) {
      fetchClients();
      fetchedRef.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOpenDialog = (client = null) => {
    if (client) {
      setEditingClient(client);
      setFormData(client);
    } else {
      setEditingClient(null);
      setFormData({
        client_id: '',
        billing_code: '',
        order_id: '',
        company: {
          name: '',
          address: {
            country: '',
            region: '',
            city: '',
            street: '',
            postal_code: ''
          }
        },
        contact: {
          position: '',
          first_name: '',
          last_name: '',
          phone: '',
          fax: '',
          email: ''
        },
        active: true,
        discount_percentage: 10.0,
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingClient(null);
  };

  const handleInputChange = (e, section = null, subsection = null) => {
    const { name, value } = e.target;
    setFormData(prev => {
      if (section) {
        if (subsection) {
          return {
            ...prev,
            [section]: {
              ...prev[section],
              [subsection]: {
                ...prev[section][subsection],
                [name]: value
              }
            }
          };
        }
        return {
          ...prev,
          [section]: {
            ...prev[section],
            [name]: value
          }
        };
      }
      return {
        ...prev,
        [name]: value
      };
    });
  };

  const handleToggleActive = () => {
    const newShowOnlyActive = !showOnlyActive;
    setShowOnlyActive(newShowOnlyActive);

    // Actualizar la lista filtrada cuando cambia el switch
    setFilteredClients(newShowOnlyActive ? clients.filter(client => client.active) : clients);
  };

  const handleSubmit = async () => {
    try {
      // Add timestamps for create/update
      const currentTime = new Date().toISOString();
      const dataToSubmit = {
        ...formData,
        created_at: editingClient ? editingClient.created_at : currentTime,
        updated_at: currentTime
      };

      const url = editingClient
        ? `${API_URL}/clients/${editingClient.client_id}`
        : `${API_URL}/clients`;

      const method = editingClient ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSubmit),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Error al guardar el cliente');
      }

      showSnackbar(
        editingClient
          ? 'Cliente actualizado correctamente'
          : 'Cliente creado correctamente',
        'success'
      );

      handleCloseDialog();
      fetchClients();
    } catch (err) {
      showSnackbar(err.message, 'error');
    }
  };

  const handleDelete = async (clientId) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar este cliente?')) {
      return;
    }

    try {
      const response = await fetch(`${API_URL}/clients/${clientId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Error al eliminar el cliente');
      }

      showSnackbar('Cliente eliminado correctamente', 'success');
      fetchClients();
    } catch (err) {
      showSnackbar(err.message, 'error');
    }
  };

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Catálogo de Clientes
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={showOnlyActive}
                onChange={handleToggleActive}
                color="primary"
              />
            }
            label="Mostrar solo activos"
          />
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Nuevo Cliente
          </Button>
        </Box>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: 'primary.main' }}>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>ID Cliente</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Código Facturación</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Empresa</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Contacto</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Email</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Teléfono</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Descuento</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Estado</TableCell>
              <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Acciones</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredClients.map((client) => (
              <TableRow key={client.client_id} sx={{
                '&:nth-of-type(odd)': { backgroundColor: 'action.hover' },
                whiteSpace: 'nowrap'
              }}>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{client.client_id}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{client.billing_code}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{client.company.name}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{`${client.contact.first_name} ${client.contact.last_name}`}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{client.contact.email}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{client.contact.phone}</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>{client.discount_percentage}%</TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  <Chip
                    label={client.active ? "Activo" : "Inactivo"}
                    color={client.active ? "success" : "error"}
                    size="small"
                    variant="filled"
                    sx={{ fontWeight: 'medium' }}
                  />
                </TableCell>
                <TableCell sx={{ padding: '8px', whiteSpace: 'nowrap' }}>
                  <IconButton
                    onClick={() => handleOpenDialog(client)}
                    color="primary"
                    size="small"
                    sx={{ mr: 1 }}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    onClick={() => handleDelete(client.client_id)}
                    color="error"
                    size="small"
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ backgroundColor: 'primary.main', color: 'white' }}>
          {editingClient ? 'Editar Cliente' : 'Nuevo Cliente'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              {/* Información básica */}
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="ID Cliente"
                  name="client_id"
                  value={formData.client_id}
                  onChange={handleInputChange}
                  disabled={!!editingClient}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Código Facturación"
                  name="billing_code"
                  value={formData.billing_code}
                  onChange={handleInputChange}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="ID Pedido"
                  name="order_id"
                  value={formData.order_id}
                  onChange={handleInputChange}
                  required
                />
              </Grid>

              {/* Información de la empresa */}
              <Grid item xs={12}>
                <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                  Información de la Empresa
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nombre de la Empresa"
                  name="name"
                  value={formData.company.name}
                  onChange={(e) => handleInputChange(e, 'company')}
                  required
                />
              </Grid>

              {/* Dirección */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="País"
                  name="country"
                  value={formData.company.address.country}
                  onChange={(e) => handleInputChange(e, 'company', 'address')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Región"
                  name="region"
                  value={formData.company.address.region}
                  onChange={(e) => handleInputChange(e, 'company', 'address')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Ciudad"
                  name="city"
                  value={formData.company.address.city}
                  onChange={(e) => handleInputChange(e, 'company', 'address')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Calle"
                  name="street"
                  value={formData.company.address.street}
                  onChange={(e) => handleInputChange(e, 'company', 'address')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Código Postal"
                  name="postal_code"
                  value={formData.company.address.postal_code}
                  onChange={(e) => handleInputChange(e, 'company', 'address')}
                  required
                />
              </Grid>

              {/* Información de contacto */}
              <Grid item xs={12}>
                <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                  Información de Contacto
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Cargo"
                  name="position"
                  value={formData.contact.position}
                  onChange={(e) => handleInputChange(e, 'contact')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Nombre"
                  name="first_name"
                  value={formData.contact.first_name}
                  onChange={(e) => handleInputChange(e, 'contact')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Apellidos"
                  name="last_name"
                  value={formData.contact.last_name}
                  onChange={(e) => handleInputChange(e, 'contact')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email"
                  name="email"
                  value={formData.contact.email}
                  onChange={(e) => handleInputChange(e, 'contact')}
                  required
                  type="email"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Teléfono"
                  name="phone"
                  value={formData.contact.phone}
                  onChange={(e) => handleInputChange(e, 'contact')}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Fax"
                  name="fax"
                  value={formData.contact.fax}
                  onChange={(e) => handleInputChange(e, 'contact')}
                />
              </Grid>

              {/* Estado activo y descuento */}
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.active}
                      onChange={(e) => setFormData({...formData, active: e.target.checked})}
                      color="primary"
                    />
                  }
                  label="Cliente Activo"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Porcentaje de Descuento"
                  type="number"
                  value={formData.discount_percentage}
                  onChange={(e) => setFormData({...formData, discount_percentage: parseFloat(e.target.value)})}
                  InputProps={{
                    endAdornment: <InputAdornment position="end">%</InputAdornment>,
                  }}
                  helperText="Descuento aplicado a este cliente"
                />
              </Grid>

              {/* Notas */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notas"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  multiline
                  rows={4}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCloseDialog} variant="outlined">Cancelar</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {editingClient ? 'Actualizar' : 'Crear'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} variant="filled">
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default ClientCatalog;
