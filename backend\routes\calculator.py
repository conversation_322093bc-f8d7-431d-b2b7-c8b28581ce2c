from fastapi import APIRouter, HTTPException
from models.models import PaginasRequest, PaginasOffsetRequest, PaginasDigitalRequest, CalculoPaginasResponse, CalculoPaginasOffsetResponse, CalculoPaginasDigitalResponse, PaperWeightRequest, PaperWeightResponse
from utils.paper_calculator import calcular_pliegos, calcular_pliegos_digital
from utils.paper_manager import get_paper_by_id
from utils.logger import log_info, log_error
from utils.paper_waste_calculator import calcular_desperdicio_papel
from folding_schemes import get_scheme

def get_serializable_page_layout(scheme_name):
    """
    Obtiene el page_layout de un esquema de plegado en un formato serializable para JSON.
    
    Args:
        scheme_name: Nombre del esquema de plegado
        
    Returns:
        Un diccionario con el page_layout o None si no se encuentra el esquema
    """
    try:
        scheme = get_scheme(scheme_name)
        if not scheme:
            return None
            
        # Convertir el page_layout a un formato serializable
        serializable_layout = {}
        
        # Procesar el lado "front"
        if "front" in scheme.page_layout:
            serializable_layout["front"] = []
            for row in scheme.page_layout["front"]:
                serializable_layout["front"].append(list(row))
                
        # Procesar el lado "back"
        if "back" in scheme.page_layout:
            serializable_layout["back"] = []
            for row in scheme.page_layout["back"]:
                serializable_layout["back"].append(list(row))
                
        return serializable_layout
    except Exception as e:
        log_error(f"Error al serializar page_layout para {scheme_name}: {str(e)}")
        return None

router = APIRouter(
    tags=["calculator"],
    responses={400: {"description": "Error en los parámetros de cálculo"}}
)

# Descripción del router (no soportada directamente en esta versión de FastAPI)
# "Endpoints para realizar cálculos relacionados con la impresión, como cálculo de pliegos y peso del papel"

@router.post("/calcular-pliegos", response_model=CalculoPaginasResponse, summary="Calcula la cantidad de pliegos necesarios para un trabajo de impresión")
def calcular_pliegos_endpoint(request: PaginasRequest):
    """
    Calcula la cantidad óptima de pliegos y planchas necesarios para imprimir un trabajo
    con las características especificadas. Tiene en cuenta el tamaño de página, tamaño de pliego,
    número de páginas, colores y tipo de máquina (offset o digital).
    Para máquinas digitales, calcula clicks en lugar de planchas.

    - **num_paginas**: Número total de páginas del trabajo (debe ser par)
    - **ancho_pagina**: Ancho de la página en milímetros
    - **alto_pagina**: Alto de la página en milímetros
    - **ancho_pliego**: Ancho del pliego en milímetros
    - **alto_pliego**: Alto del pliego en milímetros
    - **front_colors**: Número de colores en el anverso (1-4)
    - **back_colors**: Número de colores en el reverso (1-4)
    - **machine_type**: Tipo de máquina (Offset o Digital)
    - **machine_id**: ID de la máquina del catálogo (opcional)
    - **copies**: Número de copias a imprimir
    """
    try:
        # Determinar si la máquina es digital
        is_digital = request.machine_type == "Digital"

        # Variables para los parámetros de la máquina
        print_speed = None
        setup_time = None
        sheets_per_hour = None

        # Si se proporciona machine_id, verificar el tipo de máquina desde el catálogo
        if request.machine_id:
            from utils.catalog_manager import load_machines
            machines = load_machines()
            machine = next((m for m in machines if m.get("machine_id") == request.machine_id or m.get("product_id") == request.machine_id), None)
            if machine:
                # Sobrescribir is_digital basado en el tipo de máquina del catálogo
                is_digital = machine.get("type") == "Digital"

                # Si es una máquina digital, obtener la velocidad de impresión
                if is_digital:
                    print_speed = machine.get("speed")
                    print(f"Velocidad de impresión de la máquina {machine.get('name')}: {print_speed} A4/minuto")
                else:
                    # Si es una máquina offset, obtener el tiempo de arranque y la velocidad en pliegos/hora
                    setup_time = machine.get("setup_time", 30)  # Valor por defecto: 30 minutos
                    sheets_per_hour = machine.get("sheets_per_hour")
                    print(f"Máquina offset {machine.get('name')}: Setup time: {setup_time} min, Velocidad: {sheets_per_hour} pliegos/hora")

        # Calcular los pliegos necesarios
        result = calcular_pliegos(
            request.num_paginas,
            request.ancho_pagina,
            request.alto_pagina,
            request.ancho_pliego,
            request.alto_pliego,
            request.front_colors,
            request.back_colors,
            is_digital,
            request.copies,
            print_speed,
            setup_time,
            sheets_per_hour
        )
        
        # Obtener la disposición de páginas del primer esquema (asumimos que es el principal)
        if result and result.mejor_combinacion and result.mejor_combinacion.esquemas_utilizados:
            # Añadir page_layout a cada esquema en la respuesta
            for esquema in result.mejor_combinacion.esquemas_utilizados:
                if not esquema.page_layout and not esquema.nombre.startswith("Digital-") and not esquema.nombre.startswith("Custom-"):
                    esquema.page_layout = get_serializable_page_layout(esquema.nombre)
            
            primer_esquema = result.mejor_combinacion.esquemas_utilizados[0]
            paginas_ancho = primer_esquema.disposicion.paginas_ancho
            paginas_alto = primer_esquema.disposicion.paginas_alto
            es_tira_retira = primer_esquema.es_tira_retira
            sheet_type = primer_esquema.sheet_type
            
            # Ajustar las dimensiones para esquemas tira-retira
            # En tira-retira (WorkAndTurn), cada cara tiene la mitad de las páginas
            paginas_ancho_ajustado = paginas_ancho
            paginas_alto_ajustado = paginas_alto
            
            if es_tira_retira or sheet_type == "WorkAndTurn":
                # Para esquemas tira-retira, dividimos las páginas entre cara y dorso
                if paginas_ancho > paginas_alto:
                    paginas_ancho_ajustado = paginas_ancho // 2
                else:
                    paginas_alto_ajustado = paginas_alto // 2
                    
                log_info(f"Esquema tira-retira detectado: {primer_esquema.nombre}. Ajustando disposición de {paginas_ancho}x{paginas_alto} a {paginas_ancho_ajustado}x{paginas_alto_ajustado} por cara")
            
            # Calcular el desperdicio de papel
            # Extraer los parámetros de desperdicio si se proporcionan
            waste_params = {}
            if request.waste_params:
                waste_params = {
                    'sangrado': request.waste_params.get('sangrado', 3.0),
                    'pinzas': request.waste_params.get('pinzas', 10.0),
                    'margen_lateral': request.waste_params.get('margen_lateral', 5.0),
                    'margen_superior': request.waste_params.get('margen_superior', 5.0),
                    'margen_inferior': request.waste_params.get('margen_inferior', 5.0),
                    'marcas_registro': request.waste_params.get('marcas_registro', True),
                    'tiras_control': request.waste_params.get('tiras_control', True)
                }
            
            desperdicio = calcular_desperdicio_papel(
                request.ancho_pliego,
                request.alto_pliego,
                request.ancho_pagina,
                request.alto_pagina,
                paginas_ancho_ajustado,
                paginas_alto_ajustado,
                **waste_params
            )
            
            # Añadir la información de desperdicio al resultado
            result.mejor_combinacion.area_pliego_mm2 = desperdicio["area_pliego_mm2"]
            result.mejor_combinacion.area_utilizada_mm2 = desperdicio["area_utilizada_mm2"]
            result.mejor_combinacion.area_desperdiciada_mm2 = desperdicio["area_desperdiciada_mm2"]
            result.mejor_combinacion.porcentaje_desperdicio = desperdicio["porcentaje_desperdicio"]
            result.mejor_combinacion.porcentaje_aprovechamiento = desperdicio["porcentaje_aprovechamiento"]
            result.mejor_combinacion.recomendaciones_desperdicio = desperdicio["recomendaciones"]
        
        return result
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/calcular-peso-papel", response_model=PaperWeightResponse, summary="Calcula el peso total del papel para un trabajo")
def calcular_peso_papel(request: PaperWeightRequest):
    """
    Calcula el peso total del papel basado en el número de pliegos, dimensiones y gramaje.
    Es útil para estimar costos de envío y manipulación. Puede recibir un ID de papel del catálogo
    o dimensiones y gramaje específicos.

    - **paper_id**: ID del papel en el catálogo (opcional si se proporcionan dimensiones y gramaje)
    - **sheets**: Número de pliegos
    - **width_mm**: Ancho del pliego en milímetros (requerido si no se proporciona paper_id)
    - **height_mm**: Alto del pliego en milímetros (requerido si no se proporciona paper_id)
    - **weight_gsm**: Gramaje del papel en g/m² (requerido si no se proporciona paper_id)
    """
    try:
        # Validar los parámetros de entrada
        if request.sheets <= 0:
            raise HTTPException(status_code=400, detail="El número de pliegos debe ser mayor que 0")

        # Si se proporciona el ID del papel, obtener sus datos
        if request.paper_id:
            log_info(f"Buscando papel con ID: {request.paper_id}")
            paper = get_paper_by_id(request.paper_id)

            if not paper:
                log_error(f"Papel con ID {request.paper_id} no encontrado")
                raise HTTPException(status_code=404, detail=f"Papel con ID {request.paper_id} no encontrado")

            log_info(f"Papel encontrado: {paper}")

            # Obtener dimensiones y gramaje del papel
            width_mm = paper.get("dimension_width", 0)
            height_mm = paper.get("dimension_height", 0)
            weight_gsm = paper.get("weight", 0)
            paper_name = paper.get("descriptive_name", "")

            log_info(f"Dimensiones: {width_mm}x{height_mm}mm, Gramaje: {weight_gsm}g/m²")

        # Si no se proporciona el ID del papel, usar los valores proporcionados
        else:
            if not request.width_mm or not request.height_mm or not request.weight_gsm:
                raise HTTPException(
                    status_code=400,
                    detail="Si no se proporciona el ID del papel, se deben proporcionar las dimensiones y el gramaje"
                )
            width_mm = request.width_mm
            height_mm = request.height_mm
            weight_gsm = request.weight_gsm
            paper_name = None

        # Calcular el área del pliego en m²
        sheet_area_m2 = (width_mm / 1000) * (height_mm / 1000)

        # Calcular el peso de un pliego en gramos
        weight_per_sheet_g = sheet_area_m2 * weight_gsm

        # Calcular el peso total en kg
        total_weight_kg = (weight_per_sheet_g * request.sheets) / 1000

        # Preparar la respuesta
        return PaperWeightResponse(
            paper_id=request.paper_id,
            paper_name=paper_name,
            sheets=request.sheets,
            width_mm=width_mm,
            height_mm=height_mm,
            weight_gsm=weight_gsm,
            weight_per_sheet_g=round(weight_per_sheet_g, 2),
            total_weight_kg=round(total_weight_kg, 2)
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# Endpoint /calcular-pliegos-offset eliminado

@router.post("/calcular-pliegos-digital", response_model=CalculoPaginasDigitalResponse, summary="Calcula la cantidad de pliegos necesarios para un trabajo de impresión digital")
def calcular_pliegos_digital_endpoint(request: PaginasDigitalRequest):
    """
    Calcula la cantidad óptima de pliegos y clicks necesarios para imprimir un trabajo digital.
    Esta versión está diseñada específicamente para máquinas digitales y no incluye
    cálculos de planchas ya que las máquinas digitales no las utilizan.

    - **num_paginas**: Número total de páginas a imprimir
    - **ancho_pagina**: Ancho de la página en mm
    - **alto_pagina**: Alto de la página en mm
    - **ancho_pliego**: Ancho del pliego en mm
    - **alto_pliego**: Alto del pliego en mm
    - **front_colors**: Número de colores en el anverso (4 para CMYK, 1 para B/N)
    - **back_colors**: Número de colores en el reverso (0 para simplex, >0 para duplex)
    - **copies**: Número de ejemplares a imprimir
    - **binding_type**: Tipo de encuadernado (por defecto "gathering")
        - "gathering": Alzado (esquemas tradicionales como F8-7, F16-7, etc.)
        - "collection": Grapado (esquemas tradicionales como F8-7, F16-7, etc.)
        - "none": Sin encuadernado (llena los pliegos con el máximo número de páginas posible)
    """
    try:
        log_info(f"Calculando pliegos digital para: {request}")

        # Calcular los pliegos necesarios para digital
        result = calcular_pliegos_digital(
            request.num_paginas,
            request.ancho_pagina,
            request.alto_pagina,
            request.ancho_pliego,
            request.alto_pliego,
            request.front_colors,
            request.back_colors,
            request.copies,
            request.binding_type
        )

        return result
    except Exception as e:
        log_error(f"Error al calcular pliegos digital: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))