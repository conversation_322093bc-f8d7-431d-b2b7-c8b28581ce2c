/**
 * Servicio para generar resúmenes de trabajos
 */

/**
 * Normaliza la estructura de datos del cálculo de pliegos
 * @param {Object} data - Datos recibidos de la API
 * @returns {Object} - Datos normalizados
 */
const normalizeSheetCalculationData = (data) => {
  // Si no hay datos, devolver un objeto vacío
  if (!data) return {};

  // Crear una copia para no modificar el original
  const normalizedData = { ...data };

  // Si los datos ya tienen la estructura correcta, devolverlos tal cual
  if (normalizedData.mejor_combinacion && normalizedData.mejor_combinacion.esquemas_utilizados) {
    return normalizedData;
  }

  // Si los esquemas están en la raíz, moverlos a mejor_combinacion
  if (normalizedData.esquemas_utilizados) {
    normalizedData.mejor_combinacion = {
      esquemas_utilizados: normalizedData.esquemas_utilizados,
      total_pliegos: normalizedData.total_pliegos || 0,
      total_planchas: normalizedData.total_planchas || 0
    };
  }

  return normalizedData;
};

/**
 * Genera un resumen del trabajo para una parte específica
 * @param {Object} part - Parte del presupuesto
 * @param {Object} sheetCalculation - Datos del cálculo de pliegos
 * @param {Object} budget - Datos del presupuesto completo
 * @param {number} copies - Número de copias a imprimir
 * @returns {string} - Resumen del trabajo
 */
export const generatePartJobSummary = (part, sheetCalculation, budget, copies) => {
  if (!part || !sheetCalculation) return '';

  const normalizedData = normalizeSheetCalculationData(sheetCalculation);

  // Extraer información relevante
  const jobType = budget?.jobType || 'Libro';
  const partName = part.name || 'Parte sin nombre';
  const pageCount = part.pageCount || 0;
  const pageSize = part.pageSize || 'Desconocido';
  const customPageSize = part.customPageSize || '';
  const paperName = part.paper?.descriptive_name || part.paper?.name || 'Papel no especificado';
  const paperWeight = part.paper?.weight ? `${part.paper.weight}g` : '';
  const paperFullName = paperWeight ? `${paperName} ${paperWeight}` : paperName;
  const machineName = part.machine?.name || 'Máquina no especificada';
  const copiesCount = copies || budget?.copies || 1000;

  // Información de esquemas
  const totalPliegos = normalizedData.mejor_combinacion?.total_pliegos || 0;
  const totalPlanchas = normalizedData.mejor_combinacion?.total_planchas || 0;

  // Construir el resumen con el nuevo formato solicitado
  let summary = `${jobType} (${copiesCount})\n`; // Primera línea: Tipo de trabajo y cantidad
  summary += `${partName}\n`; // Segunda línea: Nombre de la parte
  summary += `${pageCount} páginas de tamaño ${pageSize}${pageSize === 'Personalizado' ? ` (${customPageSize})` : ''}\n`; // Tercera línea: Número de páginas y tamaño
  summary += `Papel: ${paperFullName}\n`; // Cuarta línea: Papel con gramaje
  summary += `Prensa: ${machineName}`; // Quinta línea: Máquina/prensa

  return summary;
};

export default {
  generatePartJobSummary
};
