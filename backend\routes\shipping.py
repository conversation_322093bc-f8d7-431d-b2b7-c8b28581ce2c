import os
from typing import Dict, List, Optional
from fastapi import APIRouter, Body, HTTPException, Query, status
from models.shipping import ShippingRecord, ShippingRecordCreate, ShippingRecordUpdate
from utils.logger import log_info, log_error
from utils.shipping_manager import (
    get_all_shipping_records,
    get_shipping_record_by_id,
    get_shipping_records_by_ot_number,
    add_shipping_record,
    update_shipping_record,
    delete_shipping_record
)
from utils.delivery_note_generator import generate_delivery_note
from utils.budget_manager import update_budget_status

router = APIRouter(
    prefix="/shipping",
    tags=["shipping"],
    responses={404: {"description": "Registro de envío no encontrado"}}
)

@router.get("/", response_model=List[ShippingRecord])
def get_shipping_records(
    ot_number: Optional[str] = Query(None, description="Filtrar por número de OT")
):
    """
    Obtiene todos los registros de envío, opcionalmente filtrados por número de OT
    """
    if ot_number:
        return get_shipping_records_by_ot_number(ot_number)
    return get_all_shipping_records()

@router.get("/{shipping_id}", response_model=ShippingRecord)
def get_shipping(shipping_id: str):
    """
    Obtiene un registro de envío por su ID
    """
    shipping = get_shipping_record_by_id(shipping_id)
    if not shipping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Registro de envío con ID {shipping_id} no encontrado"
        )
    return shipping

@router.post("/", response_model=Dict)
async def create_shipping_record(shipping_data: ShippingRecordCreate):
    """
    Crea un nuevo registro de envío y genera un albarán de entrega
    """
    try:
        log_info(f"Creando registro de envío para OT: {shipping_data.ot_number}")

        # Crear el registro de envío
        new_shipping = add_shipping_record(shipping_data.dict())

        # Actualizar el estado del presupuesto a "Enviado"
        update_result = update_budget_status(shipping_data.ot_number, "Enviado")

        # Generar el albarán de entrega
        delivery_note_path = generate_delivery_note(new_shipping)

        if delivery_note_path:
            # Extraer solo el nombre del archivo de la ruta completa
            delivery_note_filename = os.path.basename(delivery_note_path)

            return {
                "success": True,
                "message": "Registro de envío creado y albarán generado correctamente",
                "shipping_id": new_shipping["shipping_id"],
                "delivery_note_path": delivery_note_path,
                "delivery_note_filename": delivery_note_filename,
                "budget_updated": update_result.get("success", False)
            }
        else:
            return {
                "success": True,
                "message": "Registro de envío creado, pero no se pudo generar el albarán",
                "shipping_id": new_shipping["shipping_id"],
                "budget_updated": update_result.get("success", False)
            }

    except Exception as e:
        log_error(f"Error al crear registro de envío: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al crear registro de envío: {str(e)}"
        )

@router.patch("/{shipping_id}", response_model=ShippingRecord)
def update_shipping(shipping_id: str, update_data: ShippingRecordUpdate):
    """
    Actualiza un registro de envío
    """
    # Obtener el registro actual antes de actualizarlo
    current_shipping = get_shipping_record_by_id(shipping_id)
    if not current_shipping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Registro de envío con ID {shipping_id} no encontrado"
        )

    # Convertir el modelo Pydantic a diccionario, excluyendo valores nulos
    update_dict = update_data.dict(exclude_unset=True)

    # Actualizar el registro
    updated_shipping = update_shipping_record(shipping_id, update_dict)

    if not updated_shipping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Registro de envío con ID {shipping_id} no encontrado"
        )

    # Si el estado cambió a "Entregado", actualizar el estado del presupuesto a "Completado"
    if "delivery_status" in update_dict and update_dict["delivery_status"] == "Entregado" and current_shipping["delivery_status"] != "Entregado":
        log_info(f"Envío {shipping_id} marcado como Entregado. Actualizando presupuesto {updated_shipping['ot_number']} a Completado")
        update_result = update_budget_status(updated_shipping["ot_number"], "Completado")
        if not update_result.get("success", False):
            log_error(f"Error al actualizar el estado del presupuesto {updated_shipping['ot_number']}: {update_result.get('message', '')}")

    return updated_shipping

@router.delete("/{shipping_id}", response_model=Dict)
def delete_shipping(shipping_id: str):
    """
    Elimina un registro de envío
    """
    # Verificar que el registro existe
    shipping = get_shipping_record_by_id(shipping_id)
    if not shipping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Registro de envío con ID {shipping_id} no encontrado"
        )

    # Eliminar el registro
    log_info(f"Eliminando registro de envío con ID {shipping_id}")
    result = delete_shipping_record(shipping_id)

    if not result:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al eliminar el registro de envío con ID {shipping_id}"
        )

    return {
        "success": True,
        "message": f"Registro de envío con ID {shipping_id} eliminado correctamente"
    }
