import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { buildApiUrl } from '../config';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Chip,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import DeleteIcon from '@mui/icons-material/Delete';
import CloseIcon from '@mui/icons-material/Close';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

const ActivityLogsPage = () => {
  const { token, role } = useAuth();
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({
    user_id: '',
    activity_type: '',
    entity_id: '',
    start_date: null,
    end_date: null
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [confirmClearDialog, setConfirmClearDialog] = useState(false);

  // Cargar logs al montar el componente
  useEffect(() => {
    fetchLogs();
  }, [token, page, rowsPerPage]);

  // Función para obtener los logs
  const fetchLogs = async () => {
    if (role !== 'admin') {
      setError('Solo los administradores pueden ver los registros de actividad');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Construir la URL con los parámetros de filtro
      let url = buildApiUrl(`/activity-logs?limit=${rowsPerPage}&skip=${page * rowsPerPage}`);

      if (filters.user_id) url += `&user_id=${filters.user_id}`;
      if (filters.activity_type) url += `&activity_type=${filters.activity_type}`;
      if (filters.entity_id) url += `&entity_id=${filters.entity_id}`;
      if (filters.start_date) url += `&start_date=${filters.start_date.toISOString()}`;
      if (filters.end_date) url += `&end_date=${filters.end_date.toISOString()}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al obtener los registros de actividad');
      }

      const data = await response.json();
      setLogs(data);
      setTotalCount(data.length); // En una API real, esto vendría del encabezado X-Total-Count
    } catch (err) {
      setError('Error al cargar los registros de actividad: ' + err.message);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Función para aplicar filtros
  const applyFilters = () => {
    setPage(0); // Volver a la primera página al aplicar filtros
    fetchLogs();
  };

  // Función para resetear filtros
  const resetFilters = () => {
    setFilters({
      user_id: '',
      activity_type: '',
      entity_id: '',
      start_date: null,
      end_date: null
    });
    setPage(0);
    fetchLogs();
  };

  // Función para manejar cambios en los filtros
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value
    });
  };

  // Función para manejar cambios en las fechas
  const handleDateChange = (name, date) => {
    setFilters({
      ...filters,
      [name]: date
    });
  };

  // Función para manejar cambios de página
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Función para manejar cambios en filas por página
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Función para mostrar detalles de un log
  const handleShowDetails = (log) => {
    setSelectedLog(log);
    setOpenDetailDialog(true);
  };

  // Función para cerrar el diálogo de detalles
  const handleCloseDetailDialog = () => {
    setOpenDetailDialog(false);
    setSelectedLog(null);
  };

  // Función para confirmar limpieza de logs
  const handleConfirmClear = () => {
    setConfirmClearDialog(true);
  };

  // Función para cerrar el diálogo de confirmación
  const handleCloseConfirmDialog = () => {
    setConfirmClearDialog(false);
  };

  // Función para limpiar todos los logs
  const handleClearLogs = async () => {
    try {
      const response = await fetch(buildApiUrl('/activity-logs/clear'), {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Error al limpiar los registros de actividad');
      }

      const data = await response.json();
      setLogs([]);
      setTotalCount(0);
      setConfirmClearDialog(false);
      alert(data.message);
    } catch (err) {
      setError('Error al limpiar los registros: ' + err.message);
      console.error(err);
    }
  };

  // Función para obtener el color del chip según el tipo de actividad
  const getActivityTypeColor = (type) => {
    switch (type) {
      case 'login':
        return 'info';
      case 'budget_create':
        return 'success';
      case 'budget_update':
        return 'primary';
      case 'budget_approve':
        return 'secondary';
      case 'user_create':
      case 'user_update':
      case 'user_delete':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Función para formatear el tipo de actividad
  const formatActivityType = (type) => {
    switch (type) {
      case 'login':
        return 'Inicio de sesión';
      case 'budget_create':
        return 'Creación de presupuesto';
      case 'budget_update':
        return 'Modificación de presupuesto';
      case 'budget_approve':
        return 'Aprobación de presupuesto';
      case 'user_create':
        return 'Creación de usuario';
      case 'user_update':
        return 'Modificación de usuario';
      case 'user_delete':
        return 'Eliminación de usuario';
      default:
        return type;
    }
  };

  // Función para formatear la fecha
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm:ss', { locale: es });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Registros de Actividad</Typography>
        <Box>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ mr: 1 }}
          >
            {showFilters ? 'Ocultar filtros' : 'Mostrar filtros'}
          </Button>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<RefreshIcon />}
            onClick={fetchLogs}
            sx={{ mr: 1 }}
          >
            Actualizar
          </Button>
          {role === 'admin' && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleConfirmClear}
            >
              Limpiar registros
            </Button>
          )}
          <Button
            variant="outlined"
            color="secondary"
            onClick={() => window.history.back()}
            sx={{ ml: 1 }}
          >
            Cerrar
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {showFilters && (
        <Paper elevation={3} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Filtros
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <TextField
              label="ID de usuario"
              name="user_id"
              value={filters.user_id}
              onChange={handleFilterChange}
              variant="outlined"
              size="small"
              sx={{ minWidth: 200 }}
            />
            <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
              <InputLabel>Tipo de actividad</InputLabel>
              <Select
                name="activity_type"
                value={filters.activity_type}
                onChange={handleFilterChange}
                label="Tipo de actividad"
              >
                <MenuItem value="">Todos</MenuItem>
                <MenuItem value="login">Inicio de sesión</MenuItem>
                <MenuItem value="budget_create">Creación de presupuesto</MenuItem>
                <MenuItem value="budget_update">Modificación de presupuesto</MenuItem>
                <MenuItem value="budget_approve">Aprobación de presupuesto</MenuItem>
                <MenuItem value="user_create">Creación de usuario</MenuItem>
                <MenuItem value="user_update">Modificación de usuario</MenuItem>
                <MenuItem value="user_delete">Eliminación de usuario</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="ID de entidad"
              name="entity_id"
              value={filters.entity_id}
              onChange={handleFilterChange}
              variant="outlined"
              size="small"
              sx={{ minWidth: 200 }}
            />
            <DatePicker
              label="Fecha desde"
              value={filters.start_date}
              onChange={(date) => handleDateChange('start_date', date)}
              slotProps={{ textField: { size: 'small', sx: { minWidth: 200 } } }}
            />
            <DatePicker
              label="Fecha hasta"
              value={filters.end_date}
              onChange={(date) => handleDateChange('end_date', date)}
              slotProps={{ textField: { size: 'small', sx: { minWidth: 200 } } }}
            />
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={applyFilters}
              >
                Aplicar filtros
              </Button>
              <Button
                variant="outlined"
                color="secondary"
                onClick={resetFilters}
              >
                Resetear
              </Button>
            </Box>
          </Box>
        </Paper>
      )}

      <Paper elevation={3}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Fecha y hora</TableCell>
                <TableCell>Usuario</TableCell>
                <TableCell>Tipo de actividad</TableCell>
                <TableCell>Descripción</TableCell>
                <TableCell>Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : logs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    No hay registros de actividad
                  </TableCell>
                </TableRow>
              ) : (
                logs.map((log) => (
                  <TableRow key={log.log_id}>
                    <TableCell>{formatDate(log.timestamp)}</TableCell>
                    <TableCell>{log.username}</TableCell>
                    <TableCell>
                      <Chip
                        label={formatActivityType(log.activity_type)}
                        color={getActivityTypeColor(log.activity_type)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{log.description}</TableCell>
                    <TableCell>
                      <Tooltip title="Ver detalles">
                        <IconButton
                          color="primary"
                          onClick={() => handleShowDetails(log)}
                        >
                          <InfoIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Filas por página:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} de ${count}`}
        />
      </Paper>

      {/* Diálogo de detalles */}
      <Dialog open={openDetailDialog} onClose={handleCloseDetailDialog} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>Detalles del registro de actividad</span>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseDetailDialog}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {selectedLog && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">ID del registro</Typography>
                <Typography variant="body1">{selectedLog.log_id}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">Fecha y hora</Typography>
                <Typography variant="body1">{formatDate(selectedLog.timestamp)}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">Usuario</Typography>
                <Typography variant="body1">{selectedLog.username} (ID: {selectedLog.user_id})</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">Tipo de actividad</Typography>
                <Chip
                  label={formatActivityType(selectedLog.activity_type)}
                  color={getActivityTypeColor(selectedLog.activity_type)}
                />
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">Descripción</Typography>
                <Typography variant="body1">{selectedLog.description}</Typography>
              </Box>
              {selectedLog.entity_id && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary">ID de entidad</Typography>
                  <Typography variant="body1">{selectedLog.entity_id}</Typography>
                </Box>
              )}
              {selectedLog.ip_address && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary">Dirección IP</Typography>
                  <Typography variant="body1">{selectedLog.ip_address}</Typography>
                </Box>
              )}
              {selectedLog.additional_data && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary">Datos adicionales</Typography>
                  <Paper variant="outlined" sx={{ p: 2, bgcolor: '#f5f5f5' }}>
                    <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                      {JSON.stringify(selectedLog.additional_data, null, 2)}
                    </pre>
                  </Paper>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetailDialog} color="primary">
            Cerrar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Diálogo de confirmación para limpiar logs */}
      <Dialog open={confirmClearDialog} onClose={handleCloseConfirmDialog}>
        <DialogTitle>Confirmar eliminación</DialogTitle>
        <DialogContent>
          <Typography>
            ¿Estás seguro de que deseas eliminar todos los registros de actividad?
            Esta acción no se puede deshacer.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirmDialog}>Cancelar</Button>
          <Button
            variant="contained"
            color="error"
            onClick={handleClearLogs}
          >
            Eliminar todos
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ActivityLogsPage;
