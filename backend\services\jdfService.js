const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Función para generar un JDF a partir de un presupuesto
const generateJDF = (budget) => {
  try {
    // Obtener datos del presupuesto
    const {
      budget_id,
      ot_number,
      client,
      job_type,
      quantity,
      description,
      status,
      machine_id,
      custom_print_time,
      sheet_calculation,
      product_id
    } = budget;

    // Determinar el estilo de trabajo basado en el producto
    let workStyle = "Flat"; // Valor por defecto

    // Si hay un producto_id, intentar obtener el estilo de trabajo del producto
    if (product_id) {
      const productsFilePath = path.join(__dirname, '../data/products.json');
      if (fs.existsSync(productsFilePath)) {
        const productsData = fs.readFileSync(productsFilePath, 'utf8');
        const products = JSON.parse(productsData);
        const product = products.find(p => p.id === product_id);

        if (product && product.work_style) {
          workStyle = product.work_style;
        }
      }
    } else {
      // Si no hay product_id, intentar determinar el estilo de trabajo basado en el tipo de trabajo
      switch (job_type) {
        case "Revista":
        case "Libro":
        case "Catálogo":
          workStyle = "WorkAndBack";
          break;
        case "Folleto":
          workStyle = "WorkAndTurn";
          break;
        case "Flyer":
        case "Cartel":
          workStyle = "Flat";
          break;
        case "Tarjeta":
          workStyle = "Perfecting";
          break;
        default:
          workStyle = "Flat";
      }
    }

    // Obtener datos del cliente
    let clientName = "Cliente no especificado";
    let clientContact = "";
    let clientEmail = "";
    let clientId = "UNKNOWN";
    let clientCompany = "";

    // Intentar obtener datos del cliente de diferentes fuentes
    if (client) {
      // Si client es un objeto
      if (typeof client === 'object') {
        // Obtener nombre del cliente
        if (client.name) {
          clientName = client.name;
        } else if (client.company && client.company.name) {
          clientName = client.company.name;
        }

        // Obtener contacto del cliente
        clientContact = client.contact || client.contact_name || "";

        // Obtener email del cliente
        clientEmail = client.email || "";

        // Obtener ID del cliente
        clientId = client.id || client.client_id || "UNKNOWN";

        // Obtener empresa del cliente
        clientCompany = client.company?.name || client.company || "";
      }
      // Si client es un string
      else if (typeof client === 'string') {
        clientName = client;
      }
    }

    // Si hay client_data en el presupuesto, usarlo como fuente adicional
    if (budget.client_data) {
      const clientData = budget.client_data;

      // Priorizar datos de client_data si están disponibles
      if (clientData.company && clientData.company.name) {
        clientCompany = clientData.company.name;
        clientName = clientName === "Cliente no especificado" ? clientCompany : clientName;
      }

      if (clientData.contact_name) {
        clientContact = clientData.contact_name;
      }

      if (clientData.email) {
        clientEmail = clientData.email;
      }

      if (clientData.id || clientData.client_id) {
        clientId = clientData.id || clientData.client_id;
      }
    }

    // Obtener datos de la máquina
    let machineName = "Máquina no especificada";
    if (machine_id) {
      const machinesFilePath = path.join(__dirname, '../data/machines.json');
      if (fs.existsSync(machinesFilePath)) {
        const machinesData = fs.readFileSync(machinesFilePath, 'utf8');
        const machines = JSON.parse(machinesData);
        const machine = machines.find(m => m.machine_id === machine_id);

        if (machine) {
          machineName = machine.name;
        }
      }
    }

    // Obtener datos del cálculo de hojas
    let paperSize = "A4";
    let paperWeight = 90;
    let printTime = custom_print_time || 1;
    let colors = "4/4";

    if (sheet_calculation) {
      paperSize = sheet_calculation.paper_size || "A4";
      paperWeight = sheet_calculation.paper_weight || 90;
      printTime = sheet_calculation.print_time || printTime;
      colors = sheet_calculation.colors || "4/4";
    }

    // Generar el JDF
    const jdf = {
      JDF: {
        ID: `JDF_${budget_id || uuidv4()}`,
        Type: "Product",
        Status: status || "Waiting",
        JobID: ot_number || `OT-${Math.floor(Math.random() * 10000)}`,
        CustomerInfo: {
          CustomerID: clientId,
          CustomerName: clientName,
          CustomerContact: clientContact,
          CustomerEmail: clientEmail,
          CustomerCompany: clientCompany,
          CustomerData: budget.client_data || client || {}
        },
        ProductInfo: {
          ProductType: job_type || "Unknown",
          Quantity: quantity || 1000,
          Description: description || "",
          Colors: colors,
          PaperSize: paperSize,
          PaperWeight: paperWeight
        },
        ProcessInfo: {
          Machine: machineName,
          PrintTime: printTime,
          WorkStyle: workStyle
        },
        CreationDate: new Date().toISOString()
      }
    };

    return jdf;
  } catch (error) {
    console.error('Error al generar JDF:', error);
    throw error;
  }
};

// Función para guardar un JDF en un archivo
const saveJDFToFile = (jdf, filename) => {
  try {
    const jdfDir = path.join(__dirname, '../data/jdf');

    // Crear directorio si no existe
    if (!fs.existsSync(jdfDir)) {
      fs.mkdirSync(jdfDir, { recursive: true });
    }

    const filePath = path.join(jdfDir, `${filename}.json`);
    fs.writeFileSync(filePath, JSON.stringify(jdf, null, 2));

    return filePath;
  } catch (error) {
    console.error('Error al guardar JDF:', error);
    throw error;
  }
};

module.exports = {
  generateJDF,
  saveJDFToFile
};
