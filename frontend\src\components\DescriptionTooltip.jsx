import { useState, useRef } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, Paper, Popper, Fade } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

const DescriptionTooltip = ({ description }) => {
  const [open, setOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [hovering, setHovering] = useState(false);
  const textRef = useRef(null);

  // Función para mostrar el tooltip inmediatamente
  const handleMouseEnter = (event) => {
    // Guardar el elemento ancla para el Popper
    setAnchorEl(event.currentTarget);
    setHovering(true);
    setOpen(true);
  };

  // Función para ocultar el tooltip
  const handleMouseLeave = () => {
    setHovering(false);
    setOpen(false);
  };

  // Mostrar solo la primera línea en la tabla
  const firstLine = description.split('\n')[0];

  // Determinar si hay más líneas para mostrar un indicador
  const hasMoreLines = description.includes('\n');

  return (
    <Box
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      ref={textRef}
      sx={{
        cursor: 'help',
        position: 'relative',
        display: 'inline-block',
        maxWidth: '100%',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        '&:hover': {
          backgroundColor: 'rgba(0, 0, 0, 0.04)',
          borderRadius: '4px'
        }
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography
          component="span"
          sx={{
            maxWidth: '100%',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}
        >
          {firstLine}
        </Typography>
        {hasMoreLines && (
          <Typography
            component="span"
            color="primary"
            sx={{
              ml: 0.5,
              fontSize: '0.7rem',
              fontWeight: 'bold',
              display: 'inline-flex',
              alignItems: 'center'
            }}
          >
            <Box sx={{ display: 'inline-flex', alignItems: 'center' }}>
              <InfoOutlinedIcon sx={{ fontSize: '0.8rem', ml: 0.5 }} />
            </Box>
          </Typography>
        )}
      </Box>

      <Popper
        open={open}
        anchorEl={anchorEl}
        placement="bottom-start"
        transition
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 10],
            },
          },
        ]}
        sx={{ zIndex: 1500 }}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={300}>
        <Paper
          elevation={3}
          sx={{
            p: 2,
            maxWidth: 400,
            maxHeight: 300,
            overflow: 'auto',
            bgcolor: '#f5f5f5',
            border: '1px solid #e0e0e0',
            borderRadius: 1
          }}
        >
          <Typography variant="subtitle2" color="primary" gutterBottom sx={{ borderBottom: '1px solid #e0e0e0', pb: 1 }}>
            Descripción completa
          </Typography>
          <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
            {description}
          </Typography>
        </Paper>
          </Fade>
        )}
      </Popper>
    </Box>
  );
};

DescriptionTooltip.propTypes = {
  description: PropTypes.string.isRequired
};

export default DescriptionTooltip;
