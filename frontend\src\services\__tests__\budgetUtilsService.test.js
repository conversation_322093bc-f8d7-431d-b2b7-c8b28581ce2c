/**
 * Tests para budgetUtilsService
 */
import budgetUtilsService, {
  createEmptyPart,
  getPageSizeFromDimensions,
  generateOTNumber,
  createEmptyBudget,
  calculateTotalSheets,
  updateProcessQuantities,
  handlePdfInfoChange,
  handlePageCountChange,
  handlePageSizeChange,
  handleMachineInfoClick,
  handleClientInfoClick,
  handleCloseClientInfo,
  handleClosePdfDialog,
  handleAutocompleteChange,
  handleCloseNavigation,
  createColorConfig,
  updateColorConfig,
  createInitialProductConfig,
  hasBasicPartData,
  partNeedsRecalculation
} from '../budgetUtilsService';

// Mock de window.dispatchEvent
const mockDispatchEvent = jest.fn();
Object.defineProperty(window, 'dispatchEvent', {
  value: mockDispatchEvent
});

describe('budgetUtilsService', () => {
  beforeEach(() => {
    mockDispatchEvent.mockClear();
  });

  describe('createEmptyPart', () => {
    test('should create empty part with default values', () => {
      const part = createEmptyPart();
      
      expect(part).toMatchObject({
        name: 'General',
        description: '',
        assembly_order: 'None',
        pageSize: 'A4',
        customPageSize: '',
        pageCount: '',
        paper: null,
        machine: null,
        sheetCalculation: null,
        paperCost: 0,
        machineCost: 0,
        plateCost: 0,
        processCosts: [],
        totalCost: 0,
        customPrintTime: null,
        colorConfig: {
          frontColors: 4,
          backColors: 4,
          pantones: 0
        }
      });
      
      expect(part.part_id).toMatch(/^part-\d+-0$/);
    });

    test('should create part with custom index and name', () => {
      const part = createEmptyPart(5, 'Custom Part');
      
      expect(part.name).toBe('Custom Part');
      expect(part.part_id).toMatch(/^part-\d+-5$/);
    });
  });

  describe('getPageSizeFromDimensions', () => {
    test('should return A4 for null or undefined dimensions', () => {
      expect(getPageSizeFromDimensions(null)).toBe('A4');
      expect(getPageSizeFromDimensions(undefined)).toBe('A4');
      expect(getPageSizeFromDimensions({})).toBe('A4');
    });

    test('should detect standard page sizes', () => {
      expect(getPageSizeFromDimensions({ width: 210, height: 297 })).toBe('A4');
      expect(getPageSizeFromDimensions({ width: 148, height: 210 })).toBe('A5');
      expect(getPageSizeFromDimensions({ width: 297, height: 420 })).toBe('A3');
      expect(getPageSizeFromDimensions({ width: 216, height: 279 })).toBe('Carta');
    });

    test('should return Personalizado for custom dimensions', () => {
      expect(getPageSizeFromDimensions({ width: 100, height: 150 })).toBe('Personalizado');
      expect(getPageSizeFromDimensions({ width: 300, height: 400 })).toBe('Personalizado');
    });
  });

  describe('generateOTNumber', () => {
    test('should generate OT number with correct format', () => {
      const otNumber = generateOTNumber();
      
      expect(otNumber).toMatch(/^OT-\d{4}\d{4}$/);
      expect(otNumber.startsWith('OT-')).toBe(true);
    });

    test('should generate different numbers on multiple calls', () => {
      const ot1 = generateOTNumber();
      const ot2 = generateOTNumber();
      
      expect(ot1).not.toBe(ot2);
    });
  });

  describe('createEmptyBudget', () => {
    test('should create empty budget with default values', () => {
      const budget = createEmptyBudget();
      
      expect(budget).toMatchObject({
        description: '',
        client: null,
        jobType: '',
        copies: '',
        parts: [],
        shipping_cost: 0,
        total_paper_weight_kg: 0
      });
      
      expect(budget.otNumber).toMatch(/^OT-\d{4}\d{4}$/);
    });
  });

  describe('calculateTotalSheets', () => {
    test('should return sheets from selected part', () => {
      const budgetParts = [
        { sheetCalculation: { total_sheets: 100 } },
        { sheetCalculation: { total_sheets: 200 } }
      ];
      
      const result = calculateTotalSheets(1, budgetParts, null);
      expect(result).toBe(200);
    });

    test('should return sheets from general calculation when no part selected', () => {
      const sheetCalculation = { total_pliegos: 150 };
      
      const result = calculateTotalSheets(null, [], sheetCalculation);
      expect(result).toBe(150);
    });

    test('should return 0 when no data available', () => {
      const result = calculateTotalSheets(null, [], null);
      expect(result).toBe(0);
    });

    test('should prioritize mejor_combinacion when available', () => {
      const budgetParts = [
        { 
          sheetCalculation: { 
            total_sheets: 100,
            mejor_combinacion: { total_pliegos: 80 }
          } 
        }
      ];
      
      const result = calculateTotalSheets(0, budgetParts, null);
      expect(result).toBe(100); // total_sheets has priority
    });
  });

  describe('updateProcessQuantities', () => {
    test('should update quantities for processes without manual modification', () => {
      const processes = [
        { id: 1, quantity: 100, quantityModified: false },
        { id: 2, quantity: 50, quantityModified: true },
        { id: 3, quantity: 200, unit_type: 'Hora', quantityModified: false }
      ];
      
      const result = updateProcessQuantities(processes, 500);
      
      expect(result[0].quantity).toBe(500); // Updated
      expect(result[1].quantity).toBe(50);  // Not updated (manually modified)
      expect(result[2].quantity).toBe(200); // Not updated (unit type is Hora)
    });

    test('should return original array when no processes or copies', () => {
      expect(updateProcessQuantities([], 100)).toEqual([]);
      expect(updateProcessQuantities(null, 100)).toBeNull();
      expect(updateProcessQuantities([{ id: 1 }], null)).toEqual([{ id: 1 }]);
    });
  });

  describe('handlePdfInfoChange', () => {
    test('should update PDF info and budget filename', () => {
      const mockSetPdfInfo = jest.fn();
      const mockSetBudget = jest.fn();
      const info = { filename: 'test.pdf' };
      
      handlePdfInfoChange(info, mockSetPdfInfo, mockSetBudget);
      
      expect(mockSetPdfInfo).toHaveBeenCalledWith(info);
      expect(mockSetBudget).toHaveBeenCalledWith(expect.any(Function));
      
      // Test the function passed to setBudget
      const updateFunction = mockSetBudget.mock.calls[0][0];
      const updatedBudget = updateFunction({ otNumber: 'OT-001' });
      
      expect(updatedBudget).toEqual({
        otNumber: 'OT-001',
        pdf_filename: 'test.pdf'
      });
    });

    test('should remove filename when info is null', () => {
      const mockSetPdfInfo = jest.fn();
      const mockSetBudget = jest.fn();
      
      handlePdfInfoChange(null, mockSetPdfInfo, mockSetBudget);
      
      const updateFunction = mockSetBudget.mock.calls[0][0];
      const updatedBudget = updateFunction({ otNumber: 'OT-001', pdf_filename: 'old.pdf' });
      
      expect(updatedBudget.pdf_filename).toBeNull();
    });
  });

  describe('handlePageCountChange', () => {
    test('should update page count in first part', () => {
      const budgetParts = [
        { pageCount: 10 },
        { pageCount: 20 }
      ];
      const mockSetBudgetParts = jest.fn();
      
      handlePageCountChange(50, budgetParts, mockSetBudgetParts);
      
      expect(mockSetBudgetParts).toHaveBeenCalledWith([
        { pageCount: 50 },
        { pageCount: 20 }
      ]);
    });

    test('should not update when no parts exist', () => {
      const mockSetBudgetParts = jest.fn();
      
      handlePageCountChange(50, [], mockSetBudgetParts);
      
      expect(mockSetBudgetParts).not.toHaveBeenCalled();
    });
  });

  describe('handlePageSizeChange', () => {
    test('should update page size in first part', () => {
      const budgetParts = [
        { pageSize: 'A4', customPageSize: '' },
        { pageSize: 'A5', customPageSize: '' }
      ];
      const mockSetBudgetParts = jest.fn();
      
      handlePageSizeChange('Personalizado', '100x150', budgetParts, mockSetBudgetParts);
      
      expect(mockSetBudgetParts).toHaveBeenCalledWith([
        { pageSize: 'Personalizado', customPageSize: '100x150' },
        { pageSize: 'A5', customPageSize: '' }
      ]);
    });
  });

  describe('handleMachineInfoClick', () => {
    test('should set machine info and show dialog', () => {
      const machine = { id: 1, name: 'Test Machine' };
      const mockSetSelectedMachine = jest.fn();
      const mockSetSelectedPartIndex = jest.fn();
      const mockSetMachineInfoDialog = jest.fn();
      
      handleMachineInfoClick(
        machine, 
        2, 
        mockSetSelectedMachine, 
        mockSetSelectedPartIndex, 
        mockSetMachineInfoDialog
      );
      
      expect(mockSetSelectedMachine).toHaveBeenCalledWith(machine);
      expect(mockSetSelectedPartIndex).toHaveBeenCalledWith(2);
      expect(mockSetMachineInfoDialog).toHaveBeenCalledWith(true);
    });

    test('should not do anything when machine is null', () => {
      const mockSetSelectedMachine = jest.fn();
      const mockSetSelectedPartIndex = jest.fn();
      const mockSetMachineInfoDialog = jest.fn();
      
      handleMachineInfoClick(
        null, 
        2, 
        mockSetSelectedMachine, 
        mockSetSelectedPartIndex, 
        mockSetMachineInfoDialog
      );
      
      expect(mockSetSelectedMachine).not.toHaveBeenCalled();
      expect(mockSetSelectedPartIndex).not.toHaveBeenCalled();
      expect(mockSetMachineInfoDialog).not.toHaveBeenCalled();
    });
  });

  describe('handleClientInfoClick', () => {
    test('should show modal when client exists', () => {
      const client = { id: 1, name: 'Test Client' };
      const mockSetClientInfoModal = jest.fn();
      const mockShowSnackbar = jest.fn();
      
      handleClientInfoClick(client, mockSetClientInfoModal, mockShowSnackbar);
      
      expect(mockSetClientInfoModal).toHaveBeenCalledWith(true);
      expect(mockShowSnackbar).not.toHaveBeenCalled();
    });

    test('should show warning when no client selected', () => {
      const mockSetClientInfoModal = jest.fn();
      const mockShowSnackbar = jest.fn();
      
      handleClientInfoClick(null, mockSetClientInfoModal, mockShowSnackbar);
      
      expect(mockSetClientInfoModal).not.toHaveBeenCalled();
      expect(mockShowSnackbar).toHaveBeenCalledWith('Selecciona un cliente primero', 'warning');
    });
  });

  describe('handleCloseNavigation', () => {
    test('should dispatch navigation event', () => {
      handleCloseNavigation();
      
      expect(mockDispatchEvent).toHaveBeenCalledWith(
        new CustomEvent('navigate-to-budget-list')
      );
    });
  });

  describe('color configuration functions', () => {
    test('createColorConfig should extract color config from product config', () => {
      const productConfig = {
        frontColors: '4',
        backColors: '2',
        pantones: 1,
        otherProperty: 'value'
      };
      
      const colorConfig = createColorConfig(productConfig);
      
      expect(colorConfig).toEqual({
        frontColors: '4',
        backColors: '2',
        pantones: 1
      });
    });

    test('updateColorConfig should update product config', () => {
      const mockSetProductConfig = jest.fn();
      const newColorConfig = {
        frontColors: '2',
        backColors: '0',
        pantones: 2
      };
      
      updateColorConfig(newColorConfig, mockSetProductConfig);
      
      expect(mockSetProductConfig).toHaveBeenCalledWith(expect.any(Function));
      
      const updateFunction = mockSetProductConfig.mock.calls[0][0];
      const updatedConfig = updateFunction({ workStyle: 'Flat' });
      
      expect(updatedConfig).toEqual({
        workStyle: 'Flat',
        frontColors: '2',
        backColors: '0',
        pantones: 2
      });
    });

    test('createInitialProductConfig should return default config', () => {
      const config = createInitialProductConfig();
      
      expect(config).toEqual({
        frontColors: '4',
        backColors: '4',
        pantones: 0,
        selectedParts: [],
        workStyle: 'Flat',
        customOptions: {}
      });
    });
  });

  describe('validation functions', () => {
    test('hasBasicPartData should validate part data', () => {
      expect(hasBasicPartData({ paper: {}, machine: {}, pageCount: 10 })).toBe(true);
      expect(hasBasicPartData({ paper: null, machine: {}, pageCount: 10 })).toBe(false);
      expect(hasBasicPartData({ paper: {}, machine: null, pageCount: 10 })).toBe(false);
      expect(hasBasicPartData({ paper: {}, machine: {}, pageCount: null })).toBe(false);
      expect(hasBasicPartData(null)).toBe(false);
    });

    test('partNeedsRecalculation should check if part needs recalculation', () => {
      const partWithoutBasicData = { paper: null };
      const partWithCalculation = { 
        paper: {}, 
        machine: {}, 
        pageCount: 10, 
        sheetCalculation: { cost: 100 } 
      };
      const partWithoutCalculation = { 
        paper: {}, 
        machine: {}, 
        pageCount: 10 
      };
      const partWithOutdatedCalculation = { 
        paper: {}, 
        machine: {}, 
        pageCount: 10, 
        sheetCalculation: { cost: 100, outdated: true } 
      };
      
      expect(partNeedsRecalculation(partWithoutBasicData)).toBe(false);
      expect(partNeedsRecalculation(partWithCalculation)).toBe(false);
      expect(partNeedsRecalculation(partWithoutCalculation)).toBe(true);
      expect(partNeedsRecalculation(partWithOutdatedCalculation)).toBe(true);
    });
  });
});
