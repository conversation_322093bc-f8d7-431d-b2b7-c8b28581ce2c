from typing import Dict, Optional, List
from utils.logger import log_info, log_error
from models.production import ProductionProcess, ProductionStatus
from utils.production_manager import get_production_processes_by_ot_number
from utils.data_manager import read_data, write_data

def update_budget_status(ot_number: str, new_status: str = "Completado") -> Dict:
    """
    Actualiza el estado del presupuesto asociado a una OT

    Args:
        ot_number: Número de OT del presupuesto a actualizar
        new_status: Nuevo estado para el presupuesto (por defecto: "Completado")

    Returns:
        Dict: Resultado de la operación
    """
    try:
        log_info(f"Actualizando estado del presupuesto para OT: {ot_number} a {new_status}")

        # Si el estado es "Cancelado", no necesitamos verificar los procesos
        # ya que se está llamando desde la función de eliminar OT
        if new_status != "Cancelado":
            # Obtener todos los procesos de producción para esta OT
            processes = get_production_processes_by_ot_number(ot_number)

            if not processes:
                log_info(f"No se encontraron procesos para la OT: {ot_number}")
                return {
                    "success": False,
                    "message": f"No se encontraron procesos para la OT: {ot_number}"
                }

            # Verificar si todos los procesos están completados
            # Imprimir cada proceso y su estado para depuración
            log_info(f"Verificando estados de {len(processes)} procesos para OT {ot_number}:")
            for i, process in enumerate(processes):
                log_info(f"  Proceso {i+1}: ID={process.process_id}, Tipo={process.process_type}, Estado={process.status}")

            # Verificar si hay procesos cancelados
            cancelled_processes = [p for p in processes if p.status == ProductionStatus.CANCELLED]
            active_processes = [p for p in processes if p.status != ProductionStatus.CANCELLED]

            # Si todos los procesos están cancelados, no actualizar el presupuesto
            if len(cancelled_processes) == len(processes):
                log_info(f"Todos los procesos de la OT {ot_number} están cancelados. No se actualizará el presupuesto.")
                return {
                    "success": False,
                    "message": f"Todos los procesos de la OT {ot_number} están cancelados"
                }

            # Verificar si todos los procesos activos están completados
            all_completed = all(process.status == ProductionStatus.COMPLETED for process in active_processes)

            log_info(f"Procesos encontrados para OT {ot_number}: {len(processes)}, procesos activos: {len(active_processes)}, todos completados: {all_completed}")

            if not all_completed and new_status == "Completado":
                return {
                    "success": False,
                    "message": f"No todos los procesos de la OT {ot_number} están completados"
                }

        # Obtener el presupuesto asociado a esta OT
        budgets = read_data("budgets")
        log_info(f"Presupuestos cargados: {len(budgets)}")

        if not budgets:
            log_error(f"No se encontraron presupuestos")
            return {
                "success": False,
                "message": "No se encontraron presupuestos"
            }

        # Buscar el presupuesto con esta OT
        budget_found = False
        log_info(f"Buscando presupuesto para OT {ot_number} entre {len(budgets)} presupuestos")

        # Imprimir todos los números de OT para depuración
        ot_numbers = [budget.get("ot_number") for budget in budgets]
        log_info(f"Números de OT disponibles: {ot_numbers}")

        for i, budget in enumerate(budgets):
            log_info(f"Verificando presupuesto {i+1}/{len(budgets)}: OT={budget.get('ot_number')}, ID={budget.get('budget_id')}")
            if budget.get("ot_number") == ot_number:
                budget_found = True
                current_status = budget.get("status", "")
                log_info(f"Presupuesto encontrado para OT {ot_number}, estado actual: {current_status}")

                # Actualizar el estado del presupuesto al nuevo estado
                budgets[i]["status"] = new_status
                break

        if not budget_found:
            log_info(f"No se encontró presupuesto para la OT: {ot_number}")
            return {
                "success": False,
                "message": f"No se encontró presupuesto para la OT: {ot_number}"
            }

        # Guardar los cambios
        log_info(f"Guardando cambios en el presupuesto para OT {ot_number}, actualizando estado a '{new_status}'")
        result = write_data("budgets", budgets)
        if not result:
            log_error(f"Error al guardar los cambios en el presupuesto para OT {ot_number}")
            return {
                "success": False,
                "message": f"Error al guardar los cambios en el presupuesto"
            }

        # Verificar que los cambios se guardaron correctamente
        budgets_after = read_data("budgets")
        budget_found_after = False
        for budget in budgets_after:
            if budget.get("ot_number") == ot_number:
                budget_found_after = True
                current_status_after = budget.get("status", "")
                log_info(f"Verificación: Presupuesto para OT {ot_number}, estado actual: {current_status_after}")
                break

        if not budget_found_after:
            log_error(f"Verificación: No se encontró el presupuesto para OT {ot_number} después de guardar")

        log_info(f"Estado del presupuesto para OT {ot_number} actualizado a {new_status}")

        return {
            "success": True,
            "message": f"Estado del presupuesto para OT {ot_number} actualizado a {new_status}"
        }

    except Exception as e:
        log_error(f"Error al actualizar estado del presupuesto: {str(e)}")
        return {
            "success": False,
            "message": f"Error al actualizar estado del presupuesto: {str(e)}"
        }
