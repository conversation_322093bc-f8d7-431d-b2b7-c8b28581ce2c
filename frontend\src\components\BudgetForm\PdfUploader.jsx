import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Grid,
  CircularProgress,
  IconButton
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { buildApiUrl } from '../../config';

/**
 * Componente para cargar archivos PDF y mostrar su información
 *
 * @param {Object} props - Propiedades del componente
 * @param {Object} props.selectedPdf - Archivo PDF seleccionado
 * @param {Function} props.setSelectedPdf - Función para actualizar el archivo PDF seleccionado
 * @param {Function} props.onPdfInfoChange - Función que se llama cuando cambia la información del PDF
 * @param {Function} props.showSnackbar - Función para mostrar notificaciones
 * @param {Function} props.onPageCountChange - Función para actualizar el número de páginas en el componente padre
 * @param {Function} props.onPageSizeChange - Función para actualizar el tamaño de página en el componente padre
 */
const PdfUploader = ({
  selectedPdf,
  setSelectedPdf,
  onPdfInfoChange,
  showSnackbar,
  onPageCountChange,
  onPageSizeChange
}) => {
  const [pdfInfo, setPdfInfo] = useState(null);
  const [loadingPdfInfo, setLoadingPdfInfo] = useState(false);

  // Manejar la selección de archivo PDF
  const handlePdfFileChange = async (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      setSelectedPdf(file);
      showSnackbar(`Archivo seleccionado: ${file.name}`, 'success');

      setLoadingPdfInfo(true);
      setPdfInfo(null);
      try {
        // Subir el archivo al backend
        const formData = new FormData();
        formData.append('file', file);
        const uploadResponse = await fetch(buildApiUrl('/uploads/upload-pdf'), {
          method: 'POST',
          body: formData,
        });
        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json().catch(() => ({}));
          const errorMessage = errorData.detail || 'Error al subir el archivo';
          throw new Error(errorMessage);
        }

        // Obtener la respuesta con el nombre normalizado
        const uploadResult = await uploadResponse.json();
        console.log('Archivo PDF subido correctamente:', uploadResult);

        // Extraer el nombre normalizado del archivo
        const normalizedFilename = uploadResult.filename;
        const originalFilename = uploadResult.original_filename || file.name;

        if (!normalizedFilename) {
          throw new Error('El servidor no devolvió un nombre de archivo normalizado');
        }

        console.log('Nombre original:', originalFilename);
        console.log('Nombre normalizado:', normalizedFilename);

        // Guardar el nombre normalizado en el estado para referencia futura
        setSelectedPdf({
          ...file,
          normalizedName: normalizedFilename,
          originalName: originalFilename
        });

        // El backend ya se encarga de enviar el archivo al servidor externo
        // No necesitamos hacer nada adicional aquí
        console.log('El archivo PDF se ha subido correctamente al backend');

        // El backend intentará enviarlo al servidor externo automáticamente
        // Si el envío al servidor externo falla, el backend lo registrará pero no afectará al flujo principal

        // Obtener información del PDF usando el nombre normalizado
        try {
          // Usar el nombre normalizado para obtener la info del PDF
          const infoResponse = await fetch(buildApiUrl(`/uploads/pdf-info/${normalizedFilename}`));

          if (!infoResponse.ok) {
            const errorData = await infoResponse.json().catch(() => ({}));
            const errorMessage = errorData.detail || 'Error al obtener información del PDF';
            console.error('Error en la respuesta del servidor:', errorMessage);
            throw new Error(errorMessage);
          }

          const pdfInfoData = await infoResponse.json();
          console.log('Información del PDF:', pdfInfoData);

          // Actualizar el estado con la info del PDF
          setPdfInfo(pdfInfoData);

          // Notificar al componente padre sobre la info del PDF
          if (onPdfInfoChange) {
            onPdfInfoChange({
              ...pdfInfoData,
              filename: normalizedFilename // Asegurar que se usa el nombre normalizado
            });
            console.log('Notificando al componente padre sobre la info del PDF con filename:', normalizedFilename);
          }

          // Actualizar el número de páginas si está disponible
          if (onPageCountChange && pdfInfoData.num_pages) {
            onPageCountChange(pdfInfoData.num_pages.toString());
          }

          // Actualizar el tamaño de página si está disponible
          if (pdfInfoData.page_size) {
            console.log('Dimensiones:', pdfInfoData.page_size.width, 'x', pdfInfoData.page_size.height);
            if (onPageSizeChange) {
              const { width, height } = pdfInfoData.page_size;
              let pageSize = 'Personalizado';
              let customPageSize = `${width} x ${height}`;

              // Detectar tamaños estándar
              if (Math.abs(width - 210) < 5 && Math.abs(height - 297) < 5) {
                pageSize = 'A4';
                customPageSize = '';
              } else if (Math.abs(width - 148) < 5 && Math.abs(height - 210) < 5) {
                pageSize = 'A5';
                customPageSize = '';
              } else if (Math.abs(width - 297) < 5 && Math.abs(height - 420) < 5) {
                pageSize = 'A3';
                customPageSize = '';
              } else if (Math.abs(width - 216) < 5 && Math.abs(height - 279) < 5) {
                pageSize = 'Carta';
                customPageSize = '';
              }

              onPageSizeChange(pageSize, customPageSize);
            }
          }
        } catch (infoError) {
          console.error('Error al obtener información del PDF:', infoError);
          showSnackbar(`Archivo PDF subido correctamente, pero no se pudo obtener información detallada: ${infoError.message}`, 'warning');
        } finally {
          setLoadingPdfInfo(false);
        }
      } catch (err) {
        console.error('Error al procesar el PDF:', err);
        showSnackbar(err.message, 'error');
        setLoadingPdfInfo(false);
      }
    } else if (file) {
      showSnackbar('Por favor, selecciona un archivo PDF válido', 'error');
      event.target.value = null;
    }
  };

  // Manejar la eliminación del archivo PDF
  const handleRemovePdf = () => {
    setSelectedPdf(null);
    setPdfInfo(null);
    // Notificar al componente padre
    if (onPdfInfoChange) {
      onPdfInfoChange(null);
      console.log('Notificando al componente padre que se ha eliminado el PDF');
    }
  };

  return (
    <Box sx={{ border: '1px solid #ccc', borderRadius: '4px', p: 2 }}>
      <Typography variant="subtitle1" gutterBottom>
        Archivo PDF del diseño
      </Typography>

      {!selectedPdf ? (
        <Button
          variant="contained"
          component="label"
          color="primary"
          sx={{ mt: 1 }}
          disabled={loadingPdfInfo}
        >
          {loadingPdfInfo ? 'Procesando...' : 'SELECCIONAR PDF'}
          <input
            type="file"
            accept="application/pdf"
            hidden
            onChange={handlePdfFileChange}
          />
        </Button>
      ) : (
        <Box>
          {/* Información del archivo PDF */}
          <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="body2" component="div" sx={{ fontWeight: 'medium' }}>
              {selectedPdf.originalName || selectedPdf.name}
            </Typography>
            <IconButton size="small" onClick={handleRemovePdf} aria-label="eliminar pdf">
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>

          {/* Nombre normalizado (si existe) */}
          {selectedPdf.normalizedName && (
            <Box sx={{ mt: 0.5 }}>
              <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                <strong>Nombre normalizado:</strong> {selectedPdf.normalizedName}
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* Información del PDF */}
      {selectedPdf && (
        <Box sx={{ mt: 2, p: 1, bgcolor: 'background.paper', borderRadius: '4px', fontSize: '0.875rem' }}>
          <Typography variant="subtitle2" gutterBottom>
            Información del PDF:
          </Typography>

          {loadingPdfInfo ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 1 }}>
              <CircularProgress size={20} />
              <Typography variant="body2" sx={{ ml: 1 }}>
                Obteniendo información...
              </Typography>
            </Box>
          ) : pdfInfo ? (
            <Grid container spacing={1}>
              {pdfInfo.num_pages && (
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>Páginas:</strong> {pdfInfo.num_pages}
                  </Typography>
                </Grid>
              )}
              {pdfInfo.page_size && (
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>Tamaño:</strong> {pdfInfo.page_size.width} x {pdfInfo.page_size.height} mm
                  </Typography>
                </Grid>
              )}
              {pdfInfo.file_size && (
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>Tamaño archivo:</strong> {pdfInfo.file_size}
                  </Typography>
                </Grid>
              )}
            </Grid>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No se pudo obtener información del PDF.
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
};

export default PdfUploader;
