/**
 * Servicio para cálculo silencioso de pliegos
 * Extrae la funcionalidad de cálculo silencioso del BudgetForm para mejorar la modularización
 */
import { calculateSheetsPart as calculateSheetsV2 } from './unifiedSheetCalculationService';

/**
 * Configuración por defecto para el cálculo silencioso
 */
const DEFAULT_CONFIG = {
  showModal: false,
  showSuccessMessage: true,
  recalculateShipping: true
};

/**
 * Valida que una parte tenga los datos necesarios para el cálculo
 * @param {Object} part - Parte del presupuesto a validar
 * @param {string|number} copies - Número de copias
 * @returns {Object} - { isValid: boolean, error: string }
 */
const validatePartForCalculation = (part, copies) => {
  if (!part) {
    return { isValid: false, error: 'Parte no encontrada' };
  }

  if (!part.machine) {
    return { isValid: false, error: 'Selecciona una máquina para calcular pliegos' };
  }

  if (!part.paper) {
    return { isValid: false, error: 'Selecciona un papel para calcular pliegos' };
  }

  if (!part.pageCount || parseInt(part.pageCount) <= 0) {
    return { isValid: false, error: 'Ingresa un número válido de páginas' };
  }

  if (!copies || parseInt(copies) <= 0) {
    return { isValid: false, error: 'Ingresa una cantidad válida de copias' };
  }

  return { isValid: true, error: null };
};

/**
 * Actualiza una parte con los resultados del cálculo
 * @param {Object} part - Parte original
 * @param {Object} results - Resultados del cálculo
 * @param {number} shippingCost - Costo de envío para esta parte (opcional)
 * @returns {Object} - Parte actualizada
 */
const updatePartWithResults = (part, results, shippingCost = 0) => {
  const baseCost = results.costos?.costo_total || 0;
  
  return {
    ...part,
    sheetCalculation: results,
    paperCost: results.costos?.costo_papel || 0,
    machineCost: results.costos?.costo_maquina || 0,
    plateCost: results.costos?.costo_planchas || 0,
    clickCost: results.costos?.costo_click || 0,
    inkCost: results.costos?.costo_tinta || 0,
    shippingCost: shippingCost,
    totalCost: baseCost + shippingCost // Incluir el costo de envío en el costo total
  };
};

/**
 * Calcula el peso total del papel de una parte
 * @param {Object} part - Parte del presupuesto
 * @param {string|number} copies - Número de copias
 * @returns {number} - Peso total en kg
 */
const calculatePartPaperWeight = (part, copies) => {
  // No usar el peso calculado por el endpoint, ya que puede ser incorrecto
  // Siempre calcular manualmente para mayor precisión
  
  if (!part.sheetCalculation || !part.paper) {
    return 0;
  }
  
  // Registrar los datos para depuración
  console.log(`Calculando peso para parte: ${part.name || 'Sin nombre'}`);
  if (part.sheetCalculation && part.sheetCalculation.paper_weight_kg) {
    console.log(`  Peso reportado por endpoint: ${part.sheetCalculation.paper_weight_kg} kg`);
  }

  // Calcular el peso manualmente si tenemos los datos necesarios
  const totalPliegos = part.sheetCalculation.mejor_combinacion?.total_pliegos ||
                      part.sheetCalculation.total_physical_sheets ||
                      part.sheetCalculation.total_sheets || 0;

  if (totalPliegos === 0) return 0;

  const isDigital = part.machine && part.machine.type === 'Digital';
  const copiesInt = parseInt(copies) || 500;

  // Calcular pliegos de producción y maculatura
  const pliegosProduccion = totalPliegos * copiesInt;

  // Usar el valor de maculatura del cálculo o de la máquina, o un valor por defecto
  let maculaturaPorPliego = 0;
  if (!isDigital) {
    maculaturaPorPliego = part.sheetCalculation.maculatura_por_pliego ||
                        part.machine?.maculatura_por_pliego || 150;
  }

  const pliegosMaculatura = totalPliegos * maculaturaPorPliego;
  const pliegosTotales = pliegosProduccion + pliegosMaculatura;

  // Obtener dimensiones y gramaje del papel
  const gramaje = part.paper.weight || part.paper.grammage || 80; // g/m²

  // Obtener el tamaño del pliego en metros cuadrados
  let anchoPliego = 0;
  let altoPliego = 0;

  if (part.paper.dimension_width && part.paper.dimension_height) {
    // Dimensiones en mm
    anchoPliego = part.paper.dimension_width / 1000; // convertir a metros
    altoPliego = part.paper.dimension_height / 1000; // convertir a metros
  } else if (part.paper.width && part.paper.height) {
    anchoPliego = part.paper.width / 1000; // convertir a metros
    altoPliego = part.paper.height / 1000; // convertir a metros
  }

  const areaPliego = anchoPliego * altoPliego; // área en metros cuadrados

  // Calcular el peso de un pliego en kg
  const pesoPorPliego = (areaPliego * gramaje) / 1000; // convertir de gramos a kg

  // Calcular el peso total de esta parte
  const pesoTotal = pesoPorPliego * pliegosTotales;
  
  // Registrar los detalles del cálculo para depuración
  console.log(`  Detalles del cálculo de peso:`);
  console.log(`    - Pliegos totales: ${pliegosTotales} (Producción: ${pliegosProduccion}, Maculatura: ${pliegosMaculatura})`);
  console.log(`    - Dimensiones del pliego: ${anchoPliego*1000}mm x ${altoPliego*1000}mm`);
  console.log(`    - Área del pliego: ${areaPliego.toFixed(4)} m²`);
  console.log(`    - Gramaje: ${gramaje} g/m²`);
  console.log(`    - Peso por pliego: ${pesoPorPliego.toFixed(4)} kg`);
  console.log(`    - Peso total calculado: ${pesoTotal.toFixed(2)} kg`);
  
  return pesoTotal;
};

/**
 * Calcula el peso total del papel de todas las partes
 * @param {Array} parts - Array de partes del presupuesto
 * @param {string|number} copies - Número de copias
 * @returns {Object} - Objeto con peso total y pesos individuales por parte
 */
const calculateTotalPaperWeight = (parts, copies) => {
  let totalPaperWeight = 0;
  const partWeights = [];

  parts.forEach(part => {
    const partWeight = calculatePartPaperWeight(part, copies);
    partWeights.push({
      partIndex: parts.indexOf(part),
      weight: partWeight
    });
    totalPaperWeight += partWeight;
  });

  // Redondear a 2 decimales
  return {
    totalWeight: Math.round(totalPaperWeight * 100) / 100,
    partWeights: partWeights
  };
};

/**
 * Recalcula el costo de envío basado en el peso del papel
 * @param {Object} weightData - Datos de peso del papel (totalWeight y partWeights)
 * @param {Object} budget - Presupuesto actual
 * @param {Function} buildApiUrl - Función para construir URLs de API
 * @param {Function} setBudget - Función para actualizar el presupuesto
 * @returns {Promise<Object>} - Resultado del cálculo de envío con costos distribuidos por parte
 */
const recalculateShippingCost = async (weightData, budget, buildApiUrl, setBudget) => {
  try {
    // Si no hay peso, no calcular envío
    if (!weightData || weightData.totalWeight <= 0) {
      return { weight: 0, cost: 0, partCosts: [] };
    }

    // Preparar los datos para la llamada al endpoint
    console.log(`Enviando solicitud de cálculo de envío:`);
    console.log(`  - Peso total: ${weightData.totalWeight} kg`);
    console.log(`  - Pesos por parte:`, weightData.partWeights);
    
    const requestData = {
      weight_kg: weightData.totalWeight,
      country: "España" // Valor por defecto
    };

    // Si tenemos un cliente seleccionado, añadir su ID para obtener el país automáticamente
    if (budget.client && budget.client.client_id) {
      requestData.client_id = budget.client.client_id;
    }

    // Verificar el token de autenticación
    const authToken = localStorage.getItem('auth_token') || '';
    console.log('Token de autenticación disponible:', authToken ? 'Sí' : 'No');
    
    // Llamar al endpoint
    console.log('Enviando solicitud al endpoint /calculations/shipping-cost');
    const response = await fetch(buildApiUrl('/calculations/shipping-cost'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(requestData)
    });
    
    console.log('Respuesta recibida:', response.status, response.statusText);

    if (!response.ok) {
      throw new Error(`Error al calcular el costo de envío: ${response.statusText}`);
    }

    // Obtener la respuesta
    const shippingData = await response.json();
    const shippingCost = shippingData.shipping_cost || 0;
    
    console.log(`Respuesta del cálculo de envío:`);
    console.log(`  - Costo de envío: ${shippingCost} €`);
    console.log(`  - País: ${shippingData.country || 'No especificado'}`);
    console.log(`  - Factor de distancia: ${shippingData.distance_factor || 1}`);
    console.log(`  - Datos completos:`, shippingData);

    // Distribuir el costo de envío entre las partes proporcionalmente según su peso
    const partShippingCosts = [];
    if (weightData.partWeights && weightData.partWeights.length > 0 && weightData.totalWeight > 0) {
      weightData.partWeights.forEach(partWeight => {
        const partShippingCost = (partWeight.weight / weightData.totalWeight) * shippingCost;
        partShippingCosts.push({
          partIndex: partWeight.partIndex,
          shippingCost: Math.round(partShippingCost * 100) / 100, // Redondear a 2 decimales
          weight: partWeight.weight
        });
      });
    }

    // Actualizar el estado del presupuesto con el nuevo peso y costo de envío
    // Actualizamos tanto costs.shipping como shipping para mantener compatibilidad
    setBudget(prevBudget => {
      const shippingData = {
        weight_kg: weightData.totalWeight,
        cost: shippingCost,
        country: shippingData.country || "España",
        distance_factor: shippingData.distance_factor || 1,
        part_costs: partShippingCosts // Añadir los costos distribuidos por parte
      };
      
      console.log('Actualizando presupuesto con datos de envío:', shippingData);
      
      return {
        ...prevBudget,
        total_paper_weight_kg: weightData.totalWeight,
        shipping_cost: shippingCost, // Mantener compatibilidad con código antiguo
        shipping: {
          ...prevBudget.shipping,
          cost: shippingCost,
          weight: weightData.totalWeight,
          country: shippingData.country || "España",
          distance_factor: shippingData.distance_factor || 1,
          part_costs: partShippingCosts
        },
        costs: {
          ...(prevBudget.costs || {}),
          shipping: {
            ...(prevBudget.costs?.shipping || {}),
            ...shippingData
          }
        }
      };
    });

    return {
      ...shippingData,
      part_costs: partShippingCosts
    };
  } catch (error) {
    console.error('Error al calcular el costo de envío:', error);
    return { weight: 0, cost: 0, part_costs: [], error: true };
  }
};

/**
 * Función principal para calcular pliegos de forma silenciosa
 * @param {Object} params - Parámetros para el cálculo
 * @param {Array} params.budgetParts - Array de partes del presupuesto
 * @param {number} params.partIndex - Índice de la parte a calcular
 * @param {string|number} params.copies - Número de copias
 * @param {Object} params.budget - Presupuesto actual
 * @param {string} params.budgetId - ID del presupuesto (opcional)
 * @param {Function} params.buildApiUrl - Función para construir URLs de API
 * @param {Function} params.showSnackbar - Función para mostrar mensajes
 * @param {Function} params.setBudgetParts - Función para actualizar las partes
 * @param {Function} params.setBudget - Función para actualizar el presupuesto
 * @param {Function} params.setCalculatingSheets - Función para indicar estado de cálculo
 * @param {Function} params.setCurrentCalculatedPart - Función para establecer parte actual
 * @param {Function} params.setCalculationResults - Función para guardar resultados
 * @param {Object} params.config - Configuración opcional
 * @returns {Promise<Object|null>} - Resultados del cálculo o null si hay error
 */
export const calculateSheetsV2Silent = async (params) => {
  const {
    budgetParts,
    partIndex,
    copies,
    budget,
    budgetId,
    buildApiUrl,
    showSnackbar,
    setBudgetParts,
    setBudget,
    setCalculatingSheets,
    setCurrentCalculatedPart,
    setCalculationResults,
    config = DEFAULT_CONFIG
  } = params;

  try {
    // Obtener la parte seleccionada
    const part = budgetParts[partIndex];

    // Validar que la parte tenga los datos necesarios
    const validation = validatePartForCalculation(part, copies);
    if (!validation.isValid) {
      showSnackbar(validation.error, 'warning');
      return null;
    }

    // Indicar que estamos calculando
    setCalculatingSheets(true);

    // Guardar la parte actual para que esté disponible
    setCurrentCalculatedPart(part);

    // Llamar al servicio unificado de cálculo de pliegos sin mostrar el modal
    const results = await calculateSheetsV2({
      part: budgetParts[partIndex],
      partIndex,
      copies,
      buildApiUrl,
      showSnackbar,
      setCalculatingSheets,
      setBudgetParts,
      setBudget,
      setCurrentCalculatedPart,
      setSheetCalculationV2Modal: () => {}, // No mostrar el modal
      budgetParts,
      budget,
      budgetId,
      setCalculationResults
    });

    // Procesar los resultados sin mostrar el modal
    if (results) {
      // Calcular el peso total del papel y obtener los pesos por parte
      const weightData = calculateTotalPaperWeight(budgetParts, copies);

      // Recalcular el costo de envío si está configurado
      let shippingData = null;
      if (config.recalculateShipping) {
        shippingData = await recalculateShippingCost(weightData, budget, buildApiUrl, setBudget);
      }
      
      // Obtener el costo de envío para esta parte específica
      let partShippingCost = 0;
      if (shippingData && shippingData.part_costs) {
        const partCostInfo = shippingData.part_costs.find(pc => pc.partIndex === partIndex);
        if (partCostInfo) {
          partShippingCost = partCostInfo.shippingCost;
        }
      }
      
      // Actualizar la parte con los resultados del cálculo incluyendo el costo de envío
      const updatedParts = [...budgetParts];
      updatedParts[partIndex] = updatePartWithResults(part, results, partShippingCost);
      
      // Actualizar también las otras partes con sus costos de envío correspondientes
      if (shippingData && shippingData.part_costs) {
        shippingData.part_costs.forEach(costInfo => {
          const idx = costInfo.partIndex;
          if (idx !== partIndex && idx >= 0 && idx < updatedParts.length) {
            const currentPart = updatedParts[idx];
            // Solo actualizar el costo de envío y el total, manteniendo el resto de la parte igual
            updatedParts[idx] = {
              ...currentPart,
              shippingCost: costInfo.shippingCost,
              totalCost: (currentPart.totalCost || 0) - (currentPart.shippingCost || 0) + costInfo.shippingCost
            };
          }
        });
      }

      // Actualizar el estado de las partes
      setBudgetParts(updatedParts);

      // Mostrar mensaje de éxito si está configurado
      if (config.showSuccessMessage) {
        showSnackbar(`Pliegos calculados correctamente para la parte ${partIndex + 1}`, 'success');
      }

      return results;
    }

    return null;
  } catch (error) {
    console.error('Error al calcular pliegos en silencio:', error);
    showSnackbar('Error al calcular pliegos: ' + error.message, 'error');
    return null;
  } finally {
    setCalculatingSheets(false);
  }
};

/**
 * Hook personalizado para usar el cálculo silencioso
 * @param {Object} dependencies - Dependencias necesarias para el cálculo
 * @returns {Function} - Función para ejecutar el cálculo silencioso
 */
export const useSilentSheetCalculation = (dependencies) => {
  const {
    budgetParts,
    budget,
    budgetId,
    buildApiUrl,
    showSnackbar,
    setBudgetParts,
    setBudget,
    setCalculatingSheets,
    setCurrentCalculatedPart,
    setCalculationResults
  } = dependencies;

  return async (partIndex, config = DEFAULT_CONFIG) => {
    return await calculateSheetsV2Silent({
      budgetParts,
      partIndex,
      copies: budget.copies,
      budget,
      budgetId,
      buildApiUrl,
      showSnackbar,
      setBudgetParts,
      setBudget,
      setCalculatingSheets,
      setCurrentCalculatedPart,
      setCalculationResults,
      config
    });
  };
};

// Exportar también las funciones auxiliares para uso independiente
export {
  validatePartForCalculation,
  updatePartWithResults,
  calculatePartPaperWeight,
  calculateTotalPaperWeight,
  recalculateShippingCost
};
