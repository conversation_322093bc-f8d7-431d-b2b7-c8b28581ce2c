from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import signal
import sys
import os

# Importar el middleware de logging
from middleware.logging_middleware import LoggingMiddleware

# Importar el logger
from utils.logger import log_info

# Importar los routers
from routes.papers import router as papers_router
from routes.machines import router as machines_router
from routes.clients import router as clients_router
from routes.budgets import router as budgets_router
from routes.json_ot_generator import router as json_ot_generator_router
from routes.external_api import router as external_api_router
from routes.uploads import router as uploads_router
from routes.processes import router as processes_router
# Eliminado: from routes.paper_calculator import router as paper_calculator_router
from routes.logs import router as logs_router
from routes.products import router as products_router
from routes.calculations import router as calculations_router
from routes.consumables import router as consumables_router
from routes.production import router as production_router
from routes.shipping import router as shipping_router
from routes.config import router as config_router
from routes.auth import router as auth_router
from routes.activity_logs import router as activity_logs_router
from routes.invoices import router as invoices_router
from routes.dashboard_routes import router as dashboard_router
from routes.dashboard_cost_profit import router as dashboard_cost_profit_router
from routes.ot_pdf import router as ot_pdf_router
from routes.offset_calculator import router as offset_calculator_router
from routes.digital_calculator import router as digital_calculator_router
from routes.folding_schemes import router as folding_schemes_router

# Crear la aplicación FastAPI
app = FastAPI(title="API de Gestión de Papel")

# Registrar el inicio de la aplicación en el log
log_info("Iniciando la aplicación FastAPI")

# Añadir el middleware de logging
app.add_middleware(LoggingMiddleware)

# Configuración de CORS - debe ir inmediatamente después de crear la app
origins = [
    "http://localhost:5173",    # Vite dev server
    "http://localhost:4005",
    "http://127.0.0.1:5173",
    "http://127.0.0.1:4173",
    "https://imprenta.triky.app",
    "http://frontend:4005",     # Nombre del servicio en Docker
    "http://localhost:4173",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=3600,
)

# Ruta raíz
@app.get("/")
def root():
    return {"message": "API de Gestión de Papel para Impresión"}

# Incluir los routers
app.include_router(papers_router)
app.include_router(machines_router)
app.include_router(clients_router)
app.include_router(processes_router)
app.include_router(budgets_router, prefix="/budgets", tags=["budgets"])
app.include_router(json_ot_generator_router)
app.include_router(external_api_router)
app.include_router(uploads_router, prefix="/uploads", tags=["uploads"])

# Importar y añadir el router de calculator directamente para evitar conflictos
from routes.calculator import router as calculator_router
app.include_router(calculator_router)

# Eliminado: Añadir el router del calculador de pliegos

# Añadir el router de logs
app.include_router(logs_router)

# Añadir el router de productos
app.include_router(products_router)

# Añadir el router de cálculos
app.include_router(calculations_router)

# Añadir el router de consumibles
app.include_router(consumables_router)

# Añadir el router de producción
app.include_router(production_router)

# Añadir el router de envíos
app.include_router(shipping_router)

# Añadir el router de configuración
app.include_router(config_router)

# Añadir el router de autenticación
app.include_router(auth_router, tags=["auth"])

# Añadir el router de registros de actividad
app.include_router(activity_logs_router)

# Añadir el router de facturación
app.include_router(invoices_router)

# Añadir el router del dashboard
app.include_router(dashboard_router, prefix="/dashboard", tags=["dashboard"])

# Añadir el router de costes y beneficios del dashboard
app.include_router(dashboard_cost_profit_router)

# Añadir el router de PDFs de OT
app.include_router(ot_pdf_router)

# Añadir el router del calculador avanzado de offset
app.include_router(offset_calculator_router)

# Añadir el router del calculador avanzado de digital
app.include_router(digital_calculator_router)

# Añadir el router de esquemas de plegado
app.include_router(folding_schemes_router)

# Función para manejar el cierre limpio del servidor
def handle_exit(signum, frame):
    print("Cerrando el servidor de forma limpia...")
    sys.exit(0)

# Registrar manejadores de señales para cierre limpio
signal.signal(signal.SIGINT, handle_exit)  # Ctrl+C
signal.signal(signal.SIGTERM, handle_exit)  # kill

# Punto de entrada para ejecutar la aplicación
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3005)
