import { format } from 'date-fns';
import ConfigService from '../services/ConfigService';

/**
 * Utilidades para cálculos y optimización de trabajos de impresión offset
 */

// Tiempos estándar para operaciones offset (en horas)
export const OFFSET_STANDARD_TIMES = {
  SETUP: 0.5,        // Tiempo de preparación
  CLEANUP: 0.25,     // Tiempo de limpieza
  PLATE_CHANGE: 0.2, // Cambio de plancha
  INK_CHANGE: 0.15,  // Cambio de tinta
  PAPER_CHANGE: 0.2, // Cambio de papel
  MAINTENANCE: 0.5,  // Mantenimiento básico entre trabajos
  QUALITY_CHECK: 0.1 // Control de calidad
};

// Velocidades promedio para diferentes tipos de trabajos (pliegos por hora)
export const OFFSET_SPEEDS = {
  SIMPLE: 8000,      // Trabajos simples (1-2 colores)
  STANDARD: 6000,    // Trabajos estándar (4 colores)
  COMPLEX: 4000,     // Trabajos complejos (>4 colores, barnices)
  SPECIAL: 3000      // Papeles especiales o gramajes altos
};

/**
 * Obtiene los tiempos estándar desde la configuración del sistema
 * @returns {Promise<Object>} - Tiempos estándar configurados o valores por defecto
 */
export const getOffsetStandardTimes = async () => {
  try {
    const config = await ConfigService.getConfig();
    if (config && config.offset_standard_times) {
      return config.offset_standard_times;
    }
    return OFFSET_STANDARD_TIMES;
  } catch (error) {
    console.error('Error al obtener tiempos estándar de offset:', error);
    return OFFSET_STANDARD_TIMES;
  }
};

/**
 * Obtiene las velocidades desde la configuración del sistema
 * @returns {Promise<Object>} - Velocidades configuradas o valores por defecto
 */
export const getOffsetSpeeds = async () => {
  try {
    const config = await ConfigService.getConfig();
    if (config && config.offset_speeds) {
      return config.offset_speeds;
    }
    return OFFSET_SPEEDS;
  } catch (error) {
    console.error('Error al obtener velocidades de offset:', error);
    return OFFSET_SPEEDS;
  }
};

/**
 * Calcula el tiempo de impresión para trabajos offset
 * @param {Array} jobs - Lista de trabajos de impresión
 * @returns {Promise<Array>} - Lista de trabajos con tiempos calculados
 */
export const calculatePrintTime = async (jobs) => {
  // Obtener configuración actualizada
  const standardTimes = await getOffsetStandardTimes();
  const speeds = await getOffsetSpeeds();

  return jobs.map(job => {
    let printTime = 1; // Tiempo por defecto (1 hora)

    // Prioridad: custom_print_time > sheet_calculation.print_time > cálculo basado en pliegos
    if (job.custom_print_time && job.custom_print_time > 0) {
      printTime = job.custom_print_time;
    } else if (job.sheet_calculation && job.sheet_calculation.print_time) {
      printTime = job.sheet_calculation.print_time;
    } else if (job.sheet_calculation && job.sheet_calculation.total_pliegos) {
      // Calcular tiempo basado en pliegos y velocidad
      const totalSheets = job.sheet_calculation.total_pliegos * job.quantity;

      // Determinar complejidad del trabajo
      const isComplex = (job.colors && job.colors.includes('5')) ||
                        (job.colors && job.colors.includes('6')) ||
                        (job.finishing_processes && job.finishing_processes.length > 2);

      // Determinar tipo de papel
      const isSpecialPaper = job.paper_id && (
        job.paper_id.includes('Especial') ||
        job.paper_id.includes('METÁLICO') ||
        job.paper_id.includes('CARTÓN')
      );

      // Elegir velocidad según complejidad
      let speed = speeds.STANDARD;
      if (isComplex) {
        speed = speeds.COMPLEX;
      } else if (isSpecialPaper) {
        speed = speeds.SPECIAL;
      } else if (job.colors && ['1/0', '1/1', '2/0', '2/2'].includes(job.colors)) {
        speed = speeds.SIMPLE;
      }

      // Calcular tiempo de impresión
      const printingHours = totalSheets / speed;

      // Añadir tiempos de preparación y limpieza
      printTime = printingHours + standardTimes.SETUP + standardTimes.CLEANUP;

      // Añadir tiempo para cambio de planchas si es necesario
      if (job.sheet_calculation.total_planchas) {
        printTime += standardTimes.PLATE_CHANGE * job.sheet_calculation.total_planchas;
      }

      // Redondear a 0.5 horas más cercanas
      printTime = Math.ceil(printTime * 2) / 2;
    }

    return {
      ...job,
      duration: printTime,
      setup_time: standardTimes.SETUP,
      cleanup_time: standardTimes.CLEANUP
    };
  });
};

/**
 * Optimiza la secuencia de trabajos para minimizar tiempos de preparación
 * @param {Array} jobs - Lista de trabajos de impresión
 * @returns {Array} - Lista de trabajos optimizada
 */
export const optimizeJobSequence = (jobs) => {
  // Ordenar primero por estado
  const sortedByStatus = [...jobs].sort((a, b) => {
    const statusOrder = { 'Aprobado': 1, 'Enviado': 2, 'Completado': 3, 'Actualizado': 4 };
    return (statusOrder[a.status] || 5) - (statusOrder[b.status] || 5);
  });

  // Agrupar por colores similares para reducir tiempos de cambio
  const result = [];
  const colorGroups = {};

  // Agrupar trabajos por colores similares
  sortedByStatus.forEach(job => {
    const colorKey = job.colors || '4/4';
    if (!colorGroups[colorKey]) {
      colorGroups[colorKey] = [];
    }
    colorGroups[colorKey].push(job);
  });

  // Ordenar grupos por cantidad de trabajos (mayor a menor)
  const sortedColorKeys = Object.keys(colorGroups).sort(
    (a, b) => colorGroups[b].length - colorGroups[a].length
  );

  // Para cada grupo, ordenar por tamaño de papel (para minimizar ajustes)
  sortedColorKeys.forEach(colorKey => {
    const group = colorGroups[colorKey];

    // Ordenar por tamaño de papel
    group.sort((a, b) => {
      // Si ambos tienen datos de papel, ordenar por dimensiones
      if (a.paper_dimensions && b.paper_dimensions) {
        return (b.paper_dimensions.width * b.paper_dimensions.height) -
               (a.paper_dimensions.width * a.paper_dimensions.height);
      }
      return 0;
    });

    // Añadir al resultado final
    result.push(...group);
  });

  return result;
};

/**
 * Asigna horas de inicio y fin a cada trabajo
 * @param {Array} jobs - Lista de trabajos optimizada
 * @param {Date} startDate - Fecha y hora de inicio
 * @returns {Promise<Array>} - Lista de trabajos con horarios asignados
 */
export const assignTimeslots = async (jobs, startDate) => {
  // Obtener configuración actualizada
  const standardTimes = await getOffsetStandardTimes();

  let currentDate = new Date(startDate);
  // Establecer hora de inicio a las 8:00 AM
  currentDate.setHours(8, 0, 0, 0);

  return jobs.map(job => {
    const start = new Date(currentDate);
    const duration = job.duration || 1; // Al menos 1 hora

    const end = new Date(start);
    end.setTime(start.getTime() + (duration * 60 * 60 * 1000));

    // Añadir tiempo de limpieza entre trabajos
    currentDate = new Date(end);
    currentDate.setTime(currentDate.getTime() + (standardTimes.CLEANUP * 60 * 60 * 1000));

    // Si pasamos de las 20:00, saltar al día siguiente a las
    if (currentDate.getHours() >= 20) {
      currentDate.setDate(currentDate.getDate() + 1);
      currentDate.setHours(8, 0, 0, 0);
    }

    return {
      ...job,
      start_time: start,
      end_time: end
    };
  });
};

/**
 * Detecta conflictos en la programación
 * @param {Array} jobs - Lista de trabajos con horarios asignados
 * @returns {Array} - Lista de conflictos detectados
 */
export const detectConflicts = (jobs) => {
  const conflicts = [];

  for (let i = 0; i < jobs.length; i++) {
    for (let j = i + 1; j < jobs.length; j++) {
      const job1 = jobs[i];
      const job2 = jobs[j];

      // Comprobar si hay solapamiento
      if (job1.start_time < job2.end_time && job1.end_time > job2.start_time) {
        conflicts.push({
          job1,
          job2,
          type: 'overlap',
          description: `Solapamiento entre ${job1.ot_number} y ${job2.ot_number}`
        });
      }
    }
  }

  return conflicts;
};

/**
 * Encuentra el siguiente espacio libre disponible para un proceso
 * @param {Array} existingProcesses - Lista de procesos existentes
 * @param {Object} process - Proceso a programar
 * @param {Date} startAfter - Fecha mínima para iniciar (opcional)
 * @param {Number} maxAttempts - Número máximo de intentos para evitar recursión infinita
 * @returns {Promise<Object>} - Fechas de inicio y fin para el nuevo proceso
 */
export const findNextAvailableSlot = async (existingProcesses, process, startAfter = null, maxAttempts = 10) => {
  // Obtener configuración actualizada
  const standardTimes = await getOffsetStandardTimes();
  console.log('Buscando espacio libre para proceso:', process.process_id);

  // Protección contra recursión infinita
  if (maxAttempts <= 0) {
    console.warn('Máximo de intentos alcanzado para proceso:', process.process_id);
    // Programar para 7 días después como último recurso
    const fallbackDate = new Date();
    fallbackDate.setDate(fallbackDate.getDate() + 7);
    fallbackDate.setHours(8, 0, 0, 0);
    const fallbackEnd = new Date(fallbackDate);
    fallbackEnd.setTime(fallbackDate.getTime() + (process.estimated_hours * 60 * 60 * 1000));
    return { start: fallbackDate, end: fallbackEnd };
  }

  // Filtrar solo los procesos para la misma máquina
  const machineProcesses = existingProcesses.filter(p =>
    p.machine_id === process.machine_id &&
    p.process_id !== process.process_id &&
    p.status !== 'Cancelado'
  );

  console.log(`Encontrados ${machineProcesses.length} procesos en la misma máquina`);

  // Si no hay otros procesos en la misma máquina, devolver la fecha actual
  if (machineProcesses.length === 0) {
    const start = startAfter || new Date();
    const start8am = new Date(start);
    // Si es antes de las 8am, comenzar a las 8am
    if (start.getHours() < 8) {
      start8am.setHours(8, 0, 0, 0);
      const end = new Date(start8am);
      end.setTime(start8am.getTime() + (process.estimated_hours * 60 * 60 * 1000));
      return { start: start8am, end };
    }
    // Si es después de las 8pm, comenzar al día siguiente a las 8am
    if (start.getHours() >= 20) {
      start8am.setDate(start8am.getDate() + 1);
      start8am.setHours(8, 0, 0, 0);
      const end = new Date(start8am);
      end.setTime(start8am.getTime() + (process.estimated_hours * 60 * 60 * 1000));
      return { start: start8am, end };
    }
    // En horario laboral normal
    const end = new Date(start);
    end.setTime(start.getTime() + (process.estimated_hours * 60 * 60 * 1000));
    return { start, end };
  }

  // Ordenar los procesos por fecha de inicio
  const sortedProcesses = [...machineProcesses].sort((a, b) => {
    const dateA = new Date(a.start_date).getTime();
    const dateB = new Date(b.start_date).getTime();
    return dateA - dateB;
  });

  // Fecha mínima para comenzar (ahora o la fecha proporcionada)
  let currentTime = startAfter ? new Date(startAfter) : new Date();

  // Si es antes de las 8am, comenzar a las 8am
  if (currentTime.getHours() < 8) {
    currentTime.setHours(8, 0, 0, 0);
  }

  // Si es después de las 8pm, comenzar al día siguiente a las 8am
  if (currentTime.getHours() >= 20) {
    currentTime.setDate(currentTime.getDate() + 1);
    currentTime.setHours(8, 0, 0, 0);
  }

  // Duración del proceso en milisegundos
  const processDuration = process.estimated_hours * 60 * 60 * 1000;

  // Buscar un hueco entre procesos
  for (let i = 0; i < sortedProcesses.length; i++) {
    const processStart = new Date(sortedProcesses[i].start_date);

    // Si el tiempo actual más la duración es menor que el inicio del siguiente proceso,
    // hemos encontrado un hueco
    if (currentTime.getTime() + processDuration <= processStart.getTime()) {
      const start = new Date(currentTime);
      const end = new Date(currentTime);
      end.setTime(currentTime.getTime() + processDuration);

      // Verificar que no se extienda más allá de las 8pm
      if (end.getHours() >= 20) {
        // Si se extiende más allá de las 8pm, intentar el día siguiente
        const nextDay = new Date(currentTime);
        nextDay.setDate(nextDay.getDate() + 1);
        nextDay.setHours(8, 0, 0, 0);
        return await findNextAvailableSlot(existingProcesses, process, nextDay, maxAttempts - 1);
      }

      return { start, end };
    }

    // Actualizar el tiempo actual al final del proceso actual
    currentTime = new Date(sortedProcesses[i].end_date);

    // Si el tiempo actual es después de las 8pm, mover al día siguiente a las 8am
    if (currentTime.getHours() >= 20) {
      currentTime.setDate(currentTime.getDate() + 1);
      currentTime.setHours(8, 0, 0, 0);
    }
  }

  // Si llegamos aquí, no hay huecos entre procesos, así que programar después del último
  const start = new Date(currentTime);
  const end = new Date(currentTime.getTime() + processDuration);

  // Verificar que no se extienda más allá de las 8pm
  if (end.getHours() >= 20) {
    // Si se extiende más allá de las 8pm, intentar el día siguiente
    const nextDay = new Date(currentTime);
    nextDay.setDate(nextDay.getDate() + 1);
    nextDay.setHours(8, 0, 0, 0);
    return await findNextAvailableSlot(existingProcesses, process, nextDay, maxAttempts - 1);
  }

  return { start, end };
};

/**
 * Reorganiza automáticamente todos los procesos según reglas específicas:
 * - Procesos "En Proceso" dentro del horario laboral (8:00-20:00) y nunca antes de la hora actual
 * - Procesos "Cancelados" fuera del horario laboral en la parte superior del día
 * - Evita solapamientos, dando prioridad a los procesos "Completados"
 *
 * @param {Array} processes - Lista de todos los procesos
 * @param {string} machineId - ID de la máquina a reorganizar
 * @param {Date} startTime - Hora desde la que reorganizar (por defecto, hora actual)
 * @returns {Promise<Array>} - Lista de procesos reorganizados con nuevas fechas
 */
export const reorganizeProcesses = async (processes, machineId, startTime = new Date()) => {
  // Obtener configuración actualizada
  const standardTimes = await getOffsetStandardTimes();
  if (!processes || processes.length === 0) {
    return [];
  }

  console.log('Reorganizando procesos desde:', format(startTime, 'yyyy-MM-dd HH:mm'));

  // Separar procesos por estado
  // Si machineId es null, reorganizar todos los procesos agrupados por máquina
  const filterByMachine = (process) => {
    if (machineId === null) {
      // Si no se especifica machineId, incluir todos los procesos que tengan machine_id
      return process.machine_id !== undefined && process.machine_id !== null;
    } else {
      // Si se especifica machineId, filtrar solo por esa máquina
      return process.machine_id === machineId;
    }
  };

  const completedProcesses = processes.filter(p =>
    filterByMachine(p) && p.status === 'Completado'
  );

  const activeProcesses = processes.filter(p =>
    filterByMachine(p) && p.status === 'En Proceso'
  );

  const pendingProcesses = processes.filter(p =>
    filterByMachine(p) && p.status === 'Pendiente'
  );

  const canceledProcesses = processes.filter(p =>
    filterByMachine(p) && p.status === 'Cancelado'
  );

  console.log(`Encontrados: ${completedProcesses.length} completados, ${activeProcesses.length} en proceso, ${pendingProcesses.length} pendientes, ${canceledProcesses.length} cancelados`);

  // Si no hay procesos activos, pendientes ni cancelados para reorganizar, retornar vacío
  if (activeProcesses.length === 0 && pendingProcesses.length === 0 && canceledProcesses.length === 0) {
    return [];
  }

  // Crear una copia de la hora de inicio (hora actual)
  let currentTime = new Date(startTime);

  // Mover al siguiente día laborable
  // Avanzar al día siguiente
  currentTime.setDate(currentTime.getDate() + 1);
  // Establecer la hora a las 8:00 AM
  currentTime.setHours(8, 0, 0, 0);

  console.log(`Moviendo todos los trabajos pendientes y en proceso al siguiente día laborable: ${format(currentTime, 'yyyy-MM-dd HH:mm')}`);

  // Verificar si es fin de semana (0 = domingo, 6 = sábado)
  const dayOfWeek = currentTime.getDay();
  if (dayOfWeek === 0) { // Si es domingo, mover al lunes
    currentTime.setDate(currentTime.getDate() + 1);
  } else if (dayOfWeek === 6) { // Si es sábado, mover al lunes
    currentTime.setDate(currentTime.getDate() + 2);
  }

  // Ordenar procesos activos y pendientes por fecha de inicio
  const sortedActiveProcesses = [...activeProcesses].sort((a, b) =>
    new Date(a.start_date) - new Date(b.start_date)
  );

  const sortedPendingProcesses = [...pendingProcesses].sort((a, b) =>
    new Date(a.start_date) - new Date(b.start_date)
  );

  // Combinar procesos activos y pendientes
  let processesToReorganize = [...sortedActiveProcesses, ...sortedPendingProcesses];

  // Si machineId es null, agrupar procesos por máquina
  if (machineId === null) {
    // Agrupar procesos por machine_id
    const processesByMachine = {};

    processesToReorganize.forEach(process => {
      if (!processesByMachine[process.machine_id]) {
        processesByMachine[process.machine_id] = [];
      }
      processesByMachine[process.machine_id].push(process);
    });

    // Reorganizar cada grupo de máquinas por separado
    const allReorganizedProcesses = [];

    for (const machId in processesByMachine) {
      const machineProcesses = processesByMachine[machId];
      console.log(`Reorganizando ${machineProcesses.length} procesos para máquina ${machId}`);

      // Llamar recursivamente a reorganizeProcesses para cada máquina
      const reorganizedMachineProcesses = await reorganizeProcesses(
        [...processes], // Pasar todos los procesos para mantener el contexto
        machId,         // Pasar el ID de máquina específico
        startTime       // Mantener la misma hora de inicio
      );

      // Añadir los procesos reorganizados al resultado final
      allReorganizedProcesses.push(...reorganizedMachineProcesses);
    }

    return allReorganizedProcesses;
  }

  // Calcular la duración total de todos los procesos a reorganizar en milisegundos
  const totalProcessDurationMs = processesToReorganize.reduce((total, process) =>
    total + (process.estimated_hours * 60 * 60 * 1000), 0
  );

  console.log(`Duración total de procesos a reorganizar: ${totalProcessDurationMs / (60 * 60 * 1000)} horas`);

  // Un día laboral tiene 12 horas (8:00 - 20:00)
  const workdayDurationMs = 12 * 60 * 60 * 1000;

  // Reorganizar procesos
  const reorganizedProcesses = [];
  let currentWorkday = 0;
  let currentDayTimeUsedMs = 0;

  // Para cada proceso a reorganizar
  for (let i = 0; i < processesToReorganize.length; i++) {
    const process = processesToReorganize[i];
    const processDurationMs = process.estimated_hours * 60 * 60 * 1000;

    // Ya no mantenemos ningún proceso en su fecha original
    // Todos los procesos pendientes y en proceso se moverán al siguiente día laborable

    // Si este proceso no cabe en el día actual, pasar al siguiente día
    if (currentDayTimeUsedMs + processDurationMs > workdayDurationMs) {
      currentWorkday++;
      currentDayTimeUsedMs = 0;
    }

    // Calcular la fecha de inicio para este proceso
    const processDate = new Date(currentTime);
    processDate.setDate(processDate.getDate() + currentWorkday);
    processDate.setHours(8, 0, 0, 0); // Comenzar a las 8:00

    // Añadir el tiempo usado en el día actual
    const processStartMs = currentDayTimeUsedMs;
    processDate.setTime(processDate.getTime() + processStartMs);

    // Asegurarse de que la fecha de inicio no sea anterior a la hora actual
    if (processDate < startTime) {
      // Ajustar para comenzar desde la hora actual
      processDate.setTime(startTime.getTime());

      // Si la hora actual está fuera del horario laboral, ajustar
      if (processDate.getHours() < 8) {
        processDate.setHours(8, 0, 0, 0);
      } else if (processDate.getHours() >= 20) {
        processDate.setDate(processDate.getDate() + 1);
        processDate.setHours(8, 0, 0, 0);
        currentWorkday++;
      }

      // Recalcular el tiempo usado en el día
      currentDayTimeUsedMs = (processDate.getHours() - 8) * 60 * 60 * 1000 +
                            processDate.getMinutes() * 60 * 1000;
    }

    // Calcular la fecha de fin
    const processEndDate = new Date(processDate);
    processEndDate.setTime(processDate.getTime() + processDurationMs);

    // Verificar si hay solapamiento con procesos completados
    let hasOverlap = false;
    for (const completedProcess of completedProcesses) {
      const completedStart = new Date(completedProcess.start_date);
      const completedEnd = new Date(completedProcess.end_date);

      // Verificar solapamiento
      if ((processDate >= completedStart && processDate < completedEnd) ||
          (processEndDate > completedStart && processEndDate <= completedEnd) ||
          (processDate <= completedStart && processEndDate >= completedEnd)) {
        hasOverlap = true;
        break;
      }
    }

    // Si hay solapamiento, intentar mover a otro horario
    if (hasOverlap) {
      // Intentar colocar después del último proceso completado de ese día
      const sameDayCompletedProcesses = completedProcesses.filter(p => {
        const pDate = new Date(p.end_date);
        return pDate.getDate() === processDate.getDate() &&
               pDate.getMonth() === processDate.getMonth() &&
               pDate.getFullYear() === processDate.getFullYear();
      });

      if (sameDayCompletedProcesses.length > 0) {
        // Ordenar por hora de fin
        sameDayCompletedProcesses.sort((a, b) =>
          new Date(a.end_date) - new Date(b.end_date)
        );

        // Tomar el último proceso completado
        const lastCompleted = sameDayCompletedProcesses[sameDayCompletedProcesses.length - 1];
        const newStart = new Date(lastCompleted.end_date);

        // Asegurarse de que la nueva fecha de inicio no sea anterior a la hora actual
        if (newStart < startTime) {
          newStart.setTime(startTime.getTime());
        }

        // Si terminaría después de las 20:00, mover al día siguiente
        const newEnd = new Date(newStart);
        newEnd.setTime(newStart.getTime() + processDurationMs);

        if (newEnd.getHours() >= 20 || (newEnd.getHours() === 19 && newEnd.getMinutes() > 0)) {
          // Mover al día siguiente
          currentWorkday++;
          currentDayTimeUsedMs = 0;

          // Recalcular fechas
          const nextDayDate = new Date(currentTime);
          nextDayDate.setDate(nextDayDate.getDate() + currentWorkday);
          nextDayDate.setHours(8, 0, 0, 0);

          processDate.setTime(nextDayDate.getTime());
          processEndDate.setTime(processDate.getTime() + processDurationMs);
        } else {
          // Usar el horario después del último completado
          processDate.setTime(newStart.getTime());
          processEndDate.setTime(processDate.getTime() + processDurationMs);

          // Actualizar el tiempo usado en el día
          currentDayTimeUsedMs = (processEndDate.getHours() - 8) * 60 * 60 * 1000 +
                                processEndDate.getMinutes() * 60 * 1000;
        }
      }
    }

    // Crear una copia del proceso con las nuevas fechas, manteniendo todas las propiedades originales
    const updatedProcess = {
      ...process,
      start_date: processDate.toISOString(),
      end_date: processEndDate.toISOString()
    };

    // Añadir proceso reorganizado
    reorganizedProcesses.push(updatedProcess);

    // Actualizar el tiempo usado en el día actual
    currentDayTimeUsedMs += processDurationMs;
  }

  // Reorganizar procesos cancelados (fuera del horario laboral, en la parte superior)
  for (const canceledProcess of canceledProcesses) {
    // Obtener la fecha original del proceso cancelado
    const originalDate = new Date(canceledProcess.start_date);

    // Crear nueva fecha manteniendo el mismo día pero a las 6:00 AM (fuera del horario laboral)
    const canceledDate = new Date(originalDate);
    canceledDate.setHours(6, 0, 0, 0);

    // Calcular fecha de fin (1 hora de duración simbólica)
    const canceledEndDate = new Date(canceledDate);
    canceledEndDate.setHours(7, 0, 0, 0);

    // Crear una copia del proceso con las nuevas fechas, manteniendo todas las propiedades originales
    const updatedCanceledProcess = {
      ...canceledProcess,
      start_date: canceledDate.toISOString(),
      end_date: canceledEndDate.toISOString()
    };

    // Añadir proceso cancelado reorganizado
    reorganizedProcesses.push(updatedCanceledProcess);
  }

  return reorganizedProcesses;
};
