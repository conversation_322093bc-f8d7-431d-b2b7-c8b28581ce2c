import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Button,
  Menu,
  MenuItem,
  Avatar,
  Typography,
  Divider,
  ListItemIcon
} from '@mui/material';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import ExitToAppIcon from '@mui/icons-material/ExitToApp';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import KeyIcon from '@mui/icons-material/Key';

const UserMenu = () => {
  const { user, logout, role } = useAuth();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    logout();
  };

  const handleAdminUsers = () => {
    handleClose();
    navigate('/admin/users');
  };

  const handleChangePassword = () => {
    handleClose();
    // Implementar cambio de contraseña
    alert('Funcionalidad de cambio de contraseña no implementada');
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Button
        color="inherit"
        onClick={handleClick}
        startIcon={
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: 'primary.dark',
              fontSize: '0.875rem'
            }}
          >
            {user?.username?.charAt(0).toUpperCase() || 'U'}
          </Avatar>
        }
        endIcon={null}
        sx={{ textTransform: 'none', ml: 2 }}
      >
        <Typography variant="body1" sx={{ ml: 1 }}>
          {user?.username || 'Usuario'}
        </Typography>
      </Button>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          paper: {
            elevation: 3,
            sx: { minWidth: 200 }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
            {user?.username || 'Usuario'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {user?.email || '<EMAIL>'}
          </Typography>
          <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
            Rol: {role === 'admin' ? 'Administrador' : 'Usuario'}
          </Typography>
        </Box>

        <Divider />

        <MenuItem onClick={handleChangePassword}>
          <ListItemIcon>
            <KeyIcon fontSize="small" />
          </ListItemIcon>
          Cambiar contraseña
        </MenuItem>

        {role === 'admin' && (
          <MenuItem onClick={handleAdminUsers}>
            <ListItemIcon>
              <AdminPanelSettingsIcon fontSize="small" />
            </ListItemIcon>
            Gestión de usuarios
          </MenuItem>
        )}

        <Divider />

        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <ExitToAppIcon fontSize="small" />
          </ListItemIcon>
          Cerrar sesión
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default UserMenu;
