from fastapi import APIRouter, HTTPException, Request, status, Body, Depends
from typing import List, Optional, Dict, Any
import json
import os
import random
from datetime import datetime
from models.budget import Budget, BudgetPart
from models.models import SimpleBudget
from models.activity_log import ActivityType
from services.activity_log_service import ActivityLogService
from dependencies.auth import get_current_active_user
from routes.activity_logs import log_activity
import httpx
import uuid
from utils.catalog_manager import load_product_catalog, load_paper_catalog, load_machine_catalog

router = APIRouter()

# Crear instancia del servicio de logs
activity_log_service = ActivityLogService()

# Ruta al archivo JSON donde se guardarán los presupuestos
BUDGETS_FILE = "data/budgets.json"

# Asegurarse de que el directorio data existe
os.makedirs(os.path.dirname(BUDGETS_FILE), exist_ok=True)

# Función para cargar presupuestos desde el archivo JSON
def load_budgets():
    try:
        if os.path.exists(BUDGETS_FILE):
            with open(BUDGETS_FILE, "r", encoding="utf-8") as file:
                return json.load(file)
        return []
    except Exception as e:
        print(f"Error al cargar presupuestos: {e}")
        return []

# Función para guardar presupuestos en el archivo JSON
def save_budgets(budgets):
    try:
        # Verificar que todos los presupuestos tienen client_data completo
        for budget in budgets:
            if budget.get("client_data") and "discount_percentage" not in budget["client_data"]:
                # Asignar un valor por defecto si no existe
                budget["client_data"]["discount_percentage"] = 10.0

        # Guardar los presupuestos en el archivo
        with open(BUDGETS_FILE, "w", encoding="utf-8") as file:
            json.dump(budgets, file, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Error al guardar presupuestos: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al guardar presupuestos: {e}"
        )

# Función para calcular el coste total de un presupuesto y generar una estructura organizada de costes
def calculate_total_cost(budget_dict):
    """Calcula el coste total de un presupuesto sumando los costes de partes, procesos y envío
    y genera una estructura organizada de costes"""

    # Inicializar la estructura de costes
    costs_structure = {
        "parts": [],
        "processes": [],
        "shipping": {},
        "summary": {
            "parts_total": 0,
            "processes_total": 0,
            "shipping_total": 0,
            "total": 0
        }
    }

    # Procesar los costes de las partes
    parts_total = 0
    if budget_dict.get("parts") and len(budget_dict["parts"]) > 0:
        for part in budget_dict["parts"]:
            # Extraer los costes individuales directamente de la parte
            # Sin recalcular ni modificar los valores originales
            paper_cost = part.get("paper_cost", 0)
            machine_cost = part.get("machine_cost", 0)
            plate_cost = part.get("plate_cost", 0)
            ink_cost = part.get("ink_cost", 0)
            click_cost = part.get("click_cost", 0)

            # Calcular el coste de maculatura si no está presente pero tenemos los datos necesarios
            maculatura_cost = part.get("maculatura_cost", 0)

            # Si el coste de maculatura es 0, intentamos calcularlo a partir del cálculo de offset
            if maculatura_cost == 0 and part.get("offset_calculation"):
                # Si ya está calculado en offset_calculation, usamos ese valor
                if "maculatura_cost" in part["offset_calculation"]:
                    maculatura_cost = part["offset_calculation"]["maculatura_cost"]
                    print(f"Usando maculatura_cost de offset_calculation: {maculatura_cost}")
                # Si no, lo calculamos a partir de total_maculatura y paper_cost_per_1000
                elif "total_maculatura" in part["offset_calculation"] and part["offset_calculation"]["total_maculatura"] > 0:
                    if "paper_cost_per_1000" in part["offset_calculation"]:
                        paper_cost_per_sheet = part["offset_calculation"]["paper_cost_per_1000"] / 1000
                        total_maculatura = part["offset_calculation"]["total_maculatura"]
                        maculatura_cost = paper_cost_per_sheet * total_maculatura
                        print(f"Calculando maculatura_cost: {total_maculatura} hojas * {paper_cost_per_sheet} = {maculatura_cost}")

            # Imprimir información detallada para depuración de maculatura
            print(f"DEBUG - Maculatura para parte {part.get('name', 'Sin nombre')}:")
            print(f"  - maculatura_cost final: {maculatura_cost}")
            if part.get("offset_calculation"):
                if "maculatura_cost" in part["offset_calculation"]:
                    print(f"  - maculatura_cost en offset_calculation: {part['offset_calculation']['maculatura_cost']}")
                if "total_maculatura" in part["offset_calculation"]:
                    print(f"  - total_maculatura en offset_calculation: {part['offset_calculation']['total_maculatura']}")
                if "paper_cost_per_1000" in part["offset_calculation"]:
                    print(f"  - paper_cost_per_1000 en offset_calculation: {part['offset_calculation']['paper_cost_per_1000']}")


            # Calcular el subtotal de la parte
            part_subtotal = paper_cost + machine_cost + plate_cost + ink_cost + click_cost + maculatura_cost

            # Crear la estructura de costes para esta parte
            part_costs = {
                "part_id": part.get("part_id", ""),
                "name": part.get("name", "Sin nombre"),
                "costs": {
                    "paper": round(paper_cost, 2),
                    "machine": round(machine_cost, 2),
                    "plates": round(plate_cost, 2),
                    "ink": round(ink_cost, 2),
                    "click": round(click_cost, 2),
                    "maculatura": round(maculatura_cost, 2),
                    "subtotal": round(part_subtotal, 2)
                }
            }

            # Imprimir información detallada para depuración
            print(f"Parte {part.get('name', 'Sin nombre')} - Costes detallados:")
            print(f"  - paper_cost: {paper_cost}")
            print(f"  - machine_cost: {machine_cost}")
            print(f"  - plate_cost: {plate_cost}")
            print(f"  - ink_cost: {ink_cost}")
            print(f"  - click_cost: {click_cost}")
            print(f"  - maculatura_cost: {maculatura_cost}")
            print(f"  - subtotal: {part_subtotal}")

            # Añadir a la estructura de costes
            costs_structure["parts"].append(part_costs)

            # Sumar al total de partes
            parts_total += part_subtotal

    # Procesar los costes de los procesos
    processes_total = 0

    # Primero verificar si existe process_costs (nueva estructura)
    if budget_dict.get("process_costs") and len(budget_dict["process_costs"]) > 0:
        for process in budget_dict["process_costs"]:
            process_cost = process.get("total_cost", 0)

            # Crear la estructura de costes para este proceso
            process_costs = {
                "process_id": process.get("process_id", ""),
                "name": process.get("name", "Sin nombre"),
                "cost": round(process_cost, 2)
            }

            # Añadir a la estructura de costes
            costs_structure["processes"].append(process_costs)

            # Sumar al total de procesos
            processes_total += process_cost

    # Si ya tenemos el total de procesos calculado, usarlo (compatibilidad con estructura antigua)
    elif budget_dict.get("process_total_cost") is not None:
        processes_total = budget_dict["process_total_cost"]

        # Si no tenemos process_costs pero tenemos processes, usarlos para la estructura
        if budget_dict.get("processes") and len(budget_dict["processes"]) > 0:
            for process in budget_dict["processes"]:
                process_cost = process.get("total_cost", 0)

                # Crear la estructura de costes para este proceso
                process_costs = {
                    "process_id": process.get("process_id", ""),
                    "name": process.get("name", "Sin nombre"),
                    "cost": round(process_cost, 2)
                }

                # Añadir a la estructura de costes
                costs_structure["processes"].append(process_costs)

    # Procesar el coste de envío
    shipping_cost = 0
    shipping_weight = 0
    shipping_country = ""
    shipping_distance_factor = 1.0

    # Obtener datos de envío de la estructura shipping o de los campos principales
    if budget_dict.get("shipping") and isinstance(budget_dict["shipping"], dict):
        shipping_cost = budget_dict["shipping"].get("cost", 0)
        shipping_weight = budget_dict["shipping"].get("weight_kg", 0)  # Corregido: weight_kg en lugar de weight
        shipping_country = budget_dict["shipping"].get("country", "")
        shipping_distance_factor = budget_dict["shipping"].get("distance_factor", 1.0)
    elif budget_dict.get("shipping_cost") is not None:
        shipping_cost = budget_dict["shipping_cost"]
        shipping_weight = budget_dict.get("total_paper_weight_kg", 0)

    # Asegurarse de usar el peso total correcto (que incluye maculatura)
    if budget_dict.get("total_paper_weight_kg", 0) > 0:
        shipping_weight = budget_dict["total_paper_weight_kg"]
    
    # Si tenemos la estructura shipping, asegurarnos de que el peso sea consistente
    if budget_dict.get("shipping") and isinstance(budget_dict["shipping"], dict):
        # Actualizar shipping.weight_kg si es necesario
        if shipping_weight > 0 and budget_dict["shipping"].get("weight_kg", 0) != shipping_weight:
            budget_dict["shipping"]["weight_kg"] = shipping_weight
            print(f"Actualizando shipping.weight_kg a {shipping_weight} kg")
    
    # Crear la estructura de costes para el envío
    costs_structure["shipping"] = {
        "weight_kg": shipping_weight,
        "country": shipping_country,
        "distance_factor": shipping_distance_factor,
        "cost": round(shipping_cost, 2)
    }

    # Calcular el coste total sumando los subtotales
    total_cost = parts_total + processes_total + shipping_cost

    # Completar el resumen
    costs_structure["summary"] = {
        "parts_total": round(parts_total, 2),
        "processes_total": round(processes_total, 2),
        "shipping_total": round(shipping_cost, 2),
        "total": round(total_cost, 2)
    }

    # Imprimir información de depuración para verificar los cálculos
    print(f"\nCálculo de coste total:")
    print(f"  - Partes: {parts_total}")
    print(f"  - Procesos: {processes_total}")
    print(f"  - Envío: {shipping_cost}")
    print(f"  - TOTAL: {total_cost}\n")

    # Verificar si alguna parte tiene coste de tinta pero no se está incluyendo
    for part in budget_dict.get("parts", []):
        if part.get("ink_cost", 0) > 0:
            print(f"Parte {part.get('name', 'Sin nombre')} tiene coste de tinta: {part.get('ink_cost')}")
            if part.get("sheet_calculation") and "ink_cost" in part.get("sheet_calculation", {}):
                print(f"  - Coste de tinta en sheet_calculation: {part['sheet_calculation']['ink_cost']}")
            if part.get("offset_calculation") and "ink_cost" in part.get("offset_calculation", {}):
                print(f"  - Coste de tinta en offset_calculation: {part['offset_calculation']['ink_cost']}")
            if part.get("digital_calculation") and "ink_cost" in part.get("digital_calculation", {}):
                print(f"  - Coste de tinta en digital_calculation: {part['digital_calculation']['ink_cost']}")


    # Mostrar la estructura de costes en formato JSON para mejor visualización
    import json
    print("\nEstructura de costes (JSON):")
    print(json.dumps(costs_structure, indent=2, ensure_ascii=False))

    # Devolver tanto el coste total como la estructura completa
    return total_cost, costs_structure

# Función para obtener los datos completos del cliente y de las partes
async def get_complete_data(budget_dict):
    try:
        # Si hay un client_id, obtener los datos completos del cliente
        if budget_dict.get("client_id"):
            async with httpx.AsyncClient() as client:
                response = await client.get(f"http://localhost:3005/clients/{budget_dict['client_id']}")
                if response.status_code == 200:
                    client_data = response.json()
                    budget_dict["client_data"] = client_data

        # Si hay partes, obtener los datos completos de cada parte
        if budget_dict.get("parts") and isinstance(budget_dict["parts"], list):
            total_paper_weight_kg = 0.0

            for part in budget_dict["parts"]:
                # Si tenemos sheet_calculation, usamos esa información como fuente principal
                if part.get("sheet_calculation") and part["sheet_calculation"].get("machine_type"):
                    # Actualizar machine_type SOLO desde sheet_calculation.machine_type
                    part["machine_type"] = part["sheet_calculation"]["machine_type"]
                    print(f"Actualizado machine_type a {part['machine_type']} para la parte {part.get('part_id')} desde sheet_calculation.machine_type")
                # Si no tenemos machine_type en sheet_calculation pero tenemos machine_id, obtener el tipo de la máquina del catálogo
                elif part.get("machine_id") and not part.get("machine_type"):
                    # Cargar el catálogo de máquinas
                    from utils.catalog_manager import load_machines
                    machines = load_machines()
                    machine = next((m for m in machines if m.get("machine_id") == part["machine_id"]), None)
                    if machine and machine.get("type"):
                        part["machine_type"] = machine["type"]
                        # Si también tenemos sheet_calculation, actualizar ahí también
                        if part.get("sheet_calculation"):
                            part["sheet_calculation"]["machine_type"] = machine["type"]
                        print(f"Actualizado machine_type a {part['machine_type']} para la parte {part.get('part_id')} desde el catálogo de máquinas")

                # Obtener datos del papel para esta parte si es necesario
                if part.get("paper_id") and not part.get("paper_data"):
                    async with httpx.AsyncClient() as client:
                        response = await client.get(f"http://localhost:3005/papers/{part['paper_id']}")
                        if response.status_code == 200:
                            part["paper_data"] = response.json()

                # Asegurarse de que el coste de tinta se transfiera correctamente
                # Si hay un cálculo de offset, transferir el coste de tinta a la parte
                if part.get("offset_calculation") and "ink_cost" in part["offset_calculation"]:
                    part["ink_cost"] = part["offset_calculation"]["ink_cost"]
                    print(f"Transferido coste de tinta de offset_calculation a parte: {part['ink_cost']}")
                # Si hay un cálculo de pliego, transferir el coste de tinta a la parte
                elif part.get("sheet_calculation") and "ink_cost" in part["sheet_calculation"]:
                    part["ink_cost"] = part["sheet_calculation"]["ink_cost"]
                    print(f"Transferido coste de tinta de sheet_calculation a parte: {part['ink_cost']}")
                # Si hay un cálculo digital, transferir el coste de tinta a la parte
                elif part.get("digital_calculation") and "ink_cost" in part["digital_calculation"]:
                    part["ink_cost"] = part["digital_calculation"]["ink_cost"]
                    print(f"Transferido coste de tinta de digital_calculation a parte: {part['ink_cost']}")

                # Asegurarse de que el coste de maculatura se transfiera correctamente
                # Si hay un cálculo de offset, transferir el coste de maculatura a la parte
                if part.get("offset_calculation") and "maculatura_cost" in part["offset_calculation"]:
                    part["maculatura_cost"] = part["offset_calculation"]["maculatura_cost"]
                    print(f"Transferido coste de maculatura de offset_calculation a parte: {part['maculatura_cost']}")
                # Si hay un cálculo de pliego, transferir el coste de maculatura a la parte
                elif part.get("sheet_calculation") and "maculatura_cost" in part["sheet_calculation"]:
                    part["maculatura_cost"] = part["sheet_calculation"]["maculatura_cost"]
                    print(f"Transferido coste de maculatura de sheet_calculation a parte: {part['maculatura_cost']}")
                # Si no hay coste de maculatura pero sí hay cantidad de maculatura, calcular el coste
                elif part.get("offset_calculation") and "total_maculatura" in part["offset_calculation"] and part["offset_calculation"]["total_maculatura"] > 0:
                    # Si tenemos el coste por hoja de papel, podemos calcular el coste de maculatura
                    if part["offset_calculation"].get("paper_cost_per_1000"):
                        paper_cost_per_sheet = part["offset_calculation"]["paper_cost_per_1000"] / 1000
                        total_maculatura = part["offset_calculation"]["total_maculatura"]
                        part["maculatura_cost"] = paper_cost_per_sheet * total_maculatura
                        print(f"Calculado coste de maculatura: {total_maculatura} hojas * {paper_cost_per_sheet} = {part['maculatura_cost']}")

                # Calcular el peso del papel si tenemos los datos necesarios
                if part.get("paper_data") and part.get("sheet_calculation") and part["sheet_calculation"].get("mejor_combinacion"):
                    # Obtener el número total de pliegos físicos
                    total_sheets = 0
                    for esquema in part["sheet_calculation"]["mejor_combinacion"]["esquemas_utilizados"]:
                        # Si es Tira y Retira, cada pliego físico produce 2 unidades
                        factor = 0.5 if esquema.get("es_tira_retira") else 1
                        total_sheets += esquema["numero_pliegos"] * factor * budget_dict["quantity"]
                    
                    # Añadir la maculatura al total de pliegos para el cálculo del peso
                    total_maculatura_sheets = 0
                    if part.get("offset_calculation") and "total_maculatura" in part["offset_calculation"]:
                        total_maculatura_sheets = part["offset_calculation"]["total_maculatura"]
                        print(f"Añadiendo {total_maculatura_sheets} pliegos de maculatura al cálculo del peso")
                    
                    # Total de pliegos incluyendo maculatura
                    total_sheets_with_maculatura = total_sheets + total_maculatura_sheets

                    # Importar la función para calcular el peso del papel
                    from utils.paper_manager import calculate_paper_weight

                    # Calcular el peso del papel incluyendo maculatura
                    paper_weight_kg = calculate_paper_weight(part["paper_data"], total_sheets_with_maculatura)

                    # Guardar el peso del papel en la parte
                    part["sheet_calculation"]["paper_weight_kg"] = round(paper_weight_kg, 2)
                    total_paper_weight_kg += paper_weight_kg

                # Calcular el coste de envío basado en el peso total
                if total_paper_weight_kg > 0:
                    # Obtener el país del cliente sin valor predeterminado
                    country = ""
                    if budget_dict.get("client_data") and budget_dict["client_data"].get("company") and budget_dict["client_data"]["company"].get("address"):
                        country = budget_dict["client_data"]["company"]["address"].get("country", "")

                    # Importar la función para calcular el coste de envío
                    from routes.shipping_cost import calculate_shipping_cost_by_weight, get_distance_factor

                    # Calcular el coste de envío
                    shipping_cost = calculate_shipping_cost_by_weight(total_paper_weight_kg, country)
                    distance_factor = get_distance_factor(country)

                    # Guardar el peso total del papel y el coste de envío en el presupuesto
                    budget_dict["total_paper_weight_kg"] = round(total_paper_weight_kg, 2)
                    budget_dict["shipping_cost"] = round(shipping_cost, 2)
                    
                    # Actualizar la estructura shipping del presupuesto
                    if "shipping" not in budget_dict:
                        budget_dict["shipping"] = {}
                    
                    # Actualizar con los valores correctos
                    budget_dict["shipping"]["weight_kg"] = round(total_paper_weight_kg, 2)
                    budget_dict["shipping"]["country"] = country
                    budget_dict["shipping"]["distance_factor"] = distance_factor
                    budget_dict["shipping"]["cost"] = round(shipping_cost, 2)
                    
                    # Actualizar también costs.shipping si existe para mantener consistencia
                    if "costs" in budget_dict and "shipping" in budget_dict["costs"]:
                        budget_dict["costs"]["shipping"]["weight_kg"] = round(total_paper_weight_kg, 2)
                        budget_dict["costs"]["shipping"]["country"] = country
                        budget_dict["costs"]["shipping"]["distance_factor"] = distance_factor
                        budget_dict["costs"]["shipping"]["cost"] = round(shipping_cost, 2)

        return budget_dict
    except Exception as e:
        print(f"Error al obtener datos completos: {e}")
        return budget_dict  # Devolver el presupuesto original si hay error

# Obtener todos los presupuestos
@router.get("/", response_model=List[Budget])
async def get_all_budgets(current_user: Dict[str, Any] = Depends(get_current_active_user)):
    return load_budgets()

# Endpoint de prueba para verificar que la ruta funciona
@router.get("/{budget_id}/test")
async def test_budget_route(budget_id: str, current_user: Dict[str, Any] = Depends(get_current_active_user)):
    return {"status": "success", "message": f"Ruta de prueba para el presupuesto {budget_id}"}

# Actualizar el descuento del cliente para un presupuesto específico
@router.put("/{budget_id}/discount")
async def update_budget_discount(budget_id: str, discount_percentage: float = Body(..., embed=True), current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Actualiza el porcentaje de descuento del cliente para un presupuesto específico.
    """
    print(f"Recibida solicitud para actualizar descuento del presupuesto {budget_id} a {discount_percentage}%")
    try:
        budgets = load_budgets()
        print(f"Cargados {len(budgets)} presupuestos")

        # Buscar el presupuesto
        budget = next((b for b in budgets if b["budget_id"] == budget_id), None)
        if not budget:
            print(f"Presupuesto {budget_id} no encontrado")
            raise HTTPException(status_code=404, detail="Presupuesto no encontrado")

        print(f"Presupuesto encontrado: {budget['budget_id']}")

        # Verificar que el presupuesto tiene client_data
        if not budget.get("client_data"):
            print("El presupuesto no tiene client_data")
            # Si no tiene client_data, obtener los datos del cliente
            if budget.get("client_id"):
                print(f"Obteniendo datos del cliente {budget['client_id']}")
                async with httpx.AsyncClient() as client:
                    response = await client.get(f"http://localhost:3005/clients/{budget['client_id']}")
                    if response.status_code == 200:
                        budget["client_data"] = response.json()
                        print("Datos del cliente obtenidos correctamente")
                    else:
                        print(f"Error al obtener datos del cliente: {response.status_code}")
                        raise HTTPException(status_code=404, detail="Cliente no encontrado")
            else:
                print("El presupuesto no tiene cliente asignado")
                raise HTTPException(status_code=400, detail="El presupuesto no tiene cliente asignado")

        # Actualizar el descuento
        # Asegurarse de que client_data es un diccionario completo
        if not isinstance(budget['client_data'], dict):
            budget['client_data'] = {}

        # Actualizar el descuento
        budget["client_data"]["discount_percentage"] = discount_percentage

        # Actualizar la fecha de actualización
        budget["updated_at"] = datetime.now().isoformat()

        # Guardar los cambios
        save_budgets(budgets)
        print("Cambios guardados correctamente")

        return {"status": "success", "message": "Descuento actualizado correctamente", "budget_id": budget_id, "discount_percentage": discount_percentage, "client_data": budget["client_data"]}
    except Exception as e:
        print(f"Error al actualizar el descuento: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error al actualizar el descuento: {str(e)}")

# Obtener un presupuesto por ID
@router.get("/{budget_id}", response_model=Budget)
async def get_budget(budget_id: str, current_user: Dict[str, Any] = Depends(get_current_active_user)):
    budgets = load_budgets()
    for budget in budgets:
        if budget["budget_id"] == budget_id:
            # Eliminar la propiedad shipping duplicada si existe
            if "shipping" in budget:
                print(f"Eliminando propiedad shipping duplicada para presupuesto {budget_id}")
                del budget["shipping"]
                # Guardar los cambios
                save_budgets(budgets)
                
            # Asegurarse de que costs.shipping existe
            if "costs" in budget and "shipping" not in budget["costs"]:
                # Si no existe costs.shipping, crearlo con valores predeterminados
                budget["costs"]["shipping"] = {
                    "weight_kg": budget.get("total_paper_weight_kg", 0),
                    "country": "",
                    "distance_factor": 1.0,
                    "cost": budget.get("shipping_cost", 0)
                }
                print(f"Creando costs.shipping para presupuesto {budget_id}")
                # Guardar los cambios
                save_budgets(budgets)
            
            return budget
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Presupuesto con ID {budget_id} no encontrado"
    )

# Crear un nuevo presupuesto
@router.post("/", response_model=Budget, status_code=status.HTTP_201_CREATED)
async def create_budget(request: Request, budget: Budget, current_user: Dict[str, Any] = Depends(get_current_active_user)):
    budgets = load_budgets()

    # Convertir el modelo Pydantic a diccionario
    budget_dict = budget.dict()


    # Actualizar la fecha de creación y actualización
    current_time = datetime.now().isoformat()
    budget_dict["created_at"] = current_time
    budget_dict["updated_at"] = current_time

    # Añadir información del usuario que crea el presupuesto
    budget_dict["created_by"] = {
        "user_id": current_user["user_id"],
        "username": current_user["username"],
        "email": current_user.get("email", ""),
        "timestamp": current_time
    }

    # Inicializar el historial de actualizaciones
    budget_dict["updated_by"] = {
        "user_id": current_user["user_id"],
        "username": current_user["username"],
        "email": current_user.get("email", ""),
        "timestamp": current_time
    }

    # Verificar si hay partes en product_config y moverlas a parts si es necesario
    if "product_config" in budget_dict and budget_dict["product_config"] and "parts" in budget_dict["product_config"]:
        if not budget_dict.get("parts"):
            budget_dict["parts"] = budget_dict["product_config"]["parts"]
            print(f"Moviendo partes de product_config a parts: {len(budget_dict['parts'])} partes")
        del budget_dict["product_config"]["parts"]

    # Imprimir información de depuración
    print(f"Creando presupuesto con {len(budget_dict.get('parts', []))} partes")

    # Obtener datos completos de cliente, papel y máquina
    budget_dict = await get_complete_data(budget_dict)

    # Calcular y guardar el coste total y la estructura de costes
    total_cost, costs_structure = calculate_total_cost(budget_dict)
    budget_dict["total_cost"] = total_cost
    budget_dict["costs"] = costs_structure
    print(f"\nPresupuesto creado - Coste total calculado: {total_cost}\n")

    # Mostrar un resumen del presupuesto en formato JSON
    import json
    budget_summary = {
        "budget_id": budget_dict["budget_id"],
        "ot_number": budget_dict["ot_number"],
        "description": budget_dict["description"].split("\n")[0] if "\n" in budget_dict["description"] else budget_dict["description"],
        "client_id": budget_dict["client_id"],
        "quantity": budget_dict["quantity"],
        "total_cost": total_cost,
        "costs_summary": costs_structure["summary"]
    }
    print("Resumen del presupuesto (JSON):")
    print(json.dumps(budget_summary, indent=2, ensure_ascii=False))

    budgets.append(budget_dict)
    save_budgets(budgets)

    # Registrar la creación del presupuesto
    log_activity(
        request=request,
        user_id=current_user["user_id"],
        username=current_user["username"],
        activity_type=ActivityType.BUDGET_CREATE,
        description=f"Creación del presupuesto {budget_dict['budget_id']} - {budget_dict.get('name', 'Sin nombre')}",
        entity_id=budget_dict["budget_id"],
        additional_data={
            "client_id": budget_dict.get("client_id"),
            "job_type": budget_dict.get("jobType"),
            "status": budget_dict.get("status"),
            "total_cost": budget_dict.get("totalCost")
        }
    )

    return budget_dict

# Actualizar un presupuesto existente
@router.put("/{budget_id}", response_model=Budget)
async def update_budget(request: Request, budget_id: str, updated_budget: Budget, current_user: Dict[str, Any] = Depends(get_current_active_user)):
    budgets = load_budgets()

    for i, budget in enumerate(budgets):
        if budget["budget_id"] == budget_id:
            # Convertir el modelo Pydantic a diccionario
            budget_dict = updated_budget.dict()

            # Eliminar 'client_data' si existe (solo guardar 'client_id')
            if "client_data" in budget_dict:
                del budget_dict["client_data"]

            # Mantener el ID original y la fecha de creación
            budget_dict["budget_id"] = budget_id
            budget_dict["created_at"] = budget["created_at"]

            # Actualizar la fecha de actualización
            current_time = datetime.now().isoformat()
            budget_dict["updated_at"] = current_time

            # Actualizar información del usuario que modifica el presupuesto
            budget_dict["updated_by"] = {
                "user_id": current_user["user_id"],
                "username": current_user["username"],
                "email": current_user.get("email", ""),
                "timestamp": current_time
            }

            # Mantener la información del creador original
            if "created_by" in budget:
                budget_dict["created_by"] = budget["created_by"]

            # Verificar si hay partes en product_config y moverlas a parts si es necesario
            if "product_config" in budget_dict and budget_dict["product_config"] and "parts" in budget_dict["product_config"]:
                if not budget_dict.get("parts"):
                    budget_dict["parts"] = budget_dict["product_config"]["parts"]
                    print(f"Moviendo partes de product_config a parts: {len(budget_dict['parts'])} partes")
                del budget_dict["product_config"]["parts"]

            # Imprimir información de depuración

            # Obtener datos completos de cliente, papel y máquina
            budget_dict = await get_complete_data(budget_dict)

            # Calcular y guardar el coste total y la estructura de costes
            total_cost, costs_structure = calculate_total_cost(budget_dict)
            budget_dict["total_cost"] = total_cost
            budget_dict["costs"] = costs_structure
            print(f"\nPresupuesto actualizado - Coste total calculado: {total_cost}\n")
            
            # Eliminar la propiedad shipping duplicada si existe
            if "shipping" in budget_dict:
                print(f"Eliminando propiedad shipping duplicada para presupuesto {budget_id}")
                del budget_dict["shipping"]
                
            # Asegurarse de que costs.shipping existe
            if "costs" in budget_dict and "shipping" not in budget_dict["costs"]:
                # Si no existe costs.shipping, crearlo con valores predeterminados
                budget_dict["costs"]["shipping"] = {
                    "weight_kg": budget_dict.get("total_paper_weight_kg", 0),
                    "country": "",
                    "distance_factor": 1.0,
                    "cost": budget_dict.get("shipping_cost", 0)
                }
                print(f"Creando costs.shipping para presupuesto {budget_id}")
                
            # Asegurarse de que los datos de envío en costs.shipping son correctos
            if "costs" in budget_dict and "shipping" in budget_dict["costs"]:
                # Actualizar el peso si ha cambiado
                if budget_dict.get("total_paper_weight_kg") and budget_dict["total_paper_weight_kg"] > 0:
                    budget_dict["costs"]["shipping"]["weight_kg"] = budget_dict["total_paper_weight_kg"]
                    
                # Actualizar el país si hay datos de cliente
                if budget_dict.get("client_data") and budget_dict["client_data"].get("company") and budget_dict["client_data"]["company"].get("address") and budget_dict["client_data"]["company"]["address"].get("country"):
                    budget_dict["costs"]["shipping"]["country"] = budget_dict["client_data"]["company"]["address"]["country"]

            # Mostrar un resumen del presupuesto en formato JSON
            import json
            budget_summary = {
                "budget_id": budget_dict["budget_id"],
                "ot_number": budget_dict["ot_number"],
                "description": budget_dict["description"].split("\n")[0] if "\n" in budget_dict["description"] else budget_dict["description"],
                "client_id": budget_dict["client_id"],
                "quantity": budget_dict["quantity"],
                "total_cost": total_cost,
                "costs_summary": costs_structure["summary"]
            }
            print("Resumen del presupuesto actualizado (JSON):")
            print(json.dumps(budget_summary, indent=2, ensure_ascii=False))

            # Eliminar 'client_data' si existe (solo guardar 'client_id')
            if "client_data" in budget_dict:
                del budget_dict["client_data"]

            # Verificar si el estado ha cambiado a "Aprobado"
            if budget_dict.get("status") == "Aprobado" and budget.get("status") != "Aprobado":
                # Importar aquí para evitar importaciones circulares
                from utils.production_manager import add_budget_to_production
                production_processes = add_budget_to_production(budget_dict)
                print(f"Se han creado {len(production_processes)} procesos de producción")

            # Registrar la actualización del presupuesto
            activity_type = ActivityType.BUDGET_UPDATE
            description = f"Actualización del presupuesto {budget_id} - {budget_dict.get('name', 'Sin nombre')}"

            # Si el estado ha cambiado a "Aprobado", registrar como aprobación
            if budget_dict.get("status") == "Aprobado" and budget.get("status") != "Aprobado":
                activity_type = ActivityType.BUDGET_APPROVE
                description = f"Aprobación del presupuesto {budget_id} - {budget_dict.get('name', 'Sin nombre')}"

            log_activity(
                request=request,
                user_id=current_user["user_id"],
                username=current_user["username"],
                activity_type=activity_type,
                description=description,
                entity_id=budget_id,
                additional_data={
                    "client_id": budget_dict.get("client_id"),
                    "job_type": budget_dict.get("jobType"),
                    "status": budget_dict.get("status"),
                    "total_cost": budget_dict.get("totalCost"),
                    "previous_status": budget.get("status")
                }
            )

            budgets[i] = budget_dict
            save_budgets(budgets)
            return budget_dict

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Presupuesto con ID {budget_id} no encontrado"
    )

# Eliminar un presupuesto
@router.delete("/{budget_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_budget(budget_id: str, current_user: Dict[str, Any] = Depends(get_current_active_user)):
    budgets = load_budgets()

    for i, budget in enumerate(budgets):
        if budget["budget_id"] == budget_id:
            del budgets[i]
            save_budgets(budgets)
            return

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"Presupuesto con ID {budget_id} no encontrado"
    )

# Buscar presupuestos por diferentes criterios
@router.get("/search/", response_model=List[Budget])
async def search_budgets(
    client_id: Optional[str] = None,
    status: Optional[str] = None,
    job_type: Optional[str] = None,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    budgets = load_budgets()
    filtered_budgets = []

    for budget in budgets:
        # Filtrar por client_id si se proporciona
        if client_id and budget["client_id"] != client_id:
            continue

        # Filtrar por status si se proporciona
        if status and budget["status"] != status:
            continue

        # Filtrar por job_type si se proporciona
        if job_type and budget["job_type"] != job_type:
            continue

        # Filtrar por fecha de creación si se proporciona date_from
        if date_from and budget["created_at"] < date_from:
            continue

        # Filtrar por fecha de creación si se proporciona date_to
        if date_to and budget["created_at"] > date_to:
            continue

        filtered_budgets.append(budget)

    return filtered_budgets



# Crear un presupuesto simplificado
@router.post("/simple", response_model=Budget, status_code=status.HTTP_201_CREATED)
async def create_simple_budget(simple_budget: SimpleBudget, current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Crea un presupuesto con valores por defecto a partir de un JSON simplificado.
    Solo requiere el tipo de trabajo y la cantidad de ejemplares.
    """
    try:
        print("Iniciando creación de presupuesto simplificado...")
        # Cargar los catálogos necesarios
        product_catalog = load_product_catalog()
        print(f"Catálogo de productos cargado: {len(product_catalog)} productos")
        paper_catalog = load_paper_catalog()
        print(f"Catálogo de papeles cargado: {len(paper_catalog)} papeles")
        machine_catalog = load_machine_catalog()
        print(f"Catálogo de máquinas cargado: {len(machine_catalog)} máquinas")

        # Buscar el producto en el catálogo por tipo
        print(f"Buscando producto de tipo: {simple_budget.job_type}")
        product = next((p for p in product_catalog if p["type"] == simple_budget.job_type), None)
        if not product:
            raise HTTPException(status_code=404, detail=f"Tipo de trabajo '{simple_budget.job_type}' no encontrado en el catálogo")
        print(f"Producto encontrado: {product['name']} (ID: {product['product_id']})")

        # Obtener el papel y la máquina por defecto
        print("Buscando papel por defecto (Pap-001)...")
        default_paper = next((p for p in paper_catalog if p["product_id"] == "Pap-001"), None)
        if default_paper:
            print(f"Papel encontrado: {default_paper['descriptive_name']} (ID: {default_paper['product_id']})")
        else:
            print("No se encontró el papel por defecto")

        print("Buscando máquina por defecto (MAQ-003)...")
        default_machine = next((m for m in machine_catalog if m["machine_id"] == "MAQ-003"), None)
        if default_machine:
            print(f"Máquina encontrada: {default_machine['name']} (ID: {default_machine['machine_id']})")
        else:
            print("No se encontró la máquina por defecto")

        if not default_paper or not default_machine:
            raise HTTPException(status_code=404, detail="No se encontraron los recursos por defecto (papel o máquina)")

        # Generar un número de OT
        date = datetime.now()
        year = date.strftime('%y')  # Año con 2 dígitos
        month = date.strftime('%m')  # Mes con 2 dígitos

        # Cargar presupuestos existentes para obtener el último número de OT
        budgets = load_budgets()

        # Encontrar el último número de OT
        last_ot_number = None
        for budget in budgets:
            if "ot_number" in budget and budget["ot_number"].startswith(f"OT-{year}{month}"):
                ot_num = budget["ot_number"].replace(f"OT-{year}{month}", "")
                if ot_num.isdigit() and (last_ot_number is None or int(ot_num) > last_ot_number):
                    last_ot_number = int(ot_num)

        # Generar el siguiente número
        if last_ot_number is not None:
            next_number = last_ot_number + 1
        else:
            next_number = 1000  # Número inicial si no hay presupuestos previos

        ot_number = f"OT-{year}{month}{next_number}"

        # Crear las partes del presupuesto según el tipo de producto
        print("Creando partes del presupuesto...")
        parts = []
        if product["parts"]:
            print(f"El producto tiene {len(product['parts'])} partes definidas")
            for i, part_template in enumerate(product["parts"]):
                print(f"Creando parte {i+1}: {part_template['name']}")
                # Determinar el número de páginas para esta parte
                page_count = 4  # Valor por defecto para la cubierta

                # Si es la parte "Interior" o similar, usar el número de páginas del JSON
                # Si es la única parte, usar el número de páginas del JSON
                if part_template["name"].lower() == "interior" or len(product["parts"]) == 1:
                    page_count = simple_budget.page_count

                print(f"Asignando {page_count} páginas a la parte {part_template['name']}")

                part = {
                    "part_id": f"part-{uuid.uuid4().hex[:6]}",
                    "name": part_template["name"],
                    "description": part_template["description"],
                    "assembly_order": product["assembly_order"],
                    "page_size": {"width": 210, "height": 297},  # A4 por defecto
                    "page_count": page_count,  # Número de páginas asignado
                    "paper_id": default_paper["product_id"],
                    "machine_id": default_machine["machine_id"],
                    "paper_cost": 0,
                    "machine_cost": 0,
                    "process_costs": [],
                    "total_cost": 0,
                    "colorConfig": {
                        "frontColors": 4,
                        "backColors": 4,
                        "pantones": 0
                    },
                    "custom_print_time": 1
                }
                print(f"Parte creada con ID: {part['part_id']}")
                parts.append(part)
        else:
            # Si no hay partes definidas, crear una parte genérica
            print("El producto no tiene partes definidas, creando parte genérica")
            part_id = f"part-{uuid.uuid4().hex[:6]}"
            print(f"Creando parte genérica con ID: {part_id}")
            # Usar el número de páginas del JSON para la parte genérica
            page_count = simple_budget.page_count
            print(f"Asignando {page_count} páginas a la parte genérica")

            generic_part = {
                "part_id": part_id,
                "name": "General",
                "description": "",
                "assembly_order": product["assembly_order"],
                "page_size": {"width": 210, "height": 297},  # A4 por defecto
                "page_count": page_count,  # Número de páginas del JSON
                "paper_id": default_paper["product_id"],
                "machine_id": default_machine["machine_id"],
                "paper_cost": 0,
                "machine_cost": 0,
                "process_costs": [],
                "total_cost": 0,
                "colorConfig": {
                    "frontColors": 4,
                    "backColors": 4,
                    "pantones": 0
                },
                "custom_print_time": 1
            }
            print(f"Parte genérica creada: {generic_part}")
            parts.append(generic_part)

        # Añadir los acabados del producto al presupuesto
        print("Añadiendo acabados del producto...")
        process_costs = []
        if product.get("finishing_processes"):
            print(f"El producto tiene {len(product['finishing_processes'])} acabados definidos")
            for process in product["finishing_processes"]:
                # Determinar la cantidad predeterminada según el tipo de unidad
                quantity = 1
                if process.get("unit_type") != "Hora":
                    quantity = simple_budget.quantity

                # Calcular el coste total del proceso
                unit_cost = process.get("unit_cost", 0)
                total_cost = unit_cost * quantity

                process_cost = {
                    "process_id": process["process_id"],
                    "name": process["name"],
                    "type": process["type"],
                    "unit_cost": unit_cost,
                    "unit_type": process.get("unit_type", "Unidad"),
                    "quantity": quantity,
                    "total_cost": total_cost
                }
                process_costs.append(process_cost)
                print(f"Añadido acabado: {process['name']} con coste total {total_cost}")

        # Calcular el coste total de los acabados
        process_total_cost = sum(p.get("total_cost", 0) for p in process_costs)
        print(f"Coste total de acabados: {process_total_cost}")

        # Calcular los pliegos y costes para cada parte
        print("Calculando pliegos y costes para cada parte...")
        total_paper_cost = 0
        total_machine_cost = 0
        total_plate_cost = 0

        for part in parts:
            # Calcular los pliegos necesarios
            try:
                # Importar la función para calcular pliegos
                from utils.paper_calculator import calcular_pliegos
                from models.models import PaginasRequest

                # Obtener dimensiones del papel
                paper = next((p for p in paper_catalog if p["product_id"] == part["paper_id"]), None)
                if not paper:
                    continue

                # Obtener dimensiones de la máquina
                machine = next((m for m in machine_catalog if m["machine_id"] == part["machine_id"]), None)
                if not machine:
                    continue

                # Crear la solicitud para calcular pliegos
                request = {
                    "num_paginas": part["page_count"],
                    "ancho_pagina": part["page_size"]["width"],
                    "alto_pagina": part["page_size"]["height"],
                    "ancho_pliego": paper["dimension_width"],
                    "alto_pliego": paper["dimension_height"],
                    "front_colors": part["colorConfig"]["frontColors"],
                    "back_colors": part["colorConfig"]["backColors"],
                    "copies": simple_budget.quantity,
                    "paper_id": part["paper_id"],
                    "machine_id": part["machine_id"],
                    "client_country": "España"  # País por defecto
                }

                # Verificar si la máquina es digital
                is_digital = machine.get("type") == "Digital"

                # Calcular los pliegos
                result = calcular_pliegos(
                    request["num_paginas"],
                    request["ancho_pagina"],
                    request["alto_pagina"],
                    request["ancho_pliego"],
                    request["alto_pliego"],
                    request["front_colors"],
                    request["back_colors"],
                    is_digital
                )

                # Guardar el resultado del cálculo en la parte
                part["sheet_calculation"] = result.dict()

                # Calcular el coste del papel
                from utils.paper_cost_calculator import calculate_sheet_cost

                # Calcular el número total de pliegos físicos
                total_sheets = 0
                for esquema in result.mejor_combinacion.esquemas_utilizados:
                    # Si es Tira y Retira, cada pliego físico produce 2 unidades
                    factor = 0.5 if esquema.es_tira_retira else 1
                    total_sheets += esquema.numero_pliegos * factor * simple_budget.quantity

                # Calcular el coste por pliego
                sheet_cost = calculate_sheet_cost(paper)

                # Calcular el coste total del papel
                paper_cost = sheet_cost * total_sheets
                part["paper_cost"] = round(paper_cost, 2)
                total_paper_cost += paper_cost

                # Calcular el coste de la máquina
                hourly_cost = machine.get("hourly_cost", 0)
                cfa_percentage = machine.get("cfa_percentage", 20)  # Porcentaje de CFA por defecto

                # Estimar tiempo de impresión (horas)
                print_time = part.get("custom_print_time", 1)  # Tiempo personalizado o 1 hora por defecto

                # Calcular el Coste Fijo de Arranque (CFA)
                cfa_cost = (hourly_cost * cfa_percentage / 100)

                # Calcular el coste por tiempo de uso
                usage_cost = hourly_cost * print_time

                # Coste total de máquina = CFA + coste por tiempo de uso
                machine_cost = cfa_cost + usage_cost
                part["machine_cost"] = round(machine_cost, 2)
                total_machine_cost += machine_cost

                # Calcular el coste de las planchas
                total_plates = result.mejor_combinacion.total_planchas

                # Obtener el coste por plancha (valor por defecto: 15€)
                cost_per_plate = 15
                plate_cost = cost_per_plate * total_plates
                part["plate_cost"] = round(plate_cost, 2)
                total_plate_cost += plate_cost

                # Calcular el coste total de la parte
                part_total_cost = paper_cost + machine_cost + plate_cost
                part["total_cost"] = round(part_total_cost, 2)

                print(f"Parte {part['name']}: Coste papel {paper_cost}, Coste máquina {machine_cost}, Coste planchas {plate_cost}, Total {part_total_cost}")

            except Exception as e:
                print(f"Error al calcular pliegos para la parte {part['name']}: {str(e)}")
                # Continuar con la siguiente parte
                continue

        # Calcular el coste total del presupuesto
        total_cost = total_paper_cost + total_machine_cost + total_plate_cost + process_total_cost
        print(f"Coste total del presupuesto: {total_cost}")

        # Crear el presupuesto
        print("Creando presupuesto...")
        budget_id = f"PRES-{uuid.uuid4().hex[:6].upper()}"
        print(f"ID del presupuesto generado: {budget_id}")
        print(f"Número de OT generado: {ot_number}")

        # Verificar si se ha proporcionado un archivo PDF
        if simple_budget.pdf_filename:
            print(f"Archivo PDF incluido: {simple_budget.pdf_filename}")

        # Obtener la fecha y hora actual
        current_time = datetime.now().isoformat()

        budget_dict = {
            "budget_id": budget_id,
            "ot_number": ot_number,
            "description": simple_budget.description or f"{simple_budget.job_type} ({simple_budget.quantity})",
            "client_id": "Remote",  # Cliente por defecto
            "job_type": simple_budget.job_type,
            "quantity": simple_budget.quantity,
            "page_size": {"width": 210, "height": 297},  # A4 por defecto
            "status": "Pendiente",
            "created_at": current_time,
            "updated_at": current_time,
            "parts": parts,
            "process_costs": process_costs,
            "process_total_cost": process_total_cost,
            "total_paper_weight_kg": 0,  # Se calculará en get_complete_data
            "shipping_cost": 0,  # Se calculará en get_complete_data
            "total_cost": round(total_cost, 2),
            "pdf_filename": simple_budget.pdf_filename,  # Nombre del archivo PDF
            "facturado": False,
            # Añadir información del usuario que crea el presupuesto
            "created_by": {
                "user_id": current_user["user_id"],
                "username": current_user["username"],
                "email": current_user.get("email", ""),
                "timestamp": current_time
            },
            # Inicializar el historial de actualizaciones
            "updated_by": {
                "user_id": current_user["user_id"],
                "username": current_user["username"],
                "email": current_user.get("email", ""),
                "timestamp": current_time
            }
        }

        print(f"Presupuesto creado con {len(parts)} partes y {len(process_costs)} acabados")

        # Guardar el presupuesto
        print("Guardando presupuesto en la base de datos...")
        budgets.append(budget_dict)
        save_budgets(budgets)
        print("Presupuesto guardado correctamente")

        # Registrar la actividad
        print("Registrando actividad en el log...")
        # No registramos la actividad por ahora para evitar errores
        # La función log_activity requiere un objeto Request y otros parámetros
        print("Actividad no registrada (se implementará más adelante)")

        print("Proceso completado con éxito")
        return budget_dict
    except Exception as e:
        print(f"ERROR: {str(e)}")
        print(f"Tipo de error: {type(e).__name__}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error al crear presupuesto simplificado: {str(e)}")

# Duplicar un presupuesto existente
@router.post("/{budget_id}/duplicate", response_model=Budget, status_code=status.HTTP_201_CREATED)
async def duplicate_budget(budget_id: str, current_user: Dict[str, Any] = Depends(get_current_active_user)):
    """
    Duplica un presupuesto existente con un nuevo número de OT.
    """
    try:
        # Obtener el presupuesto original
        budget = await get_budget(budget_id)
        if not budget:
            raise HTTPException(status_code=404, detail="Presupuesto no encontrado")

        # Crear una copia del presupuesto
        budget_copy = budget.copy()

        # Generar un nuevo ID de presupuesto y número de OT
        budget_copy["budget_id"] = f"PRES-{uuid.uuid4().hex[:6].upper()}"

        # Generar un número de OT con máximo 8 cifras
        date = datetime.now()
        year = date.strftime('%y')  # Año con 2 dígitos
        month = date.strftime('%m')  # Mes con 2 dígitos
        # Generar un número aleatorio de 4 dígitos para mantener el número total en 8 cifras
        random_num = str(random.randint(1000, 9999))
        budget_copy["ot_number"] = f"OT-{year}{month}{random_num}"

        # Actualizar fechas
        current_time = datetime.now().isoformat()
        budget_copy["created_at"] = current_time
        budget_copy["updated_at"] = current_time

        # Guardar el nuevo presupuesto
        budgets = load_budgets()
        budgets.append(budget_copy)
        save_budgets(budgets)

        # Obtener el presupuesto completo con datos relacionados
        new_budget = await get_complete_data(budget_copy)
        return Budget(**new_budget)

    except Exception as e:
        print(f"Error al duplicar presupuesto: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error al duplicar presupuesto: {str(e)}")
