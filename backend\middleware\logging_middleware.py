"""
Middleware para registrar las solicitudes HTTP
"""
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from utils.logger import log_request

class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware para registrar todas las solicitudes HTTP
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """
        Procesa la solicitud y registra información en el log
        
        Args:
            request: Objeto de solicitud FastAPI
            call_next: Función para continuar con el procesamiento de la solicitud
        
        Returns:
            Respuesta HTTP
        """
        # Registrar la solicitud entrante
        log_request(request)
        
        # Procesar la solicitud
        response = await call_next(request)
        
        # Registrar la respuesta
        log_request(request, response.status_code)
        
        return response
