import React from 'react';
import PropTypes from 'prop-types';
import {
  Grid,
  TextField,
  Autocomplete,
  IconButton,
  Tooltip,
  Box
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import JobTypeSelector from '../JobTypeSelector';

/**
 * Componente para manejar la información básica del presupuesto
 */
const BasicInfoSection = ({
  budget,
  handleChange,
  handleAutocompleteChange,
  handleClientInfoClick,
  clients,
  jobTypes
}) => {
  return (
    <>
      {/* Número de OT */}
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Número de OT"
          name="otNumber"
          value={budget.otNumber}
          InputProps={{
            readOnly: true,
          }}
          variant="outlined"
        />
      </Grid>

      {/* Cliente */}
      <Grid item xs={12} md={6}>
        <div style={{ display: 'flex', alignItems: 'flex-start' }}>
          <Autocomplete
            options={clients || []}
            getOptionLabel={(option) => {
              if (!option) return '';
              return option.company?.name || option.client_id || '';
            }}
            value={budget.client}
            onChange={(_, newValue) => {
              handleAutocompleteChange('client', newValue);
            }}
            renderOption={(props, option) => (
              <li {...props} key={option.client_id || `client-${Math.random()}`}>
                {option.company?.name || option.client_id || 'Cliente sin nombre'}
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Cliente"
                variant="outlined"
                required
              />
            )}
            sx={{ flexGrow: 1 }}
          />
          <Tooltip title="Ver información del cliente">
            <span>
              <IconButton
                color="primary"
                onClick={handleClientInfoClick}
                disabled={!budget.client}
                sx={{ mt: 1, ml: 1 }}
              >
                <InfoIcon />
              </IconButton>
            </span>
          </Tooltip>
        </div>
      </Grid>

      {/* Descripción - Ocupa toda la fila */}
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Descripción"
          name="description"
          value={budget.description}
          onChange={handleChange}
          variant="outlined"
          multiline
          rows={2}
        />
      </Grid>

      {/* Tipo de trabajo */}
      <Grid item xs={12} md={6}>
        <JobTypeSelector
          jobType={budget.jobType}
          onChange={handleChange}
          disabled={false}
          useProductCatalog={true}
        />
      </Grid>

      {/* Número de ejemplares */}
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Número de ejemplares"
          name="copies"
          type="number"
          value={budget.copies}
          onChange={handleChange}
          variant="outlined"
          required
          inputProps={{ min: 1 }}
        />
      </Grid>
    </>
  );
};

BasicInfoSection.propTypes = {
  budget: PropTypes.object.isRequired,
  handleChange: PropTypes.func.isRequired,
  handleAutocompleteChange: PropTypes.func.isRequired,
  handleClientInfoClick: PropTypes.func.isRequired,
  clients: PropTypes.array,
  jobTypes: PropTypes.array
};

export default BasicInfoSection;
