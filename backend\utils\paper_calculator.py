from typing import Optional
from fastapi import HTTPException
from folding_schemes import get_scheme, get_all_schemes, can_fit_scheme
from models.models import CalculoPaginasResponse, CalculoPaginasOffsetResponse, CalculoPaginasDigitalResponse, CombinacionResponse, CombinacionOffsetResponse, CombinacionDigitalResponse, EsquemaResponse, DisposicionResponse, SheetType
from utils.sheet_type_analyzer import analyze_sheet_breakdown, get_total_plates_needed, get_total_passes_needed
from utils.logger import log_info, log_debug
from utils.schema_serializer import get_serializable_page_layout

def calcular_pliegos(num_paginas: int, ancho_pagina: float, alto_pagina: float, ancho_pliego: float, alto_pliego: float, front_colors: int = 4, back_colors: int = 4, is_digital: bool = False, copies: int = 1, print_speed: int = None, setup_time: int = None, sheets_per_hour: int = None, print_units: int = 4):
    if num_paginas % 2 != 0:
        raise HTTPException(status_code=400, detail="El número de páginas debe ser par")

    # Verificar qué esquemas son posibles con las dimensiones dadas
    esquemas_posibles = {}
    for nombre, esquema in get_all_schemes().items():
        puede_caber, orientacion, pags_ancho, pags_alto = can_fit_scheme(
            esquema, ancho_pagina, alto_pagina, ancho_pliego, alto_pliego
        )
        if puede_caber:
            es_tira_retira = "tira_retira" in orientacion
            # Simplificamos la orientación a Rotate0 o Rotate90
            orientacion_simplificada = "Rotate90" if "horizontal" in orientacion else "Rotate0"
            esquemas_posibles[nombre] = {
                "paginas_por_pliego": esquema.pages_per_sheet,
                "paginas_ancho": pags_ancho,
                "paginas_alto": pags_alto,
                "orientacion": orientacion_simplificada,
                "es_tira_retira": es_tira_retira
            }

    if not esquemas_posibles:
        # Si es una máquina digital, crear un esquema personalizado
        if is_digital:
            log_info(f"No se encontraron esquemas estándar para máquina digital. Creando esquema personalizado.")

            # Calcular cuántas páginas caben en un pliego (anverso y reverso)
            paginas_ancho = max(1, int(ancho_pliego // ancho_pagina))
            paginas_alto = max(1, int(alto_pliego // alto_pagina))

            # Verificar si podemos rotar las páginas para aprovechar mejor el espacio
            paginas_ancho_rotado = max(1, int(ancho_pliego // alto_pagina))
            paginas_alto_rotado = max(1, int(alto_pliego // ancho_pagina))

            # Usar la orientación que permita más páginas por pliego
            if paginas_ancho * paginas_alto < paginas_ancho_rotado * paginas_alto_rotado:
                paginas_ancho = paginas_ancho_rotado
                paginas_alto = paginas_alto_rotado
                orientacion = "Rotate90"
            else:
                orientacion = "Rotate0"

            # Calcular el número total de páginas por pliego (anverso + reverso si es duplex)
            paginas_por_pliego_anverso = paginas_ancho * paginas_alto
            paginas_por_pliego = paginas_por_pliego_anverso * 2 if back_colors > 0 else paginas_por_pliego_anverso

            # Asegurarnos de que paginas_por_pliego sea al menos 1
            paginas_por_pliego = max(1, paginas_por_pliego)

            # Crear un esquema personalizado para digital
            nombre_esquema = f"Digital-{paginas_por_pliego}"
            esquemas_posibles[nombre_esquema] = {
                "paginas_por_pliego": paginas_por_pliego,
                "paginas_ancho": paginas_ancho,
                "paginas_alto": paginas_alto,
                "orientacion": orientacion,
                "es_tira_retira": False  # No es tira-retira, cada cara tiene su propio contenido
            }

            log_info(f"Esquema personalizado para digital: {paginas_ancho}x{paginas_alto} páginas por cara, {paginas_por_pliego} páginas por pliego")
        else:
            raise HTTPException(
                status_code=400,
                detail="Las dimensiones de página son demasiado grandes para cualquier esquema de plegado"
            )

    # Calcular todas las combinaciones posibles de pliegos
    combinaciones_validas = []

    def calcular_pliegos_necesarios(esquema_pages: int, paginas_restantes: int) -> tuple[int, int]:
        if paginas_restantes < esquema_pages:
            return 0, paginas_restantes
        pliegos = paginas_restantes // esquema_pages
        paginas_sobrantes = paginas_restantes % esquema_pages
        return pliegos, paginas_sobrantes

    def probar_combinacion_recursiva(paginas_restantes: int, esquemas_usados: dict, esquemas_disponibles: list):
        if paginas_restantes == 0:
            # Calculamos el total de pliegos, planchas y clicks
            total_pliegos = sum(esquemas_usados.values())
            total_planchas = 0
            total_clicks = 0

            # Construir la lista de esquemas utilizados con su información
            esquemas_utilizados = []
            for nombre_esquema, num_pliegos in esquemas_usados.items():
                esquema_info = esquemas_posibles[nombre_esquema]
                es_tira_retira = esquema_info["es_tira_retira"]

                if is_digital:
                    # Para máquinas digitales, calculamos clicks en lugar de planchas
                    if back_colors > 0:  # Si hay colores en el reverso, es duplex (4/4)
                        # Dos clicks por pliego (anverso y reverso)
                        clicks_por_pliego = 2
                    else:  # Si no hay colores en el reverso, es simplex (4/0)
                        # Un click por pliego (solo anverso)
                        clicks_por_pliego = 1

                    # Sumamos los clicks para este esquema, multiplicando por el número de copias
                    total_clicks += num_pliegos * clicks_por_pliego * copies
                else:
                    # Para máquinas offset, calculamos planchas
                    if es_tira_retira:
                        # Para tira y retira, se utilizan las mismas planchas para ambas caras (volteando el pliego)
                        # Por lo tanto, solo se necesitan planchas para los colores del anverso
                        planchas_por_pliego = front_colors
                    else:
                        # Para solo tira, se necesita una plancha por cada color en el anverso Y en el reverso
                        planchas_por_pliego = front_colors + back_colors
                    total_planchas += num_pliegos * planchas_por_pliego

                # Ajustar las dimensiones para esquemas tira-retira
                paginas_ancho_ajustado = esquema_info["paginas_ancho"]
                paginas_alto_ajustado = esquema_info["paginas_alto"]
                
                if es_tira_retira:
                    # Para esquemas tira-retira, dividimos las páginas entre cara y dorso
                    if paginas_ancho_ajustado > paginas_alto_ajustado:
                        paginas_ancho_ajustado = paginas_ancho_ajustado // 2
                    else:
                        paginas_alto_ajustado = paginas_alto_ajustado // 2
                
                # Obtener el page_layout si es un esquema estándar
                page_layout = None
                if not nombre_esquema.startswith("Digital-") and not nombre_esquema.startswith("Custom-"):
                    esquema_obj = get_scheme(nombre_esquema)
                    if esquema_obj:
                        page_layout = esquema_obj.page_layout
                
                esquemas_utilizados.append({
                    "nombre": nombre_esquema,
                    "numero_pliegos": num_pliegos,
                    "paginas_por_pliego": esquema_info["paginas_por_pliego"],
                    "disposicion": {
                        "paginas_ancho": paginas_ancho_ajustado,
                        "paginas_alto": paginas_alto_ajustado,
                        "orientacion": esquema_info["orientacion"]
                    },
                    "es_tira_retira": es_tira_retira,
                    "sheet_type": "WorkAndTurn" if es_tira_retira else "Flat",
                    "page_layout": page_layout
                })

            combinaciones_validas.append({
                "esquemas_utilizados": esquemas_utilizados,
                "total_pliegos": total_pliegos,
                "total_planchas": total_planchas,
                "total_clicks": total_clicks
            })
            return

        # Probamos cada esquema disponible
        for esquema in esquemas_disponibles:
            paginas_por_pliego = get_scheme(esquema).pages_per_sheet
            pliegos, paginas_sobrantes = calcular_pliegos_necesarios(paginas_por_pliego, paginas_restantes)

            if pliegos > 0:
                nuevo_esquemas = esquemas_usados.copy()
                if esquema in nuevo_esquemas:
                    nuevo_esquemas[esquema] += pliegos
                else:
                    nuevo_esquemas[esquema] = pliegos

                # Continuamos con los esquemas más pequeños para las páginas restantes
                idx_actual = esquemas_disponibles.index(esquema)
                probar_combinacion_recursiva(
                    paginas_sobrantes,
                    nuevo_esquemas,
                    esquemas_disponibles[idx_actual:]
                )

    # Ordenar esquemas por número de páginas (descendente)
    # Para esquemas personalizados (que empiezan con "Digital-"), usamos el valor de paginas_por_pliego
    # Para esquemas estándar, usamos get_scheme(x).pages_per_sheet
    esquemas_ordenados = sorted(
        esquemas_posibles.keys(),
        key=lambda x: esquemas_posibles[x]["paginas_por_pliego"] if x.startswith("Digital-") else get_scheme(x).pages_per_sheet,
        reverse=True
    )

    # Probar todas las combinaciones posibles
    probar_combinacion_recursiva(num_paginas, {}, esquemas_ordenados)

    if not combinaciones_validas:
        raise HTTPException(
            status_code=400,
            detail="No se encontró ninguna combinación válida para el número de páginas especificado"
        )

    # Encontrar la mejor combinación (menos planchas y menos pliegos)
    mejor_combinacion = min(
        combinaciones_validas,
        key=lambda x: (x["total_planchas"], x["total_pliegos"])
    )

    # Usamos el valor de print_units proporcionado en los parámetros

    # Realizar el análisis de tipo de pliego
    sheet_breakdown = analyze_sheet_breakdown(
        mejor_combinacion["esquemas_utilizados"],
        colors_front=front_colors,
        colors_back=back_colors,
        print_units=print_units
    )

    # Actualizar el número total de planchas según el análisis
    mejor_combinacion["total_planchas"] = get_total_plates_needed(sheet_breakdown)

    # Añadir información sobre el número total de pasadas
    mejor_combinacion["total_passes"] = get_total_passes_needed(sheet_breakdown)

    # Preparar el resultado - solo devolver la mejor combinación

    # Convertir los esquemas utilizados al formato esperado
    esquemas_utilizados = []
    for esquema in sheet_breakdown:
        # Crear el objeto DisposicionResponse
        disposicion = DisposicionResponse(
            paginas_ancho=esquema["disposicion"]["paginas_ancho"],
            paginas_alto=esquema["disposicion"]["paginas_alto"],
            orientacion=esquema["disposicion"]["orientacion"]
        )

        # Crear el objeto EsquemaResponse con la disposición y el tipo de pliego
        esquemas_utilizados.append(EsquemaResponse(
            nombre=esquema["nombre"],
            numero_pliegos=esquema["numero_pliegos"],
            paginas_por_pliego=esquema["paginas_por_pliego"],
            disposicion=disposicion,
            es_tira_retira=esquema["es_tira_retira"],
            sheet_type=esquema["sheet_type"],
            plates_needed=esquema["plates_needed"],
            needs_two_passes=esquema["needs_two_passes"]
        ))

    # Calcular el tiempo estimado
    a4_per_sheet = None
    estimated_time_minutes = None
    estimated_time_hours = None

    if is_digital and print_speed:
        # Cálculo para máquinas digitales
        # Dimensiones de A4 en mm (210 x 297)
        a4_width = 210
        a4_height = 297

        # Calcular cuántos A4 caben en la hoja (por cara)
        # Para SRA3 (320x450mm o similar) siempre caben 2 A4 por cara
        a4_per_sheet = 2  # Valor por defecto para SRA3

        print(f"Dimensiones del pliego: {ancho_pliego}x{alto_pliego}mm")
        print(f"Dimensiones de A4: {a4_width}x{a4_height}mm")

        # Verificar si las dimensiones son suficientes para 2 A4 por cara
        # Comprobamos si caben 2 A4 en orientación vertical u horizontal
        if (ancho_pliego >= a4_width * 2 and alto_pliego >= a4_height) or (ancho_pliego >= a4_height and alto_pliego >= a4_width * 2):
            # Caben 2 A4 en la hoja (orientación vertical u horizontal)
            a4_per_sheet = 2
            print(f"Caben 2 A4 por cara en el pliego")
        else:
            # Verificar si al menos cabe 1 A4
            if (ancho_pliego >= a4_width and alto_pliego >= a4_height) or (ancho_pliego >= a4_height and alto_pliego >= a4_width):
                # Cabe 1 A4 en la hoja
                a4_per_sheet = 1
                print(f"Cabe 1 A4 por cara en el pliego")
            else:
                # No cabe ni un A4 completo (caso raro)
                a4_per_sheet = 1
                print(f"ADVERTENCIA: Las dimensiones del pliego son menores que un A4 estándar")

        # Calcular el número total de A4 a imprimir
        # Para impresión dúplex (back_colors > 0), cada click representa una cara, pero cada pliego contiene 2 A4 por cara
        # Si es dúplex, un pliego (2 clicks) contiene 4 A4 (2 por cada cara)
        if back_colors > 0:  # Si hay colores en el reverso, es duplex (4/4)
            # En modo dúplex, cada pliego tiene 2 clicks (anverso y reverso) y cada click imprime a4_per_sheet páginas A4
            total_pliegos = mejor_combinacion["total_pliegos"]
            total_a4_sides = total_pliegos * a4_per_sheet * 2  # multiplicamos por 2 porque cada pliego tiene 2 caras
        else:
            # En modo simplex, cada pliego tiene 1 click y cada click imprime a4_per_sheet páginas A4
            total_a4_sides = mejor_combinacion["total_clicks"] * a4_per_sheet

        print(f"Total A4 sides: {total_a4_sides}, Print speed: {print_speed} A4/min")

        # Calcular el tiempo estimado en minutos
        estimated_time_minutes = total_a4_sides / print_speed if print_speed > 0 else 0

        # Convertir a horas
        estimated_time_hours = estimated_time_minutes / 60

    elif not is_digital and sheets_per_hour:
        # Cálculo para máquinas offset usando los nuevos campos
        # Obtener el tiempo de arranque (setup_time) en minutos
        setup_time_minutes = setup_time if setup_time is not None else 30  # Valor por defecto: 30 minutos

        # Calcular el total de pliegos físicos a imprimir
        total_pliegos_fisicos = mejor_combinacion["total_pliegos"] * copies

        # Considerar el número total de pasadas si está disponible
        total_passes = mejor_combinacion.get("total_passes", None)
        if total_passes:
            # Si tenemos el número de pasadas, lo usamos para calcular el tiempo
            # Cada pasada implica procesar un pliego físico
            effective_pliegos = total_passes * copies
        else:
            # Si no tenemos el número de pasadas, usamos el método anterior
            effective_pliegos = total_pliegos_fisicos

        # Calcular el tiempo de impresión en horas
        print_time_hours = effective_pliegos / sheets_per_hour

        # Convertir a minutos
        print_time_minutes = print_time_hours * 60

        # Tiempo total = tiempo de arranque + tiempo de impresión
        estimated_time_minutes = setup_time_minutes + print_time_minutes

        # Convertir a horas
        estimated_time_hours = estimated_time_minutes / 60

        print(f"Máquina offset: Setup time: {setup_time_minutes} min, Velocidad: {sheets_per_hour} pliegos/hora")
        print(f"Total pliegos: {total_pliegos_fisicos}, Tiempo impresión: {print_time_minutes:.2f} min")
        print(f"Tiempo total estimado: {estimated_time_minutes:.2f} min ({estimated_time_hours:.2f} horas)")

    else:
        # Método antiguo para máquinas offset sin sheets_per_hour
        # Estimación simple: 1 hora por cada 1000 pliegos
        total_pliegos_fisicos = mejor_combinacion["total_pliegos"] * copies
        estimated_time_hours = max(1, total_pliegos_fisicos / 1000)
        estimated_time_minutes = estimated_time_hours * 60

    # Crear la respuesta con el formato correcto
    combinacion = CombinacionResponse(
        esquemas_utilizados=esquemas_utilizados,
        total_pliegos=mejor_combinacion["total_pliegos"],
        total_planchas=mejor_combinacion["total_planchas"],
        total_passes=mejor_combinacion.get("total_passes", None),
        total_clicks=mejor_combinacion["total_clicks"],
        a4_per_sheet=a4_per_sheet,
        print_speed=print_speed,
        setup_time=setup_time,
        sheets_per_hour=sheets_per_hour,
        estimated_time_minutes=round(estimated_time_minutes, 2) if estimated_time_minutes is not None else None,
        estimated_time_hours=round(estimated_time_hours, 2) if estimated_time_hours is not None else None
    )

    resultado = CalculoPaginasResponse(
        mejor_combinacion=combinacion
    )

    return resultado

def calcular_pliegos_offset(num_paginas: int, ancho_pagina: float, alto_pagina: float, ancho_pliego: float, alto_pliego: float, front_colors: int = 4, back_colors: int = 4, print_units: Optional[int] = 4, binding_type: Optional[str] = "gathering"):
    """
    Versión simplificada de calcular_pliegos específica para máquinas offset.
    No incluye cálculos de tiempo, velocidad o clicks digitales.

    Args:
        num_paginas: Número total de páginas a imprimir
        ancho_pagina: Ancho de la página en mm
        alto_pagina: Alto de la página en mm
        ancho_pliego: Ancho del pliego en mm
        alto_pliego: Alto del pliego en mm
        front_colors: Número de colores en el anverso (por defecto 4 para CMYK)
        back_colors: Número de colores en el reverso (por defecto 4 para CMYK)
        print_units: Número de cuerpos de impresión (por defecto 4)
        binding_type: Tipo de encuadernado (por defecto "gathering")
            - "gathering": Alzado (esquemas tradicionales como F8-7, F16-7, etc.)
            - "collection": Grapado (esquemas tradicionales como F8-7, F16-7, etc.)
            - "none": Sin encuadernado (llenar pliegos con páginas hasta el máximo posible)

    Returns:
        CalculoPaginasResponse: Objeto con la información de la mejor combinación de pliegos
    """
    # Solo verificamos que el número de páginas sea par si el tipo de encuadernado no es "none"
    if binding_type != "none" and num_paginas % 2 != 0:
        raise HTTPException(status_code=400, detail="El número de páginas debe ser par para encuadernados tipo alzado o grapado")

    # Asegurarnos de que print_units tenga un valor predeterminado si es None
    if print_units is None:
        print_units = 4  # Valor predeterminado: 4 cuerpos de impresión

    log_info(f"Calculando pliegos offset para {num_paginas} páginas, dimensiones: {ancho_pagina}x{alto_pagina}mm, pliego: {ancho_pliego}x{alto_pliego}mm")
    log_info(f"Colores: {front_colors}/{back_colors}, Cuerpos de impresión: {print_units}")

    # Verificar qué esquemas son posibles con las dimensiones dadas
    esquemas_posibles = {}

    # Si el tipo de encuadernado es "none", calculamos el máximo número de páginas por pliego
    if binding_type == "none":
        log_info(f"Usando tipo de encuadernado 'none': Llenando pliegos con el máximo número de páginas posible")

        # Calcular cuántas páginas caben en un pliego (anverso y reverso)
        paginas_ancho = max(1, int(ancho_pliego // ancho_pagina))
        paginas_alto = max(1, int(alto_pliego // alto_pagina))

        # Verificar si podemos rotar las páginas para aprovechar mejor el espacio
        paginas_ancho_rotado = max(1, int(ancho_pliego // alto_pagina))
        paginas_alto_rotado = max(1, int(alto_pliego // ancho_pagina))

        # Usar la orientación que permita más páginas por pliego
        if paginas_ancho * paginas_alto < paginas_ancho_rotado * paginas_alto_rotado:
            paginas_ancho = paginas_ancho_rotado
            paginas_alto = paginas_alto_rotado
            orientacion = "Rotate90"
        else:
            orientacion = "Rotate0"

        # Calcular el número total de páginas por pliego (anverso + reverso si es duplex)
        paginas_por_pliego_anverso = paginas_ancho * paginas_alto
        paginas_por_pliego = paginas_por_pliego_anverso * 2 if back_colors > 0 else paginas_por_pliego_anverso

        # Asegurarnos de que paginas_por_pliego sea al menos 1
        paginas_por_pliego = max(1, paginas_por_pliego)

        # Crear un esquema personalizado para "none"
        nombre_esquema = f"Custom-{paginas_por_pliego}"
        esquemas_posibles[nombre_esquema] = {
            "paginas_por_pliego": paginas_por_pliego,
            "paginas_ancho": paginas_ancho,
            "paginas_alto": paginas_alto,
            "orientacion": orientacion,
            "es_tira_retira": False  # No es tira-retira, cada cara tiene su propio contenido
        }

        log_info(f"Esquema personalizado para 'none': {paginas_ancho}x{paginas_alto} páginas por cara, {paginas_por_pliego} páginas por pliego")
    else:
        # Para "gathering" y "collection", usamos los esquemas tradicionales
        for nombre, esquema in get_all_schemes().items():
            puede_caber, orientacion, pags_ancho, pags_alto = can_fit_scheme(
                esquema, ancho_pagina, alto_pagina, ancho_pliego, alto_pliego
            )
            if puede_caber:
                es_tira_retira = "tira_retira" in orientacion
                esquemas_posibles[nombre] = {
                    "paginas_por_pliego": esquema.pages_per_sheet,
                    "paginas_ancho": pags_ancho,
                    "paginas_alto": pags_alto,
                    "orientacion": orientacion,
                    "es_tira_retira": es_tira_retira
                }

    if not esquemas_posibles:
        # Si no hay esquemas posibles y el tipo de encuadernado es "none", crear un esquema personalizado
        if binding_type == "none":
            log_info(f"No se encontraron esquemas posibles, pero el tipo de encuadernado es 'none'. Creando un esquema personalizado.")

            # Crear un esquema personalizado con 1 página por pliego
            nombre_esquema = "Custom-1"
            esquemas_posibles[nombre_esquema] = {
                "paginas_por_pliego": 1,
                "paginas_ancho": 1,
                "paginas_alto": 1,
                "orientacion": "Rotate0",
                "es_tira_retira": False
            }

            log_info(f"Esquema personalizado para 'none' (caso especial): 1 página por pliego")
        else:
            raise HTTPException(
                status_code=400,
                detail="Las dimensiones de página son demasiado grandes para cualquier esquema de plegado"
            )

    # Calcular todas las combinaciones posibles de pliegos
    combinaciones_validas = []

    def calcular_pliegos_necesarios(esquema_pages: int, paginas_restantes: int) -> tuple[int, int]:
        if paginas_restantes < esquema_pages:
            # Para el tipo de encuadernado "none", si quedan páginas pero no alcanzan para un pliego completo,
            # usamos un pliego adicional para las páginas restantes
            if binding_type == "none" and paginas_restantes > 0:
                return 1, 0
            return 0, paginas_restantes

        pliegos = paginas_restantes // esquema_pages
        paginas_sobrantes = paginas_restantes % esquema_pages

        # Para el tipo de encuadernado "none", si quedan páginas sobrantes, usamos un pliego adicional
        if binding_type == "none" and paginas_sobrantes > 0:
            pliegos += 1
            paginas_sobrantes = 0

        return pliegos, paginas_sobrantes

    def probar_combinacion_recursiva(paginas_restantes: int, esquemas_usados: dict, esquemas_disponibles: list):
        # Para el tipo de encuadernado "none", permitimos que queden páginas sobrantes
        if paginas_restantes == 0 or (binding_type == "none" and len(esquemas_usados) > 0):
            # Calculamos el total de pliegos y planchas
            total_pliegos = sum(esquemas_usados.values())
            total_planchas = 0

            # Construir la lista de esquemas utilizados con su información
            esquemas_utilizados = []
            for nombre_esquema, num_pliegos in esquemas_usados.items():
                esquema_info = esquemas_posibles[nombre_esquema]
                es_tira_retira = esquema_info["es_tira_retira"]

                # Para máquinas offset, calculamos planchas
                # Nota: Este cálculo inicial es aproximado y será refinado más adelante
                # por la función analyze_sheet_breakdown

                # Si es tira-retira, siempre usamos WorkAndTurn
                # Si la máquina tiene 8 o más cuerpos, siempre usamos Perfecting y todas las planchas disponibles
                work_style = "WorkAndTurn" if es_tira_retira else "WorkAndBack"
                if print_units >= 8 and back_colors > 0:
                    # Para máquinas de 8 o más cuerpos, siempre se usan todas las planchas
                    # El número de planchas debe ser múltiplo de 8 (o del número de cuerpos)
                    planchas_por_pliego = print_units

                    # Forzar el tipo de trabajo a Perfecting
                    work_style = "Perfecting"
                    es_tira_retira = False  # No es tira-retira, es Perfecting

                    log_info(f"Máquina de {print_units} cuerpos: Usando Perfecting para el esquema {nombre_esquema}")
                elif es_tira_retira:
                    # Para tira y retira, se utilizan las mismas planchas para ambas caras (volteando el pliego)
                    # Por lo tanto, solo se necesitan planchas para los colores del anverso
                    planchas_por_pliego = front_colors
                else:
                    # Para solo tira, se necesita una plancha por cada color en el anverso Y en el reverso
                    planchas_por_pliego = front_colors + back_colors

                # Cálculo inicial aproximado
                total_planchas += num_pliegos * planchas_por_pliego

                # Determinar el tipo de pliego (sheet_type) basado en es_tira_retira
                # Si es tira-retira, siempre usamos WorkAndTurn independientemente de otros valores
                sheet_type = None
                if es_tira_retira:
                    sheet_type = "WorkAndTurn"
                elif work_style == "Perfecting":
                    sheet_type = "Perfecting"
                elif work_style == "WorkAndBack":
                    sheet_type = "WorkAndBack"
                elif not es_tira_retira and back_colors == 0:
                    sheet_type = "Flat"
                else:
                    # Ajustar las dimensiones para esquemas tira-retira
                    paginas_ancho_ajustado = esquema_info["paginas_ancho"]
                    paginas_alto_ajustado = esquema_info["paginas_alto"]
                    
                    if es_tira_retira:
                        # Para esquemas tira-retira, dividimos las páginas entre cara y dorso
                        if paginas_ancho_ajustado > paginas_alto_ajustado:
                            paginas_ancho_ajustado = paginas_ancho_ajustado // 2
                        else:
                            paginas_alto_ajustado = paginas_alto_ajustado // 2
                    
                    # Obtener el page_layout si es un esquema estándar
                    page_layout = None
                    if not nombre_esquema.startswith("Custom-"):
                        esquema_obj = get_scheme(nombre_esquema)
                        if esquema_obj:
                            page_layout = esquema_obj.page_layout
                    
                    # Añadir el esquema a la lista de esquemas utilizados
                    esquemas_utilizados.append({
                        "nombre": nombre_esquema,
                        "numero_pliegos": num_pliegos,
                        "paginas_por_pliego": esquema_info["paginas_por_pliego"],
                        "disposicion": {
                            "paginas_ancho": paginas_ancho_ajustado,
                            "paginas_alto": paginas_alto_ajustado,
                            "orientacion": esquema_info["orientacion"]
                        },
                        "es_tira_retira": es_tira_retira,
                        "sheet_type": "WorkAndTurn" if es_tira_retira else "Flat",
                        "page_layout": page_layout
                    })

            combinaciones_validas.append({
                "esquemas_utilizados": esquemas_utilizados,
                "total_pliegos": total_pliegos,
                "total_planchas": total_planchas,
                "total_clicks": 0  # No se usan clicks para offset
            })
            return

        # Probamos cada esquema disponible
        for esquema in esquemas_disponibles:
            # Para esquemas personalizados (que empiezan con "Custom-"), usamos el valor de paginas_por_pliego
            # Para esquemas estándar, usamos get_scheme(x).pages_per_sheet
            if esquema.startswith("Custom-"):
                paginas_por_pliego = esquemas_posibles[esquema]["paginas_por_pliego"]
            else:
                paginas_por_pliego = get_scheme(esquema).pages_per_sheet

            pliegos, paginas_sobrantes = calcular_pliegos_necesarios(paginas_por_pliego, paginas_restantes)

            if pliegos > 0:
                nuevo_esquemas = esquemas_usados.copy()
                if esquema in nuevo_esquemas:
                    nuevo_esquemas[esquema] += pliegos
                else:
                    nuevo_esquemas[esquema] = pliegos

                # Continuamos con los esquemas más pequeños para las páginas restantes
                idx_actual = esquemas_disponibles.index(esquema)
                probar_combinacion_recursiva(
                    paginas_sobrantes,
                    nuevo_esquemas,
                    esquemas_disponibles[idx_actual:]
                )

    # Ordenar esquemas por número de páginas (descendente)
    # Para esquemas personalizados (que empiezan con "Custom-"), usamos el valor de paginas_por_pliego
    # Para esquemas estándar, usamos get_scheme(x).pages_per_sheet
    esquemas_ordenados = sorted(
        esquemas_posibles.keys(),
        key=lambda x: esquemas_posibles[x]["paginas_por_pliego"] if x.startswith("Custom-") else get_scheme(x).pages_per_sheet,
        reverse=True
    )

    # Probar todas las combinaciones posibles
    probar_combinacion_recursiva(num_paginas, {}, esquemas_ordenados)

    if not combinaciones_validas:
        raise HTTPException(
            status_code=400,
            detail="No se encontró ninguna combinación válida para el número de páginas especificado"
        )

    # Encontrar la mejor combinación (menos planchas y menos pliegos)
    mejor_combinacion = min(
        combinaciones_validas,
        key=lambda x: (x["total_planchas"], x["total_pliegos"])
    )

    # Realizar el análisis de tipo de pliego con el número de cuerpos de impresión proporcionado
    sheet_breakdown = analyze_sheet_breakdown(
        mejor_combinacion["esquemas_utilizados"],
        colors_front=front_colors,
        colors_back=back_colors,
        print_units=print_units  # Usamos el valor proporcionado en los parámetros
    )

    # Actualizar el número total de planchas según el análisis
    mejor_combinacion["total_planchas"] = get_total_plates_needed(sheet_breakdown)

    # Añadir información sobre el número total de pasadas
    mejor_combinacion["total_passes"] = get_total_passes_needed(sheet_breakdown)

    # Convertir los esquemas utilizados al formato esperado
    esquemas_utilizados = []
    for esquema in sheet_breakdown:
        # Crear el objeto DisposicionResponse
        disposicion = DisposicionResponse(
            paginas_ancho=esquema["disposicion"]["paginas_ancho"],
            paginas_alto=esquema["disposicion"]["paginas_alto"],
            orientacion=esquema["disposicion"]["orientacion"]
        )

        # Crear el objeto EsquemaResponse con la disposición y el tipo de pliego
        esquemas_utilizados.append(EsquemaResponse(
            nombre=esquema["nombre"],
            numero_pliegos=esquema["numero_pliegos"],
            paginas_por_pliego=esquema["paginas_por_pliego"],
            disposicion=disposicion,
            es_tira_retira=esquema["es_tira_retira"],
            sheet_type=esquema["sheet_type"],
            plates_needed=esquema["plates_needed"],
            needs_two_passes=esquema["needs_two_passes"]
        ))

    # Crear la respuesta con el formato simplificado para offset
    combinacion = CombinacionOffsetResponse(
        esquemas_utilizados=esquemas_utilizados,
        total_pliegos=mejor_combinacion["total_pliegos"],
        total_planchas=mejor_combinacion["total_planchas"],
        total_passes=mejor_combinacion.get("total_passes", None)
    )

    resultado = CalculoPaginasOffsetResponse(
        mejor_combinacion=combinacion
    )

    log_info(f"Resultado del cálculo de pliegos offset: {mejor_combinacion['total_pliegos']} pliegos, {mejor_combinacion['total_planchas']} planchas")
    log_debug(f"Esquemas utilizados: {esquemas_utilizados}")

    return resultado

def calcular_pliegos_digital(num_paginas: int, ancho_pagina: float, alto_pagina: float, ancho_pliego: float, alto_pliego: float, front_colors: int = 4, back_colors: int = 0, copies: int = 1, binding_type: Optional[str] = "gathering"):
    """
    Versión específica para máquinas digitales que calcula los pliegos necesarios y clicks.
    No incluye cálculos de planchas ya que las máquinas digitales no las utilizan.

    Args:
        num_paginas: Número total de páginas a imprimir
        ancho_pagina: Ancho de la página en mm
        alto_pagina: Alto de la página en mm
        ancho_pliego: Ancho del pliego en mm
        alto_pliego: Alto del pliego en mm
        front_colors: Número de colores en el anverso (4 para CMYK, 1 para B/N)
        back_colors: Número de colores en el reverso (0 para simplex, >0 para duplex)
        copies: Número de ejemplares a imprimir
        binding_type: Tipo de encuadernado (por defecto "gathering")
            - "gathering": Alzado (esquemas tradicionales como F8-7, F16-7, etc.)
            - "collection": Grapado (esquemas tradicionales como F8-7, F16-7, etc.)
            - "none": Sin encuadernado (llenar pliegos con páginas hasta el máximo posible)

    Returns:
        CalculoPaginasDigitalResponse: Objeto con la información de la mejor combinación de pliegos
    """
    # Solo verificamos que el número de páginas sea par si el tipo de encuadernado no es "none"
    if binding_type != "none" and num_paginas % 2 != 0:
        raise HTTPException(status_code=400, detail="El número de páginas debe ser par para encuadernados tipo alzado o grapado")

    # Determinar si es duplex o simplex basado en back_colors
    is_duplex = back_colors > 0

    # Determinar si es color o blanco y negro basado en front_colors
    is_color = front_colors > 1  # Si front_colors > 1, asumimos que es color (CMYK)

    log_info(f"Calculando pliegos digital para {num_paginas} páginas, dimensiones: {ancho_pagina}x{alto_pagina}mm, pliego: {ancho_pliego}x{alto_pliego}mm")
    log_info(f"Colores: {front_colors}/{back_colors}, Duplex: {is_duplex}, Color: {is_color}")

    # Verificar qué esquemas son posibles con las dimensiones dadas
    esquemas_posibles = {}

    # Si el tipo de encuadernado es "none", calculamos el máximo número de páginas por pliego
    if binding_type == "none":
        log_info(f"Usando tipo de encuadernado 'none': Llenando pliegos con el máximo número de páginas posible")

        # Calcular cuántas páginas caben en un pliego (anverso y reverso)
        paginas_ancho = max(1, int(ancho_pliego // ancho_pagina))
        paginas_alto = max(1, int(alto_pliego // alto_pagina))

        # Verificar si podemos rotar las páginas para aprovechar mejor el espacio
        paginas_ancho_rotado = max(1, int(ancho_pliego // alto_pagina))
        paginas_alto_rotado = max(1, int(alto_pliego // ancho_pagina))

        # Usar la orientación que permita más páginas por pliego
        if paginas_ancho * paginas_alto < paginas_ancho_rotado * paginas_alto_rotado:
            paginas_ancho = paginas_ancho_rotado
            paginas_alto = paginas_alto_rotado
            orientacion = "Rotate90"
        else:
            orientacion = "Rotate0"

        # Calcular el número total de páginas por pliego (anverso + reverso si es duplex)
        paginas_por_pliego_anverso = paginas_ancho * paginas_alto
        paginas_por_pliego = paginas_por_pliego_anverso * 2 if is_duplex else paginas_por_pliego_anverso

        # Asegurarnos de que paginas_por_pliego sea al menos 1
        paginas_por_pliego = max(1, paginas_por_pliego)

        # Crear un esquema personalizado para "none"
        nombre_esquema = f"Digital-{paginas_por_pliego}"
        esquemas_posibles[nombre_esquema] = {
            "paginas_por_pliego": paginas_por_pliego,
            "paginas_ancho": paginas_ancho,
            "paginas_alto": paginas_alto,
            "orientacion": orientacion,
            "es_tira_retira": False  # No es tira-retira, cada cara tiene su propio contenido
        }

        log_info(f"Esquema personalizado para digital: {paginas_ancho}x{paginas_alto} páginas por cara, {paginas_por_pliego} páginas por pliego")
    else:
        # Para "gathering" y "collection", usamos los esquemas tradicionales
        for nombre, esquema in get_all_schemes().items():
            puede_caber, orientacion, pags_ancho, pags_alto = can_fit_scheme(
                esquema, ancho_pagina, alto_pagina, ancho_pliego, alto_pliego
            )
            if puede_caber:
                es_tira_retira = "tira_retira" in orientacion
                esquemas_posibles[nombre] = {
                    "paginas_por_pliego": esquema.pages_per_sheet,
                    "paginas_ancho": pags_ancho,
                    "paginas_alto": pags_alto,
                    "orientacion": orientacion,
                    "es_tira_retira": es_tira_retira
                }

    if not esquemas_posibles:
        # Si no hay esquemas posibles, crear un esquema personalizado
        log_info(f"No se encontraron esquemas estándar para máquina digital. Creando esquema personalizado.")

        # Calcular cuántas páginas caben en un pliego (anverso y reverso)
        paginas_ancho = max(1, int(ancho_pliego // ancho_pagina))
        paginas_alto = max(1, int(alto_pliego // alto_pagina))

        # Verificar si podemos rotar las páginas para aprovechar mejor el espacio
        paginas_ancho_rotado = max(1, int(ancho_pliego // alto_pagina))
        paginas_alto_rotado = max(1, int(alto_pliego // ancho_pagina))

        # Usar la orientación que permita más páginas por pliego
        if paginas_ancho * paginas_alto < paginas_ancho_rotado * paginas_alto_rotado:
            paginas_ancho = paginas_ancho_rotado
            paginas_alto = paginas_alto_rotado
            orientacion = "Rotate90"
        else:
            orientacion = "Rotate0"

        # Calcular el número total de páginas por pliego (anverso + reverso si es duplex)
        paginas_por_pliego_anverso = paginas_ancho * paginas_alto
        paginas_por_pliego = paginas_por_pliego_anverso * 2 if is_duplex else paginas_por_pliego_anverso

        # Asegurarnos de que paginas_por_pliego sea al menos 1
        paginas_por_pliego = max(1, paginas_por_pliego)

        # Crear un esquema personalizado para digital
        nombre_esquema = f"Digital-{paginas_por_pliego}"
        esquemas_posibles[nombre_esquema] = {
            "paginas_por_pliego": paginas_por_pliego,
            "paginas_ancho": paginas_ancho,
            "paginas_alto": paginas_alto,
            "orientacion": orientacion,
            "es_tira_retira": False  # No es tira-retira, cada cara tiene su propio contenido
        }

        log_info(f"Esquema personalizado para digital: {paginas_ancho}x{paginas_alto} páginas por cara, {paginas_por_pliego} páginas por pliego")

    # Calcular todas las combinaciones posibles de pliegos
    combinaciones_validas = []

    def calcular_pliegos_necesarios(esquema_pages: int, paginas_restantes: int) -> tuple[int, int]:
        if paginas_restantes < esquema_pages:
            # Para el tipo de encuadernado "none", si quedan páginas pero no alcanzan para un pliego completo,
            # usamos un pliego adicional para las páginas restantes
            if binding_type == "none" and paginas_restantes > 0:
                return 1, 0
            return 0, paginas_restantes

        pliegos = paginas_restantes // esquema_pages
        paginas_sobrantes = paginas_restantes % esquema_pages

        # Para el tipo de encuadernado "none", si quedan páginas sobrantes, usamos un pliego adicional
        if binding_type == "none" and paginas_sobrantes > 0:
            pliegos += 1
            paginas_sobrantes = 0

        return pliegos, paginas_sobrantes

    def probar_combinacion_recursiva(paginas_restantes: int, esquemas_usados: dict, esquemas_disponibles: list):
        # Para el tipo de encuadernado "none", permitimos que queden páginas sobrantes
        if paginas_restantes == 0 or (binding_type == "none" and len(esquemas_usados) > 0):
            # Calculamos el total de pliegos y clicks
            total_pliegos = sum(esquemas_usados.values())

            # Calcular clicks según si es duplex o simplex
            clicks_por_pliego = 2 if is_duplex else 1
            total_clicks = total_pliegos * clicks_por_pliego * copies

            # Construir la lista de esquemas utilizados con su información
            esquemas_utilizados = []
            for nombre_esquema, num_pliegos in esquemas_usados.items():
                esquema_info = esquemas_posibles[nombre_esquema]

                # Ajustar las dimensiones para esquemas tira-retira
                paginas_ancho_ajustado = esquema_info["paginas_ancho"]
                paginas_alto_ajustado = esquema_info["paginas_alto"]
                
                if esquema_info["es_tira_retira"]:
                    # Para esquemas tira-retira, dividimos las páginas entre cara y dorso
                    if paginas_ancho_ajustado > paginas_alto_ajustado:
                        paginas_ancho_ajustado = paginas_ancho_ajustado // 2
                    else:
                        paginas_alto_ajustado = paginas_alto_ajustado // 2
                
                # Obtener el page_layout si es un esquema estándar
                page_layout = None
                if not nombre_esquema.startswith("Digital-") and not nombre_esquema.startswith("Custom-"):
                    esquema_obj = get_scheme(nombre_esquema)
                    if esquema_obj:
                        page_layout = esquema_obj.page_layout
                
                esquemas_utilizados.append({
                    "nombre": nombre_esquema,
                    "numero_pliegos": num_pliegos,
                    "paginas_por_pliego": esquema_info["paginas_por_pliego"],
                    "disposicion": {
                        "paginas_ancho": paginas_ancho_ajustado,
                        "paginas_alto": paginas_alto_ajustado,
                        "orientacion": esquema_info["orientacion"]
                    },
                    "es_tira_retira": esquema_info["es_tira_retira"],
                    "sheet_type": "WorkAndTurn" if esquema_info["es_tira_retira"] else "WorkAndBack",
                    "page_layout": page_layout,
                    "is_duplex": is_duplex,
                    "is_color": is_color
                })

            combinaciones_validas.append({
                "esquemas_utilizados": esquemas_utilizados,
                "total_pliegos": total_pliegos,
                "total_clicks": total_clicks,
                "is_duplex": is_duplex,
                "is_color": is_color
            })
            return

        # Probamos cada esquema disponible
        for esquema in esquemas_disponibles:
            paginas_por_pliego = esquemas_posibles[esquema]["paginas_por_pliego"]
            pliegos, paginas_sobrantes = calcular_pliegos_necesarios(paginas_por_pliego, paginas_restantes)

            if pliegos > 0:
                nuevo_esquemas = esquemas_usados.copy()
                if esquema in nuevo_esquemas:
                    nuevo_esquemas[esquema] += pliegos
                else:
                    nuevo_esquemas[esquema] = pliegos

                # Continuamos con los esquemas más pequeños para las páginas restantes
                idx_actual = esquemas_disponibles.index(esquema)
                probar_combinacion_recursiva(
                    paginas_sobrantes,
                    nuevo_esquemas,
                    esquemas_disponibles[idx_actual:]
                )

    # Ordenar esquemas por número de páginas (descendente)
    esquemas_ordenados = sorted(
        esquemas_posibles.keys(),
        key=lambda x: esquemas_posibles[x]["paginas_por_pliego"],
        reverse=True
    )

    # Probar todas las combinaciones posibles
    probar_combinacion_recursiva(num_paginas, {}, esquemas_ordenados)

    if not combinaciones_validas:
        raise HTTPException(
            status_code=400,
            detail="No se encontró ninguna combinación válida para el número de páginas especificado"
        )

    # Encontrar la mejor combinación (menos pliegos)
    mejor_combinacion = min(
        combinaciones_validas,
        key=lambda x: x["total_pliegos"]
    )

    # Calcular cuántos A4 caben en la hoja (por cara)
    # Para SRA3 (320x450mm o similar) siempre caben 2 A4 por cara
    a4_width = 210
    a4_height = 297

    # Verificar si las dimensiones son suficientes para 2 A4 por cara
    if (ancho_pliego >= a4_width * 2 and alto_pliego >= a4_height) or (ancho_pliego >= a4_height and alto_pliego >= a4_width * 2):
        # Caben 2 A4 en la hoja (orientación vertical u horizontal)
        a4_per_cara = 2
        log_info(f"Caben 2 A4 por cara en el pliego")
    else:
        # Verificar si al menos cabe 1 A4
        if (ancho_pliego >= a4_width and alto_pliego >= a4_height) or (ancho_pliego >= a4_height and alto_pliego >= a4_width):
            # Cabe 1 A4 en la hoja
            a4_per_cara = 1
            log_info(f"Cabe 1 A4 por cara en el pliego")
        else:
            # No cabe ni un A4 completo (caso raro)
            a4_per_cara = 1
            log_info(f"ADVERTENCIA: Las dimensiones del pliego son menores que un A4 estándar")

    # Calcular el total de A4 por pliego (considerando ambas caras si es dúplex)
    a4_per_sheet = a4_per_cara * (2 if is_duplex else 1)
    log_info(f"Total de A4 por pliego: {a4_per_sheet} ({a4_per_cara} por cara × {2 if is_duplex else 1} caras)")

    # Convertir los esquemas utilizados al formato esperado
    esquemas_utilizados = []
    for esquema in mejor_combinacion["esquemas_utilizados"]:
        # Crear el objeto DisposicionResponse
        disposicion = DisposicionResponse(
            paginas_ancho=esquema["disposicion"]["paginas_ancho"],
            paginas_alto=esquema["disposicion"]["paginas_alto"],
            orientacion=esquema["disposicion"]["orientacion"]
        )

        # Crear el objeto EsquemaResponse con la disposición
        esquemas_utilizados.append(EsquemaResponse(
            nombre=esquema["nombre"],
            numero_pliegos=esquema["numero_pliegos"],
            paginas_por_pliego=esquema["paginas_por_pliego"],
            disposicion=disposicion,
            es_tira_retira=esquema["es_tira_retira"]
        ))

    # Crear la respuesta con el formato correcto para digital
    combinacion = CombinacionDigitalResponse(
        esquemas_utilizados=esquemas_utilizados,
        total_pliegos=mejor_combinacion["total_pliegos"],
        total_clicks=mejor_combinacion["total_clicks"],
        is_duplex=mejor_combinacion["is_duplex"],
        is_color=mejor_combinacion["is_color"],
        a4_per_sheet=a4_per_sheet
    )

    resultado_digital = CalculoPaginasDigitalResponse(
        mejor_combinacion=combinacion
    )

    log_info(f"Resultado del cálculo de pliegos digital: {mejor_combinacion['total_pliegos']} pliegos, {mejor_combinacion['total_clicks']} clicks")
    log_debug(f"Esquemas utilizados: {esquemas_utilizados}")

    return resultado_digital
