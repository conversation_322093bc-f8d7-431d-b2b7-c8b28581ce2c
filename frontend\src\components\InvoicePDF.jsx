import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  CircularProgress,
  Typography
} from '@mui/material';
import { downloadInvoice } from '../services/invoiceService';

const InvoicePDF = ({ invoicePath, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [fileName, setFileName] = useState('');

  useEffect(() => {
    const loadPdf = async () => {
      try {
        setLoading(true);
        setError(null);

        // Extraer el nombre del archivo de la ruta
        const extractedFileName = invoicePath.split('/').pop();
        setFileName(extractedFileName);

        // Descargar la factura como blob
        const blob = await downloadInvoice(invoicePath);
        
        // Crear una URL para el blob
        const url = URL.createObjectURL(blob);
        setPdfUrl(url);
      } catch (err) {
        console.error('Error al cargar el PDF de la factura:', err);
        setError(err.message || 'Error al cargar el PDF');
      } finally {
        setLoading(false);
      }
    };

    if (invoicePath) {
      loadPdf();
    }

    // Limpiar la URL del blob al desmontar el componente
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [invoicePath]);

  const handleDownload = () => {
    if (pdfUrl) {
      const a = document.createElement('a');
      a.href = pdfUrl;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  if (loading) {
    return (
      <Dialog open={true} maxWidth="md" fullWidth>
        <DialogTitle>Cargando Factura</DialogTitle>
        <DialogContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={true} maxWidth="md" fullWidth>
        <DialogTitle>Error</DialogTitle>
        <DialogContent>
          <Typography color="error">{error}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cerrar</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog open={true} maxWidth="xl" fullWidth>
      <DialogTitle>
        Factura {fileName}
        <Button
          variant="contained"
          color="primary"
          onClick={handleDownload}
          style={{ position: 'absolute', right: '24px' }}
        >
          Descargar PDF
        </Button>
      </DialogTitle>
      <DialogContent sx={{ p: 1 }}>
        <Box sx={{ width: '100%', height: 'calc(100vh - 150px)' }}>
          {pdfUrl && (
            <iframe
              src={`${pdfUrl}#toolbar=0&navpanes=0`}
              width="100%"
              height="100%"
              style={{ border: 'none' }}
              title="Factura PDF"
            />
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cerrar</Button>
      </DialogActions>
    </Dialog>
  );
};

InvoicePDF.propTypes = {
  invoicePath: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired
};

export default InvoicePDF;
