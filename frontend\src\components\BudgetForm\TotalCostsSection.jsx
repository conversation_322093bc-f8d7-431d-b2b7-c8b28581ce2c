// React es necesario para JSX aunque no se use directamente
// eslint-disable-next-line no-unused-vars
import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  Grid,
  Paper,
  Typography,
  Box
} from '@mui/material';
import CalculateIcon from '@mui/icons-material/Calculate';
import PrintIcon from '@mui/icons-material/Print';
import BuildIcon from '@mui/icons-material/Build';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import SummarizeIcon from '@mui/icons-material/Summarize';

/**
 * Componente para mostrar los costos totales del presupuesto
 */
const TotalCostsSection = ({
  budgetParts,
  calculatedProcessCost,
  budget,
  setBudget // Añadir setBudget como prop opcional
}) => {
  // Extraer solo los datos relevantes para el cálculo de costos
  // Esto evita recálculos innecesarios cuando cambian campos irrelevantes
  const relevantPartsData = useMemo(() => {
    return budgetParts.map(part => ({
      // Solo incluimos los datos necesarios para el cálculo de costos
      sheetCalculation: part.sheetCalculation,
      machine: part.machine ? { type: part.machine.type } : null,
      paper: part.paper ? { price_per_1000: part.paper.price_per_1000 } : null,
      name: part.name
    }));
  }, [budgetParts.map(part => {
    // Solo nos interesan los cambios en estos valores específicos
    return JSON.stringify({
      sheetCalculation: part.sheetCalculation,
      machineType: part.machine?.type,
      paperPrice: part.paper?.price_per_1000,
      name: part.name
    });
  }).join('|')]);

  // Extraer solo los datos relevantes del presupuesto para el cálculo de envío
  const relevantBudgetData = useMemo(() => {
    return {
      shipping: budget.shipping,
      costs: budget.costs,  // Añadir costs para acceder a costs.shipping
      client_data: budget.client_data,
      total_paper_weight_kg: budget.total_paper_weight_kg,
      shipping_cost: budget.shipping_cost
    };
  }, [budget.shipping, budget.costs, budget.client_data, budget.total_paper_weight_kg, budget.shipping_cost]);

  // Calcular todos los costos en un solo useMemo para evitar recálculos innecesarios
  const {
    partsCostTotal,
    processCost,
    shippingCost,
    shippingWeightText,
    shippingCountry,
    distanceFactor,
    isInternational,
    totalCost
  } = useMemo(() => {
    // Si tenemos setBudget y los datos de envío están en diferentes lugares,
    // sincronizar los datos para mantener consistencia
    if (setBudget && relevantBudgetData.costs?.shipping && relevantBudgetData.shipping) {
      // Si hay datos en costs.shipping pero no en shipping, actualizar shipping
      if (relevantBudgetData.costs.shipping.cost && (!relevantBudgetData.shipping.cost || relevantBudgetData.costs.shipping.cost !== relevantBudgetData.shipping.cost)) {
        setBudget(prevBudget => ({
          ...prevBudget,
          shipping: {
            ...prevBudget.shipping,
            cost: relevantBudgetData.costs.shipping.cost,
            weight: relevantBudgetData.costs.shipping.weight_kg || relevantBudgetData.costs.shipping.weight,
            country: relevantBudgetData.costs.shipping.country,
            distance_factor: relevantBudgetData.costs.shipping.distance_factor
          }
        }));
      }
      
      // Si hay datos en shipping pero no en costs.shipping, actualizar costs.shipping
      if (relevantBudgetData.shipping.cost && (!relevantBudgetData.costs.shipping.cost || relevantBudgetData.shipping.cost !== relevantBudgetData.costs.shipping.cost)) {
        setBudget(prevBudget => ({
          ...prevBudget,
          costs: {
            ...(prevBudget.costs || {}),
            shipping: {
              ...(prevBudget.costs?.shipping || {}),
              cost: relevantBudgetData.shipping.cost,
              weight_kg: relevantBudgetData.shipping.weight,
              country: relevantBudgetData.shipping.country,
              distance_factor: relevantBudgetData.shipping.distance_factor
            }
          }
        }));
      }
    }
    
    // Calcular el costo total de las partes
    let partsCostTotal = 0;
    if (relevantPartsData && relevantPartsData.length > 0) {
      partsCostTotal = relevantPartsData.reduce((total, part) => {
        // Si la parte tiene un cálculo de hojas, calcular los costos
        if (part.sheetCalculation) {
          // Obtener los costos directamente del endpoint
          let paperCost = part.sheetCalculation.paper_cost || 0;
          let machineCost = 0;

          // Usar la misma lógica que en BudgetPartForm.jsx
          if (part.sheetCalculation.hourly_cost && part.sheetCalculation.total_time_hours) {
            machineCost = part.sheetCalculation.total_time_hours * part.sheetCalculation.hourly_cost;
          } else if (part.sheetCalculation.machine_cost !== undefined) {
            machineCost = part.sheetCalculation.machine_cost;
          } else if (part.sheetCalculation.printing_cost !== undefined) {
            machineCost = part.sheetCalculation.printing_cost;
            if (part.sheetCalculation.cfa_cost !== undefined) {
              machineCost += part.sheetCalculation.cfa_cost;
            }
          }

          // Determinar si es una máquina digital
          const isDigital = part.machine && part.machine.type === 'Digital';
          let plateCost = 0;
          let inkCost = 0;
          let clickCost = 0;

          if (isDigital) {
            // Para máquinas digitales, usar el costo de clicks
            clickCost = part.sheetCalculation.click_cost || 0;
            return total + (paperCost + machineCost + clickCost);
          } else {
            // Para máquinas offset, usar el costo de planchas y tinta
            plateCost = part.sheetCalculation.plates_cost || 0;
            inkCost = part.sheetCalculation.ink_cost || 0;
            
            // Calcular el costo de maculatura
            let calculatedMaculaturaCost = 0;
            if (part.sheetCalculation?.total_maculatura > 0) {
              const precioPorMillar = part.sheetCalculation.paper_cost_per_1000 ||
                                    (part.paper && part.paper.price_per_1000) || 0;
              calculatedMaculaturaCost = (part.sheetCalculation.total_maculatura * precioPorMillar) / 1000;
            }
            
            return total + (paperCost + machineCost + plateCost + inkCost + calculatedMaculaturaCost);
          }
        }
        return total;
      }, 0);
    }

    // Obtener el costo de procesos (acabados)
    const processCost = calculatedProcessCost || 0;

    // Obtener el costo de envío
    let shippingCost = 0;
    let shippingWeightText = '';
    let shippingCountry = '';
    let distanceFactor = 1;
    let isInternational = false;
    
    if (relevantBudgetData) {
      // Siempre usar total_paper_weight_kg como fuente única del peso del papel
      const weight = relevantBudgetData.total_paper_weight_kg || 0;
      shippingWeightText = weight > 0 ? `(${weight.toFixed(2)} kg)` : '';
      
      // Obtener el costo de envío, país y factor de distancia
      // Primero intentamos usar budget.costs.shipping, y si no existe, usamos budget.shipping
      const shippingData = relevantBudgetData.costs?.shipping || relevantBudgetData.shipping;
      
      if (shippingData) {
        // Usar weight_kg (o weight como fallback) para mantener consistencia
        const shippingWeight = shippingData.weight_kg || shippingData.weight || weight;
        if (shippingWeight > 0) {
          shippingWeightText = `(${shippingWeight.toFixed(2)} kg)`;
        }
        
        shippingCost = shippingData.cost || relevantBudgetData.shipping_cost || 0;
        shippingCountry = shippingData.country || '';
        distanceFactor = shippingData.distance_factor || 1;
        // Un país es internacional si no es España y no está vacío
        isInternational = shippingCountry !== '' && shippingCountry.toLowerCase() !== 'españa' && shippingCountry.toLowerCase() !== 'espana';
      } else if (relevantBudgetData.shipping_cost) {
        shippingCost = relevantBudgetData.shipping_cost || 0;
      }
      
      // Si tenemos datos del cliente, obtener el país de allí si no lo tenemos ya
      if (!shippingCountry && relevantBudgetData.client_data?.company?.address?.country) {
        shippingCountry = relevantBudgetData.client_data.company.address.country;
        // Actualizar isInternational basado en el país del cliente
        isInternational = shippingCountry !== '' && shippingCountry.toLowerCase() !== 'españa' && shippingCountry.toLowerCase() !== 'espana';
      }
    }

    // Calcular el costo total
    const totalCost = partsCostTotal + processCost + shippingCost;
    
    return { 
      partsCostTotal, 
      processCost, 
      shippingCost, 
      shippingWeightText, 
      shippingCountry,
      distanceFactor,
      isInternational,
      totalCost 
    };
  }, [budget, budget.costs?.shipping, budget.shipping, budget.total_paper_weight_kg, relevantBudgetData, relevantPartsData, calculatedProcessCost, setBudget]);

  return (
    <Grid item xs={12}>
      <Paper elevation={3} sx={{ p: 3, mt: 2, borderRadius: 2 }}>
        {/* Encabezado con icono */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <CalculateIcon sx={{ mr: 1, fontSize: '1.8rem', color: '#1976d2' }} />
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
            Costos Totales del Presupuesto
          </Typography>
        </Box>
        
        <Box sx={{ 
          bgcolor: '#f8f9fa', 
          p: 2, 
          borderRadius: 1,
          mb: 2,
          border: '1px solid #e0e0e0'
        }}>
          {/* Mostrar costos de cada parte dinámicamente */}
          {budgetParts && budgetParts.map((part, index) => {
            // Calcular el costo total de la parte
            let calculatedTotal = 0;
            if (part.sheetCalculation) {
              // Obtener los costos directamente del endpoint
              let paperCost = part.sheetCalculation.paper_cost || 0;
              let machineCost = 0;

              // Usar la misma lógica que en BudgetPartForm.jsx
              if (part.sheetCalculation.hourly_cost && part.sheetCalculation.total_time_hours) {
                machineCost = part.sheetCalculation.total_time_hours * part.sheetCalculation.hourly_cost;
              } else if (part.sheetCalculation.machine_cost !== undefined) {
                machineCost = part.sheetCalculation.machine_cost;
              } else if (part.sheetCalculation.printing_cost !== undefined) {
                machineCost = part.sheetCalculation.printing_cost;
                if (part.sheetCalculation.cfa_cost !== undefined) {
                  machineCost += part.sheetCalculation.cfa_cost;
                }
              }

              // Determinar si es una máquina digital
              const isDigital = part.machine && part.machine.type === 'Digital';
              let plateCost = 0;
              let inkCost = 0;
              let clickCost = 0;

              if (isDigital) {
                clickCost = part.sheetCalculation.click_cost || 0;
                calculatedTotal = paperCost + machineCost + clickCost;
              } else {
                plateCost = part.sheetCalculation.plates_cost || 0;
                inkCost = part.sheetCalculation.ink_cost || 0;
                
                // Calcular el costo de maculatura
                let calculatedMaculaturaCost = 0;
                if (part.sheetCalculation?.total_maculatura > 0) {
                  const precioPorMillar = part.sheetCalculation.paper_cost_per_1000 ||
                                        (part.paper && part.paper.price_per_1000) || 0;
                  calculatedMaculaturaCost = (part.sheetCalculation.total_maculatura * precioPorMillar) / 1000;
                }
                
                calculatedTotal = paperCost + machineCost + plateCost + inkCost + calculatedMaculaturaCost;
              }
            }
            
            return (
              <Box 
                key={`part-${index}`}
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 1,
                  pb: 1,
                  borderBottom: index < budgetParts.length - 1 ? '1px dashed #e0e0e0' : 'none'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <PrintIcon sx={{ mr: 1, fontSize: '1.2rem', color: '#1976d2' }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                    Parte {index + 1} ({part.name || 'Sin nombre'})
                  </Typography>
                </Box>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                  {calculatedTotal.toFixed(2)} €
                </Typography>
              </Box>
            );
          })}

          {/* Mostrar costo de acabados calculado */}
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: 1,
              pb: 1,
              borderBottom: '1px dashed #e0e0e0'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <BuildIcon sx={{ mr: 1, fontSize: '1.2rem', color: '#9c27b0' }} />
              <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                Acabados
              </Typography>
            </Box>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
              {processCost.toFixed(2)} €
            </Typography>
          </Box>

          {/* Mostrar costo de envío calculado */}
          <Box 
            sx={{ 
              display: 'flex', 
              alignItems: 'center',
              justifyContent: 'space-between',
              mb: 1,
              pb: 1,
              borderBottom: '1px dashed #e0e0e0'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <LocalShippingIcon sx={{ mr: 1, fontSize: '1.2rem', color: '#ff9800' }} />
              <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                Envío {shippingWeightText}
              </Typography>
            </Box>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
              {shippingCost.toFixed(2)} €
            </Typography>
          </Box>
            
          {/* Mostrar información de país y factor de distancia */}
          {isInternational && (
            <Box 
              sx={{ 
                display: 'flex', 
                alignItems: 'center',
                justifyContent: 'flex-end',
                mt: 0.5
              }}
            >
              <Typography variant="caption" sx={{ fontStyle: 'italic', color: '#757575' }}>
                Envío internacional a {shippingCountry} (factor: {distanceFactor.toFixed(1)}x)
              </Typography>
            </Box>
          )}
        </Box>

        {/* Costo Total */}
        <Paper 
          elevation={3} 
          sx={{ 
            p: 2, 
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            bgcolor: '#f1f8e9',
            borderLeft: '4px solid #2e7d32'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SummarizeIcon sx={{ mr: 1, fontSize: '1.5rem', color: '#2e7d32' }} />
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              Costo Total
            </Typography>
          </Box>
          <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
            {totalCost.toFixed(2)} €
          </Typography>
        </Paper>
      </Paper>
    </Grid>
  );
};

TotalCostsSection.propTypes = {
  budgetParts: PropTypes.array.isRequired,
  calculatedProcessCost: PropTypes.number.isRequired,
  budget: PropTypes.object.isRequired,
  setBudget: PropTypes.func // Opcional, para actualizar el estado del presupuesto
};

export default TotalCostsSection;
