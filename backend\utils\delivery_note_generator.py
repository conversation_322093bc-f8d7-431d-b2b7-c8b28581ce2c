import os
import json
from datetime import datetime
from typing import Dict, Any, Optional
from fpdf import FPDF
from utils.data_manager import read_data
from utils.production_manager import get_production_processes_by_ot_number

# Directorio para guardar los albaranes generados
DELIVERY_NOTES_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "delivery_notes")

def generate_delivery_note(shipping_data: Dict[str, Any]) -> Optional[str]:
    """
    Genera un albarán de entrega en PDF

    Args:
        shipping_data: Datos del envío

    Returns:
        str: Ruta al archivo PDF generado, o None si hubo un error
    """
    try:
        # Asegurarse de que el directorio existe
        os.makedirs(DELIVERY_NOTES_DIR, exist_ok=True)

        # Obtener datos del presupuesto
        budgets = read_data("budgets")
        budget_data = None

        for budget in budgets:
            if budget.get("ot_number") == shipping_data["ot_number"]:
                budget_data = budget
                break

        if not budget_data:
            print(f"No se encontró presupuesto para OT {shipping_data['ot_number']}")
            return None

        # Obtener procesos de producción
        processes = get_production_processes_by_ot_number(shipping_data["ot_number"])

        # Crear PDF
        pdf = FPDF()
        pdf.add_page()

        # Ruta al logo
        logo_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "images", "trikyprinter-logo.png")

        # Verificar si el logo existe
        if os.path.exists(logo_path):
            # Añadir logo en la esquina superior izquierda
            pdf.image(logo_path, x=10, y=10, w=50)

            # Información de la empresa en la esquina superior derecha
            pdf.set_font("Arial", "", 8)
            pdf.set_xy(150, 10)
            pdf.cell(50, 5, "TrikyPrinter", 0, 2, "R")
            pdf.cell(50, 5, "c/Costa Rica 11", 0, 2, "R")
            pdf.cell(50, 5, "28016 Madrid", 0, 2, "R")
            pdf.cell(50, 5, "Tel: +34 91 000 00 00", 0, 2, "R")
            pdf.cell(50, 5, "Email: <EMAIL>", 0, 2, "R")

            # Mover el cursor después del logo
            pdf.set_y(40)
        else:
            # Si no se encuentra el logo, usar texto
            pdf.set_font("Arial", "B", 16)
            pdf.cell(190, 10, "TrikyPrinter", 0, 1, "L")
            pdf.set_font("Arial", "", 8)
            pdf.cell(190, 5, "c/Costa Rica 11, 28016 Madrid", 0, 1, "L")
            pdf.cell(190, 5, "Tel: +34 91 000 00 00 - Email: <EMAIL>", 0, 1, "L")
            pdf.ln(5)

        # Configurar fuentes para el título
        pdf.set_font("Arial", "B", 16)

        # Título
        pdf.cell(190, 10, "ALBARÁN DE ENTREGA", 0, 1, "C")
        pdf.ln(5)

        # Información del albarán
        pdf.set_font("Arial", "B", 12)
        pdf.cell(190, 10, f"Albarán Nº: {shipping_data['shipping_id']}", 0, 1)
        pdf.cell(190, 10, f"Fecha: {datetime.fromisoformat(shipping_data['shipping_date']).strftime('%d/%m/%Y')}", 0, 1)
        pdf.ln(5)

        # Información del cliente
        pdf.set_font("Arial", "B", 12)
        pdf.cell(190, 10, "DATOS DEL CLIENTE", 0, 1)
        pdf.set_font("Arial", "", 10)
        pdf.cell(190, 10, f"Cliente: {shipping_data['client_name']}", 0, 1)

        # Dirección de entrega
        address = shipping_data["delivery_address"]
        address_str = f"{address.get('street', '')}, {address.get('city', '')}, {address.get('postal_code', '')}"
        pdf.cell(190, 10, f"Dirección de entrega: {address_str}", 0, 1)
        pdf.cell(190, 10, f"Persona de contacto: {shipping_data['contact_person']}", 0, 1)
        pdf.cell(190, 10, f"Teléfono: {shipping_data['contact_phone']}", 0, 1)
        pdf.ln(5)

        # Información del pedido
        pdf.set_font("Arial", "B", 12)
        pdf.cell(190, 10, "DATOS DEL PEDIDO", 0, 1)
        pdf.set_font("Arial", "", 10)
        pdf.cell(190, 10, f"Número de OT: {shipping_data['ot_number']}", 0, 1)
        pdf.cell(190, 10, f"Descripción: {budget_data.get('description', 'Sin descripción')}", 0, 1)
        pdf.cell(190, 10, f"Cantidad: {budget_data.get('quantity', 0)} unidades", 0, 1)
        pdf.ln(5)

        # Información del envío
        pdf.set_font("Arial", "B", 12)
        pdf.cell(190, 10, "DATOS DEL ENVÍO", 0, 1)
        pdf.set_font("Arial", "", 10)
        pdf.cell(190, 10, f"Transportista: {shipping_data['carrier']}", 0, 1)

        if shipping_data.get("tracking_number"):
            pdf.cell(190, 10, f"Número de seguimiento: {shipping_data['tracking_number']}", 0, 1)

        pdf.cell(190, 10, f"Número de bultos: {shipping_data['packages']}", 0, 1)

        if shipping_data.get("weight"):
            pdf.cell(190, 10, f"Peso: {shipping_data['weight']} kg", 0, 1)

        if shipping_data.get("notes"):
            pdf.ln(5)
            pdf.set_font("Arial", "B", 12)
            pdf.cell(190, 10, "OBSERVACIONES", 0, 1)
            pdf.set_font("Arial", "", 10)
            pdf.multi_cell(190, 10, shipping_data["notes"])

        # Firmas
        pdf.ln(20)
        pdf.cell(95, 10, "Firma del transportista", 0, 0, "C")
        pdf.cell(95, 10, "Firma del cliente", 0, 1, "C")
        pdf.ln(20)
        pdf.cell(95, 10, "___________________", 0, 0, "C")
        pdf.cell(95, 10, "___________________", 0, 1, "C")

        # Pie de página
        pdf.set_y(-30)
        pdf.set_font("Arial", "I", 8)
        pdf.cell(0, 10, f"Documento generado el {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}", 0, 1, "C")

        # Añadir línea separadora
        pdf.line(10, pdf.get_y(), 200, pdf.get_y())
        pdf.ln(2)

        # Información de la empresa en el pie de página
        pdf.cell(0, 5, "TrikyPrinter - c/Costa Rica 11, 28016 Madrid - CIF: B12345678", 0, 1, "C")
        pdf.cell(0, 5, "Tel: +34 91 000 00 00 - Email: <EMAIL> - www.trikyprinter.com", 0, 1, "C")

        # Guardar el PDF
        filename = f"albaran_{shipping_data['shipping_id']}_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf"
        file_path = os.path.join(DELIVERY_NOTES_DIR, filename)
        pdf.output(file_path)

        return file_path

    except Exception as e:
        print(f"Error al generar albarán de entrega: {str(e)}")
        return None
