from enum import Enum
from typing import Optional, Union, List
from pydantic import BaseModel, Field


class ConsumableType(str, Enum):
    """Tipo de consumible"""
    PLATE = "Plancha"
    INK = "Tinta"
    BOX = "Caja"
    GLUE = "Pegamento"
    STAPLE = "Grapa"
    WIRE = "Alambre"
    OTHER = "Otro"


class ConsumableStatus(str, Enum):
    """Estado del consumible"""
    ACTIVE = "Activo"
    INACTIVE = "Inactivo"
    LOW_STOCK = "Stock Bajo"


class ConsumableCreate(BaseModel):
    """Modelo para crear un nuevo consumible"""
    consumable_id: str
    name: str
    type: ConsumableType
    description: str
    manufacturer: str
    unit_cost: float = Field(default=0.0)
    unit_type: str = Field(default="Unidad")
    
    # Campos específicos según el tipo de consumible
    # Para planchas
    plates_per_job: Optional[int] = None
    
    # Para tintas
    sheets_per_kg: Optional[int] = None
    color: Optional[str] = None
    
    # Para cajas
    capacity: Optional[int] = None  # Número de ejemplares que caben en la caja
    dimensions: Optional[dict] = None  # Dimensiones de la caja (ancho, alto, profundidad)
    
    # Campos comunes adicionales
    stock_quantity: Optional[float] = None
    min_stock_quantity: Optional[float] = None
    status: ConsumableStatus = Field(default=ConsumableStatus.ACTIVE)
    notes: Optional[str] = None


class ConsumableUpdate(BaseModel):
    """Modelo para actualizar un consumible existente"""
    name: Optional[str] = None
    type: Optional[ConsumableType] = None
    description: Optional[str] = None
    manufacturer: Optional[str] = None
    unit_cost: Optional[float] = None
    unit_type: Optional[str] = None
    
    # Campos específicos según el tipo de consumible
    plates_per_job: Optional[int] = None
    sheets_per_kg: Optional[int] = None
    color: Optional[str] = None
    capacity: Optional[int] = None
    dimensions: Optional[dict] = None
    
    # Campos comunes adicionales
    stock_quantity: Optional[float] = None
    min_stock_quantity: Optional[float] = None
    status: Optional[ConsumableStatus] = None
    notes: Optional[str] = None


class Consumable(ConsumableCreate):
    """Modelo completo de un consumible"""
    created_at: str
    updated_at: Optional[str] = None

    class Config:
        schema_extra = {
            "example": {
                "consumable_id": "CONS-001",
                "name": "Plancha Offset 70x100",
                "type": "Plancha",
                "description": "Plancha de aluminio para impresión offset",
                "manufacturer": "Agfa",
                "unit_cost": 5.75,
                "unit_type": "Unidad",
                "plates_per_job": 4,
                "stock_quantity": 50,
                "min_stock_quantity": 10,
                "status": "Activo",
                "notes": "Planchas para máquina B1",
                "created_at": "2023-01-01T12:00:00",
                "updated_at": "2023-01-01T12:00:00"
            }
        }
