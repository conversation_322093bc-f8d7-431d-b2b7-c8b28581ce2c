from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from enum import Enum


class ActivityType(str, Enum):
    LOGIN = "login"
    BUDGET_CREATE = "budget_create"
    BUDGET_UPDATE = "budget_update"
    BUDGET_APPROVE = "budget_approve"
    USER_CREATE = "user_create"
    USER_UPDATE = "user_update"
    USER_DELETE = "user_delete"


class ActivityLog(BaseModel):
    log_id: str = Field(...)
    user_id: str
    username: str
    activity_type: ActivityType
    description: str
    entity_id: Optional[str] = None  # ID del presupuesto, usuario, etc.
    timestamp: datetime = Field(default_factory=datetime.now)
    ip_address: Optional[str] = None
    additional_data: Optional[dict] = None
