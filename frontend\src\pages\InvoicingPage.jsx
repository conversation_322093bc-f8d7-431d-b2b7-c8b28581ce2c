import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  CircularProgress,
  Snackbar,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  Chip
} from '@mui/material';
import {
  PictureAsPdf as PdfIcon,
  Refresh as RefreshIcon,
  CalendarMonth as CalendarIcon,
  Receipt as ReceiptIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import InvoicePDF from '../components/InvoicePDF';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { getBillableBudgets, generateInvoice, listInvoices, downloadInvoice } from '../services/invoiceService';
import DescriptionTooltip from '../components/DescriptionTooltip';
import ClientInfoTooltip from '../components/ClientInfoTooltip';
import { useAuth } from '../contexts/AuthContext';
import { buildApiUrl } from '../config';

// Componente principal de la página de facturación
const InvoicingPage = () => {
  // Obtener el token de autenticación
  const { token } = useAuth();

  // Estado para las pestañas
  const [tabValue, setTabValue] = useState(0);

  // Estado para los presupuestos facturables
  const [billableBudgets, setBillableBudgets] = useState([]);
  const [loadingBudgets, setLoadingBudgets] = useState(false);

  // Estado para las facturas
  const [invoices, setInvoices] = useState([]);
  const [loadingInvoices, setLoadingInvoices] = useState(false);

  // Estado para los clientes
  const [clients, setClients] = useState([]);

  // Referencias para controlar las llamadas a la API
  const fetchedClientsRef = useRef(false);

  // Estado para el filtro de facturas
  const [filterYear, setFilterYear] = useState(new Date().getFullYear());
  const [filterMonth, setFilterMonth] = useState(new Date().getMonth() + 1);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);

  // Estado para mensajes de notificación
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Estado para el diálogo de visualización de PDF
  const [pdfDialog, setPdfDialog] = useState({
    open: false,
    invoicePath: null
  });

  // Función para obtener los clientes desde la API
  const fetchClients = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl('/clients'), {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!response.ok) throw new Error('Error al obtener los clientes');
      const data = await response.json();
      setClients(data.filter(client => client.active));
    } catch (err) {
      console.error('Error fetching clients:', err);
      showSnackbar(err.message, 'error');
    }
  }, [token]);

  // Función para obtener el cliente a partir de su ID
  const getClient = (clientId) => {
    return clients.find(c => c.client_id === clientId) || null;
  };

  // Cargar presupuestos facturables y clientes al montar el componente
  useEffect(() => {
    if (tabValue === 0) {
      fetchBillableBudgets();
    } else {
      fetchInvoices();
    }

    if (!fetchedClientsRef.current) {
      fetchClients();
      fetchedClientsRef.current = true;
    }
  }, [tabValue, fetchClients]);

  // Función para cargar presupuestos facturables
  const fetchBillableBudgets = async () => {
    try {
      setLoadingBudgets(true);
      const data = await getBillableBudgets();
      setBillableBudgets(data);
    } catch (error) {
      console.error('Error al cargar presupuestos facturables:', error);
      showSnackbar(`Error al cargar presupuestos facturables: ${error.message}`, 'error');
    } finally {
      setLoadingBudgets(false);
    }
  };

  // Función para cargar facturas
  const fetchInvoices = async () => {
    try {
      setLoadingInvoices(true);
      const data = await listInvoices(filterYear, filterMonth);
      setInvoices(data);
    } catch (error) {
      console.error('Error al cargar facturas:', error);
      showSnackbar(`Error al cargar facturas: ${error.message}`, 'error');
    } finally {
      setLoadingInvoices(false);
    }
  };

  // Función para generar una factura
  const handleGenerateInvoice = async (budgetId) => {
    try {
      setLoadingBudgets(true);
      const result = await generateInvoice(budgetId);
      showSnackbar(`Factura generada correctamente: ${result.invoice_number}`, 'success');

      // Actualizar la lista de presupuestos facturables
      fetchBillableBudgets();
    } catch (error) {
      console.error('Error al generar factura:', error);
      showSnackbar(`Error al generar factura: ${error.message}`, 'error');
    } finally {
      setLoadingBudgets(false);
    }
  };

  // Función para descargar una factura
  const handleDownloadInvoice = async (invoicePath) => {
    try {
      const blob = await downloadInvoice(invoicePath);

      // Crear un enlace para descargar el archivo
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = invoicePath.split('/').pop();
      document.body.appendChild(a);
      a.click();

      // Limpiar
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error al descargar factura:', error);
      showSnackbar(`Error al descargar factura: ${error.message}`, 'error');
    }
  };

  // Función para abrir el visor de PDF
  const handleOpenPdfViewer = (invoicePath) => {
    setPdfDialog({
      open: true,
      invoicePath
    });
  };

  // Función para cerrar el visor de PDF
  const handleClosePdfViewer = () => {
    setPdfDialog({
      open: false,
      invoicePath: null
    });
  };

  // Función para mostrar mensajes en el snackbar
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // Función para cerrar el snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Función para cambiar de pestaña
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Función para abrir el diálogo de filtro
  const handleOpenFilterDialog = () => {
    setFilterDialogOpen(true);
  };

  // Función para cerrar el diálogo de filtro
  const handleCloseFilterDialog = () => {
    setFilterDialogOpen(false);
  };

  // Función para aplicar el filtro
  const handleApplyFilter = () => {
    fetchInvoices();
    setFilterDialogOpen(false);
  };

  // Generar años para el selector
  const years = [];
  const currentYear = new Date().getFullYear();
  for (let i = currentYear - 5; i <= currentYear; i++) {
    years.push(i);
  }

  // Generar meses para el selector
  const months = [
    { value: 1, label: 'Enero' },
    { value: 2, label: 'Febrero' },
    { value: 3, label: 'Marzo' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Mayo' },
    { value: 6, label: 'Junio' },
    { value: 7, label: 'Julio' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Septiembre' },
    { value: 10, label: 'Octubre' },
    { value: 11, label: 'Noviembre' },
    { value: 12, label: 'Diciembre' }
  ];

  // Formatear fecha
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    try {
      if (dateString.includes('T')) {
        // Formato ISO
        return format(new Date(dateString), 'dd/MM/yyyy', { locale: es });
      } else if (dateString.includes('-')) {
        // Formato YYYY-MM
        const [year, month] = dateString.split('-');
        return `${months.find(m => m.value === parseInt(month))?.label} ${year}`;
      }

      return dateString;
    } catch (error) {
      console.error('Error al formatear fecha:', error);
      return dateString;
    }
  };

  // Renderizar chip de estado
  const renderStatusChip = (status) => {
    let color = 'default';

    switch (status) {
      case 'Completado':
        color = 'primary';
        break;
      case 'Enviado':
        color = 'success';
        break;
      case 'Facturado':
        color = 'secondary';
        break;
      case 'Rechazado':
        color = 'error';
        break;
      case 'Aprobado':
        color = 'success';
        break;
      case 'Actualizado':
        color = 'warning';
        break;
      case 'Cancelado':
        color = 'default';
        break;
      default:
        color = 'default';
    }

    return (
      <Chip
        label={status}
        color={color}
        size="small"
        sx={{ fontWeight: 'bold', minWidth: '100px' }}
      />
    );
  };

  return (
    <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Facturación</Typography>

        {tabValue === 0 ? (
          <Box>
            <Button
              variant="contained"
              color="primary"
              startIcon={<ReceiptIcon />}
              onClick={() => setTabValue(1)}
              sx={{ mr: 2 }}
            >
              Ver Facturas Emitidas
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchBillableBudgets}
              disabled={loadingBudgets}
            >
              Actualizar
            </Button>
          </Box>
        ) : (
          <Box>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={handleOpenFilterDialog}
              sx={{ mr: 2 }}
            >
              Mostrar Filtros
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchInvoices}
              disabled={loadingInvoices}
            >
              Actualizar
            </Button>
          </Box>
        )}
      </Box>

      <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 3 }}>
        <Tab label="Presupuestos Facturables" />
        <Tab label="Facturas Emitidas" />
      </Tabs>

      {/* Pestaña de Presupuestos Facturables */}
      {tabValue === 0 && (
        <TableContainer component={Paper} sx={{ maxHeight: 'calc(100vh - 250px)' }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>OT</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Descripción</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Cliente</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Tipo</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Cantidad</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Coste Total</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>PVP</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Estado</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Fecha</TableCell>
                <TableCell align="center" sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loadingBudgets ? (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    <CircularProgress size={30} />
                  </TableCell>
                </TableRow>
              ) : billableBudgets.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} align="center">
                    No hay presupuestos facturables
                  </TableCell>
                </TableRow>
              ) : (
                billableBudgets.map((budget) => (
                  <TableRow key={budget.budget_id}>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{budget.ot_number}</TableCell>
                    <TableCell sx={{ maxWidth: '300px', fontSize: '0.85rem' }}>
                      <DescriptionTooltip description={budget.description || 'Sin descripción'} />
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>
                      <ClientInfoTooltip client={getClient(budget.client_id) || budget.client_data} />
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{budget.job_type}</TableCell>
                    <TableCell align="center" sx={{ fontSize: '0.85rem', whiteSpace: 'nowrap' }}>{budget.quantity}</TableCell>
                    <TableCell align="right" sx={{ fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
                        {budget.total_cost?.toFixed(2)} €
                      </Typography>
                    </TableCell>
                    <TableCell align="right" sx={{ fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
                        {budget.pvp?.toFixed(2)} €
                      </Typography>
                    </TableCell>
                    <TableCell align="center" sx={{ fontSize: '0.85rem' }}>{renderStatusChip(budget.status)}</TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{formatDate(budget.updated_at)}</TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                        <Tooltip title="A Facturar">
                          <IconButton
                            color="primary"
                            size="small"
                            onClick={() => handleGenerateInvoice(budget.budget_id)}
                            disabled={loadingBudgets}
                            sx={{ padding: '4px' }}
                          >
                            <ReceiptIcon sx={{ fontSize: '1rem' }} />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Pestaña de Facturas Emitidas */}
      {tabValue === 1 && (
        <TableContainer component={Paper} sx={{ maxHeight: 'calc(100vh - 250px)' }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Número de Factura</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>OT</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Descripción</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Cliente</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Tipo</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Cantidad</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Importe</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Fecha Creación</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Fecha Completado</TableCell>
                <TableCell sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Fecha Envío</TableCell>
                <TableCell align="center" sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}>Acciones</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loadingInvoices ? (
                <TableRow>
                  <TableCell colSpan={11} align="center">
                    <CircularProgress size={30} />
                  </TableCell>
                </TableRow>
              ) : invoices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={11} align="center">
                    No hay facturas emitidas para el período seleccionado
                  </TableCell>
                </TableRow>
              ) : (
                invoices.map((invoice) => (
                  <TableRow key={invoice.invoice_number}>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{invoice.invoice_number}</TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{invoice.ot_number || 'N/A'}</TableCell>
                    <TableCell sx={{ maxWidth: '300px', fontSize: '0.85rem' }}>
                      <DescriptionTooltip description={invoice.description || 'Sin descripción'} />
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>
                      <ClientInfoTooltip client={getClient(invoice.client_id) || invoice.client_data || {
                        company: { name: invoice.client_company || invoice.client_name || 'Cliente no especificado' },
                        contact: {}
                      }} />
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{invoice.job_type || 'N/A'}</TableCell>
                    <TableCell align="center" sx={{ fontSize: '0.85rem', whiteSpace: 'nowrap' }}>{invoice.quantity || 'N/A'}</TableCell>
                    <TableCell align="right" sx={{ fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
                      {invoice.amount ? (
                        <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.85rem', whiteSpace: 'nowrap' }}>
                          {invoice.amount.toFixed(2)} €
                        </Typography>
                      ) : 'N/A'}
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{formatDate(invoice.created_at) || 'N/A'}</TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{formatDate(invoice.completed_at) || 'N/A'}</TableCell>
                    <TableCell sx={{ fontSize: '0.85rem' }}>{formatDate(invoice.shipped_at) || 'N/A'}</TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                        <Tooltip title="Ver Detalles">
                          <span>
                            <IconButton
                              color="info"
                              size="small"
                              sx={{ padding: '4px' }}
                            >
                              <VisibilityIcon sx={{ fontSize: '1rem' }} />
                            </IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip title="Descargar Factura">
                          <span>
                            <IconButton
                              color="primary"
                              size="small"
                              onClick={() => handleDownloadInvoice(invoice.invoice_path)}
                              sx={{ padding: '4px' }}
                            >
                              <DownloadIcon sx={{ fontSize: '1rem' }} />
                            </IconButton>
                          </span>
                        </Tooltip>
                        <Tooltip title="Ver Factura">
                          <span>
                            <IconButton
                              color="secondary"
                              size="small"
                              onClick={() => handleOpenPdfViewer(invoice.invoice_path)}
                              sx={{ padding: '4px' }}
                            >
                              <PdfIcon sx={{ fontSize: '1rem' }} />
                            </IconButton>
                          </span>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Diálogo de filtro de facturas */}
      <Dialog
        open={filterDialogOpen}
        onClose={handleCloseFilterDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Filtrar Facturas</Typography>
          <Button
            variant="outlined"
            color="primary"
            size="small"
            onClick={() => {
              setFilterYear(new Date().getFullYear());
              setFilterMonth(new Date().getMonth() + 1);
            }}
          >
            Restablecer Filtros
          </Button>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Período</Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    select
                    label="Año"
                    value={filterYear}
                    onChange={(e) => setFilterYear(e.target.value)}
                    fullWidth
                    size="small"
                  >
                    {years.map((year) => (
                      <MenuItem key={year} value={year}>
                        {year}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    select
                    label="Mes"
                    value={filterMonth}
                    onChange={(e) => setFilterMonth(e.target.value)}
                    fullWidth
                    size="small"
                  >
                    {months.map((month) => (
                      <MenuItem key={month.value} value={month.value}>
                        {month.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Ordenar por</Typography>
              <TextField
                select
                label="Campo"
                value="invoice_date"
                fullWidth
                size="small"
              >
                <MenuItem value="invoice_date">Fecha de factura</MenuItem>
                <MenuItem value="invoice_number">Número de factura</MenuItem>
                <MenuItem value="client_name">Cliente</MenuItem>
                <MenuItem value="amount">Importe</MenuItem>
              </TextField>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button onClick={handleCloseFilterDialog} variant="outlined">Cancelar</Button>
          <Button onClick={handleApplyFilter} variant="contained" color="primary">Aplicar Filtros</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar para mensajes */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Diálogo para visualizar el PDF de la factura */}
      {pdfDialog.open && (
        <InvoicePDF
          invoicePath={pdfDialog.invoicePath}
          onClose={handleClosePdfViewer}
        />
      )}
    </Paper>
  );
};

export default InvoicingPage;
