import React from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Box,
  Typography
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PdfUploader from './PdfUploader';

/**
 * Componente para el diálogo del selector de PDF
 */
const PdfSelectorDialog = ({
  open,
  onClose,
  selectedPdf,
  setSelectedPdf,
  onPdfInfoChange,
  showSnackbar,
  onPageCountChange,
  onPageSizeChange,
  pdfFilename
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        Seleccionar Archivo PDF
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <PdfUploader
          selectedPdf={selectedPdf}
          setSelectedPdf={setSelectedPdf}
          onPdfInfoChange={onPdfInfoChange}
          showSnackbar={showSnackbar}
          onPageCountChange={onPageCountChange}
          onPageSizeChange={onPageSizeChange}
        />

        {pdfFilename && (
          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="subtitle1" gutterBottom>
              Archivo PDF seleccionado:
            </Typography>
            <Typography variant="body2">
              {pdfFilename}
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

PdfSelectorDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedPdf: PropTypes.object,
  setSelectedPdf: PropTypes.func.isRequired,
  onPdfInfoChange: PropTypes.func.isRequired,
  showSnackbar: PropTypes.func.isRequired,
  onPageCountChange: PropTypes.func.isRequired,
  onPageSizeChange: PropTypes.func.isRequired,
  pdfFilename: PropTypes.string
};

export default PdfSelectorDialog;
