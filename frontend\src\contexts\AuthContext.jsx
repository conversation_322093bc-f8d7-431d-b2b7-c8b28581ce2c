import React, { createContext, useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { buildApiUrl } from '../config';

// Crear el contexto de autenticación
const AuthContext = createContext();

// Hook personalizado para usar el contexto de autenticación
export const useAuth = () => {
  return useContext(AuthContext);
};

// Proveedor del contexto de autenticación
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('auth_token') || null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [role, setRole] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Efecto para verificar la autenticación al cargar la aplicación
  useEffect(() => {
    const checkAuth = async () => {
      const storedToken = localStorage.getItem('auth_token');

      if (storedToken) {
        try {
          // Verificar el token con el backend
          const response = await fetch(buildApiUrl('/auth/me'), {
            headers: {
              'Authorization': `Bearer ${storedToken}`
            }
          });

          if (response.ok) {
            const userData = await response.json();
            setUser(userData);
            setToken(storedToken);
            setIsAuthenticated(true);
            setRole(userData.role);
          } else {
            // Token inválido, limpiar el almacenamiento
            localStorage.removeItem('auth_token');
            setUser(null);
            setToken(null);
            setIsAuthenticated(false);
            setRole(null);
          }
        } catch (error) {
          console.error('Error al verificar la autenticación:', error);
          localStorage.removeItem('auth_token');
          setUser(null);
          setToken(null);
          setIsAuthenticated(false);
          setRole(null);
        }
      }

      setLoading(false);
    };

    checkAuth();
  }, []);

  // Función para iniciar sesión
  const login = async (username, password) => {
    try {
      // Crear un objeto URLSearchParams para enviar las credenciales como application/x-www-form-urlencoded
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('password', password);

      // Realizar la solicitud de inicio de sesión
      const response = await fetch(buildApiUrl('/auth/login'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al iniciar sesión');
      }

      const data = await response.json();

      // Guardar el token en el almacenamiento local
      localStorage.setItem('auth_token', data.access_token);

      // Obtener información del usuario
      const userResponse = await fetch(buildApiUrl('/auth/me'), {
        headers: {
          'Authorization': `Bearer ${data.access_token}`
        }
      });

      if (!userResponse.ok) {
        throw new Error('Error al obtener información del usuario');
      }

      const userData = await userResponse.json();

      // Actualizar el estado
      setToken(data.access_token);
      setUser(userData);
      setIsAuthenticated(true);
      setRole(userData.role);

      return { success: true };
    } catch (error) {
      console.error('Error en login:', error);
      return { success: false, error: error.message };
    }
  };

  // Función para cerrar sesión
  const logout = () => {
    localStorage.removeItem('auth_token');
    setUser(null);
    setToken(null);
    setIsAuthenticated(false);
    setRole(null);
    navigate('/login');
  };

  // Valor del contexto
  const value = {
    user,
    token,
    isAuthenticated,
    role,
    loading,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
