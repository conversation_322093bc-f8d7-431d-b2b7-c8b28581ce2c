/**
 * Servicio especializado para el envío y validación de presupuestos
 * Extrae la lógica compleja de handleSubmit del BudgetForm
 */

import budgetSubmitService from './budgetSubmitService';

/**
 * Valida que las partes del presupuesto tengan cálculos actualizados en modo edición
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {boolean} isEditMode - Si está en modo edición
 * @returns {Object} - { isValid: boolean, error: string, partsCount: number }
 */
export const validatePartsCalculations = (budgetParts, isEditMode) => {
  if (!isEditMode) {
    return { isValid: true, error: null, partsCount: 0 };
  }

  // Verificar si hay alguna parte que no tenga cálculos actualizados
  const partsWithoutCalculation = budgetParts.filter(part => {
    // Verificar si la parte tiene los datos básicos necesarios para el cálculo
    if (part.paper && part.machine && part.pageCount) {
      // Si tiene los datos básicos pero no tiene sheetCalculation o está desactualizado
      return !part.sheetCalculation || part.sheetCalculation.outdated === true;
    }
    // Si no tiene los datos básicos, no la consideramos para la validación
    return false;
  });

  if (partsWithoutCalculation.length > 0) {
    const error = `No se puede actualizar el presupuesto. ${partsWithoutCalculation.length} ${partsWithoutCalculation.length === 1 ? 'parte necesita' : 'partes necesitan'} ser recalculada${partsWithoutCalculation.length === 1 ? '' : 's'}.`;
    return { isValid: false, error, partsCount: partsWithoutCalculation.length };
  }

  return { isValid: true, error: null, partsCount: 0 };
};

/**
 * Calcula el costo de una parte individual
 * @param {Object} part - Parte del presupuesto
 * @returns {number} - Costo total de la parte
 */
export const calculatePartCost = (part) => {
  if (!part.sheetCalculation) {
    return 0;
  }

  // Obtener los costos directamente del endpoint
  const paperCost = part.sheetCalculation.paper_cost || 0;
  const machineCost = part.sheetCalculation.machine_cost || 0;

  // Determinar si es una máquina digital
  const isDigital = part.machine && part.machine.type === 'Digital';

  if (isDigital) {
    // Para máquinas digitales, usar el costo de clicks
    const clickCost = part.sheetCalculation.click_cost || 0;
    return paperCost + machineCost + clickCost;
  } else {
    // Para máquinas offset, usar el costo de planchas, tinta y maculatura
    const plateCost = part.sheetCalculation.plates_cost || 0;
    const inkCost = part.sheetCalculation.ink_cost || 0;

    // Calcular el costo de maculatura si está disponible (solo para offset)
    let calculatedMaculaturaCost = 0;
    if (part.sheetCalculation?.total_maculatura > 0) {
      const precioPorMillar = part.sheetCalculation.paper_cost_per_1000 ||
                            (part.paper && part.paper.price_per_1000) || 0;
      calculatedMaculaturaCost = (part.sheetCalculation.total_maculatura * precioPorMillar) / 1000;
    }

    return paperCost + machineCost + plateCost + inkCost + calculatedMaculaturaCost;
  }
};

/**
 * Calcula el costo total de todas las partes del presupuesto
 * @param {Array} budgetParts - Partes del presupuesto
 * @returns {number} - Costo total de las partes
 */
export const calculatePartsCostTotal = (budgetParts) => {
  if (!budgetParts || budgetParts.length === 0) {
    return 0;
  }

  return budgetParts.reduce((total, part) => {
    return total + calculatePartCost(part);
  }, 0);
};

/**
 * Calcula el costo total del presupuesto
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {number} calculatedProcessCost - Costo de procesos
 * @param {Object} budget - Presupuesto actual
 * @returns {Object} - { partsCostTotal, processCost, shippingCost, totalCost }
 */
export const calculateBudgetTotalCost = (budgetParts, calculatedProcessCost, budget) => {
  const partsCostTotal = calculatePartsCostTotal(budgetParts);
  const processCost = calculatedProcessCost || 0;
  const shippingCost = budget.shipping?.cost || 0;
  const totalCost = partsCostTotal + processCost + shippingCost;

  return {
    partsCostTotal,
    processCost,
    shippingCost,
    totalCost
  };
};

/**
 * Prepara el presupuesto actualizado con los costos calculados
 * @param {Object} budget - Presupuesto original
 * @param {Object} costBreakdown - Desglose de costos
 * @returns {Object} - Presupuesto actualizado
 */
export const prepareBudgetForSubmission = (budget, costBreakdown) => {
  return {
    ...budget,
    // Copiar los valores de shipping a los campos principales
    shipping_cost: budget.shipping?.cost || 0,
    total_paper_weight_kg: budget.shipping?.weight || 0,
    // Añadir el costo total
    total_cost: costBreakdown.totalCost,
    // Añadir desglose de costos para referencia
    cost_breakdown: {
      parts_cost: costBreakdown.partsCostTotal,
      process_cost: costBreakdown.processCost,
      shipping_cost: costBreakdown.shippingCost,
      total_cost: costBreakdown.totalCost
    }
  };
};

/**
 * Maneja la navegación después del envío exitoso
 * @param {number} delay - Retraso en milisegundos antes de navegar
 */
export const handlePostSubmissionNavigation = (delay = 1500) => {
  setTimeout(() => {
    // Cambiar a la pestaña de listado de presupuestos
    window.dispatchEvent(new CustomEvent('navigate-to-budget-list'));
  }, delay);
};

/**
 * Función principal para manejar el envío del presupuesto
 * @param {Object} params - Parámetros para el envío
 * @returns {Promise<Object|null>} - Presupuesto guardado o null si falló
 */
export const handleBudgetSubmission = async (params) => {
  const {
    e,
    budget,
    budgetParts,
    selectedProcesses,
    calculatedProcessCost,
    selectedPdf,
    pdfInfo,
    colorConfig,
    productConfig,
    budgetId,
    isEditMode,
    showSnackbar,
    setCalculatedProcessCost,
    buildApiUrl,
    budgetSubmitService
  } = params;

  try {
    // Prevenir el comportamiento por defecto del formulario
    e.preventDefault();

    // 1. Validar que todas las partes hayan sido recalculadas si estamos en modo edición
    const validation = validatePartsCalculations(budgetParts, isEditMode);
    if (!validation.isValid) {
      showSnackbar(validation.error, 'error');
      return null;
    }

    // 2. Calcular el costo total (suma de partes, acabados y envío)
    const costBreakdown = calculateBudgetTotalCost(budgetParts, calculatedProcessCost, budget);

    // 3. Preparar el presupuesto actualizado
    const updatedBudget = prepareBudgetForSubmission(budget, costBreakdown);

    // 4. Usar el servicio para enviar el presupuesto
    const savedBudget = await budgetSubmitService.submitBudget({
      e,
      budget: updatedBudget,
      budgetParts,
      selectedProcesses,
      calculatedProcessCost,
      selectedPdf,
      pdfInfo,
      colorConfig,
      productConfig,
      budgetId,
      isEditMode,
      showSnackbar,
      setCalculatedProcessCost,
      buildApiUrl
    });

    // 5. Si el presupuesto se guardó correctamente, manejar la navegación
    if (savedBudget) {
      handlePostSubmissionNavigation();
    }

    return savedBudget;
  } catch (error) {
    console.error('Error en handleBudgetSubmission:', error);
    showSnackbar(`Error al procesar el presupuesto: ${error.message}`, 'error');
    return null;
  }
};

/**
 * Función simplificada para el envío de presupuestos
 * @param {Event} e - Evento del formulario
 * @param {Object} budget - Presupuesto
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {Array} selectedProcesses - Procesos seleccionados
 * @param {number} calculatedProcessCost - Costo calculado de procesos
 * @param {Object} selectedPdf - PDF seleccionado
 * @param {Object} pdfInfo - Información del PDF
 * @param {Object} colorConfig - Configuración de colores
 * @param {Object} productConfig - Configuración del producto
 * @param {string} budgetId - ID del presupuesto (para edición)
 * @param {boolean} isEditMode - Modo edición
 * @param {Function} showSnackbar - Función para mostrar mensajes
 * @param {Function} setCalculatedProcessCost - Setter para costo de procesos
 * @param {Function} buildApiUrl - Función para construir URLs
 * @param {Object} budgetSubmitService - Servicio de envío
 * @returns {Promise<Object|null>} - Presupuesto guardado o null
 */
export const submitBudget = async (
  e,
  budget,
  budgetParts,
  selectedProcesses,
  calculatedProcessCost,
  selectedPdf,
  pdfInfo,
  colorConfig,
  productConfig,
  budgetId,
  isEditMode,
  showSnackbar,
  setCalculatedProcessCost,
  buildApiUrl,
  budgetSubmitService
) => {
  return await handleBudgetSubmission({
    e,
    budget,
    budgetParts,
    selectedProcesses,
    calculatedProcessCost,
    selectedPdf,
    pdfInfo,
    colorConfig,
    productConfig,
    budgetId,
    isEditMode,
    showSnackbar,
    setCalculatedProcessCost,
    buildApiUrl,
    budgetSubmitService
  });
};

/**
 * Obtiene estadísticas del presupuesto para validación
 * @param {Array} budgetParts - Partes del presupuesto
 * @param {boolean} isEditMode - Modo edición
 * @returns {Object} - Estadísticas del presupuesto
 */
export const getBudgetStats = (budgetParts, isEditMode) => {
  const totalParts = budgetParts.length;
  const partsWithCalculations = budgetParts.filter(part => part.sheetCalculation).length;
  const partsWithoutCalculations = totalParts - partsWithCalculations;
  const validation = validatePartsCalculations(budgetParts, isEditMode);

  return {
    totalParts,
    partsWithCalculations,
    partsWithoutCalculations,
    isValid: validation.isValid,
    validationError: validation.error,
    needsRecalculation: !validation.isValid && isEditMode
  };
};

export default {
  handleBudgetSubmission,
  submitBudget,
  validatePartsCalculations,
  calculatePartCost,
  calculatePartsCostTotal,
  calculateBudgetTotalCost,
  prepareBudgetForSubmission,
  handlePostSubmissionNavigation,
  getBudgetStats
};
