import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from models.shipping import ShippingRecord

# Ruta al archivo de envíos
SHIPPING_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "shipping.json")

def load_shipping_data() -> List[Dict[str, Any]]:
    """
    Carga los datos de envíos desde el archivo JSON
    """
    try:
        if os.path.exists(SHIPPING_FILE):
            with open(SHIPPING_FILE, "r", encoding="utf-8") as file:
                return json.load(file)
        return []
    except Exception as e:
        print(f"Error al cargar datos de envíos: {str(e)}")
        return []

def save_shipping_data(shipping_data: List[Dict[str, Any]]) -> bool:
    """
    Guarda los datos de envíos en el archivo JSON
    """
    try:
        # Asegurarse de que el directorio existe
        os.makedirs(os.path.dirname(SHIPPING_FILE), exist_ok=True)

        with open(SHIPPING_FILE, "w", encoding="utf-8") as file:
            json.dump(shipping_data, file, indent=4, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error al guardar datos de envíos: {str(e)}")
        return False

def add_shipping_record(shipping_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Añade un nuevo registro de envío
    """
    # Cargar datos existentes
    shipping_records = load_shipping_data()

    # Crear un nuevo registro
    new_record = ShippingRecord(**shipping_data).dict()

    # Añadir el nuevo registro
    shipping_records.append(new_record)

    # Guardar los datos actualizados
    save_shipping_data(shipping_records)

    return new_record

def get_shipping_record_by_id(shipping_id: str) -> Optional[Dict[str, Any]]:
    """
    Obtiene un registro de envío por su ID
    """
    shipping_records = load_shipping_data()
    for record in shipping_records:
        if record["shipping_id"] == shipping_id:
            return record
    return None

def get_shipping_records_by_ot_number(ot_number: str) -> List[Dict[str, Any]]:
    """
    Obtiene todos los registros de envío asociados a un número de OT
    """
    shipping_records = load_shipping_data()
    return [record for record in shipping_records if record["ot_number"] == ot_number]

def get_all_shipping_records() -> List[Dict[str, Any]]:
    """
    Obtiene todos los registros de envío
    """
    return load_shipping_data()

def update_shipping_record(shipping_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Actualiza un registro de envío
    """
    shipping_records = load_shipping_data()

    for i, record in enumerate(shipping_records):
        if record["shipping_id"] == shipping_id:
            # Actualizar solo los campos proporcionados
            for key, value in update_data.items():
                if key in record:
                    record[key] = value

            # Actualizar la fecha de actualización
            record["updated_at"] = datetime.now().isoformat()

            # Guardar los cambios
            shipping_records[i] = record
            save_shipping_data(shipping_records)

            return record

    return None

def delete_shipping_record(shipping_id: str) -> bool:
    """
    Elimina un registro de envío por su ID
    """
    shipping_records = load_shipping_data()

    # Buscar el registro a eliminar
    for i, record in enumerate(shipping_records):
        if record["shipping_id"] == shipping_id:
            # Eliminar el registro
            shipping_records.pop(i)

            # Guardar los cambios
            return save_shipping_data(shipping_records)

    return False
