import { useState, useEffect, useMemo } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  CircularProgress,
  Alert,
  AlertTitle,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Tooltip,
  FormControlLabel,
  Switch
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';

import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/es';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import withDragAndDrop from 'react-big-calendar/lib/addons/dragAndDrop';
import 'react-big-calendar/lib/addons/dragAndDrop/styles.css';
// Función local para reorganizar procesos (anteriormente en offsetUtils)
const reorganizeProcesses = async (processes, machineId, fromDate) => {
  // Filtrar procesos pendientes y en proceso para la máquina específica
  const processesToMove = processes.filter(process =>
    process.machine_id === machineId &&
    (process.status === 'Pendiente' || process.status === 'En Proceso')
  );

  if (processesToMove.length === 0) {
    return [];
  }

  // Calcular el siguiente día laborable
  const nextWorkDay = new Date(fromDate);
  nextWorkDay.setDate(nextWorkDay.getDate() + 1);

  // Si es fin de semana, mover al lunes
  if (nextWorkDay.getDay() === 0) { // Domingo
    nextWorkDay.setDate(nextWorkDay.getDate() + 1);
  } else if (nextWorkDay.getDay() === 6) { // Sábado
    nextWorkDay.setDate(nextWorkDay.getDate() + 2);
  }

  // Establecer hora de inicio (8:00 AM)
  nextWorkDay.setHours(8, 0, 0, 0);

  // Reorganizar procesos secuencialmente
  let currentStartTime = new Date(nextWorkDay);

  return processesToMove.map(process => {
    const duration = process.estimated_hours || 1;
    const endTime = new Date(currentStartTime);
    endTime.setTime(currentStartTime.getTime() + (duration * 60 * 60 * 1000));

    const updatedProcess = {
      ...process,
      start_date: currentStartTime.toISOString(),
      end_date: endTime.toISOString()
    };

    // Actualizar tiempo de inicio para el siguiente proceso
    currentStartTime = new Date(endTime);

    return updatedProcess;
  });
};
import { buildApiUrl } from '../../config';
import { ApiInterceptor } from '../../services/simplifiedServices';
import { format } from 'date-fns';
import PropTypes from 'prop-types';
import MachineContainer from './MachineContainer';
import ListIcon from '@mui/icons-material/List';

// Categorías de máquinas
const MACHINE_CATEGORIES = {
  'IMPRESION': ['Offset', 'Digital', 'Plotter', 'CTP'],
  'ACABADOS': ['Encuadernadora', 'Guillotina', 'Plegadora']
};

// Configurar moment para localización
moment.locale('es');
const localizer = momentLocalizer(moment);

// Crear el componente de calendario con drag & drop
const DnDCalendar = withDragAndDrop(Calendar);

// Colores para los diferentes estados
const STATUS_COLORS = {
  'Pendiente': '#ff9800',  // Naranja
  'En Proceso': '#2196f3', // Azul
  'Completado': '#4caf50', // Verde
  'Cancelado': '#f44336'   // Rojo
};

const MachinePlanner = () => {
  // Estado para la máquina seleccionada
  const [selectedMachine, setSelectedMachine] = useState(null);
  // Estados básicos
  const [processes, setProcesses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedProcess, setSelectedProcess] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [successMessage, setSuccessMessage] = useState(null);
  const [machines, setMachines] = useState([]);
  const [loadingMachines, setLoadingMachines] = useState(true);

  // Función para navegar a la lista de producción
  const navigateToProductionList = () => {
    // Verificar que tenemos un proceso seleccionado
    if (!selectedProcess || !selectedProcess.process_id) {
      // No hay proceso seleccionado o el proceso no tiene ID
      setError('No se puede navegar a la lista de producción porque no hay proceso seleccionado');
      return;
    }

    // Navegando a la lista de producción con el proceso seleccionado

    // Almacenar el ID del proceso en sessionStorage directamente
    sessionStorage.setItem('highlightProcessId', selectedProcess.process_id);

    // Mostrar mensaje de éxito
    setSuccessMessage(`Navegando a la lista de producción para el proceso ${selectedProcess.name}`);

    // Cerrar el modal
    setIsEditModalOpen(false);

    // Navegar directamente a la pestaña de ProductionList
    // Esto es más compatible que usar CustomEvent
    window.location.hash = '#production';

    // Recargar la página para asegurar que se cargue ProductionList
    // con el ID del proceso almacenado en sessionStorage
    window.location.reload();
  };

  // Estados para varios filtros y vistas
  const [view, setView] = useState('week'); // 'day', 'week', 'work_week'
  const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'Pendiente', 'En Proceso', etc.

  // Cargar todas las máquinas
  useEffect(() => {
    const fetchMachines = async () => {
      try {
        setLoadingMachines(true);
        setError(null);

        const url = buildApiUrl('/machines/');
        const response = await ApiInterceptor.fetch(url);

        if (!response.ok) {
          throw new Error(`Error al obtener máquinas: ${response.statusText}`);
        }

        const data = await response.json();

        // Filtrar solo máquinas activas y que deben mostrarse en el planificador
        const activeMachines = data.filter(machine =>
          machine.status === 'Activa' &&
          (machine.planificar === undefined || machine.planificar === true)
        );

        // Ordenar máquinas: primero impresión, luego acabados
        activeMachines.sort((a, b) => {
          // Determinar la categoría de cada máquina
          const categoryA = MACHINE_CATEGORIES.IMPRESION.includes(a.type) ? 'IMPRESION' : 'ACABADOS';
          const categoryB = MACHINE_CATEGORIES.IMPRESION.includes(b.type) ? 'IMPRESION' : 'ACABADOS';

          // Si son de diferentes categorías, ordenar por categoría
          if (categoryA !== categoryB) {
            return categoryA === 'IMPRESION' ? -1 : 1;
          }

          // Si son de la misma categoría, ordenar por tipo y luego por nombre
          if (a.type !== b.type) {
            return a.type.localeCompare(b.type);
          }

          return a.name.localeCompare(b.name);
        });

        // Máquinas activas encontradas
        setMachines(activeMachines);

        // Si no hay máquina seleccionada y hay máquinas disponibles, seleccionar la primera
        if (!selectedMachine && activeMachines.length > 0) {
          setSelectedMachine(activeMachines[0]);
        }
      } catch (err) {
        // Error al cargar máquinas
        setError(`Error al cargar las máquinas: ${err.message}`);
      } finally {
        setLoadingMachines(false);
      }
    };

    fetchMachines();
  }, [selectedMachine]);

  // Cargar procesos de producción
  useEffect(() => {
    const fetchProcesses = async () => {
      // Si no hay máquina seleccionada, no cargar procesos
      if (!selectedMachine) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const url = buildApiUrl('/production/');

        // Obteniendo procesos desde la API

        const response = await ApiInterceptor.fetch(url);
        if (!response.ok) {
          throw new Error(`Error al obtener procesos: ${response.statusText}`);
        }

        const data = await response.json();

        // Filtrar procesos para la máquina seleccionada (tanto impresión como acabado)
        const machineProcesses = data.filter(process =>
          process.machine_id === selectedMachine.machine_id
        );

        // Procesos encontrados para la máquina seleccionada

        // Verificar si hay tiempos de presupuesto disponibles para estos procesos
        // y actualizar las fechas de inicio/fin si es necesario
        const updatedProcesses = await Promise.all(machineProcesses.map(async (process) => {
          // Si no hay ID de presupuesto, devolver el proceso sin cambios
          if (!process.budget_id) return process;

          try {
            // Obtener los tiempos del presupuesto
            const budgetTimesUrl = buildApiUrl(`/budgets/${process.budget_id}/times`);
            const budgetTimesResponse = await ApiInterceptor.fetch(budgetTimesUrl);

            if (!budgetTimesResponse.ok) {
              return process; // Si no se pueden obtener los tiempos, devolver el proceso sin cambios
            }

            const budgetTimes = await budgetTimesResponse.json();

            // Si no hay tiempos para este presupuesto, devolver el proceso sin cambios
            if (!budgetTimes || Object.keys(budgetTimes).length === 0) {
              return process;
            }

            // Buscar el tiempo para este proceso
            let budgetTime = null;

            // Intentar con diferentes formatos de clave
            const possibleKeys = [
              `${process.process_type}: ${process.name.replace(`${process.process_type}: `, '')}`,
              process.name,
              process.name.replace(`${process.process_type}: `, '')
            ];

            for (const key of possibleKeys) {
              if (budgetTimes[key] !== undefined) {
                budgetTime = budgetTimes[key];
                break;
              }
            }

            // Si encontramos un tiempo en el presupuesto y es diferente al actual,
            // actualizar la fecha de fin para que sea coherente
            if (budgetTime !== null && Math.abs(budgetTime - parseFloat(process.estimated_hours)) > 0.01) {
              const startDate = new Date(process.start_date);
              const newEndDate = new Date(startDate);
              newEndDate.setTime(startDate.getTime() + (budgetTime * 60 * 60 * 1000));

              // Actualizar el proceso en el backend
              const updateUrl = buildApiUrl(`/production/${process.process_id}`);
              const updateResponse = await ApiInterceptor.fetch(updateUrl, {
                method: 'PATCH',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  estimated_hours: budgetTime,
                  end_date: newEndDate.toISOString()
                })
              });

              if (updateResponse.ok) {
                const updatedProcess = await updateResponse.json();
                return updatedProcess;
              }
            }

            return process;
          } catch (error) {
            return process; // En caso de error, devolver el proceso sin cambios
          }
        }));

        setProcesses(updatedProcesses);
        setSuccessMessage(`Se han cargado ${updatedProcesses.length} trabajos para ${selectedMachine.name}`);

        // Limpiar mensaje después de 3 segundos
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (err) {
        // Error al cargar procesos
        setError(`Error al cargar los procesos: ${err.message}`);

        // Limpiar mensaje de error después de 10 segundos
        setTimeout(() => {
          setError(null);
        }, 10000);
      } finally {
        setLoading(false);
      }
    };

    fetchProcesses();
  }, [selectedMachine]);

  // Convertir procesos a eventos para el calendario
  const calendarEvents = useMemo(() => {
    if (!processes.length) return [];

    // Convirtiendo procesos a eventos de calendario

    return processes.map(process => {
      // Determinar color según estado
      const backgroundColor = STATUS_COLORS[process.status] || '#757575';

      return {
        id: process.process_id,
        title: `${process.ot_number} - ${process.name}`,
        start: new Date(process.start_date),
        end: new Date(process.end_date),
        allDay: false,
        resource: process,
        backgroundColor
      };
    });
  }, [processes]);

  // Filtrar eventos por estado
  const filteredEvents = useMemo(() => {
    if (filterStatus === 'all') return calendarEvents;

    return calendarEvents.filter(event =>
      event.resource.status === filterStatus
    );
  }, [calendarEvents, filterStatus]);

  // Función para verificar si un evento se solapa con otros
  const checkForOverlap = (eventId, start, end) => {
    if (!selectedMachine) return false;

    // Filtrar eventos de la misma máquina, excluyendo el evento actual
    const machineEvents = filteredEvents.filter(event =>
      event.id !== eventId &&
      event.resource.machine_id === selectedMachine.machine_id
    );

    // Verificar solapamientos
    for (const event of machineEvents) {
      // Si el inicio del nuevo evento está entre el inicio y fin de un evento existente
      if ((start >= event.start && start < event.end) ||
          // O si el fin del nuevo evento está entre el inicio y fin de un evento existente
          (end > event.start && end <= event.end) ||
          // O si el nuevo evento engloba completamente a un evento existente
          (start <= event.start && end >= event.end)) {
        return true; // Hay solapamiento
      }
    }

    return false; // No hay solapamiento
  };

  // Función para manejar el cambio de fecha/vista en el calendario
  const handleNavigate = (date) => {
    // Navegando a fecha seleccionada
    setSelectedDate(date);
  };

  // Función para manejar el cambio de vista en el calendario
  const handleViewChange = (newView) => {
    // Cambiando a vista seleccionada
    setView(newView);
  };

  // Función para manejar el clic en un evento (abrir detalles)
  const handleSelectEvent = (event) => {
    // Evento seleccionado para edición
    setSelectedProcess(event.resource);
    setIsEditModalOpen(true);
  };

  // Función para guardar cambios en un proceso
  const handleSaveProcess = async () => {
    try {
      setLoading(true);

      // Preparar datos para actualizar
      const updateData = {
        status: selectedProcess.status,
        start_date: selectedProcess.start_date,
        end_date: selectedProcess.end_date,
        estimated_hours: selectedProcess.estimated_hours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${selectedProcess.process_id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === selectedProcess.process_id ? { ...p, ...updateData } : p
        )
      );

      setSuccessMessage(`Proceso ${selectedProcess.name} actualizado correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);

      // Cerrar modal
      setIsEditModalOpen(false);
      setSelectedProcess(null);
    } catch (err) {
      // Error al guardar proceso
      setError(`Error al guardar los cambios: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para cambiar el estado de un proceso
  const handleStatusChange = (e) => {
    setSelectedProcess({
      ...selectedProcess,
      status: e.target.value
    });
  };

  // Función para cambiar duración estimada
  const handleDurationChange = (e) => {
    const hours = parseFloat(e.target.value);
    if (isNaN(hours) || hours <= 0) return;

    // Formatear a 2 decimales
    const formattedHours = parseFloat(hours.toFixed(2));

    // Actualizar horas estimadas
    setSelectedProcess({
      ...selectedProcess,
      estimated_hours: formattedHours,

      // También actualizar fecha de fin basada en nueva duración
      end_date: (() => {
        const start = new Date(selectedProcess.start_date);
        const end = new Date(start);
        end.setTime(start.getTime() + (formattedHours * 60 * 60 * 1000));
        return end.toISOString();
      })()
    });
  };

  // Función para manejar el movimiento de un evento
  const handleEventDrop = async ({ event, start, end }) => {
    try {
      // Evento movido a nueva posición en el calendario

      // Verificar si el proceso está completado
      if (event.resource.status === 'Completado') {
        setError('No se pueden mover procesos completados');
        return;
      }

      // Verificar que los procesos "En Proceso" no se coloquen antes de la fecha actual
      if (event.resource.status === 'En Proceso') {
        const now = new Date();
        if (start < now) {
          setError('No se pueden programar procesos "En Proceso" antes de la fecha y hora actual');
          return;
        }
      }

      // Verificar solapamientos
      if (checkForOverlap(event.id, start, end)) {
        setError('No se puede mover el evento a esta posición porque se solapa con otro proceso');
        return;
      }

      setLoading(true);

      // Calcular la duración en horas
      const durationMs = end - start;
      const durationHours = parseFloat((durationMs / (1000 * 60 * 60)).toFixed(2));

      // Preparar datos para actualizar
      const updateData = {
        start_date: start.toISOString(),
        end_date: end.toISOString(),
        estimated_hours: durationHours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${event.id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === event.id ? {
            ...p,
            start_date: start.toISOString(),
            end_date: end.toISOString(),
            estimated_hours: durationHours
          } : p
        )
      );

      setSuccessMessage(`Proceso ${event.title} actualizado correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      // Error al mover evento
      setError(`Error al actualizar el proceso: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para manejar el redimensionamiento de un evento
  const handleEventResize = async ({ event, start, end }) => {
    try {
      // Evento redimensionado con nuevas fechas

      // Verificar si el proceso está completado
      if (event.resource.status === 'Completado') {
        setError('No se pueden modificar procesos completados');
        return;
      }

      // Verificar que los procesos "En Proceso" no se redimensionen antes de la fecha actual
      if (event.resource.status === 'En Proceso') {
        const now = new Date();
        if (start < now) {
          setError('No se pueden redimensionar procesos "En Proceso" antes de la fecha y hora actual');
          return;
        }
      }

      // Verificar solapamientos
      if (checkForOverlap(event.id, start, end)) {
        setError('No se puede redimensionar el evento a esta posición porque se solapa con otro proceso');
        return;
      }

      setLoading(true);

      // Calcular la duración en horas
      const durationMs = end - start;
      const durationHours = parseFloat((durationMs / (1000 * 60 * 60)).toFixed(2));

      // Preparar datos para actualizar
      const updateData = {
        start_date: start.toISOString(),
        end_date: end.toISOString(),
        estimated_hours: durationHours
      };

      // Actualizar en el backend
      const url = buildApiUrl(`/production/${event.id}`);

      const response = await ApiInterceptor.fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error(`Error al actualizar proceso: ${response.statusText}`);
      }

      // Actualizar la lista local de procesos
      setProcesses(prevProcesses =>
        prevProcesses.map(p =>
          p.process_id === event.id ? {
            ...p,
            start_date: start.toISOString(),
            end_date: end.toISOString(),
            estimated_hours: durationHours
          } : p
        )
      );

      setSuccessMessage(`Duración de ${event.title} actualizada correctamente`);

      // Limpiar mensaje después de 3 segundos
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      // Error al redimensionar evento
      setError(`Error al actualizar la duración: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Función para manejar la reorganización automática de procesos
  const handleReorganizeProcesses = async () => {
    if (!selectedMachine) {
      setError('No hay máquina seleccionada');
      return;
    }

    try {
      setLoading(true);

      // Limpiar mensajes anteriores
      setError(null);
      setSuccessMessage(null);

      // Obtener la hora actual
      const now = new Date();

      // Reorganizar procesos para la máquina seleccionada
      const reorganized = await reorganizeProcesses(processes, selectedMachine.machine_id, now);

      if (reorganized.length === 0) {
        setSuccessMessage('No hay procesos pendientes o en proceso para mover');
        setLoading(false);

        // Limpiar mensaje después de 5 segundos
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);

        return;
      }

      // Reorganizando procesos desde la fecha actual

      // Actualizar cada proceso en el backend
      const updatePromises = reorganized.map(async (process) => {
        // Solo actualizar si las fechas han cambiado
        const originalProcess = processes.find(p => p.process_id === process.process_id);
        if (!originalProcess) {
          // No se encontró el proceso original
          return process; // Devolver el proceso sin cambios
        }

        // Verificar si las fechas han cambiado
        if (originalProcess.start_date === process.start_date &&
            originalProcess.end_date === process.end_date) {
          // El proceso no ha cambiado, no se actualiza
          return process; // No hay cambios, devolver el proceso original
        }

        // Preparar solo los datos que necesitamos actualizar
        const updateData = {
          start_date: process.start_date,
          end_date: process.end_date,
          estimated_hours: process.estimated_hours
        };

        // Asegurarse de que el ID sea correcto
        const processId = process.process_id;
        // Actualizando proceso

        const url = buildApiUrl(`/production/${processId}`);

        try {
          const response = await ApiInterceptor.fetch(url, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
          });

          if (!response.ok) {
            throw new Error(`Error al actualizar proceso ${processId}: ${response.statusText}`);
          }

          return await response.json();
        } catch (error) {
          // Error al actualizar proceso
          throw error;
        }
      });

      try {
        // Esperar a que todas las actualizaciones se completen
        const updatedProcesses = await Promise.all(updatePromises);

        // Actualizar la lista local de procesos
        setProcesses(prevProcesses => {
          const newProcesses = [...prevProcesses];

          // Actualizar cada proceso modificado
          updatedProcesses.forEach(updatedProcess => {
            if (!updatedProcess) return; // Ignorar procesos nulos

            const index = newProcesses.findIndex(p => p.process_id === updatedProcess.process_id);
            if (index !== -1) {
              newProcesses[index] = updatedProcess;
            }
          });

          return newProcesses;
        });

        setSuccessMessage(`Se han movido ${reorganized.length} procesos al siguiente día laborable`);

        // Limpiar mensaje después de 5 segundos
        setTimeout(() => {
          setSuccessMessage(null);
        }, 5000);
      } catch (error) {
        // Error al procesar las actualizaciones
        setError(`Error al reorganizar procesos: ${error.message}`);

        // Limpiar mensaje de error después de 10 segundos
        setTimeout(() => {
          setError(null);
        }, 10000);
      }
    } catch (err) {
      // Error al reorganizar procesos
      setError(`Error al reorganizar procesos: ${err.message}`);

      // Limpiar mensaje de error después de 10 segundos
      setTimeout(() => {
        setError(null);
      }, 10000);
    } finally {
      setLoading(false);
    }
  };

  // Personalizar la apariencia de los eventos en el calendario
  const eventPropGetter = (event) => {
    return {
      style: {
        backgroundColor: event.backgroundColor,
        borderLeft: `4px solid ${event.backgroundColor}`,
        color: 'white'
      }
    };
  };

  // Componente personalizado para mostrar eventos en el calendario
  const CustomEvent = ({ event }) => {
    return (
      <Box sx={{ height: '100%', overflow: 'hidden', p: 0.5 }}>
        <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
          {event.resource && event.resource.ot_number ? `OT: ${event.resource.ot_number}` : 'Sin OT'}
        </Typography>
        <Typography variant="caption" sx={{ display: 'block' }}>
          {event.resource && event.resource.name ? event.resource.name : 'Sin nombre'}
        </Typography>
      </Box>
    );
  };

  // Validación de propiedades para CustomEvent
  CustomEvent.propTypes = {
    event: PropTypes.shape({
      resource: PropTypes.shape({
        ot_number: PropTypes.string,
        name: PropTypes.string
      })
    }).isRequired
  };

  // Función para manejar el cambio de máquina seleccionada
  const handleMachineChange = (machine) => {
    // Máquina seleccionada para planificación
    setSelectedMachine(machine);
    setProcesses([]);
  };

  // Renderizar pantalla de carga
  if ((loading || loadingMachines) && processes.length === 0 && selectedMachine) {
    return (
      <Container>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh', flexDirection: 'column' }}>
          <CircularProgress />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Cargando planificación de {selectedMachine.name}...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Paper elevation={3} sx={{ p: 3, width: '100%', boxSizing: 'border-box' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ mr: 2 }}>
              <Typography variant="h4">
                Planificación
              </Typography>
              {selectedMachine && (
                <Typography variant="subtitle1" color="text.secondary" sx={{ mt: 0.5 }}>
                  Máquina: <Box component="span" sx={{ fontWeight: 'bold', color: selectedMachine.type === 'Offset' || selectedMachine.type === 'Digital' ? 'primary.main' : 'success.main' }}>{selectedMachine.name} ({selectedMachine.type})</Box>
                </Typography>
              )}
            </Box>

            <FormControlLabel
              control={
                <Switch
                  checked={false}
                  onChange={() => {}}
                  color="secondary"
                  size="small"
                />
              }
              label="Vista compacta"
              sx={{ ml: 1, mr: 2 }}
            />

            {/* Contenedores de máquinas */}
            <Box sx={{
              display: 'flex',
              flexDirection: 'row',
              overflow: 'hidden',
              maxWidth: 'fit-content',
              gap: 1 // Pequeño espacio entre los contenedores
            }}>
              {/* Contenedor para máquinas de impresión */}
              <MachineContainer
                title="IMPRESIÓN"
                colorCategory="primary"
                machines={machines}
                selectedMachine={selectedMachine}
                onMachineSelect={handleMachineChange}
                machineFilter={(machine) => MACHINE_CATEGORIES.IMPRESION.includes(machine.type)}
              />

              {/* Contenedor para máquinas de acabados */}
              <MachineContainer
                title="ACABADOS"
                colorCategory="success"
                machines={machines}
                selectedMachine={selectedMachine}
                onMachineSelect={handleMachineChange}
                machineFilter={(machine) => MACHINE_CATEGORIES.ACABADOS.includes(machine.type)}
              />
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {/* Controles de filtro y reorganización */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center'
            }}>
              <FormControl size="small" sx={{ minWidth: 120, mr: 1 }}>
                <InputLabel>Estado</InputLabel>
                <Select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  label="Estado"
                  size="small"
                >
                  <MenuItem value="all">Todos</MenuItem>
                  <MenuItem value="Pendiente">Pendiente</MenuItem>
                  <MenuItem value="En Proceso">En Proceso</MenuItem>
                  <MenuItem value="Completado">Completado</MenuItem>
                  <MenuItem value="Cancelado">Cancelado</MenuItem>
                </Select>
              </FormControl>

              <Button
                variant="outlined"
                color="secondary"
                onClick={handleReorganizeProcesses}
                disabled={loading || !selectedMachine}
                size="small"
                startIcon={<RefreshIcon />}
              >
                Reorganizar
              </Button>
            </Box>
          </Box>
        </Box>

        {!selectedMachine && (
          <Alert severity="info" sx={{ mb: 2, py: 1 }} variant="outlined">
            Selecciona una máquina para ver su planificación
          </Alert>
        )}

        {/* Calendario */}
        <Box sx={{ height: '75vh', mb: 2 }}>
          <DnDCalendar
            localizer={localizer}
            events={filteredEvents}
            startAccessor="start"
            endAccessor="end"
            defaultView={view}
            views={['day', 'week', 'work_week']}
            step={30}
            timeslots={2}
            onNavigate={handleNavigate}
            onView={handleViewChange}
            onSelectEvent={handleSelectEvent}
            eventPropGetter={eventPropGetter}
            components={{
              event: CustomEvent
            }}
            date={selectedDate}
            min={new Date(new Date().setHours(7, 0, 0))}
            max={new Date(new Date().setHours(20, 0, 0))}
            messages={{
              day: 'Día',
              week: 'Semana',
              work_week: 'Semana laboral',
              month: 'Mes',
              previous: 'Anterior',
              next: 'Siguiente',
              today: 'Hoy',
              agenda: 'Agenda',
              showMore: total => `+ Ver más (${total})`
            }}
            resizable
            selectable
            onEventDrop={handleEventDrop}
            onEventResize={handleEventResize}
            popup
            tooltipAccessor={null}
          />
        </Box>

        {/* Mensajes de error y éxito en la parte inferior */}
        <Box sx={{ position: 'fixed', bottom: 20, left: 0, right: 0, zIndex: 1000, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2, width: '80%', maxWidth: '800px' }} onClose={() => setError(null)}>
              <AlertTitle>Error</AlertTitle>
              {error}
            </Alert>
          )}

          {successMessage && (
            <Alert severity="success" sx={{ mb: 2, width: '80%', maxWidth: '800px' }} onClose={() => setSuccessMessage(null)}>
              <AlertTitle>Éxito</AlertTitle>
              {successMessage}
            </Alert>
          )}
        </Box>

        {/* Leyenda */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
          <Typography variant="subtitle2" sx={{ width: '100%', mb: 1 }}>Leyenda:</Typography>

          {/* Estados */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            {Object.entries(STATUS_COLORS).map(([status, color]) => (
              <Box
                key={status}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  backgroundColor: color,
                  color: 'white',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '0.75rem'
                }}
              >
                {status}
              </Box>
            ))}
          </Box>
        </Box>
      </Paper>

      {/* Modal de edición */}
      {selectedProcess && (
        <Dialog
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Detalles del Trabajo: {selectedProcess.name}
          </DialogTitle>

          <DialogContent dividers>
            <Stack spacing={2}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="OT"
                  value={selectedProcess.ot_number || ''}
                  fullWidth
                  InputProps={{ readOnly: true }}
                />

                <FormControl fullWidth>
                  <InputLabel>Estado</InputLabel>
                  <Select
                    value={selectedProcess.status || 'Pendiente'}
                    onChange={handleStatusChange}
                    label="Estado"
                  >
                    <MenuItem value="Pendiente">Pendiente</MenuItem>
                    <MenuItem value="En Proceso">En Proceso</MenuItem>
                    <MenuItem value="Completado">Completado</MenuItem>
                    <MenuItem value="Cancelado">Cancelado</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <TextField
                label="Descripción"
                value={selectedProcess.description || ''}
                fullWidth
                multiline
                rows={2}
                InputProps={{ readOnly: true }}
              />

              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="Cantidad"
                  value={selectedProcess.quantity || ''}
                  fullWidth
                  InputProps={{ readOnly: true }}
                />

                <TextField
                  label="Duración estimada (horas)"
                  value={selectedProcess.estimated_hours ? parseFloat(selectedProcess.estimated_hours).toFixed(2) : ''}
                  fullWidth
                  onChange={handleDurationChange}
                  type="number"
                  inputProps={{ min: 0.5, step: 0.5 }}
                />
              </Box>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <TextField
                  label="Fecha y hora de inicio"
                  type="datetime-local"
                  value={format(new Date(selectedProcess.start_date), "yyyy-MM-dd'T'HH:mm")}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  onChange={(e) => {
                    const newStart = new Date(e.target.value);
                    const duration = selectedProcess.estimated_hours;

                    // Calcular nueva fecha de fin
                    const newEnd = new Date(newStart);
                    newEnd.setTime(newStart.getTime() + (duration * 60 * 60 * 1000));

                    setSelectedProcess({
                      ...selectedProcess,
                      start_date: newStart.toISOString(),
                      end_date: newEnd.toISOString()
                    });
                  }}
                />

                <TextField
                  label="Fecha y hora de fin"
                  type="datetime-local"
                  value={format(new Date(selectedProcess.end_date), "yyyy-MM-dd'T'HH:mm")}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  InputProps={{ readOnly: true }}
                />
              </Box>

              {selectedProcess.dependencies && selectedProcess.dependencies.length > 0 && (
                <Box>
                  <Typography variant="subtitle2">Dependencias:</Typography>
                  <Paper variant="outlined" sx={{ p: 1 }}>
                    {selectedProcess.dependencies.map(depId => (
                      <Typography key={depId} variant="body2">
                        • {depId}
                      </Typography>
                    ))}
                  </Paper>
                </Box>
              )}
            </Stack>
          </DialogContent>

          <DialogActions>
            <Button
              onClick={navigateToProductionList}
              startIcon={<ListIcon />}
              variant="outlined"
              color="info"
              sx={{ mr: 'auto', fontWeight: 'bold' }}
            >
              Ver en Lista de Producción
            </Button>
            <Button onClick={() => setIsEditModalOpen(false)}>
              Cancelar
            </Button>
            <Button
              onClick={handleSaveProcess}
              variant="contained"
              color="primary"
              disabled={loading}
            >
              {loading ? 'Guardando...' : 'Guardar Cambios'}
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Container>
  );
};

export default MachinePlanner;
