from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from typing import Optional
from utils.paper_cost_calculator import calculate_sheet_cost
from utils.catalog_manager import load_paper_catalog

router = APIRouter(
    tags=["paper_calculator"],
    responses={404: {"description": "Papel no encontrado"}}
)

class SheetCalculationRequest(BaseModel):
    paper_id: str
    sheets_per_unit: int
    units: int
    waste_percentage: Optional[float] = 5.0  # Porcentaje de merma por defecto

class SheetCalculationResponse(BaseModel):
    paper_id: str
    paper_name: str
    sheets_per_unit: int
    units: int
    waste_percentage: float
    total_sheets_without_waste: int
    waste_sheets: int
    total_sheets: int
    sheet_cost: float
    total_cost: float

# Endpoint eliminado
