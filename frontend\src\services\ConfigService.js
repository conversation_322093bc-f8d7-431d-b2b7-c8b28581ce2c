import { buildApiUrl } from '../config';
import ApiInterceptor from './ApiInterceptor';
import LogService from './logService';

class ConfigService {
  /**
   * Obtiene toda la configuración de la aplicación
   * @returns {Promise<Object>} - Objeto de configuración
   */
  static async getConfig() {
    try {
      const url = buildApiUrl('/config');
      const response = await ApiInterceptor.fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al obtener la configuración');
      }

      const data = await response.json();

      LogService.logUserAction('get_config', {
        success: true
      });

      return data;
    } catch (error) {
      console.error('Error al obtener la configuración:', error);

      LogService.logError('Error al obtener la configuración', {
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Obtiene un valor específico de la configuración
   * @param {string} key - Clave de configuración
   * @returns {Promise<Object>} - Valor de configuración
   */
  static async getConfigValue(key) {
    try {
      const url = buildApiUrl(`/config/${key}`);
      const response = await ApiInterceptor.fetch(url);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error al obtener la configuración '${key}'`);
      }

      const data = await response.json();

      LogService.logUserAction('get_config_value', {
        key,
        success: true
      });

      return data;
    } catch (error) {
      console.error(`Error al obtener la configuración '${key}':`, error);

      LogService.logError(`Error al obtener la configuración '${key}'`, {
        key,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Actualiza la configuración de la aplicación
   * @param {Object} configData - Datos de configuración a actualizar
   * @returns {Promise<Object>} - Configuración actualizada
   */
  static async updateConfig(configData) {
    try {
      const url = buildApiUrl('/config');
      const response = await ApiInterceptor.fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(configData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al actualizar la configuración');
      }

      const data = await response.json();

      LogService.logUserAction('update_config', {
        config: configData,
        success: true
      });

      return data;
    } catch (error) {
      console.error('Error al actualizar la configuración:', error);

      LogService.logError('Error al actualizar la configuración', {
        config: configData,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Actualiza un valor específico de la configuración
   * @param {string} key - Clave de configuración
   * @param {any} value - Valor de configuración
   * @returns {Promise<Object>} - Configuración actualizada
   */
  static async updateConfigValue(key, value) {
    try {
      const url = buildApiUrl(`/config/${key}`);
      const response = await ApiInterceptor.fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ [key]: value })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error al actualizar la configuración '${key}'`);
      }

      const data = await response.json();

      LogService.logUserAction('update_config_value', {
        key,
        value,
        success: true
      });

      return data;
    } catch (error) {
      console.error(`Error al actualizar la configuración '${key}':`, error);

      LogService.logError(`Error al actualizar la configuración '${key}'`, {
        key,
        value,
        error: error.message
      });

      throw error;
    }
  }
}

export default ConfigService;
