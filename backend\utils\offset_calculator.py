from fastapi import HTTPException
from utils.catalog_manager import load_machines, load_paper_catalog
from utils.data_loader import load_consumables
from models.models import SheetType
import math

def calculate_offset_cost_and_time(
    machine_id: str,
    total_sheets: int,
    copies: int,
    total_plates: int = 0,
    colors_front: int = 4,
    colors_back: int = 0,
    is_workandturn: bool = False,  # Si es tira-retira (workandturn) (True) o no (False)
    sheet_type: SheetType = SheetType.WORK_AND_BACK,  # Tipo de pliego
    folding_scheme: str = None,  # Esquema de plegado (ej: "F4-1", "F8-7", etc.)
    paper_id: str = None,
    custom_setup_time: int = None,
    custom_sheets_per_hour: int = None,
    custom_maculatura: int = None,
    esquemas_utilizados: list = None  # Lista de esquemas utilizados para cálculos más precisos
):
    """
    Calcula el coste y tiempo de impresión para una máquina offset utilizando los nuevos parámetros.

    Args:
        machine_id: ID de la máquina offset
        total_sheets: Número total de pliegos a imprimir
        copies: Número de ejemplares
        total_plates: Número total de planchas necesarias
        colors_front: Número de colores en el anverso
        colors_back: Número de colores en el reverso (0 para impresión a una cara)
        is_workandturn: Si es tira-retira (workandturn) (True) o no (False). En tira-retira se usan las mismas planchas para ambas caras
        sheet_type: Tipo de pliego. Opciones:
            - "Flat": Impresión a una cara o sin volteo
            - "WorkAndTurn": Tira-retira (mismas planchas, volteando el pliego)
            - "WorkAndBack": Diferentes planchas para cada cara
            - "Perfecting": Impresión de ambas caras en una pasada (máquinas de 8+ cuerpos)
        folding_scheme: Esquema de plegado (ej: "F4-1", "F8-7", etc.)
        paper_id: ID del papel (opcional)
        custom_setup_time: Tiempo de arranque personalizado (en minutos)
        custom_sheets_per_hour: Velocidad personalizada (pliegos/hora)
        custom_maculatura: Número personalizado de pliegos adicionales para arranque

    Returns:
        dict: Diccionario con los resultados del cálculo
    """
    # Cargar datos de la máquina
    machines = load_machines()
    machine = next((m for m in machines if m.get("machine_id") == machine_id), None)

    if not machine:
        raise HTTPException(status_code=404, detail=f"Máquina con ID {machine_id} no encontrada")

    # Verificar que la máquina es de tipo Offset
    if machine.get("type") != "Offset":
        raise HTTPException(status_code=400, detail=f"La máquina {machine_id} no es de tipo Offset")

    # Obtener parámetros de la máquina
    hourly_cost = machine.get("hourly_cost", 0)
    cfa_percentage = machine.get("cfa_percentage", 0)
    ink_consumption = machine.get("ink_consumption", 2.0)  # Consumo de tinta en gramos/m²
    print_units = machine.get("print_units", 4)  # Número de cuerpos de impresión (por defecto 4)

    # Usar valores personalizados si se proporcionan, de lo contrario usar los de la máquina
    setup_time = custom_setup_time if custom_setup_time is not None else machine.get("setup_time", 30)
    sheets_per_hour = custom_sheets_per_hour if custom_sheets_per_hour is not None else machine.get("sheets_per_hour", 0)
    maculatura = custom_maculatura if custom_maculatura is not None else machine.get("maculatura", 150)  # Pliegos adicionales para arranque (maculatura)

    # Si no hay velocidad definida, usar un valor por defecto
    if sheets_per_hour <= 0:
        sheets_per_hour = 5000  # Valor por defecto: 5000 pliegos/hora

    # Calcular temporalmente el total de pliegos físicos sin considerar tira/retira
    # Lo ajustaremos después de determinar el tipo de pliego
    temp_physical_sheets = total_sheets * copies

    # Calcular el total de maculatura para todo el trabajo
    total_maculatura = total_sheets * maculatura

    # Determinar si la máquina puede imprimir en modo tira-retira (workandturn)
    # Las máquinas de 8 cuerpos o más no usan tira-retira, imprimen ambas caras a la vez
    can_use_workandturn = print_units < 8

    # Verificar si tenemos información detallada de los esquemas utilizados
    if esquemas_utilizados:
        print(f"DEBUG: Usando información detallada de esquemas_utilizados. Ignorando parámetros redundantes: folding_scheme={folding_scheme}, sheet_type={sheet_type}, is_workandturn={is_workandturn}")
        # No necesitamos ajustar el tipo de pliego general ni determinar is_using_workandturn,
        # ya que usaremos la información de cada esquema directamente

        # Mantenemos estos valores para compatibilidad con el resto del código,
        # pero no los usaremos para los cálculos que dependen de los esquemas
        is_using_workandturn = False

        # Determinar si algún esquema es tira-retira para mostrar en el resultado
        has_workandturn_schemes = False
        for esquema in esquemas_utilizados:
            try:
                # Los esquemas pueden ser objetos Pydantic (EsquemaResponse) o diccionarios
                if isinstance(esquema, dict):
                    es_tira_retira = esquema.get("es_tira_retira", False)
                else:
                    # Es un objeto Pydantic
                    es_tira_retira = getattr(esquema, 'es_tira_retira', False)

                if es_tira_retira:
                    has_workandturn_schemes = True
                    break
            except Exception as e:
                print(f"ERROR: No se pudo determinar si el esquema es tira-retira: {str(e)}")

        # Actualizar is_workandturn para el resultado
        is_workandturn = has_workandturn_schemes
    else:
        # Ajustar el tipo de pliego según las capacidades de la máquina y los parámetros
        # Si la máquina tiene 8 o más cuerpos y es impresión a doble cara, forzar el tipo de pliego a "Perfecting"
        if print_units >= 8 and colors_back > 0:
            sheet_type = SheetType.PERFECTING
        # Si el tipo de pliego es "Perfecting" pero la máquina no tiene suficientes cuerpos, usar "WorkAndBack"
        elif sheet_type == SheetType.PERFECTING and print_units < 8:
            sheet_type = SheetType.WORK_AND_BACK
        # Si el tipo de pliego es "WorkAndTurn" pero la máquina no puede usar tira-retira, usar "WorkAndBack"
        elif sheet_type == SheetType.WORK_AND_TURN and not can_use_workandturn:
            sheet_type = SheetType.WORK_AND_BACK
        # Si es impresión a una cara, forzar el tipo de pliego a "Flat"
        elif colors_back == 0:
            sheet_type = SheetType.FLAT

        # Compatibilidad con el parámetro is_workandturn
        is_using_workandturn = (sheet_type == SheetType.WORK_AND_TURN) or (is_workandturn and can_use_workandturn)

    # Ahora que hemos determinado el tipo de pliego y si se está usando tira-retira,
    # calculamos el total de pliegos físicos considerando estos factores

    # Verificar si tenemos información detallada de los esquemas utilizados
    # esquemas_utilizados ya es un parámetro de la función

    if esquemas_utilizados:
        # Si tenemos información detallada de los esquemas, calculamos el total de pliegos físicos
        # teniendo en cuenta cada esquema individualmente
        total_physical_sheets = 0
        for esquema in esquemas_utilizados:
            try:
                # Los esquemas pueden ser objetos Pydantic (EsquemaResponse) o diccionarios
                if isinstance(esquema, dict):
                    num_pliegos = esquema.get("numero_pliegos", 0)
                    es_tira_retira = esquema.get("es_tira_retira", False)
                    nombre_esquema = esquema.get("nombre", "desconocido")
                else:
                    # Es un objeto Pydantic
                    num_pliegos = getattr(esquema, 'numero_pliegos', 0)
                    es_tira_retira = getattr(esquema, 'es_tira_retira', False)
                    nombre_esquema = getattr(esquema, 'nombre', "desconocido")

                # Calcular el factor según el tipo de pliego
                factor = 0.5 if es_tira_retira else 1.0
                # Calcular los pliegos físicos para este esquema
                physical_sheets_esquema = math.ceil(num_pliegos * copies * factor)
                # Sumar al total
                total_physical_sheets += physical_sheets_esquema
                print(f"DEBUG: Esquema {nombre_esquema}: num_pliegos={num_pliegos}, es_tira_retira={es_tira_retira}, factor={factor}, physical_sheets_esquema={physical_sheets_esquema}")
            except Exception as e:
                print(f"ERROR: No se pudo procesar el esquema: {str(e)}")
    else:
        # Si no tenemos información detallada, usamos el tipo de pliego general
        if is_using_workandturn:
            # Si es tira-retira, cada pliego físico produce 2 unidades
            # Aplicar un factor de 0.5 para los pliegos tira-retira
            total_physical_sheets = math.ceil((temp_physical_sheets) / 2)
            print(f"DEBUG: Usando tira-retira general. total_sheets={total_sheets}, copies={copies}, temp_physical_sheets={temp_physical_sheets}, total_physical_sheets={total_physical_sheets}")
        else:
            total_physical_sheets = temp_physical_sheets
            print(f"DEBUG: No usando tira-retira general. total_sheets={total_sheets}, copies={copies}, total_physical_sheets={total_physical_sheets}")

    # Calcular el total de papel necesario (producción + maculatura)
    total_paper = total_physical_sheets + total_maculatura

    # Determinar si necesitamos una o dos pasadas por la máquina según el tipo de pliego
    if esquemas_utilizados:
        # Si tenemos información detallada de los esquemas, determinamos si necesitamos dos pasadas
        # basándonos en si algún esquema es WorkAndBack y requiere dos pasadas
        needs_two_passes = False
        for esquema in esquemas_utilizados:
            try:
                # Determinar si este esquema es tira-retira
                # Los esquemas pueden ser objetos Pydantic (EsquemaResponse) o diccionarios
                if isinstance(esquema, dict):
                    es_tira_retira = esquema.get("es_tira_retira", False)
                    colores_anverso = esquema.get("colores_anverso", colors_front)
                    colores_reverso = esquema.get("colores_reverso", colors_back)
                else:
                    # Es un objeto Pydantic
                    es_tira_retira = getattr(esquema, 'es_tira_retira', False)
                    # Para los colores, usamos los valores por defecto del endpoint
                    colores_anverso = colors_front
                    colores_reverso = colors_back

                # Si no es tira-retira y es impresión a doble cara, y los colores totales superan los cuerpos de la máquina,
                # necesitamos dos pasadas
                if not es_tira_retira and colores_reverso > 0 and (colores_anverso + colores_reverso > print_units):
                    needs_two_passes = True
                    break
            except Exception as e:
                print(f"ERROR: No se pudo determinar si el esquema necesita dos pasadas: {str(e)}")
    else:
        # Si no tenemos información detallada, usamos el tipo de pliego general
        if sheet_type == SheetType.PERFECTING:
            # Perfecting: imprime ambas caras en una sola pasada
            needs_two_passes = False
        elif sheet_type == SheetType.WORK_AND_TURN:
            # WorkAndTurn: usa las mismas planchas para ambas caras, una pasada
            needs_two_passes = False
        elif sheet_type == SheetType.FLAT:
            # Flat: impresión a una cara, una pasada
            needs_two_passes = False
        else:  # WorkAndBack
            # WorkAndBack: diferentes planchas para cada cara, dos pasadas si hay colores en el reverso
            needs_two_passes = colors_back > 0 and (colors_front + colors_back > print_units)

    # Si necesitamos dos pasadas, duplicamos el número de pliegos físicos para el cálculo del tiempo
    effective_physical_sheets = total_physical_sheets * (2 if needs_two_passes else 1)

    # Calcular el tiempo de impresión en horas
    # Incluir la maculatura en el cálculo del tiempo de impresión
    total_sheets_to_print = effective_physical_sheets + total_maculatura
    pure_printing_time_hours = total_sheets_to_print / sheets_per_hour
    pure_printing_time_minutes = pure_printing_time_hours * 60

    # Solo hay un arranque por trabajo, independientemente de si se necesitan dos pasadas o no
    setup_time_total = setup_time

    # Calcular el tiempo de cambio de planchas
    # El tiempo de cambio de planchas depende del número de cambios, que se calcula como
    # el número total de planchas dividido por el número de cuerpos de impresión
    # Redondeamos hacia arriba para asegurarnos de que siempre tenemos un número entero de cambios
    plate_changes = math.ceil(total_plates / print_units) if print_units > 0 else 0
    # El tiempo por cambio de planchas es un 50% del tiempo de setup
    plate_change_time_per_set = setup_time * 0.5
    # Tiempo total de cambio de planchas
    plate_change_time_total = plate_change_time_per_set * plate_changes

    # Tiempo total = tiempo de arranque + tiempo de cambio de planchas + tiempo de impresión
    total_time_minutes = setup_time_total + plate_change_time_total + pure_printing_time_minutes
    total_time_hours = total_time_minutes / 60

    # Calcular el coste fijo de arranque (CFA)
    cfa_cost = (hourly_cost * cfa_percentage / 100) if cfa_percentage > 0 else 0

    # Calcular el coste de impresión pura (solo tiempo de impresión * coste por hora)
    pure_printing_cost = hourly_cost * pure_printing_time_hours
    
    # Calcular el coste del tiempo de setup y cambio de planchas
    setup_cost = hourly_cost * (setup_time_total / 60)  # Convertir minutos a horas
    plate_change_cost = hourly_cost * (plate_change_time_total / 60)  # Convertir minutos a horas
    
    # Coste total de máquina (setup + cambio de planchas + impresión)
    total_machine_cost = hourly_cost * total_time_hours  # Usar directamente el tiempo total

    # Inicializar variables para costes opcionales
    paper_cost = None
    paper_name = None
    paper_cost_per_1000 = None
    paper_info = None
    plates_cost = None
    ink_cost = None
    ink_weight_kg = None

    # Obtener las tintas del catálogo de consumibles
    consumables = load_consumables()
    inks = [c for c in consumables if c.get("type") == "Tinta" and c.get("color", "").lower() in ["cyan", "magenta", "amarillo", "negro"]]

    # Calcular el precio medio de las tintas CMYK
    if inks:
        ink_prices = [ink.get("unit_cost", 0) for ink in inks]
        ink_price_per_kg = sum(ink_prices) / len(ink_prices)
    else:
        # Si no hay tintas en el catálogo, usar el precio predeterminado
        ink_price_per_kg = 18.0  # Valor predeterminado: 18€/kg

    ink_price_per_gram = ink_price_per_kg / 1000  # Precio por gramo

    # Calcular el coste del papel si se proporciona paper_id
    paper_cost_per_sheet = 0
    maculatura_cost = 0
    
    if paper_id:
        paper_catalog = load_paper_catalog()
        paper = next((p for p in paper_catalog if p.get("product_id") == paper_id), None)

        if paper:
            paper_name = paper.get("descriptive_name", "")
            paper_cost_per_1000 = paper.get("price_per_1000", 0)
            paper_cost_per_sheet = paper_cost_per_1000 / 1000
            paper_cost = paper_cost_per_sheet * total_physical_sheets
            # Calcular el coste de maculatura (usando el mismo coste por hoja que el papel normal)
            maculatura_cost = paper_cost_per_sheet * total_maculatura
            print(f"DEBUG - Calculando coste de maculatura: {total_maculatura} hojas * {paper_cost_per_sheet} = {maculatura_cost}")
            # Guardar toda la información del papel
            paper_info = paper

            # Calcular el coste de tinta basado en el área del papel y el consumo de tinta
            paper_width_m = paper.get("dimension_width", 0) / 1000  # Convertir mm a metros
            paper_height_m = paper.get("dimension_height", 0) / 1000  # Convertir mm a metros
            paper_area_m2 = paper_width_m * paper_height_m  # Área en metros cuadrados

            # Calcular el consumo total de tinta en gramos
            # Si hay colores en el reverso, multiplicamos por 2 el área
            total_area_m2 = paper_area_m2 * total_physical_sheets * (2 if colors_back > 0 else 1)
            ink_grams = total_area_m2 * ink_consumption

            # Calcular el peso de la tinta en kg
            ink_weight_kg = ink_grams / 1000

            # Calcular el coste de la tinta
            ink_cost = ink_grams * ink_price_per_gram

    # Calcular el número de planchas necesarias si no se proporciona
    if total_plates <= 0:
        if esquemas_utilizados:
            # Si tenemos información detallada de los esquemas, calculamos el número de planchas
            # teniendo en cuenta cada esquema individualmente
            total_plates = 0
            for esquema in esquemas_utilizados:
                try:
                    # Los esquemas pueden ser objetos Pydantic (EsquemaResponse) o diccionarios
                    if isinstance(esquema, dict):
                        es_tira_retira = esquema.get("es_tira_retira", False)
                        colores_anverso = esquema.get("colores_anverso", colors_front)
                        colores_reverso = esquema.get("colores_reverso", colors_back)
                    else:
                        # Es un objeto Pydantic
                        es_tira_retira = getattr(esquema, 'es_tira_retira', False)
                        # Para los colores, usamos los valores por defecto del endpoint
                        colores_anverso = colors_front
                        colores_reverso = colors_back

                    # Calcular el número de planchas para este esquema
                    if es_tira_retira:
                        # Tira-retira, solo planchas para el anverso (se usan las mismas para ambas caras)
                        planchas_esquema = colores_anverso
                    else:
                        # WorkAndBack, planchas diferentes para cada cara
                        planchas_esquema = colores_anverso + colores_reverso

                    # Sumar al total
                    total_plates += planchas_esquema
                    print(f"DEBUG: Planchas para esquema: {planchas_esquema}")
                except Exception as e:
                    print(f"ERROR: No se pudo calcular las planchas para el esquema: {str(e)}")
        else:
            # Si no tenemos información detallada, usamos el tipo de pliego general
            if sheet_type == SheetType.FLAT:
                # Impresión a una cara, solo planchas para el anverso
                total_plates = colors_front
            elif sheet_type == SheetType.WORK_AND_TURN:
                # Tira-retira, solo planchas para el anverso (se usan las mismas para ambas caras)
                total_plates = colors_front
            elif sheet_type == SheetType.PERFECTING:
                # Perfecting, planchas para ambas caras (se imprimen a la vez)
                if print_units >= 8:
                    # Para máquinas de 8 o más cuerpos, el número de planchas debe ser múltiplo del número de cuerpos
                    total_plates = print_units
                else:
                    total_plates = colors_front + colors_back
            else:  # WorkAndBack
                # WorkAndBack, planchas diferentes para cada cara
                total_plates = colors_front + colors_back

    # Si la máquina tiene 8 o más cuerpos, asegurarnos de que el número de planchas sea múltiplo del número de cuerpos
    if print_units >= 8 and colors_back > 0:
        # Redondear hacia arriba al múltiplo más cercano del número de cuerpos
        if total_plates % print_units != 0:
            total_plates = ((total_plates // print_units) + 1) * print_units

    # Calcular el coste de las planchas
    if total_plates > 0:
        # Intentar obtener el precio de las planchas del catálogo de consumibles
        # Ya hemos cargado los consumibles antes
        plates = [c for c in consumables if c.get("type") == "Plancha"]

        if plates:
            # Usar la primera plancha disponible
            plate = plates[0]
            plate_unit_cost = plate.get("unit_cost", 0)
            plates_cost = plate_unit_cost * total_plates
        else:
            # Si no hay planchas en el catálogo, usar el precio predeterminado
            plate_unit_cost = 5.75  # Valor predeterminado: 5.75€ por plancha
            plates_cost = plate_unit_cost * total_plates

    # Calcular el coste total
    total_cost = cfa_cost + total_machine_cost
    if paper_cost is not None:
        total_cost += paper_cost
    if plates_cost is not None:
        total_cost += plates_cost
    if ink_cost is not None:
        total_cost += ink_cost
    if maculatura_cost is not None:
        total_cost += maculatura_cost

    # Preparar la respuesta
    result = {
        "machine_id": machine_id,
        "machine_name": machine.get("name", ""),
        "total_sheets": total_sheets,
        "copies": copies,
        "total_physical_sheets": total_physical_sheets,
        "total_plates": total_plates,
        "colors_front": colors_front,
        "colors_back": colors_back,
        "maculatura": maculatura,
        "total_maculatura": total_maculatura,
        "total_paper": total_paper,
        "using_detailed_schemes": esquemas_utilizados is not None and len(esquemas_utilizados) > 0,

        # Información de la máquina
        "hourly_cost": hourly_cost,
        "cfa_percentage": cfa_percentage,
        "setup_time": setup_time,
        "sheets_per_hour": sheets_per_hour,
        "maculatura": maculatura,
        "ink_consumption": ink_consumption,
        "ink_price_per_kg": ink_price_per_kg,
        "print_units": print_units,

        # Información del papel
        "paper_id": paper_id,
        "paper_name": paper_name,
        "paper_cost_per_1000": paper_cost_per_1000,
        "paper_info": paper_info,

        # Cálculos de tiempo
        "setup_time_minutes": float(setup_time),
        "setup_time_total_minutes": float(setup_time_total),  # Tiempo de arranque total (considerando pasadas adicionales)
        "plate_change_time_minutes": round(plate_change_time_total, 2),
        "plate_changes": round(plate_changes, 2),
        "printing_time_minutes": round(pure_printing_time_minutes, 2),  # Campo requerido por el modelo OffsetCalculationResponse
        "pure_printing_time_minutes": round(pure_printing_time_minutes, 2),
        "total_time_minutes": round(total_time_minutes, 2),
        "total_time_hours": round(total_time_hours, 2),
        "total_printing_time_minutes": round(total_time_minutes, 2),
        "total_printing_time_hours": round(total_time_hours, 2),

        # Cálculos de coste
        "cfa_cost": round(cfa_cost, 2),
        "setup_cost": round(setup_cost, 2),
        "plate_change_cost": round(plate_change_cost, 2),
        "pure_printing_cost": round(pure_printing_cost, 2),
        "printing_cost": round(total_machine_cost, 2),  # El coste de impresión ahora es el coste total de máquina
        "total_machine_cost": round(total_machine_cost, 2),
        "paper_cost": round(paper_cost, 2) if paper_cost is not None else None,
        "plates_cost": round(plates_cost, 2) if plates_cost is not None else None,
        "ink_cost": round(ink_cost, 2) if ink_cost is not None else None,
        "ink_weight_kg": round(ink_weight_kg, 2) if ink_weight_kg is not None else None,
        "maculatura_cost": round(maculatura_cost, 2) if maculatura_cost is not None else None,
        "total_cost": round(total_cost, 2)
    }

    return result
