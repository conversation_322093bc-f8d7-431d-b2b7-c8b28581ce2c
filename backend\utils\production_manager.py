import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from models.production import ProductionProcess, ProductionProcessType, ProductionStatus, ProductionProcessV2, ProductionOrder, OTStatus
from utils.logger import log_info, log_error
import httpx
from utils.catalog_manager import load_process_catalog

# Ruta al archivo de producción
PRODUCTION_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "produccion.json")

def load_production_data() -> List[Dict[str, Any]]:
    """
    Carga los datos de producción desde el archivo JSON
    """
    try:
        if os.path.exists(PRODUCTION_FILE):
            with open(PRODUCTION_FILE, "r", encoding="utf-8") as file:
                return json.load(file)
        return []
    except Exception as e:
        print(f"Error al cargar datos de producción: {str(e)}")
        return []

def save_production_data(production_data: List[Dict[str, Any]]) -> bool:
    """
    Guarda los datos de producción en el archivo JSON
    """
    try:
        with open(PRODUCTION_FILE, "w", encoding="utf-8") as file:
            json.dump(production_data, file, indent=4, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error al guardar datos de producción: {str(e)}")
        return False

def add_budget_to_production_v2(budget_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Añade un presupuesto aprobado a la lista de producción usando la nueva estructura jerárquica
    """
    production_data = load_production_data()

    # Verificar si el presupuesto ya está en producción
    existing_ot = None
    for item in production_data:
        if isinstance(item, dict) and item.get("budget_id") == budget_data["budget_id"]:
            if "ot_id" in item:  # Nueva estructura
                existing_ot = item
                break
            elif "ot_number" in item:  # Estructura antigua
                log_info(f"El presupuesto {budget_data['budget_id']} ya está en producción con la estructura antigua")
                # Aquí podríamos migrar los datos a la nueva estructura si es necesario
                return None

    if existing_ot:
        log_info(f"El presupuesto {budget_data['budget_id']} ya está en producción con la estructura nueva")
        return existing_ot

    # Fecha actual para planificación y como fecha de aprobación
    current_date = datetime.now()
    approval_date = current_date.isoformat()

    # Obtener datos básicos del presupuesto
    budget_id = budget_data["budget_id"]
    ot_number = budget_data["ot_number"]
    client_id = budget_data["client_id"]
    quantity = budget_data.get("quantity", 0)

    # Lista para almacenar los nuevos procesos
    processes = []

    # Procesar cada parte de impresión como un proceso separado
    if "parts" in budget_data and budget_data["parts"]:
        for i, part in enumerate(budget_data["parts"]):
            # Calcular fechas de inicio y fin para este proceso
            # Asumimos que cada proceso de impresión toma 1 día por defecto
            start_date = (current_date + timedelta(days=i)).isoformat()

            # Obtener el tiempo estimado del presupuesto
            estimated_hours = 1.0  # Valor por defecto (1 hora)

            # Prioridad 1: Usar custom_print_time si está definido
            if part.get("custom_print_time") is not None:
                estimated_hours = float(part["custom_print_time"])
                log_info(f"Usando tiempo personalizado para {part.get('name', f'Parte {i+1}')}: {estimated_hours} horas")

            # Prioridad 2: Usar total_time_hours de sheet_calculation.machine_data
            elif part.get("sheet_calculation") and part["sheet_calculation"].get("machine_data") and part["sheet_calculation"]["machine_data"].get("total_time_hours") is not None:
                estimated_hours = float(part["sheet_calculation"]["machine_data"]["total_time_hours"])
                log_info(f"Usando tiempo de cálculo de pliegos para {part.get('name', f'Parte {i+1}')}: {estimated_hours} horas")

            # Prioridad 3: Usar total_time_hours de sheet_calculation
            elif part.get("sheet_calculation") and part["sheet_calculation"].get("total_time_hours") is not None:
                estimated_hours = float(part["sheet_calculation"]["total_time_hours"])
                log_info(f"Usando tiempo total de sheet_calculation para {part.get('name', f'Parte {i+1}')}: {estimated_hours} horas")

            # Si no hay ningún tiempo definido, calcular basado en el costo (método original)
            else:
                machine_cost = part.get("machine_cost", 0)
                machine_data = part.get("machine_data", {})
                hourly_cost = machine_data.get("hourly_cost", 120)  # Valor por defecto
                estimated_hours = max(1, machine_cost / hourly_cost) if hourly_cost > 0 else 1
                log_info(f"Calculando tiempo basado en costo para {part.get('name', f'Parte {i+1}')}: {estimated_hours} horas")

            # Fecha de finalización basada en las horas estimadas
            end_date = (current_date + timedelta(days=i, hours=estimated_hours)).isoformat()

            # Crear proceso de impresión
            printing_process = {
                "process_id": f"PROD-{budget_id}-P{i+1}",
                "process_type": ProductionProcessType.PRINTING.value,
                "name": f"Impresión: {part.get('name', f'Parte {i+1}')}",
                "description": part.get("description", "") or f"Impresión de {part.get('name', f'Parte {i+1}')}",
                "quantity": quantity,
                "status": ProductionStatus.PENDING.value,
                "start_date": start_date,
                "end_date": end_date,
                "estimated_hours": estimated_hours,
                "machine_id": part.get("machine_id"),
                "dependencies": [],
                "created_at": current_date.isoformat(),
                "updated_at": current_date.isoformat()
            }

            processes.append(printing_process)

    # Procesar cada proceso de acabado como un proceso separado
    if "process_costs" in budget_data and budget_data["process_costs"]:
        # Los procesos de acabado dependen de que se completen todos los procesos de impresión
        printing_process_ids = [p["process_id"] for p in processes if p["process_type"] == ProductionProcessType.PRINTING.value]

        for i, process in enumerate(budget_data["process_costs"]):
            # Determinar el tipo de proceso basado en el tipo de acabado
            process_type = process.get("type", "")
            production_type = ProductionProcessType.FINISHING.value

            if process_type in ["Corte", "Plegado", "Encuadernación", "Barnizado", "Laminado", "Troquelado"]:
                production_type = ProductionProcessType.FINISHING.value
            elif process_type in ["Embalaje", "Manipulado"]:
                production_type = ProductionProcessType.HANDLING.value
            elif process_type in ["Envío", "Transporte"]:
                production_type = ProductionProcessType.SHIPPING.value

            # Calcular fechas de inicio y fin para este proceso
            # Los procesos de acabado comienzan después de que terminen todos los procesos de impresión
            days_offset = len(processes) + i
            start_date = (current_date + timedelta(days=days_offset)).isoformat()

            # Estimar horas basadas en el tipo de unidad y cantidad
            unit_type = process.get("unit_type", "Unidad")
            process_quantity = process.get("quantity", 1)

            # Si el tipo de unidad es "Hora", las horas estimadas son la cantidad
            if unit_type == "Hora":
                estimated_hours = process_quantity
            else:
                # Para otros tipos, estimamos 1 hora por cada 100 unidades (mínimo 1 hora)
                estimated_hours = max(1, process_quantity / 100)

            # Fecha de finalización basada en las horas estimadas
            end_date = (current_date + timedelta(days=days_offset, hours=estimated_hours)).isoformat()

            # Buscar máquina compatible en el catálogo de procesos
            machine_id = None
            process_catalog = load_process_catalog()
            process_id = process.get("process_id")

            if process_id:
                # Buscar el proceso en el catálogo
                catalog_process = next((p for p in process_catalog if p["process_id"] == process_id), None)

                if catalog_process and "compatible_machines" in catalog_process and catalog_process["compatible_machines"]:
                    # Asignar la primera máquina compatible
                    machine_id = catalog_process["compatible_machines"][0]
                    log_info(f"Asignando máquina {machine_id} al proceso de acabado {process.get('name', '')}")

            # Crear proceso de acabado
            finishing_process = {
                "process_id": f"PROD-{budget_id}-F{i+1}",
                "process_type": production_type,
                "name": f"{process_type}: {process.get('name', '')}",
                "description": f"{process.get('name', '')} - {process_quantity} {unit_type}",
                "quantity": process_quantity,
                "status": ProductionStatus.PENDING.value,
                "start_date": start_date,
                "end_date": end_date,
                "estimated_hours": estimated_hours,
                "machine_id": machine_id,  # Asignar la máquina compatible
                "dependencies": printing_process_ids,
                "created_at": current_date.isoformat(),
                "updated_at": current_date.isoformat()
            }

            processes.append(finishing_process)

    # Crear la nueva OT con la estructura jerárquica
    new_ot = {
        "ot_id": ot_number,
        "budget_id": budget_id,
        "client_id": client_id,
        "approval_date": approval_date,
        "status": OTStatus.PENDING.value,
        "processes": processes,
        "created_at": current_date.isoformat(),
        "updated_at": current_date.isoformat()
    }

    # Añadir la nueva OT a los datos de producción
    production_data.append(new_ot)

    # Guardar los datos actualizados
    save_production_data(production_data)

    log_info(f"Presupuesto {budget_id} añadido a producción con OT {ot_number} y {len(processes)} procesos")
    return new_ot

def add_budget_to_production(budget_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Añade un presupuesto aprobado a la lista de producción
    """
    # Primero intentamos usar la nueva estructura
    new_ot = add_budget_to_production_v2(budget_data)
    if new_ot is not None:
        # Convertir la nueva estructura a la antigua para mantener compatibilidad
        production_data = load_production_data()
        return production_data

    # Si no se pudo usar la nueva estructura, usamos la antigua
    production_data = load_production_data()

    # Verificar si el presupuesto ya está en producción
    existing_processes = [p for p in production_data if isinstance(p, dict) and p.get("budget_id") == budget_data["budget_id"] and "ot_number" in p]
    if existing_processes:
        log_info(f"El presupuesto {budget_data['budget_id']} ya está en producción")
        return production_data

    # Fecha actual para planificación
    current_date = datetime.now()

    # Lista para almacenar los nuevos procesos
    new_processes = []

    # Obtener datos básicos del presupuesto
    budget_id = budget_data["budget_id"]
    ot_number = budget_data["ot_number"]
    client_id = budget_data["client_id"]
    quantity = budget_data.get("quantity", 0)

    # Procesar cada parte de impresión como un proceso separado
    if "parts" in budget_data and budget_data["parts"]:
        for i, part in enumerate(budget_data["parts"]):
            # Calcular fechas de inicio y fin para este proceso
            # Asumimos que cada proceso de impresión toma 1 día por defecto
            start_date = (current_date + timedelta(days=i)).isoformat()

            # Obtener el tiempo estimado del presupuesto
            estimated_hours = 1.0  # Valor por defecto (1 hora)

            # Prioridad 1: Usar custom_print_time si está definido
            if part.get("custom_print_time") is not None:
                estimated_hours = float(part["custom_print_time"])
                log_info(f"Usando tiempo personalizado para {part.get('name', f'Parte {i+1}')}: {estimated_hours} horas")

            # Prioridad 2: Usar total_time_hours de sheet_calculation.machine_data
            elif part.get("sheet_calculation") and part["sheet_calculation"].get("machine_data") and part["sheet_calculation"]["machine_data"].get("total_time_hours") is not None:
                estimated_hours = float(part["sheet_calculation"]["machine_data"]["total_time_hours"])
                log_info(f"Usando tiempo de cálculo de pliegos para {part.get('name', f'Parte {i+1}')}: {estimated_hours} horas")

            # Prioridad 3: Usar total_time_hours de sheet_calculation
            elif part.get("sheet_calculation") and part["sheet_calculation"].get("total_time_hours") is not None:
                estimated_hours = float(part["sheet_calculation"]["total_time_hours"])
                log_info(f"Usando tiempo total de sheet_calculation para {part.get('name', f'Parte {i+1}')}: {estimated_hours} horas")

            # Si no hay ningún tiempo definido, calcular basado en el costo (método original)
            else:
                machine_cost = part.get("machine_cost", 0)
                machine_data = part.get("machine_data", {})
                hourly_cost = machine_data.get("hourly_cost", 120)  # Valor por defecto
                estimated_hours = max(1, machine_cost / hourly_cost) if hourly_cost > 0 else 1
                log_info(f"Calculando tiempo basado en costo para {part.get('name', f'Parte {i+1}')}: {estimated_hours} horas")

            # Fecha de finalización basada en las horas estimadas
            end_date = (current_date + timedelta(days=i, hours=estimated_hours)).isoformat()

            # Crear proceso de impresión
            printing_process = {
                "process_id": f"PROD-{budget_id}-P{i+1}",
                "budget_id": budget_id,
                "ot_number": ot_number,
                "client_id": client_id,
                "process_type": ProductionProcessType.PRINTING.value,
                "name": f"Impresión: {part.get('name', f'Parte {i+1}')}",
                "description": part.get("description", "") or f"Impresión de {part.get('name', f'Parte {i+1}')}",
                "quantity": quantity,
                "status": ProductionStatus.PENDING.value,
                "start_date": start_date,
                "end_date": end_date,
                "estimated_hours": estimated_hours,
                "machine_id": part.get("machine_id"),
                "dependencies": [],
                "created_at": current_date.isoformat(),
                "updated_at": current_date.isoformat()
            }

            new_processes.append(printing_process)

    # Procesar cada proceso de acabado como un proceso separado
    if "process_costs" in budget_data and budget_data["process_costs"]:
        # Los procesos de acabado dependen de que se completen todos los procesos de impresión
        printing_process_ids = [p["process_id"] for p in new_processes if p["process_type"] == ProductionProcessType.PRINTING.value]

        for i, process in enumerate(budget_data["process_costs"]):
            # Determinar el tipo de proceso basado en el tipo de acabado
            process_type = process.get("type", "")
            production_type = ProductionProcessType.FINISHING.value

            if process_type in ["Corte", "Plegado", "Encuadernación", "Barnizado", "Laminado", "Troquelado"]:
                production_type = ProductionProcessType.FINISHING.value
            elif process_type in ["Embalaje", "Manipulado"]:
                production_type = ProductionProcessType.HANDLING.value
            elif process_type in ["Envío", "Transporte"]:
                production_type = ProductionProcessType.SHIPPING.value

            # Calcular fechas de inicio y fin para este proceso
            # Los procesos de acabado comienzan después de que terminen todos los procesos de impresión
            days_offset = len(new_processes) + i
            start_date = (current_date + timedelta(days=days_offset)).isoformat()

            # Estimar horas basadas en el tipo de unidad y cantidad
            unit_type = process.get("unit_type", "Unidad")
            process_quantity = process.get("quantity", 1)

            # Si el tipo de unidad es "Hora", las horas estimadas son la cantidad
            if unit_type == "Hora":
                estimated_hours = process_quantity
            else:
                # Para otros tipos, estimamos 1 hora por cada 100 unidades (mínimo 1 hora)
                estimated_hours = max(1, process_quantity / 100)

            # Fecha de finalización basada en las horas estimadas
            end_date = (current_date + timedelta(days=days_offset, hours=estimated_hours)).isoformat()

            # Buscar máquina compatible en el catálogo de procesos
            machine_id = None
            process_catalog = load_process_catalog()
            process_id = process.get("process_id")

            if process_id:
                # Buscar el proceso en el catálogo
                catalog_process = next((p for p in process_catalog if p["process_id"] == process_id), None)

                if catalog_process and "compatible_machines" in catalog_process and catalog_process["compatible_machines"]:
                    # Asignar la primera máquina compatible
                    machine_id = catalog_process["compatible_machines"][0]
                    log_info(f"Asignando máquina {machine_id} al proceso de acabado {process.get('name', '')}")

            # Crear proceso de acabado
            finishing_process = {
                "process_id": f"PROD-{budget_id}-F{i+1}",
                "budget_id": budget_id,
                "ot_number": ot_number,
                "client_id": client_id,
                "process_type": production_type,
                "name": f"{process_type}: {process.get('name', '')}",
                "description": f"{process.get('name', '')} - {process_quantity} {unit_type}",
                "quantity": process_quantity,
                "status": ProductionStatus.PENDING.value,
                "start_date": start_date,
                "end_date": end_date,
                "estimated_hours": estimated_hours,
                "machine_id": machine_id,  # Asignar la máquina compatible
                "dependencies": printing_process_ids,
                "created_at": current_date.isoformat(),
                "updated_at": current_date.isoformat()
            }

            new_processes.append(finishing_process)

    # Añadir los nuevos procesos a los datos de producción
    production_data.extend(new_processes)

    # Guardar los datos actualizados
    save_production_data(production_data)

    log_info(f"Presupuesto {budget_id} añadido a producción con OT {ot_number} y {len(new_processes)} procesos (estructura antigua)")
    return production_data

def get_production_process_by_id(process_id: str) -> Optional[Dict[str, Any]]:
    """
    Obtiene un proceso de producción por su ID
    """
    production_data = load_production_data()

    # Buscar en la estructura antigua
    for process in production_data:
        if isinstance(process, dict) and "process_id" in process and process["process_id"] == process_id:
            return process

    # Buscar en la nueva estructura
    for ot in production_data:
        if isinstance(ot, dict) and "processes" in ot and isinstance(ot["processes"], list):
            for process in ot["processes"]:
                if process["process_id"] == process_id:
                    # Añadir campos de la OT para compatibilidad
                    process_copy = process.copy()
                    process_copy["ot_number"] = ot["ot_id"]
                    process_copy["budget_id"] = ot["budget_id"]
                    process_copy["client_id"] = ot["client_id"]
                    return process_copy

    return None

def update_production_process(process_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Actualiza un proceso de producción
    """
    production_data = load_production_data()
    current_date = datetime.now().isoformat()

    # Buscar en la estructura antigua
    for i, process in enumerate(production_data):
        if isinstance(process, dict) and "process_id" in process and process["process_id"] == process_id:
            # Actualizar solo los campos proporcionados
            for key, value in update_data.items():
                if key in process:
                    process[key] = value

            # Actualizar la fecha de actualización
            process["updated_at"] = current_date

            # Guardar los cambios
            production_data[i] = process
            save_production_data(production_data)

            log_info(f"Proceso {process_id} actualizado (estructura antigua)")
            return process

    # Buscar en la nueva estructura
    for i, ot in enumerate(production_data):
        if isinstance(ot, dict) and "processes" in ot and isinstance(ot["processes"], list):
            for j, process in enumerate(ot["processes"]):
                if process["process_id"] == process_id:
                    # Actualizar solo los campos proporcionados
                    for key, value in update_data.items():
                        if key in process:
                            process[key] = value

                    # Actualizar la fecha de actualización
                    process["updated_at"] = current_date
                    ot["updated_at"] = current_date

                    # Guardar los cambios
                    production_data[i]["processes"][j] = process
                    save_production_data(production_data)

                    # Crear una copia del proceso con campos de la OT para compatibilidad
                    process_copy = process.copy()
                    process_copy["ot_number"] = ot["ot_id"]
                    process_copy["budget_id"] = ot["budget_id"]
                    process_copy["client_id"] = ot["client_id"]

                    log_info(f"Proceso {process_id} actualizado (estructura nueva)")
                    return process_copy

    log_error(f"Proceso {process_id} no encontrado para actualizar")
    return None

def get_production_processes_by_budget_id(budget_id: str) -> List[Dict[str, Any]]:
    """
    Obtiene todos los procesos de producción asociados a un presupuesto
    """
    production_data = load_production_data()
    result = []

    # Buscar en la estructura antigua
    result.extend([p for p in production_data if isinstance(p, dict) and "budget_id" in p and p["budget_id"] == budget_id])

    # Buscar en la nueva estructura
    for ot in production_data:
        if isinstance(ot, dict) and "budget_id" in ot and ot["budget_id"] == budget_id and "processes" in ot and isinstance(ot["processes"], list):
            # Añadir campos de la OT a cada proceso para compatibilidad
            for process in ot["processes"]:
                process_copy = process.copy()
                process_copy["ot_number"] = ot["ot_id"]
                process_copy["budget_id"] = ot["budget_id"]
                process_copy["client_id"] = ot["client_id"]
                result.append(process_copy)

    return result

def get_all_production_processes() -> List[Dict[str, Any]]:
    """
    Obtiene todos los procesos de producción
    """
    production_data = load_production_data()
    result = []

    # Incluir procesos de la estructura antigua
    result.extend([p for p in production_data if isinstance(p, dict) and "process_id" in p and "ot_number" in p])

    # Incluir procesos de la nueva estructura
    for ot in production_data:
        if isinstance(ot, dict) and "processes" in ot and isinstance(ot["processes"], list):
            # Añadir campos de la OT a cada proceso para compatibilidad
            for process in ot["processes"]:
                process_copy = process.copy()
                process_copy["ot_number"] = ot["ot_id"]
                process_copy["budget_id"] = ot["budget_id"]
                process_copy["client_id"] = ot["client_id"]
                result.append(process_copy)

    return result

def get_production_processes_by_type(process_type: ProductionProcessType) -> List[Dict[str, Any]]:
    """
    Obtiene todos los procesos de producción de un tipo específico
    """
    production_data = load_production_data()
    result = []

    # Buscar en la estructura antigua
    result.extend([p for p in production_data if isinstance(p, dict) and "process_type" in p and p["process_type"] == process_type.value])

    # Buscar en la nueva estructura
    for ot in production_data:
        if isinstance(ot, dict) and "processes" in ot and isinstance(ot["processes"], list):
            for process in ot["processes"]:
                if process["process_type"] == process_type.value:
                    # Añadir campos de la OT para compatibilidad
                    process_copy = process.copy()
                    process_copy["ot_number"] = ot["ot_id"]
                    process_copy["budget_id"] = ot["budget_id"]
                    process_copy["client_id"] = ot["client_id"]
                    result.append(process_copy)

    return result

def get_production_processes_by_status(status: ProductionStatus) -> List[Dict[str, Any]]:
    """
    Obtiene todos los procesos de producción con un estado específico
    """
    production_data = load_production_data()
    result = []

    # Buscar en la estructura antigua
    result.extend([p for p in production_data if isinstance(p, dict) and "status" in p and p["status"] == status.value])

    # Buscar en la nueva estructura
    for ot in production_data:
        if isinstance(ot, dict) and "processes" in ot and isinstance(ot["processes"], list):
            for process in ot["processes"]:
                if process["status"] == status.value:
                    # Añadir campos de la OT para compatibilidad
                    process_copy = process.copy()
                    process_copy["ot_number"] = ot["ot_id"]
                    process_copy["budget_id"] = ot["budget_id"]
                    process_copy["client_id"] = ot["client_id"]
                    result.append(process_copy)

    return result

def get_production_processes_by_ot_number(ot_number: str) -> List[ProductionProcess]:
    """
    Obtiene todos los procesos de producción asociados a un número de OT

    Args:
        ot_number: Número de OT

    Returns:
        List[ProductionProcess]: Lista de procesos de producción
    """
    production_data = load_production_data()
    result = []

    # Buscar en la estructura antigua
    old_processes = [
        process
        for process in production_data
        if isinstance(process, dict) and "ot_number" in process and process.get("ot_number") == ot_number
    ]
    result.extend([ProductionProcess(**process) for process in old_processes])

    # Buscar en la nueva estructura
    for ot in production_data:
        if isinstance(ot, dict) and "ot_id" in ot and ot["ot_id"] == ot_number and "processes" in ot and isinstance(ot["processes"], list):
            for process in ot["processes"]:
                # Añadir campos de la OT para compatibilidad
                process_copy = process.copy()
                process_copy["ot_number"] = ot["ot_id"]
                process_copy["budget_id"] = ot["budget_id"]
                process_copy["client_id"] = ot["client_id"]
                result.append(ProductionProcess(**process_copy))

    return result

def delete_production_process(process_id: str) -> bool:
    """
    Elimina un proceso de producción por su ID

    Args:
        process_id: ID del proceso a eliminar

    Returns:
        bool: True si el proceso fue eliminado, False si no se encontró
    """
    production_data = load_production_data()

    # Buscar en la estructura antigua
    for i, process in enumerate(production_data):
        if isinstance(process, dict) and "process_id" in process and process["process_id"] == process_id:
            # Eliminar el proceso
            deleted_process = production_data.pop(i)

            # Guardar los cambios
            save_production_data(production_data)

            # Registrar la eliminación en el log
            log_info(f"Proceso de producción eliminado (estructura antigua): {deleted_process['process_id']} - {deleted_process['name']}")

            return True

    # Buscar en la nueva estructura
    for i, ot in enumerate(production_data):
        if isinstance(ot, dict) and "processes" in ot and isinstance(ot["processes"], list):
            for j, process in enumerate(ot["processes"]):
                if process["process_id"] == process_id:
                    # Eliminar el proceso
                    deleted_process = production_data[i]["processes"].pop(j)

                    # Actualizar la fecha de actualización de la OT
                    production_data[i]["updated_at"] = datetime.now().isoformat()

                    # Guardar los cambios
                    save_production_data(production_data)

                    # Registrar la eliminación en el log
                    log_info(f"Proceso de producción eliminado (estructura nueva): {deleted_process['process_id']} - {deleted_process['name']}")

                    return True

    # Si no se encontró el proceso, retornar False
    log_error(f"Proceso {process_id} no encontrado para eliminar")
    return False

def delete_production_processes_by_ot(ot_number: str) -> dict:
    """
    Elimina todos los procesos de producción asociados a un número de OT

    Args:
        ot_number: Número de OT cuyos procesos se eliminarán

    Returns:
        dict: Información sobre la operación (procesos eliminados, éxito)
    """
    production_data = load_production_data()
    deleted_processes = []

    # Buscar en la estructura antigua
    old_processes = [p for p in production_data if isinstance(p, dict) and "ot_number" in p and p.get("ot_number") == ot_number]
    deleted_processes.extend(old_processes)

    # Filtrar los procesos que NO pertenecen a la OT en la estructura antigua (estos se mantendrán)
    remaining_processes = [p for p in production_data if not (isinstance(p, dict) and "ot_number" in p and p.get("ot_number") == ot_number)]

    # Buscar en la nueva estructura
    ot_index = None
    for i, ot in enumerate(remaining_processes):
        if isinstance(ot, dict) and "ot_id" in ot and ot["ot_id"] == ot_number:
            ot_index = i
            if "processes" in ot and isinstance(ot["processes"], list):
                # Añadir campos de la OT a cada proceso para compatibilidad en el registro
                for process in ot["processes"]:
                    process_copy = process.copy()
                    process_copy["ot_number"] = ot["ot_id"]
                    process_copy["budget_id"] = ot["budget_id"]
                    process_copy["client_id"] = ot["client_id"]
                    deleted_processes.append(process_copy)
            break

    # Si se encontró la OT en la nueva estructura, eliminarla
    if ot_index is not None:
        remaining_processes.pop(ot_index)

    # Si no hay procesos para esta OT, retornar información
    if not deleted_processes:
        return {
            "success": False,
            "message": f"No se encontraron procesos para la OT {ot_number}",
            "deleted_count": 0
        }

    # Guardar los datos actualizados
    save_production_data(remaining_processes)

    # Registrar la eliminación en el log
    deleted_count = len(deleted_processes)
    log_info(f"OT eliminada: {ot_number} - {deleted_count} procesos eliminados")

    return {
        "success": True,
        "message": f"OT {ot_number} eliminada correctamente. {deleted_count} procesos eliminados.",
        "deleted_count": deleted_count,
        "processes": [p["process_id"] for p in deleted_processes]
    }

def migrate_to_new_structure() -> dict:
    """
    Migra los datos de producción de la estructura antigua a la nueva estructura jerárquica

    Returns:
        dict: Información sobre la operación (OTs migradas, procesos migrados, éxito)
    """
    production_data = load_production_data()

    # Verificar si hay datos en la estructura antigua
    old_processes = [p for p in production_data if isinstance(p, dict) and "ot_number" in p and "process_id" in p]

    if not old_processes:
        return {
            "success": True,
            "message": "No hay datos en la estructura antigua para migrar",
            "migrated_ots": 0,
            "migrated_processes": 0
        }

    # Agrupar procesos por OT
    ot_processes = {}
    for process in old_processes:
        ot_number = process["ot_number"]
        if ot_number not in ot_processes:
            ot_processes[ot_number] = []
        ot_processes[ot_number].append(process)

    # Crear nuevas OTs con la estructura jerárquica
    new_ots = []
    for ot_number, processes in ot_processes.items():
        # Tomar datos comunes del primer proceso
        first_process = processes[0]
        budget_id = first_process["budget_id"]
        client_id = first_process["client_id"]
        created_at = first_process["created_at"]

        # Crear nueva OT
        new_ot = {
            "ot_id": ot_number,
            "budget_id": budget_id,
            "client_id": client_id,
            "approval_date": created_at,  # Usar la fecha de creación como fecha de aprobación
            "status": OTStatus.PENDING.value,  # Estado por defecto
            "processes": [],
            "created_at": created_at,
            "updated_at": datetime.now().isoformat()
        }

        # Añadir procesos a la OT (sin los campos que ahora están en la OT)
        for process in processes:
            process_copy = process.copy()
            # Eliminar campos que ahora están en la OT
            if "ot_number" in process_copy:
                del process_copy["ot_number"]
            if "budget_id" in process_copy:
                del process_copy["budget_id"]
            if "client_id" in process_copy:
                del process_copy["client_id"]

            new_ot["processes"].append(process_copy)

        # Determinar el estado general de la OT basado en los estados de los procesos
        process_statuses = [p["status"] for p in new_ot["processes"]]
        if all(status == ProductionStatus.COMPLETED.value for status in process_statuses):
            new_ot["status"] = OTStatus.COMPLETED.value
        elif any(status == ProductionStatus.IN_PROGRESS.value for status in process_statuses):
            new_ot["status"] = OTStatus.IN_PROGRESS.value

        new_ots.append(new_ot)

    # Filtrar los elementos que no son procesos de la estructura antigua
    non_process_items = [item for item in production_data if not (isinstance(item, dict) and "ot_number" in item and "process_id" in item)]

    # Combinar los elementos no-proceso con las nuevas OTs
    new_production_data = non_process_items + new_ots

    # Guardar los datos migrados
    save_production_data(new_production_data)

    # Registrar la migración en el log
    migrated_ots = len(new_ots)
    migrated_processes = len(old_processes)
    log_info(f"Migración completada: {migrated_ots} OTs y {migrated_processes} procesos migrados a la nueva estructura")

    return {
        "success": True,
        "message": f"Migración completada: {migrated_ots} OTs y {migrated_processes} procesos migrados a la nueva estructura",
        "migrated_ots": migrated_ots,
        "migrated_processes": migrated_processes
    }

