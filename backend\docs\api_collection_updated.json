{"info": {"name": "Imprenta API Collection", "description": "Colección de endpoints esenciales para la API del sistema de gestión de imprenta", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "item": [{"name": "Clientes", "item": [{"name": "Obtener todos los clientes", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/clients/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", ""]}}}, {"name": "Obtener cliente por ID", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/clients/CL-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", "CL-001"]}}}, {"name": "Crear nuevo cliente", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/clients/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": \"CL-NEW\",\n  \"billing_code\": \"BC-001\",\n  \"order_id\": \"OT-001\",\n  \"company\": {\n    \"name\": \"Nueva Empresa S.L.\",\n    \"address\": {\n      \"country\": \"España\",\n      \"region\": \"Madrid\",\n      \"city\": \"Madrid\",\n      \"street\": \"Calle Principal 123\",\n      \"postal_code\": \"28001\"\n    }\n  },\n  \"contact\": {\n    \"position\": \"<PERSON><PERSON><PERSON>\",\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON>\",\n    \"phone\": \"*********\",\n    \"fax\": \"*********\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"active\": true,\n  \"created_at\": \"2025-03-11\",\n  \"updated_at\": \"2025-03-11\",\n  \"notes\": \"Cliente nuevo\"\n}"}}}, {"name": "Actualizar cliente", "request": {"method": "PUT", "url": {"raw": "http://localhost:3005/clients/CL-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", "CL-001"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"active\": true,\n  \"notes\": \"Cliente actualizado\"\n}"}}}, {"name": "Eliminar cliente", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/clients/CL-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["clients", "CL-001"]}}}]}, {"name": "Presupuestos", "item": [{"name": "Obtener todos los presupuestos", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/budgets/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", ""]}}}, {"name": "Obtener presupuesto por ID", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/budgets/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "PRES-ABC123"]}}}, {"name": "Crear nuevo presupuesto", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/budgets/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ot_number\": \"OT-12345\",\n  \"description\": \"Folletos promocionales\",\n  \"client_id\": \"Customer-1\",\n  \"job_type\": \"Folleto\",\n  \"quantity\": 1000,\n  \"page_size\": {\n    \"width\": 210,\n    \"height\": 297\n  },\n  \"page_count\": 8,\n  \"parts\": [\n    {\n      \"name\": \"Portada\",\n      \"description\": \"Portada y contraportada\",\n      \"page_count\": 4,\n      \"page_size\": {\n        \"width\": 210,\n        \"height\": 297\n      },\n      \"paper_id\": \"PAP-123456\",\n      \"machine_id\": \"MAQ-123456\"\n    },\n    {\n      \"name\": \"Interior\",\n      \"description\": \"Páginas interiores\",\n      \"page_count\": 4,\n      \"page_size\": {\n        \"width\": 210,\n        \"height\": 297\n      },\n      \"paper_id\": \"PAP-654321\",\n      \"machine_id\": \"MAQ-654321\"\n    }\n  ],\n  \"process_costs\": [\n    {\n      \"process_id\": \"PROC-123456\",\n      \"name\": \"Corte (Guillotina)\",\n      \"type\": \"Corte\",\n      \"quantity\": 1,\n      \"unit_cost\": 15.0,\n      \"unit_type\": \"Hora\"\n    }\n  ]\n}"}}}, {"name": "Actualizar presupuesto", "request": {"method": "PUT", "url": {"raw": "http://localhost:3005/budgets/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "PRES-ABC123"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 2000,\n  \"status\": \"En Proceso\"\n}"}}}, {"name": "Eliminar presupuesto", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/budgets/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "PRES-ABC123"]}}}, {"name": "Buscar presupuestos", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/budgets/search?client_id=Customer-1&status=Pendiente&job_type=Folleto", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "search"], "query": [{"key": "client_id", "value": "Customer-1"}, {"key": "status", "value": "Pendiente"}, {"key": "job_type", "value": "<PERSON><PERSON><PERSON>"}]}}}, {"name": "Duplicar presupuesto", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/budgets/PRES-ABC123/duplicate", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["budgets", "PRES-ABC123", "duplicate"]}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Obtener todos los papeles", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/papers/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", ""]}}}, {"name": "Obtener papel por ID", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/papers/Pap-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", "Pap-001"]}}}, {"name": "<PERSON><PERSON><PERSON> nuevo papel", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/papers/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": \"Pap-NEW\",\n  \"descriptive_name\": \"Offset 100g A3\",\n  \"media_type\": \"Paper\",\n  \"category\": \"Offset\",\n  \"finish\": \"Natural\",\n  \"dimension_width\": 420,\n  \"dimension_height\": 297,\n  \"thickness\": 100,\n  \"weight\": 100,\n  \"grainDirection\": \"Long\",\n  \"manufacturer\": \"PaperCo\",\n  \"inStock\": true,\n  \"color\": \"Blanco\",\n  \"notes\": \"Papel de alta calidad\",\n  \"price_per_1000\": 25.5,\n  \"cost_per_ton\": 1200.0\n}"}}}, {"name": "<PERSON>ual<PERSON><PERSON> papel", "request": {"method": "PUT", "url": {"raw": "http://localhost:3005/papers/Pap-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", "Pap-001"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"inStock\": true,\n  \"price_per_1000\": 28.5\n}"}}}, {"name": "Eliminar papel", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/papers/Pap-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["papers", "Pap-001"]}}}]}, {"name": "Máquinas", "item": [{"name": "Obtener todas las máquinas", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/machines/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", ""]}}}, {"name": "Obtener máquina por ID", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/machines/M-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", "M-001"]}}}, {"name": "Crear nueva máquina", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/machines/", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", ""]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"machine_id\": \"M-NEW\",\n  \"name\": \"Nueva Prensa Digital\",\n  \"type\": \"Digital\",\n  \"manufacturer\": \"PrintCorp\",\n  \"model\": \"DP-2000\",\n  \"max_width\": 330,\n  \"max_height\": 488,\n  \"min_width\": 100,\n  \"min_height\": 148,\n  \"max_thickness\": 300,\n  \"status\": \"Activa\",\n  \"purchase_date\": \"2025-01-15\",\n  \"last_maintenance\": \"2025-03-01\",\n  \"hourly_cost\": 45.0,\n  \"cfa_percentage\": 15.0,\n  \"notes\": \"Máquina nueva de alta velocidad\"\n}"}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "url": {"raw": "http://localhost:3005/machines/M-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", "M-001"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"Activa\",\n  \"last_maintenance\": \"2025-03-10\",\n  \"hourly_cost\": 50.0,\n  \"notes\": \"Mantenimiento realizado\"\n}"}}}, {"name": "Eliminar m<PERSON><PERSON>", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/machines/M-001", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["machines", "M-001"]}}}]}, {"name": "Calculadora de Pliegos", "item": [{"name": "Calcular pliegos necesarios", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/calcular-pliegos", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["calcular-pliegos"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"num_paginas\": 16,\n  \"ancho_pagina\": 210,\n  \"alto_pagina\": 297,\n  \"ancho_pliego\": 700,\n  \"alto_pliego\": 1000,\n  \"front_colors\": 4,\n  \"back_colors\": 4,\n  \"paper_id\": \"PAP-001\",\n  \"copies\": 1000,\n  \"client_country\": \"España\"\n}"}}}, {"name": "Calcular peso del papel", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/calcular-peso-papel", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["calcular-peso-papel"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paper_id\": \"PAP-001\",\n  \"sheets\": 500,\n  \"width_mm\": 700,\n  \"height_mm\": 1000,\n  \"weight_gsm\": 90\n}"}}}, {"name": "Calcular hojas", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/calculate-sheets", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["calculate-sheets"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paper_id\": \"PAP-001\",\n  \"sheets_per_unit\": 4,\n  \"units\": 1000,\n  \"waste_percentage\": 5.0\n}"}}}]}, {"name": "JSON-OT y JDF", "item": [{"name": "Generar JSON-OT", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/json-ot/generate/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["json-ot", "generate", "PRES-ABC123"]}}}, {"name": "Guardar JSON-OT", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/json-ot/save/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["json-ot", "save", "PRES-ABC123"]}}}, {"name": "Renderizar plantilla JDF", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/json-ot/render/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["json-ot", "render", "PRES-ABC123"]}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Obtener token de autenticación", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/external/get-token", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["external", "get-token"]}}}, {"name": "Enviar JDF de presupuesto", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/external/send-budget-jdf/PRES-ABC123", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["external", "send-budget-jdf", "PRES-ABC123"]}}}, {"name": "Enviar JDF personalizado", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/external/send-jdf", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["external", "send-jdf"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"jdf_data\": {\n    \"jobId\": \"OT-12345\",\n    \"customer\": \"Cliente Ejemplo\",\n    \"product\": \"Folleto\",\n    \"quantity\": 1000,\n    \"pages\": 16,\n    \"mediaSize\": {\n      \"width\": 210,\n      \"height\": 297\n    }\n  }\n}"}}}]}, {"name": "Uploads y Archivos", "item": [{"name": "Subir PDF", "request": {"method": "POST", "url": {"raw": "http://localhost:3005/uploads/upload-pdf", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["uploads", "upload-pdf"]}, "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/your/file.pdf"}]}}}, {"name": "Listar archivos", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/uploads/files", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["uploads", "files"]}}}, {"name": "Obtener información de PDF", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/uploads/pdf-info/ejemplo.pdf", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["uploads", "pdf-info", "ejemplo.pdf"]}}}, {"name": "Descargar archivo", "request": {"method": "GET", "url": {"raw": "http://localhost:3005/uploads/download/ejemplo.pdf", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["uploads", "download", "ejemplo.pdf"]}}}, {"name": "Eliminar archivo", "request": {"method": "DELETE", "url": {"raw": "http://localhost:3005/uploads/ejemplo.pdf", "protocol": "http", "host": ["localhost"], "port": "3005", "path": ["uploads", "ejemplo.pdf"]}}}]}]}