// services/ApiService.js
import { buildApiUrl } from '../config';
import LogService from './logService';

/**
 * Servicio centralizado para realizar peticiones HTTP a la API
 * con manejo automático de autenticación
 */
class ApiService {
  /**
   * Realiza una petición HTTP a la API con el token de autenticación
   *
   * @param {string} endpoint - Endpoint de la API (sin la URL base)
   * @param {object} options - Opciones de la petición
   * @param {string} token - Token de autenticación (opcional, si no se proporciona se intentará obtener del localStorage)
   * @returns {Promise} - Promesa con la respuesta
   */
  static async fetch(endpoint, options = {}, token = null) {
    // Obtener el token del localStorage si no se proporciona
    const authToken = token || localStorage.getItem('auth_token');

    // Construir la URL completa
    const url = buildApiUrl(endpoint);

    // Preparar las opciones de la petición
    const fetchOptions = {
      ...options,
      headers: {
        ...options.headers,
      }
    };

    // Añadir el token de autenticación si está disponible
    if (authToken) {
      fetchOptions.headers = {
        ...fetchOptions.headers,
        'Authorization': `Bearer ${authToken}`
      };
    }

    // Extraer datos para el log
    const method = options.method || 'GET';
    const data = options.body ?
      (typeof options.body === 'string' ? JSON.parse(options.body) : options.body)
      : null;

    try {
      console.log(`[ApiService] Ejecutando fetch a ${url} (${method})`);

      // Realizar la petición
      const response = await fetch(url, fetchOptions);

      // Registrar la operación en el log
      LogService.logApiOperation(
        url,
        method,
        data,
        response.status
      );

      // Registrar información detallada de la respuesta para depuración
      console.log(`[ApiService] Respuesta de ${url} (${method}):`, {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries([...response.headers.entries()]),
        ok: response.ok
      });

      // Si la respuesta es 401 (Unauthorized), podría ser que el token ha expirado
      if (response.status === 401) {
        console.warn('Error de autenticación en la petición a la API. El token podría haber expirado.');
        // Aquí se podría implementar un mecanismo de refresh token si fuera necesario
      }

      return response;
    } catch (error) {
      // Registrar el error en el log
      LogService.logError(`Error en petición ${method} a ${url}`, {
        error: error.message,
        data
      });

      throw error;
    }
  }

  /**
   * Realiza una petición GET a la API
   *
   * @param {string} endpoint - Endpoint de la API (sin la URL base)
   * @param {object} options - Opciones adicionales de la petición
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise} - Promesa con la respuesta
   */
  static async get(endpoint, options = {}, token = null) {
    return this.fetch(endpoint, { ...options, method: 'GET' }, token);
  }

  /**
   * Realiza una petición POST a la API
   *
   * @param {string} endpoint - Endpoint de la API (sin la URL base)
   * @param {object} data - Datos a enviar en el cuerpo de la petición
   * @param {object} options - Opciones adicionales de la petición
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise} - Promesa con la respuesta
   */
  static async post(endpoint, data, options = {}, token = null) {
    // Convertir los datos a JSON para el cuerpo de la petición
    const jsonData = JSON.stringify(data);

    // Registrar los datos enviados para depuración
    console.log(`[ApiService] POST ${endpoint} - Datos enviados:`, data);
    console.log(`[ApiService] POST ${endpoint} - JSON enviado:`, jsonData);

    const fetchOptions = {
      ...options,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: jsonData
    };

    // Registrar la URL completa
    const fullUrl = buildApiUrl(endpoint);
    console.log(`[ApiService] Enviando POST a ${fullUrl}`);

    return this.fetch(endpoint, fetchOptions, token);
  }

  /**
   * Realiza una petición PUT a la API
   *
   * @param {string} endpoint - Endpoint de la API (sin la URL base)
   * @param {object} data - Datos a enviar en el cuerpo de la petición
   * @param {object} options - Opciones adicionales de la petición
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise} - Promesa con la respuesta
   */
  static async put(endpoint, data, options = {}, token = null) {
    const fetchOptions = {
      ...options,
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: JSON.stringify(data)
    };

    return this.fetch(endpoint, fetchOptions, token);
  }

  /**
   * Realiza una petición PATCH a la API
   *
   * @param {string} endpoint - Endpoint de la API (sin la URL base)
   * @param {object} data - Datos a enviar en el cuerpo de la petición
   * @param {object} options - Opciones adicionales de la petición
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise} - Promesa con la respuesta
   */
  static async patch(endpoint, data, options = {}, token = null) {
    const fetchOptions = {
      ...options,
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: JSON.stringify(data)
    };

    return this.fetch(endpoint, fetchOptions, token);
  }

  /**
   * Realiza una petición DELETE a la API
   *
   * @param {string} endpoint - Endpoint de la API (sin la URL base)
   * @param {object} options - Opciones adicionales de la petición
   * @param {string} token - Token de autenticación (opcional)
   * @returns {Promise} - Promesa con la respuesta
   */
  static async delete(endpoint, options = {}, token = null) {
    return this.fetch(endpoint, { ...options, method: 'DELETE' }, token);
  }
}

export default ApiService;
