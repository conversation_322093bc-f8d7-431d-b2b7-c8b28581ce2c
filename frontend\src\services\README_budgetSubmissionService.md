# Budget Submission Service

## Descripción

El `budgetSubmissionService` es un servicio especializado que extrae la lógica compleja de envío y validación de presupuestos del componente `BudgetForm`. Este servicio maneja toda la validación, cálculo de costos, preparación de datos y envío de presupuestos, proporcionando una interfaz unificada y robusta.

## Motivación

La función `handleSubmit` original en `BudgetForm` tenía más de 123 líneas de código con múltiples responsabilidades complejas:
- Validación de partes calculadas en modo edición
- Cálculo complejo de costos por parte (Digital vs Offset)
- Cálculo de maculatura para máquinas offset
- Suma de costos de partes, procesos y envío
- Preparación del presupuesto para envío
- Manejo de navegación post-envío
- Manejo de errores y mensajes al usuario

Al extraer esta lógica a un servicio especializado, conseguimos:
- **Mejor modularización**: Separación clara de responsabilidades
- **Testabilidad mejorada**: Funciones más pequeñas y específicas
- **Reutilización**: Lógica disponible para otros componentes
- **Mantenibilidad**: Código más organizado y legible
- **Robustez**: Mejor manejo de errores y validaciones

## Estructura del Servicio

### Funciones de Validación

#### `validatePartsCalculations(budgetParts, isEditMode)`
Valida que todas las partes tengan cálculos actualizados en modo edición.

**Retorna:**
```javascript
{
  isValid: boolean,        // Si la validación es exitosa
  error: string|null,      // Mensaje de error si aplica
  partsCount: number       // Número de partes que necesitan recálculo
}
```

### Funciones de Cálculo de Costos

#### `calculatePartCost(part)`
Calcula el costo individual de una parte considerando el tipo de máquina.

**Lógica de cálculo:**
- **Máquinas Digitales**: `papel + máquina + clicks`
- **Máquinas Offset**: `papel + máquina + planchas + tinta + maculatura`

#### `calculatePartsCostTotal(budgetParts)`
Suma los costos de todas las partes del presupuesto.

#### `calculateBudgetTotalCost(budgetParts, calculatedProcessCost, budget)`
Calcula el desglose completo de costos del presupuesto.

**Retorna:**
```javascript
{
  partsCostTotal: number,  // Costo total de partes
  processCost: number,     // Costo de procesos/acabados
  shippingCost: number,    // Costo de envío
  totalCost: number        // Costo total del presupuesto
}
```

### Funciones de Preparación

#### `prepareBudgetForSubmission(budget, costBreakdown)`
Prepara el presupuesto con todos los costos calculados para envío.

#### `handlePostSubmissionNavigation(delay)`
Maneja la navegación automática después del envío exitoso.

### Función Principal

#### `handleBudgetSubmission(params)`
Función principal que coordina todo el proceso de envío.

## Uso en BudgetForm

### Antes (Código Original)
```javascript
// 123 líneas de lógica compleja
const handleSubmit = async (e) => {
  e.preventDefault();

  // Validar que todas las partes hayan sido recalculadas si estamos en modo edición
  if (isEditMode) {
    const partsWithoutCalculation = budgetParts.filter(part => {
      if (part.paper && part.machine && part.pageCount) {
        return !part.sheetCalculation || part.sheetCalculation.outdated === true;
      }
      return false;
    });

    if (partsWithoutCalculation.length > 0) {
      showSnackbar(
        `No se puede actualizar el presupuesto. ${partsWithoutCalculation.length} ${partsWithoutCalculation.length === 1 ? 'parte necesita' : 'partes necesitan'} ser recalculada${partsWithoutCalculation.length === 1 ? '' : 's'}.`,
        'error'
      );
      return;
    }
  }

  // Calcular el costo total (suma de partes, acabados y envío)
  let partsCostTotal = 0;
  if (budgetParts && budgetParts.length > 0) {
    partsCostTotal = budgetParts.reduce((total, part) => {
      if (part.sheetCalculation) {
        let paperCost = part.sheetCalculation.paper_cost || 0;
        let machineCost = part.sheetCalculation.machine_cost || 0;

        const isDigital = part.machine && part.machine.type === 'Digital';
        
        // ... lógica compleja de cálculo por tipo de máquina
        // ... 60+ líneas más de cálculos
      }
      return total;
    }, 0);
  }

  // ... resto de la lógica de preparación y envío
};
```

### Después (Con Servicio)
```javascript
// 24 líneas delegando al servicio
const handleSubmit = async (e) => {
  try {
    await budgetSubmissionService.handleBudgetSubmission({
      e,
      budget,
      budgetParts,
      selectedProcesses,
      calculatedProcessCost,
      selectedPdf,
      pdfInfo,
      colorConfig,
      productConfig,
      budgetId,
      isEditMode,
      showSnackbar,
      setCalculatedProcessCost,
      buildApiUrl,
      budgetSubmitService
    });
  } catch (error) {
    console.error('Error al enviar el presupuesto:', error);
    showSnackbar(`Error al enviar el presupuesto: ${error.message}`, 'error');
  }
};
```

## Beneficios Obtenidos

### 1. **Reducción Dramática de Complejidad**
- BudgetForm: De ~123 líneas a ~24 líneas
- Reducción del 80% en código del componente
- Función más legible y mantenible

### 2. **Validación Robusta**
- Validación específica para modo edición
- Mensajes de error descriptivos
- Conteo preciso de partes sin calcular

### 3. **Cálculos Especializados**
- Lógica diferenciada para máquinas Digital vs Offset
- Cálculo automático de maculatura para offset
- Manejo correcto de costos de clicks para digital

### 4. **Mejor Manejo de Errores**
- Try-catch en operaciones críticas
- Mensajes específicos por tipo de error
- Logging detallado para debugging

## Lógica de Cálculo de Costos

### Máquinas Digitales
```javascript
// Componentes del costo
const digitalCost = paperCost + machineCost + clickCost;

// No incluye:
// - Costo de planchas (no aplica)
// - Costo de tinta (incluido en clicks)
// - Maculatura (no aplica)
```

### Máquinas Offset
```javascript
// Componentes del costo
const offsetCost = paperCost + machineCost + plateCost + inkCost + maculaturaCost;

// Cálculo de maculatura
const maculaturaCost = (totalMaculatura * precioPorMillar) / 1000;
```

## Flujo de Trabajo del Servicio

```
handleBudgetSubmission()
├── e.preventDefault()                    // Prevenir envío por defecto
├── validatePartsCalculations()           // Validar partes calculadas
├── calculateBudgetTotalCost()           // Calcular costos totales
│   ├── calculatePartsCostTotal()        // Sumar costos de partes
│   │   └── calculatePartCost()          // Costo individual por parte
│   ├── processCost                      // Costo de acabados
│   └── shippingCost                     // Costo de envío
├── prepareBudgetForSubmission()         // Preparar datos finales
├── budgetSubmitService.submitBudget()   // Enviar a la API
└── handlePostSubmissionNavigation()     // Navegar al listado
```

## Ejemplo de Uso Independiente

```javascript
import budgetSubmissionService from '../services/budgetSubmissionService';

// Validar partes antes del envío
const validation = budgetSubmissionService.validatePartsCalculations(budgetParts, true);
if (!validation.isValid) {
  console.log(validation.error);
}

// Calcular costo de una parte específica
const partCost = budgetSubmissionService.calculatePartCost(part);

// Obtener desglose completo de costos
const costBreakdown = budgetSubmissionService.calculateBudgetTotalCost(
  budgetParts, 
  processeCost, 
  budget
);

// Obtener estadísticas del presupuesto
const stats = budgetSubmissionService.getBudgetStats(budgetParts, isEditMode);
```

## Testing

El servicio incluye tests comprehensivos:

```javascript
describe('budgetSubmissionService', () => {
  test('should validate parts calculations correctly', () => { ... });
  test('should calculate digital machine costs', () => { ... });
  test('should calculate offset machine costs with maculatura', () => { ... });
  test('should handle submission workflow', () => { ... });
  test('should manage post-submission navigation', () => { ... });
  // ... 15+ tests más
});
```

## Consideraciones de Implementación

### Validación de Partes
- Solo valida en modo edición
- Ignora partes sin datos básicos
- Cuenta partes con cálculos desactualizados

### Cálculo de Costos
- Diferenciación automática Digital vs Offset
- Manejo de valores nulos/undefined
- Cálculo preciso de maculatura

### Manejo de Errores
- Try-catch en operaciones asíncronas
- Mensajes descriptivos para el usuario
- Logging para debugging

### Performance
- Cálculos optimizados con reduce
- Validación temprana para evitar procesamiento innecesario
- Preparación eficiente de datos

## Configuración de Validación

### Criterios de Validación
```javascript
// Una parte necesita recálculo si:
const needsRecalculation = 
  part.paper && part.machine && part.pageCount &&  // Tiene datos básicos
  (!part.sheetCalculation ||                       // No tiene cálculo
   part.sheetCalculation.outdated === true);       // O está desactualizado
```

### Mensajes de Error
- Singular: "1 parte necesita ser recalculada"
- Plural: "X partes necesitan ser recalculadas"

## Próximos Pasos

1. **Añadir validaciones adicionales**:
   - Validación de datos obligatorios
   - Validación de rangos de valores
   - Validación de compatibilidad papel-máquina

2. **Mejorar cálculos**:
   - Soporte para más tipos de máquinas
   - Cálculos de descuentos por volumen
   - Cálculos de márgenes de ganancia

3. **Optimizaciones**:
   - Cache de cálculos complejos
   - Validación asíncrona
   - Progreso de envío para presupuestos grandes

4. **Integración**:
   - Hooks personalizados para React
   - Integración con Redux/Zustand
   - Métricas de uso y performance

La refactorización ha sido exitosa, reduciendo significativamente la complejidad del BudgetForm mientras mejora la robustez, testabilidad y mantenibilidad del código de envío de presupuestos.
