import { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip
} from '@mui/material';
import { buildApiUrl } from '../config';
import { ApiInterceptor } from '../services/simplifiedServices';

// Colores para los diferentes tipos de máquinas
const TYPE_COLORS = {
  'Offset': 'primary',
  'Digital': 'secondary',
  'Plotter': 'info',
  'CTP': 'warning',
  'Encuadernadora': 'success',
  'Guillotina': 'error',
  'Plegadora': 'default'
};

// Categorías de máquinas
const MACHINE_CATEGORIES = {
  'IMPRESION': ['Offset', 'Digital', 'Plotter', 'CTP'],
  'ACABADOS': ['Encuadernadora', 'Guillotina', 'Plegadora']
};

const MachineSelector = ({ selectedMachine, onMachineChange }) => {
  const [machines, setMachines] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Cargar máquinas activas
  useEffect(() => {
    const fetchMachines = async () => {
      try {
        setLoading(true);
        setError(null);

        const url = buildApiUrl('/machines/');
        const response = await ApiInterceptor.fetch(url);

        if (!response.ok) {
          throw new Error(`Error al obtener máquinas: ${response.statusText}`);
        }

        const data = await response.json();

        // Filtrar solo máquinas activas y que deben mostrarse en el planificador
        const activeMachines = data.filter(machine =>
          machine.status === 'Activa' &&
          (machine.planificar === undefined || machine.planificar === true)
        );

        // Ordenar máquinas: primero impresión, luego acabados
        activeMachines.sort((a, b) => {
          // Determinar la categoría de cada máquina
          const categoryA = MACHINE_CATEGORIES.IMPRESION.includes(a.type) ? 'IMPRESION' : 'ACABADOS';
          const categoryB = MACHINE_CATEGORIES.IMPRESION.includes(b.type) ? 'IMPRESION' : 'ACABADOS';

          // Si son de diferentes categorías, ordenar por categoría
          if (categoryA !== categoryB) {
            return categoryA === 'IMPRESION' ? -1 : 1;
          }

          // Si son de la misma categoría, ordenar por tipo y luego por nombre
          if (a.type !== b.type) {
            return a.type.localeCompare(b.type);
          }

          return a.name.localeCompare(b.name);
        });

        console.log(`Encontradas ${activeMachines.length} máquinas activas:`, activeMachines);
        setMachines(activeMachines);

        // Si no hay máquina seleccionada y hay máquinas disponibles, seleccionar la primera
        if (!selectedMachine && activeMachines.length > 0 && onMachineChange) {
          onMachineChange(activeMachines[0]);
        }
      } catch (err) {
        console.error('Error al cargar máquinas:', err);
        setError(`Error al cargar las máquinas: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchMachines();
  }, [selectedMachine, onMachineChange]);

  const handleChange = (event) => {
    const machineId = event.target.value;
    const selectedMachine = machines.find(m => m.machine_id === machineId);

    if (selectedMachine && onMachineChange) {
      onMachineChange(selectedMachine);
    }
  };

  if (loading) {
    return <Typography variant="body2">Cargando máquinas...</Typography>;
  }

  if (error) {
    return <Typography variant="body2" color="error">{error}</Typography>;
  }

  if (machines.length === 0) {
    return <Typography variant="body2">No hay máquinas activas disponibles</Typography>;
  }

  return (
    <Box sx={{ minWidth: 200 }}>
      <FormControl fullWidth size="small">
        <InputLabel id="machine-selector-label">Máquina</InputLabel>
        <Select
          labelId="machine-selector-label"
          id="machine-selector"
          value={selectedMachine?.machine_id || ''}
          label="Máquina"
          onChange={handleChange}
          renderValue={(selected) => {
            const machine = machines.find(m => m.machine_id === selected);
            if (!machine) return selected;

            return (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  label={machine.type}
                  size="small"
                  color={TYPE_COLORS[machine.type] || 'default'}
                  sx={{ height: 20, fontSize: '0.7rem' }}
                />
                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                  {machine.name}
                </Typography>
              </Box>
            );
          }}
        >
          {/* Encabezado para máquinas de impresión */}
          <MenuItem
            disabled
            sx={{
              backgroundColor: 'primary.main',
              color: 'white',
              fontWeight: 'bold',
              pointerEvents: 'none'
            }}
          >
            IMPRESIÓN
          </MenuItem>

          {/* Máquinas de impresión */}
          {machines
            .filter(machine => MACHINE_CATEGORIES.IMPRESION.includes(machine.type))
            .map((machine) => (
              <MenuItem key={machine.machine_id} value={machine.machine_id}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip
                    label={machine.type}
                    size="small"
                    color={TYPE_COLORS[machine.type] || 'default'}
                    sx={{ height: 20, fontSize: '0.7rem' }}
                  />
                  <Typography variant="body2">
                    {machine.name}
                  </Typography>
                </Box>
              </MenuItem>
            ))
          }

          {/* Encabezado para máquinas de acabados */}
          <MenuItem
            disabled
            sx={{
              backgroundColor: 'success.main',
              color: 'white',
              fontWeight: 'bold',
              pointerEvents: 'none',
              mt: 1
            }}
          >
            ACABADOS
          </MenuItem>

          {/* Máquinas de acabados */}
          {machines
            .filter(machine => MACHINE_CATEGORIES.ACABADOS.includes(machine.type))
            .map((machine) => (
              <MenuItem key={machine.machine_id} value={machine.machine_id}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip
                    label={machine.type}
                    size="small"
                    color={TYPE_COLORS[machine.type] || 'default'}
                    sx={{ height: 20, fontSize: '0.7rem' }}
                  />
                  <Typography variant="body2">
                    {machine.name}
                  </Typography>
                </Box>
              </MenuItem>
            ))
          }
        </Select>
      </FormControl>
    </Box>
  );
};

export default MachineSelector;
