from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, List, Any
from datetime import datetime, timedelta
import json
import os
from collections import defaultdict

from dependencies.auth import get_current_user
from utils.data_manager import read_data
from utils.logger import log_info

router = APIRouter(
    prefix="/dashboard",
    tags=["dashboard"],
    responses={401: {"description": "No autorizado"}}
)

@router.get("/cost-profit")
async def get_cost_profit_stats(current_user: dict = Depends(get_current_user)):
    """
    Obtiene estadísticas de costes y beneficios para el dashboard
    """
    try:
        # Registrar la acción
        log_info(f"Usuario {current_user['username']} solicitó estadísticas de costes y beneficios")

        # Obtener datos
        budgets = read_data("budgets")
        
        # Cargar configuración para obtener el porcentaje de beneficio
        config_path = os.path.join("config", "app_config.json")
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        
        profit_percentage = config.get("profit_percentage", 30)
        
        # Inicializar contadores
        total_stats = {
            "paper_cost": 0,
            "plate_cost": 0,
            "machine_cost": 0,
            "process_cost": 0,
            "shipping_cost": 0,
            "total_cost": 0,
            "list_price": 0,
            "final_price": 0,
            "profit": 0,
            "completed_count": 0,
            "invoiced_count": 0
        }
        
        # Estadísticas por mes (últimos 6 meses)
        monthly_stats = {}
        today = datetime.now()
        
        for i in range(6):
            month_date = (today - timedelta(days=30*i))
            month_key = f"{month_date.year}-{month_date.month:02d}"
            month_name = f"{get_month_name(month_date.month)} {month_date.year}"
            
            monthly_stats[month_key] = {
                "name": month_name,
                "paper_cost": 0,
                "plate_cost": 0,
                "machine_cost": 0,
                "process_cost": 0,
                "shipping_cost": 0,
                "total_cost": 0,
                "list_price": 0,
                "final_price": 0,
                "profit": 0,
                "count": 0
            }
        
        # Estadísticas por tipo de trabajo
        job_type_stats = defaultdict(lambda: {
            "paper_cost": 0,
            "plate_cost": 0,
            "machine_cost": 0,
            "process_cost": 0,
            "shipping_cost": 0,
            "total_cost": 0,
            "list_price": 0,
            "final_price": 0,
            "profit": 0,
            "count": 0
        })
        
        # Procesar cada presupuesto
        for budget in budgets:
            # Solo considerar presupuestos completados o facturados
            status = budget.get("status", "")
            if status not in ["Completado", "Facturado", "Enviado"]:
                continue
                
            # Incrementar contadores según el estado
            if status == "Completado" or status == "Enviado":
                total_stats["completed_count"] += 1
            elif status == "Facturado":
                total_stats["invoiced_count"] += 1
            
            # Extraer fecha de creación
            created_at = budget.get("created_at", "")
            if not created_at:
                continue
                
            try:
                created_date = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                month_key = f"{created_date.year}-{created_date.month:02d}"
            except (ValueError, TypeError):
                continue
            
            # Calcular costes
            paper_cost = 0
            plate_cost = 0
            machine_cost = 0
            
            # Sumar costes de partes
            for part in budget.get("parts", []):
                paper_cost += part.get("paper_cost", 0)
                plate_cost += part.get("plate_cost", 0)
                machine_cost += part.get("machine_cost", 0)
            
            # Sumar costes de procesos
            process_cost = budget.get("process_total_cost", 0)
            
            # Sumar coste de envío
            shipping_cost = budget.get("shipping_cost", 0)
            
            # Calcular coste total
            total_cost = paper_cost + plate_cost + machine_cost + process_cost + shipping_cost
            
            # Calcular precio de lista (con margen de beneficio)
            list_price = total_cost * (1 + profit_percentage / 100)
            
            # Aplicar descuento del cliente si existe
            client_data = budget.get("client_data", {})
            discount_percentage = client_data.get("discount_percentage", 0)
            final_price = list_price * (1 - discount_percentage / 100)
            
            # Calcular beneficio
            profit = final_price - total_cost
            
            # Actualizar estadísticas totales
            total_stats["paper_cost"] += paper_cost
            total_stats["plate_cost"] += plate_cost
            total_stats["machine_cost"] += machine_cost
            total_stats["process_cost"] += process_cost
            total_stats["shipping_cost"] += shipping_cost
            total_stats["total_cost"] += total_cost
            total_stats["list_price"] += list_price
            total_stats["final_price"] += final_price
            total_stats["profit"] += profit
            
            # Actualizar estadísticas mensuales si está en los últimos 6 meses
            if month_key in monthly_stats:
                monthly_stats[month_key]["paper_cost"] += paper_cost
                monthly_stats[month_key]["plate_cost"] += plate_cost
                monthly_stats[month_key]["machine_cost"] += machine_cost
                monthly_stats[month_key]["process_cost"] += process_cost
                monthly_stats[month_key]["shipping_cost"] += shipping_cost
                monthly_stats[month_key]["total_cost"] += total_cost
                monthly_stats[month_key]["list_price"] += list_price
                monthly_stats[month_key]["final_price"] += final_price
                monthly_stats[month_key]["profit"] += profit
                monthly_stats[month_key]["count"] += 1
            
            # Actualizar estadísticas por tipo de trabajo
            job_type = budget.get("job_type", "Otro")
            job_type_stats[job_type]["paper_cost"] += paper_cost
            job_type_stats[job_type]["plate_cost"] += plate_cost
            job_type_stats[job_type]["machine_cost"] += machine_cost
            job_type_stats[job_type]["process_cost"] += process_cost
            job_type_stats[job_type]["shipping_cost"] += shipping_cost
            job_type_stats[job_type]["total_cost"] += total_cost
            job_type_stats[job_type]["list_price"] += list_price
            job_type_stats[job_type]["final_price"] += final_price
            job_type_stats[job_type]["profit"] += profit
            job_type_stats[job_type]["count"] += 1
        
        # Convertir estadísticas mensuales a lista ordenada por fecha
        monthly_list = []
        for month_key in sorted(monthly_stats.keys(), reverse=True):
            monthly_list.append(monthly_stats[month_key])
        
        # Convertir estadísticas por tipo de trabajo a lista
        job_type_list = []
        for job_type, stats in job_type_stats.items():
            job_type_list.append({
                "name": job_type,
                **stats
            })
        
        # Ordenar por beneficio total
        job_type_list.sort(key=lambda x: x["profit"], reverse=True)
        
        # Calcular porcentajes de beneficio
        if total_stats["total_cost"] > 0:
            total_stats["profit_percentage"] = (total_stats["profit"] / total_stats["total_cost"]) * 100
        else:
            total_stats["profit_percentage"] = 0
            
        # Redondear valores numéricos a 2 decimales
        for key in ["paper_cost", "plate_cost", "machine_cost", "process_cost", "shipping_cost", 
                   "total_cost", "list_price", "final_price", "profit", "profit_percentage"]:
            total_stats[key] = round(total_stats[key], 2)
        
        for month_data in monthly_list:
            for key in ["paper_cost", "plate_cost", "machine_cost", "process_cost", "shipping_cost", 
                       "total_cost", "list_price", "final_price", "profit"]:
                month_data[key] = round(month_data[key], 2)
        
        for job_type_data in job_type_list:
            for key in ["paper_cost", "plate_cost", "machine_cost", "process_cost", "shipping_cost", 
                       "total_cost", "list_price", "final_price", "profit"]:
                job_type_data[key] = round(job_type_data[key], 2)
            
            # Calcular porcentaje de beneficio para cada tipo de trabajo
            if job_type_data["total_cost"] > 0:
                job_type_data["profit_percentage"] = round((job_type_data["profit"] / job_type_data["total_cost"]) * 100, 2)
            else:
                job_type_data["profit_percentage"] = 0
        
        return {
            "total_stats": total_stats,
            "monthly_stats": monthly_list,
            "job_type_stats": job_type_list
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener estadísticas de costes y beneficios: {str(e)}")

def get_month_name(month: int) -> str:
    """
    Obtiene el nombre del mes en español
    """
    months = [
        "Ene", "Feb", "Mar", "Abr", "May", "Jun",
        "Jul", "Ago", "Sep", "Oct", "Nov", "Dic"
    ]
    return months[month - 1]
