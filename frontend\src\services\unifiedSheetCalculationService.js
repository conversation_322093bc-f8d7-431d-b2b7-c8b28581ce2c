/**
 * Servicio unificado para el cálculo de pliegos
 * Este servicio actúa como fachada para los servicios específicos de offset y digital
 */
import { calculateOffsetSheets, calculateTotalPhysicalSheets } from './offsetCalculationService';
import { calculateDigitalSheets } from './digitalCalculationService';

/**
 * Calcula los pliegos para una parte específica del presupuesto
 * Determina automáticamente qué servicio específico utilizar según el tipo de máquina
 * @param {Object} params - Parámetros para el cálculo (ver servicios específicos)
 * @returns {Promise<Object>} - Resultado del cálculo
 */
export const calculateSheetsPart = async (params) => {
  try {
    // Validar que tenemos los parámetros necesarios
    const { part, showSnackbar, setCalculatingSheets } = params;

    if (!part || !part.machine) {
      showSnackbar('No se puede calcular: faltan datos de la máquina', 'error');
      return null;
    }

    // Validar que tenemos un papel seleccionado
    if (!part.paper) {
      showSnackbar('No se puede calcular: selecciona un papel', 'error');
      return null;
    }

    // Validar que tenemos una cantidad de copias
    if (!params.copies || parseInt(params.copies) <= 0) {
      showSnackbar('No se puede calcular: ingresa una cantidad válida de copias', 'error');
      return null;
    }

    // Determinar el tipo de máquina
    const machineType = part.machine.type;

    // Modificar los parámetros para usar el modal V2
    const modifiedParams = {
      ...params,
      // Si existe setSheetCalculationV2Modal, usarlo como setSheetCalculationModal
      setSheetCalculationModal: params.setSheetCalculationV2Modal || params.setSheetCalculationModal,
      // Proporcionar setSheetCalculation si no existe
      setSheetCalculation: params.setSheetCalculation || params.setCalculationResults || function(data) {
        if (params.setCalculationResults) {
          params.setCalculationResults(data);
        }
      }
    };

    // Llamar al servicio correspondiente según el tipo de máquina
    if (machineType === 'Offset') {
      return await calculateOffsetSheets(modifiedParams);
    } else if (machineType === 'Digital') {
      return await calculateDigitalSheets(modifiedParams);
    } else {
      // Si el tipo de máquina no es reconocido, mostrar un error
      showSnackbar(`Tipo de máquina no soportado: ${machineType}`, 'error');
      console.error(`Tipo de máquina no soportado: ${machineType}`);
      setCalculatingSheets(false);
      return null;
    }
  } catch (error) {
    console.error('Error al calcular pliegos V2:', error);
    if (params.showSnackbar) {
      params.showSnackbar(`Error al calcular pliegos: ${error.message}`, 'error');
    }
    if (params.setCalculatingSheets) {
      params.setCalculatingSheets(false);
    }
    return null;
  }
};

// Exportar también la función de cálculo de pliegos físicos desde offsetCalculationService
export { calculateTotalPhysicalSheets };

export default {
  calculateSheetsPart,
  calculateTotalPhysicalSheets
};
