/**
 * Servicio especializado para manejar la actualización del tiempo de impresión
 * Extrae la lógica compleja de handleUpdatePrintTime del BudgetForm
 */

/**
 * Prepara los datos de solicitud para máquinas digitales
 * @param {Object} part - Parte del presupuesto
 * @param {Object} budget - Presupuesto actual
 * @param {number} time - Tiempo personalizado de impresión
 * @returns {Object} - Datos preparados para la API
 */
export const prepareDigitalRequestData = (part, budget, time) => {
  return {
    machine_id: part.machine.machine_id || part.machine.product_id,
    copies: parseInt(budget.copies) || 500,
    paper_id: part.paper?.paper_id || part.paper?.product_id,
    is_duplex: part.isDuplex !== undefined ? part.isDuplex : true,
    is_color: part.isColor !== undefined ? part.isColor : true,
    num_paginas: parseInt(part.pageCount) || 1,
    custom_print_time: time
  };
};

/**
 * Prepara los datos de solicitud para máquinas offset
 * @param {Object} part - Parte del presupuesto
 * @param {Object} budget - Presupuesto actual
 * @param {number} time - Tiempo personalizado de impresión
 * @returns {Object} - Datos preparados para la API
 */
export const prepareOffsetRequestData = (part, budget, time) => {
  return {
    machine_id: part.machine.machine_id || part.machine.product_id,
    copies: parseInt(budget.copies) || 500,
    paper_id: part.paper?.paper_id || part.paper?.product_id,
    num_paginas: parseInt(part.pageCount) || 1,
    ancho_pagina: part.customPageSize?.width || 210,
    alto_pagina: part.customPageSize?.height || 297,
    colors_front: part.colorConfig?.frontColors || 4,
    colors_back: part.colorConfig?.backColors || 0,
    custom_print_time: time
  };
};

/**
 * Valida que una parte tenga los datos necesarios para recalcular
 * @param {Object} part - Parte del presupuesto
 * @returns {Object} - { isValid: boolean, error: string }
 */
export const validatePartForRecalculation = (part) => {
  if (!part.machine) {
    return { isValid: false, error: 'Faltan datos necesarios para recalcular (máquina)' };
  }

  if (!part.paper) {
    return { isValid: false, error: 'Faltan datos necesarios para recalcular (papel)' };
  }

  if (!part.pageCount) {
    return { isValid: false, error: 'Faltan datos necesarios para recalcular (páginas)' };
  }

  return { isValid: true, error: null };
};

/**
 * Actualiza una parte con los datos calculados por el backend
 * @param {Object} part - Parte original
 * @param {Object} calculationData - Datos calculados por el backend
 * @param {number} time - Tiempo personalizado
 * @returns {Object} - Parte actualizada
 */
export const updatePartWithCalculation = (part, calculationData, time) => {
  return {
    ...part,
    customPrintTime: time,
    // Usar datos calculados por el backend
    sheetCalculation: calculationData,
    paperCost: calculationData.paper_cost || 0,
    machineCost: calculationData.machine_cost || 0,
    plateCost: calculationData.plates_cost || 0,
    clickCost: calculationData.click_cost || 0,
    inkCost: calculationData.ink_cost || 0,
    totalCost: calculationData.total_cost || 0
  };
};

/**
 * Realiza la llamada a la API para recalcular con el nuevo tiempo
 * @param {string} endpoint - Endpoint de la API
 * @param {Object} requestData - Datos de la solicitud
 * @param {Function} buildApiUrl - Función para construir URLs
 * @returns {Promise<Object>} - Datos calculados por el backend
 */
export const callRecalculationAPI = async (endpoint, requestData, buildApiUrl) => {
  const response = await fetch(buildApiUrl(endpoint), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`
    },
    body: JSON.stringify(requestData)
  });

  if (!response.ok) {
    throw new Error(`Error del servidor: ${response.statusText}`);
  }

  return await response.json();
};

/**
 * Función principal para actualizar el tiempo de impresión de una parte específica
 * @param {Object} params - Parámetros para la actualización
 * @returns {Promise<Object>} - Parte actualizada
 */
export const updatePartPrintTime = async (params) => {
  const {
    part,
    partIndex,
    time,
    budget,
    budgetParts,
    buildApiUrl,
    setBudgetParts,
    setCurrentCalculatedPart,
    showSnackbar
  } = params;

  // Validar que tenemos los datos necesarios
  const validation = validatePartForRecalculation(part);
  if (!validation.isValid) {
    showSnackbar(validation.error, 'warning');
    throw new Error(validation.error);
  }

  try {
    // Determinar el endpoint correcto según el tipo de máquina
    const isDigital = part.machine.type === 'Digital';
    const endpoint = isDigital ? '/v2/calculate-digital' : '/v2/calculate-offset';

    // Preparar datos para el endpoint V2
    const requestData = isDigital
      ? prepareDigitalRequestData(part, budget, time)
      : prepareOffsetRequestData(part, budget, time);

    // Llamar al endpoint V2 para recalcular completamente
    const calculationData = await callRecalculationAPI(endpoint, requestData, buildApiUrl);

    // Actualizar la parte con los datos calculados por el backend
    const updatedParts = [...budgetParts];
    const updatedPart = updatePartWithCalculation(part, calculationData, time);
    updatedParts[partIndex] = updatedPart;

    // Actualizar estados
    setBudgetParts(updatedParts);
    setCurrentCalculatedPart(updatedPart);

    // Mostrar mensaje de éxito
    showSnackbar(`Tiempo actualizado a ${time} horas. Costos recalculados.`, 'success');

    return updatedPart;
  } catch (error) {
    const errorMessage = `Error al recalcular con nuevo tiempo: ${error.message}`;
    showSnackbar(errorMessage, 'error');
    throw new Error(errorMessage);
  }
};

/**
 * Función para actualizar el tiempo personalizado general (sin parte específica)
 * @param {Object} params - Parámetros para la actualización
 * @returns {Promise<void>}
 */
export const updateGeneralPrintTime = async (params) => {
  const {
    time,
    setCustomMachinePrintTime,
    showSnackbar
  } = params;

  try {
    // Actualizar el tiempo personalizado general
    setCustomMachinePrintTime(time);

    // Mostrar mensaje de éxito
    showSnackbar(`Tiempo de impresión actualizado a ${time} horas`, 'success');
  } catch (error) {
    const errorMessage = `Error al actualizar tiempo general: ${error.message}`;
    showSnackbar(errorMessage, 'error');
    throw new Error(errorMessage);
  }
};

/**
 * Obtiene el tiempo por defecto de una parte calculada
 * @param {Object} currentCalculatedPart - Parte calculada actualmente
 * @returns {number} - Tiempo por defecto en horas
 */
export const getDefaultTimeFromPart = (currentCalculatedPart) => {
  if (!currentCalculatedPart) return 1;

  return currentCalculatedPart.sheetCalculation?.estimated_time_hours ||
         currentCalculatedPart.clicksData?.estimated_time_hours ||
         1; // Valor por defecto mínimo
};

/**
 * Maneja el reseteo del tiempo personalizado de la máquina
 * @param {Object} params - Parámetros para el reseteo
 * @returns {Promise<void>}
 */
export const handleCustomMachineTimeReset = async (params) => {
  const {
    currentCalculatedPart,
    selectedPartIndex,
    setCustomMachinePrintTime,
    handleUpdatePrintTime,
    showSnackbar
  } = params;

  try {
    // Resetear el tiempo personalizado
    setCustomMachinePrintTime(null);

    // Si tenemos una parte calculada actualmente, usar el tiempo por defecto del backend
    if (currentCalculatedPart && selectedPartIndex !== null) {
      // Obtener el tiempo por defecto calculado por el backend
      const defaultTime = getDefaultTimeFromPart(currentCalculatedPart);

      // Usar la función de actualización de tiempo que llama a los endpoints V2
      await handleUpdatePrintTime(defaultTime);
    }
  } catch (error) {
    const errorMessage = `Error al resetear tiempo personalizado: ${error.message}`;
    showSnackbar(errorMessage, 'error');
    throw new Error(errorMessage);
  }
};

/**
 * Función principal que maneja la actualización del tiempo de impresión
 * Decide si actualizar una parte específica o el tiempo general
 * @param {Object} params - Parámetros completos para la actualización
 * @returns {Promise<Object|void>} - Parte actualizada o void para tiempo general
 */
export const handlePrintTimeUpdate = async (params) => {
  const {
    time,
    selectedPartIndex,
    budgetParts,
    budget,
    buildApiUrl,
    setBudgetParts,
    setCurrentCalculatedPart,
    setCustomMachinePrintTime,
    setMachineInfoDialog,
    showSnackbar
  } = params;

  try {
    let result;

    if (selectedPartIndex !== null && budgetParts[selectedPartIndex]) {
      // Actualizar tiempo de una parte específica
      const part = budgetParts[selectedPartIndex];

      result = await updatePartPrintTime({
        part,
        partIndex: selectedPartIndex,
        time,
        budget,
        budgetParts,
        buildApiUrl,
        setBudgetParts,
        setCurrentCalculatedPart,
        showSnackbar
      });
    } else {
      // Actualizar tiempo personalizado general
      await updateGeneralPrintTime({
        time,
        setCustomMachinePrintTime,
        showSnackbar
      });
    }

    // Cerrar el modal después de actualizar el tiempo
    setTimeout(() => {
      setMachineInfoDialog(false);
    }, 500);

    return result;
  } catch (error) {
    console.error('Error en handlePrintTimeUpdate:', error);
    // El error ya fue mostrado en las funciones internas
    throw error;
  }
};

export default {
  handlePrintTimeUpdate,
  handleCustomMachineTimeReset,
  updatePartPrintTime,
  updateGeneralPrintTime,
  prepareDigitalRequestData,
  prepareOffsetRequestData,
  validatePartForRecalculation,
  updatePartWithCalculation,
  callRecalculationAPI,
  getDefaultTimeFromPart
};
