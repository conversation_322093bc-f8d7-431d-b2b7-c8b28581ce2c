from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import uuid

class UserInfo(BaseModel):
    user_id: Optional[str] = None
    username: Optional[str] = None
    email: Optional[str] = None
    timestamp: Optional[str] = None

class BudgetPart(BaseModel):
    part_id: str = Field(default_factory=lambda: f"PART-{uuid.uuid4().hex[:6].upper()}")
    name: str
    description: Optional[str] = ""
    assembly_order: Optional[str] = None
    page_size: Dict[str, Any]
    page_count: int
    paper_id: Optional[str] = None
    machine_id: Optional[str] = None
    machine_type: Optional[str] = None
    sheet_calculation: Optional[Dict[str, Any]] = None
    # Costos básicos
    paper_cost: Optional[float] = 0.0
    machine_cost: Optional[float] = 0.0
    # Costos específicos para offset
    plate_cost: Optional[float] = 0.0
    ink_cost: Optional[float] = 0.0
    maculatura_cost: Optional[float] = 0.0
    # Costos específicos para digital
    click_cost: Optional[float] = 0.0
    # Datos adicionales para digital
    clicks_data: Optional[Dict[str, Any]] = None
    # Otros costos
    process_costs: Optional[List[Dict[str, Any]]] = None
    total_cost: Optional[float] = 0.0
    custom_print_time: Optional[float] = None
    # Datos de papel y máquina para referencia rápida
    paper_data: Optional[Dict[str, Any]] = None
    machine_data: Optional[Dict[str, Any]] = None

class Budget(BaseModel):
    budget_id: str = Field(default_factory=lambda: f"PRES-{uuid.uuid4().hex[:6].upper()}")
    ot_number: Optional[str] = None
    description: str
    client_id: str
    job_type: str
    quantity: int
    page_size: Dict[str, Any]
    pdf_filename: Optional[str] = None
    status: str = "Pendiente"  # Pendiente, Actualizado, Aprobado, Enviado, Rechazado, Completado
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    parts: List[BudgetPart]
    process_costs: Optional[List[Dict[str, Any]]] = None
    process_total_cost: Optional[float] = 0.0
    total_paper_weight_kg: Optional[float] = 0.0
    shipping_cost: Optional[float] = 0.0
    facturado: bool = False
    fecha_factura: Optional[str] = None
    numero_factura: Optional[str] = None
    created_by: Optional[UserInfo] = None
    updated_by: Optional[UserInfo] = None
    total_cost: Optional[float] = None
    costs: Optional[Dict[str, Any]] = None

    class Config:
        schema_extra = {
            "example": {
                "budget_id": "PRES-123ABC",
                "ot_number": "OT-25057740",
                "description": "Revista de 40 páginas tamaño A4",
                "client_id": "Customer-2",
                "job_type": "Revista",
                "quantity": 500,
                "page_size": {"width": 210, "height": 297},
                "pdf_filename": None,
                "status": "Aprobado",
                "created_at": "2025-05-10T15:55:52.734946",
                "updated_at": "2025-05-10T16:48:47.212152",
                "parts": [
                    {
                        "part_id": "PART-001",
                        "name": "Revista",
                        "description": "Revista con grapado a caballete",
                        "assembly_order": "Collecting",
                        "page_size": {"width": 210, "height": 297},
                        "page_count": 40,
                        "paper_id": "Pap-003",
                        "machine_id": "MAQ-003",
                        "machine_type": "Offset",
                        "sheet_calculation": {},
                        "paper_cost": 120.75,
                        "machine_cost": 40.0,
                        "plate_cost": 115.0,
                        "click_cost": 0.0,
                        "process_costs": [],
                        "total_cost": 275.75,
                        "custom_print_time": None
                    }
                ],
                "process_costs": [
                    {
                        "process_id": "PROC-001",
                        "name": "Corte (Guillotina)",
                        "quantity": 1,
                        "unit_cost": 20.0,
                        "cost": 20.0
                    }
                ],
                "process_total_cost": 20.0,
                "total_paper_weight_kg": 15.0,
                "shipping_cost": 10.0,
                "facturado": False,
                "fecha_factura": None,
                "numero_factura": None,
                "created_by": {
                    "user_id": "839e4cca-6efe-42a6-867d-a4cb112f4b86",
                    "username": "Triky",
                    "email": "<EMAIL>",
                    "timestamp": "2025-05-18T14:26:25.432592"
                },
                "updated_by": {
                    "user_id": "839e4cca-6efe-42a6-867d-a4cb112f4b86",
                    "username": "Triky",
                    "email": "<EMAIL>",
                    "timestamp": "2025-05-18T14:34:39.540965"
                },
                "total_cost": 305.75,
                "costs": {
                    "parts": [
                        {
                            "part_id": "PART-001",
                            "name": "Revista",
                            "costs": {
                                "paper": 120.75,
                                "machine": 40.0,
                                "plates": 115.0,
                                "subtotal": 275.75
                            }
                        }
                    ],
                    "processes": [
                        {
                            "process_id": "PROC-001",
                            "name": "Corte (Guillotina)",
                            "cost": 20.0
                        }
                    ],
                    "shipping": 10.0,
                    "summary": {
                        "parts": 275.75,
                        "processes": 20.0,
                        "shipping": 10.0,
                        "total": 305.75
                    }
                }
            }
        }
