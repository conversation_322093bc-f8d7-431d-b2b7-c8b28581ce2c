import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import DashboardService from '../../services/DashboardService';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';

// Colores para los gráficos
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

// Componente para mostrar las estadísticas de costes y beneficios
const CostProfitDashboard = () => {
  const { token } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    total_stats: {
      paper_cost: 0,
      plate_cost: 0,
      machine_cost: 0,
      process_cost: 0,
      shipping_cost: 0,
      total_cost: 0,
      list_price: 0,
      final_price: 0,
      profit: 0,
      profit_percentage: 0,
      completed_count: 0,
      invoiced_count: 0
    },
    monthly_stats: [],
    job_type_stats: []
  });

  // Función para cargar las estadísticas de costes y beneficios
  const fetchCostProfitStats = useCallback(async () => {
    try {
      const data = await DashboardService.getCostProfitStats(token);
      setStats(data);
    } catch (err) {
      console.error('Error al cargar estadísticas de costes y beneficios:', err);
      setError(err.message);
    }
  }, [token]);

  // Cargar datos al montar el componente
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);

      try {
        await fetchCostProfitStats();
      } catch (err) {
        setError('Error al cargar datos de costes y beneficios');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [fetchCostProfitStats]);

  // Función para formatear valores monetarios
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'EUR'
    }).format(value);
  };

  // Función para formatear porcentajes
  const formatPercentage = (value) => {
    return `${value.toFixed(2)}%`;
  };

  // Renderizar indicador de carga
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Renderizar mensaje de error
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  // Preparar datos para el gráfico de costes por categoría
  const costCategoryData = [
    { name: 'Papel', value: stats.total_stats.paper_cost },
    { name: 'Planchas', value: stats.total_stats.plate_cost },
    { name: 'Máquinas', value: stats.total_stats.machine_cost },
    { name: 'Procesos', value: stats.total_stats.process_cost },
    { name: 'Envíos', value: stats.total_stats.shipping_cost }
  ];

  // Preparar datos para el gráfico de beneficios por tipo de trabajo
  const jobTypeProfitData = stats.job_type_stats.map(job => ({
    name: job.name,
    profit: job.profit,
    percentage: job.profit_percentage
  }));

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Resumen de Costes y Beneficios
      </Typography>

      <Grid container spacing={3}>
        {/* Tarjetas de resumen */}
        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Coste Total
              </Typography>
              <Typography variant="h4">
                {formatCurrency(stats.total_stats.total_cost)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Precio de Venta
              </Typography>
              <Typography variant="h4">
                {formatCurrency(stats.total_stats.final_price)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Beneficio
              </Typography>
              <Typography variant="h4">
                {formatCurrency(stats.total_stats.profit)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6} lg={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Margen de Beneficio
              </Typography>
              <Typography variant="h4">
                {formatPercentage(stats.total_stats.profit_percentage)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Tabla de costes por categoría */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardHeader title="Costes por Categoría" />
            <Divider />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Categoría</TableCell>
                      <TableCell align="right">Coste</TableCell>
                      <TableCell align="right">% del Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    <TableRow>
                      <TableCell>Papel</TableCell>
                      <TableCell align="right">{formatCurrency(stats.total_stats.paper_cost)}</TableCell>
                      <TableCell align="right">
                        {formatPercentage((stats.total_stats.paper_cost / stats.total_stats.total_cost) * 100)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Planchas</TableCell>
                      <TableCell align="right">{formatCurrency(stats.total_stats.plate_cost)}</TableCell>
                      <TableCell align="right">
                        {formatPercentage((stats.total_stats.plate_cost / stats.total_stats.total_cost) * 100)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Máquinas</TableCell>
                      <TableCell align="right">{formatCurrency(stats.total_stats.machine_cost)}</TableCell>
                      <TableCell align="right">
                        {formatPercentage((stats.total_stats.machine_cost / stats.total_stats.total_cost) * 100)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Procesos</TableCell>
                      <TableCell align="right">{formatCurrency(stats.total_stats.process_cost)}</TableCell>
                      <TableCell align="right">
                        {formatPercentage((stats.total_stats.process_cost / stats.total_stats.total_cost) * 100)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Envíos</TableCell>
                      <TableCell align="right">{formatCurrency(stats.total_stats.shipping_cost)}</TableCell>
                      <TableCell align="right">
                        {formatPercentage((stats.total_stats.shipping_cost / stats.total_stats.total_cost) * 100)}
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'bold' }}>Total</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                        {formatCurrency(stats.total_stats.total_cost)}
                      </TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                        100%
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Gráfico de costes por categoría */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardHeader title="Distribución de Costes" />
            <Divider />
            <CardContent sx={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={costCategoryData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {costCategoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [formatCurrency(value), 'Coste']} />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Tabla de beneficios por tipo de trabajo */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Beneficios por Tipo de Trabajo" />
            <Divider />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Tipo de Trabajo</TableCell>
                      <TableCell align="right">Cantidad</TableCell>
                      <TableCell align="right">Coste Total</TableCell>
                      <TableCell align="right">Precio de Venta</TableCell>
                      <TableCell align="right">Beneficio</TableCell>
                      <TableCell align="right">Margen</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stats.job_type_stats.map((job, index) => (
                      <TableRow key={index}>
                        <TableCell>{job.name}</TableCell>
                        <TableCell align="right">{job.count}</TableCell>
                        <TableCell align="right">{formatCurrency(job.total_cost)}</TableCell>
                        <TableCell align="right">{formatCurrency(job.final_price)}</TableCell>
                        <TableCell align="right">{formatCurrency(job.profit)}</TableCell>
                        <TableCell align="right">{formatPercentage(job.profit_percentage)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Gráfico de beneficios por mes */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Evolución de Costes y Beneficios por Mes" />
            <Divider />
            <CardContent sx={{ height: 400 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={stats.monthly_stats}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [formatCurrency(value), '']} />
                  <Legend />
                  <Bar dataKey="total_cost" name="Coste Total" fill="#8884d8" />
                  <Bar dataKey="final_price" name="Precio de Venta" fill="#82ca9d" />
                  <Bar dataKey="profit" name="Beneficio" fill="#ffc658" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CostProfitDashboard;
