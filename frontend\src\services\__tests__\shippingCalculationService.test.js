/**
 * Tests para shippingCalculationService
 */
import shippingCalculationService, {
  prepareShippingRequestData,
  callShippingCalculationAPI,
  calculateLocalShippingCost,
  updateBudgetWithShippingData,
  validateShippingWeight,
  recalculateShippingCost,
  getCurrentShippingCost,
  getCurrentPaperWeight,
  isShippingCalculatedLocally
} from '../shippingCalculationService';

// Mock de fetch global
global.fetch = jest.fn();

// Mock de localStorage
const mockLocalStorage = {
  getItem: jest.fn(() => 'mock-token'),
  setItem: jest.fn(),
  removeItem: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

describe('shippingCalculationService', () => {
  beforeEach(() => {
    fetch.mockClear();
    mockLocalStorage.getItem.mockClear();
  });

  describe('prepareShippingRequestData', () => {
    test('should prepare basic request data without client', () => {
      const result = prepareShippingRequestData(15.5);
      
      expect(result).toEqual({
        weight_kg: 15.5,
        country: "España"
      });
    });

    test('should include client_id when client is provided', () => {
      const client = { client_id: 'client-123' };
      const result = prepareShippingRequestData(10.2, client);
      
      expect(result).toEqual({
        weight_kg: 10.2,
        country: "España",
        client_id: 'client-123'
      });
    });

    test('should handle client without client_id', () => {
      const client = { name: 'Test Client' };
      const result = prepareShippingRequestData(5.0, client);
      
      expect(result).toEqual({
        weight_kg: 5.0,
        country: "España"
      });
    });
  });

  describe('validateShippingWeight', () => {
    test('should return true for positive weight', () => {
      expect(validateShippingWeight(10.5)).toBe(true);
      expect(validateShippingWeight(0.1)).toBe(true);
    });

    test('should return false for zero or negative weight', () => {
      expect(validateShippingWeight(0)).toBe(false);
      expect(validateShippingWeight(-5)).toBe(false);
    });
  });

  describe('calculateLocalShippingCost', () => {
    test('should calculate cost for weight <= 5kg', () => {
      const result = calculateLocalShippingCost(3);
      
      expect(result).toEqual({
        weight: 3,
        cost: 10,
        country: "España",
        distance_factor: 1,
        calculated_locally: true
      });
    });

    test('should calculate cost for weight <= 10kg', () => {
      const result = calculateLocalShippingCost(8);
      
      expect(result).toEqual({
        weight: 8,
        cost: 15,
        country: "España",
        distance_factor: 1,
        calculated_locally: true
      });
    });

    test('should calculate cost for weight <= 20kg', () => {
      const result = calculateLocalShippingCost(15);
      
      expect(result).toEqual({
        weight: 15,
        cost: 20,
        country: "España",
        distance_factor: 1,
        calculated_locally: true
      });
    });

    test('should calculate cost for weight > 20kg', () => {
      const result = calculateLocalShippingCost(25);
      
      expect(result).toEqual({
        weight: 25,
        cost: 27.5, // 25 + (25-20) * 0.5
        country: "España",
        distance_factor: 1,
        calculated_locally: true
      });
    });

    test('should handle negative weight', () => {
      const result = calculateLocalShippingCost(-5);
      
      expect(result.weight).toBe(0);
      expect(result.cost).toBe(10);
    });
  });

  describe('callShippingCalculationAPI', () => {
    test('should make successful API call', async () => {
      const mockResponse = {
        shipping_cost: 25.50,
        country: "Francia",
        distance_factor: 1.2
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const requestData = { weight_kg: 10, country: "España" };
      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);

      const result = await callShippingCalculationAPI(requestData, mockBuildApiUrl);

      expect(fetch).toHaveBeenCalledWith(
        'http://api.test/calculations/shipping-cost',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          }),
          body: JSON.stringify(requestData)
        })
      );

      expect(result).toEqual(mockResponse);
    });

    test('should throw error on failed API call', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        statusText: 'Internal Server Error'
      });

      const requestData = { weight_kg: 10 };
      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);

      await expect(callShippingCalculationAPI(requestData, mockBuildApiUrl))
        .rejects.toThrow('Error al calcular el costo de envío: Internal Server Error');
    });
  });

  describe('updateBudgetWithShippingData', () => {
    test('should update budget with shipping data', () => {
      const shippingData = {
        shipping_cost: 30,
        country: "Francia",
        distance_factor: 1.5
      };
      const totalPaperWeight = 12;
      const mockSetBudget = jest.fn();

      const result = updateBudgetWithShippingData(shippingData, totalPaperWeight, mockSetBudget);

      expect(mockSetBudget).toHaveBeenCalledWith(expect.any(Function));
      
      // Verificar que la función pasada actualiza correctamente
      const updateFunction = mockSetBudget.mock.calls[0][0];
      const updatedBudget = updateFunction({ otNumber: 'OT-001' });
      
      expect(updatedBudget).toEqual({
        otNumber: 'OT-001',
        total_paper_weight_kg: 12,
        shipping: {
          weight: 12,
          cost: 30,
          country: "Francia",
          distance_factor: 1.5,
          calculated_locally: false
        }
      });

      expect(result).toEqual({
        weight: 12,
        cost: 30,
        country: "Francia",
        distance_factor: 1.5,
        calculated_locally: false
      });
    });

    test('should handle local calculation data', () => {
      const shippingData = {
        cost: 15,
        country: "España",
        calculated_locally: true
      };
      const totalPaperWeight = 8;
      const mockSetBudget = jest.fn();

      const result = updateBudgetWithShippingData(shippingData, totalPaperWeight, mockSetBudget);

      expect(result.calculated_locally).toBe(true);
      expect(result.cost).toBe(15);
    });
  });

  describe('recalculateShippingCost', () => {
    test('should successfully calculate shipping cost', async () => {
      const mockShippingData = {
        shipping_cost: 20,
        country: "España",
        distance_factor: 1
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockShippingData
      });

      const mockCalculateTotalPaperWeight = jest.fn(() => 10);
      const mockSetBudget = jest.fn();
      const mockShowSnackbar = jest.fn();
      const mockBuildApiUrl = jest.fn(path => `http://api.test${path}`);

      const params = {
        budgetParts: [{ paper: { weight: 80 } }],
        budget: { copies: 500, client: { client_id: 'client-1' } },
        calculateTotalPaperWeight: mockCalculateTotalPaperWeight,
        buildApiUrl: mockBuildApiUrl,
        setBudget: mockSetBudget,
        showSnackbar: mockShowSnackbar
      };

      const result = await recalculateShippingCost(params);

      expect(mockCalculateTotalPaperWeight).toHaveBeenCalledWith(params.budgetParts, params.budget.copies);
      expect(fetch).toHaveBeenCalled();
      expect(mockSetBudget).toHaveBeenCalled();
      expect(result.cost).toBe(20);
      expect(result.weight).toBe(10);
    });

    test('should return zero cost for invalid weight', async () => {
      const mockCalculateTotalPaperWeight = jest.fn(() => 0);
      const mockSetBudget = jest.fn();
      const mockShowSnackbar = jest.fn();

      const params = {
        budgetParts: [],
        budget: { copies: 0 },
        calculateTotalPaperWeight: mockCalculateTotalPaperWeight,
        buildApiUrl: jest.fn(),
        setBudget: mockSetBudget,
        showSnackbar: mockShowSnackbar
      };

      const result = await recalculateShippingCost(params);

      expect(result).toEqual({ weight: 0, cost: 0 });
      expect(fetch).not.toHaveBeenCalled();
    });

    test('should fallback to local calculation on API error', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const mockCalculateTotalPaperWeight = jest.fn(() => 15);
      const mockSetBudget = jest.fn();
      const mockShowSnackbar = jest.fn();
      const mockBuildApiUrl = jest.fn();

      const params = {
        budgetParts: [{ paper: { weight: 80 } }],
        budget: { copies: 500, total_paper_weight_kg: 15 },
        calculateTotalPaperWeight: mockCalculateTotalPaperWeight,
        buildApiUrl: mockBuildApiUrl,
        setBudget: mockSetBudget,
        showSnackbar: mockShowSnackbar
      };

      const result = await recalculateShippingCost(params);

      expect(mockShowSnackbar).toHaveBeenCalledWith(
        expect.stringContaining('Error al calcular el costo de envío'),
        'error'
      );
      expect(result.calculated_locally).toBe(true);
      expect(result.cost).toBe(20); // 15kg -> 20 euros
    });
  });

  describe('utility functions', () => {
    test('getCurrentShippingCost should return shipping cost', () => {
      const budget1 = { shipping: { cost: 25 } };
      const budget2 = { shipping_cost: 30 };
      const budget3 = {};

      expect(getCurrentShippingCost(budget1)).toBe(25);
      expect(getCurrentShippingCost(budget2)).toBe(30);
      expect(getCurrentShippingCost(budget3)).toBe(0);
    });

    test('getCurrentPaperWeight should return paper weight', () => {
      const budget1 = { shipping: { weight: 12.5 } };
      const budget2 = { total_paper_weight_kg: 8.2 };
      const budget3 = {};

      expect(getCurrentPaperWeight(budget1)).toBe(12.5);
      expect(getCurrentPaperWeight(budget2)).toBe(8.2);
      expect(getCurrentPaperWeight(budget3)).toBe(0);
    });

    test('isShippingCalculatedLocally should return correct boolean', () => {
      const budget1 = { shipping: { calculated_locally: true } };
      const budget2 = { shipping: { calculated_locally: false } };
      const budget3 = {};

      expect(isShippingCalculatedLocally(budget1)).toBe(true);
      expect(isShippingCalculatedLocally(budget2)).toBe(false);
      expect(isShippingCalculatedLocally(budget3)).toBe(false);
    });
  });
});
