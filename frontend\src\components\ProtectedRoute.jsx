import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

/**
 * Componente para proteger rutas que requieren autenticación
 * @param {Object} props - Propiedades del componente
 * @param {React.ReactNode} props.children - Componentes hijos a renderizar si el usuario está autenticado
 * @param {string} [props.requiredRole] - Rol requerido para acceder a la ruta (opcional)
 * @returns {React.ReactNode} - Componente hijo o redirección a login
 */
const ProtectedRoute = ({ children, requiredRole }) => {
  const { isAuthenticated, role, loading } = useAuth();
  const location = useLocation();

  // Si está cargando, mostrar un indicador de carga
  if (loading) {
    return <div>Cargando...</div>;
  }

  // Si no está autenticado, redirigir a la página de login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Si se requiere un rol específico y el usuario no lo tiene, redirigir a una página de acceso denegado
  if (requiredRole && role !== requiredRole) {
    return <Navigate to="/access-denied" replace />;
  }

  // Si está autenticado y tiene el rol requerido, mostrar los componentes hijos
  return children;
};

export default ProtectedRoute;
