import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { buildApiUrl } from '../config';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  TextField,
  IconButton,
  Tooltip,
  Paper
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import DownloadIcon from '@mui/icons-material/Download';
import CodeIcon from '@mui/icons-material/Code';

const BudgetJsonViewer = ({ budgetId, onClose }) => {
  const [budget, setBudget] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [copied, setCopied] = useState(false);

  // Cargar datos del presupuesto
  useEffect(() => {
    const fetchBudget = async () => {
      try {
        setLoading(true);
        setError(null);

        // Obtener el token de autenticación del localStorage
        const token = localStorage.getItem('auth_token');

        if (!token) {
          throw new Error('No se encontró el token de autenticación. Por favor, inicie sesión nuevamente.');
        }

        // Obtener el presupuesto
        const response = await fetch(buildApiUrl(`/budgets/${budgetId}`), {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        if (!response.ok) throw new Error('Error al obtener el presupuesto');

        const data = await response.json();

        // Simplificar el objeto sheet_calculation si existe
        if (data.sheet_calculation) {
          // Conservar solo la mejor combinación y adaptarla al formato JDF
          const mejorCombinacion = data.sheet_calculation.mejor_combinacion;

          if (mejorCombinacion && mejorCombinacion.esquemas_utilizados) {
            // Adaptar los esquemas utilizados para que sean consistentes con el formato JDF
            mejorCombinacion.esquemas_utilizados = mejorCombinacion.esquemas_utilizados.map(esquema => {
              // Asegurarse de que el estilo de trabajo sea consistente con el JDF
              const workStyle = esquema.es_tira_retira ? "WorkAndTurn" : "WorkAndBack";

              return {
                ...esquema,
                work_style: workStyle
              };
            });
          }

          const simplifiedSheetCalculation = {
            esquema_utilizado: mejorCombinacion
          };

          data.sheet_calculation = simplifiedSheetCalculation;
        }

        setBudget(data);
        setLoading(false);
      } catch (err) {
        console.error('Error al cargar datos:', err);
        setError(err.message);
        setLoading(false);
      }
    };

    fetchBudget();
  }, [budgetId]);

  // Función para copiar el JSON al portapapeles
  const copyToClipboard = () => {
    if (budget) {
      navigator.clipboard.writeText(JSON.stringify(budget, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // Función para descargar el JSON como archivo
  const downloadJson = () => {
    if (budget) {
      const jsonString = JSON.stringify(budget, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Presupuesto_${budget.ot_number ? budget.ot_number.replace('OT-', '') : budget.budget_id}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <Dialog
      open={true}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CodeIcon sx={{ mr: 1 }} />
          Datos JSON del Presupuesto {budget?.ot_number ? budget.ot_number.replace('OT-', '') : 'cargando...'}
        </Box>
        {budget && (
          <Box>
            <Tooltip title={copied ? "¡Copiado!" : "Copiar JSON"}>
              <IconButton onClick={copyToClipboard} color={copied ? "success" : "default"}>
                <ContentCopyIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Descargar JSON">
              <IconButton onClick={downloadJson}>
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </DialogTitle>
      <DialogContent dividers>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error" variant="body1">
            Error: {error}
          </Typography>
        ) : budget ? (
          <Box>
            <Paper elevation={2} sx={{ p: 2, mb: 3, bgcolor: '#f5f5f5' }}>
              <Typography variant="h6" gutterBottom>Resumen del Presupuesto</Typography>
              <Typography variant="body1">OT: <strong>{budget.ot_number.replace('OT-', '')}</strong></Typography>
              <Typography variant="body1">Descripción: <strong>{budget.description}</strong></Typography>
              <Typography variant="body1">Cliente: <strong>{budget.client?.company?.name || 'No especificado'}</strong></Typography>
              <Typography variant="body1">Tipo de trabajo: <strong>{budget.job_type}</strong></Typography>
              <Typography variant="body1">Cantidad: <strong>{budget.quantity}</strong></Typography>
              <Typography variant="body1">Estado: <strong>{budget.status}</strong></Typography>
            </Paper>

            <TextField
              label="JSON del Presupuesto"
              multiline
              fullWidth
              variant="outlined"
              value={JSON.stringify(budget, null, 2)}
              InputProps={{
                readOnly: true,
                sx: {
                  fontFamily: 'monospace',
                  fontSize: '0.9rem',
                  minHeight: '40vh',
                  maxHeight: '60vh',
                  overflowY: 'auto'
                }
              }}
            />
          </Box>
        ) : (
          <Typography>No se encontraron datos</Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

BudgetJsonViewer.propTypes = {
  budgetId: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired
};

export default BudgetJsonViewer;
